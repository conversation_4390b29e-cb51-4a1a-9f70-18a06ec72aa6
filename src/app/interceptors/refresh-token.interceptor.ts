import { catchError, tap, map, switchMap, filter, take } from 'rxjs/operators';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
} from '@angular/common/http';

import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { isPlatformBrowser } from '@angular/common';
@Injectable()
export class RefreshTokenInterceptorService implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private router: Router,
    private spinner: NgxSpinnerService,
    @Inject(PLATFORM_ID) private platformId: any
  ) {}
  private refreshingInProgress: boolean;
  private accessTokenSubject: BehaviorSubject<string> =
    new BehaviorSubject<string>(null);
  private refreshTokenCallMade = false;
  intercept(
    req: HttpRequest<any>,
    next: HttpHand<PERSON>
  ): Observable<HttpEvent<any>> | any {
    if (isPlatformBrowser(this.platformId)) {
      const accessToken = sessionStorage.getItem('accessToken');
      return next.handle(this.addAuthorizationHeader(req, accessToken)).pipe(
        catchError((err) => {
          this.spinner.hide();
          let urlInput = new URL(req.url);
          console.log(urlInput.hostname);
          if (urlInput.hostname === 'api.collector.terrain.art') {
            return throwError(err);
          }
          if (err.status === 401 && !this.refreshTokenCallMade) {
            const refreshToken = localStorage.getItem('refreshToken');
            if (refreshToken) {
              this.refreshTokenCallMade = true;
              return this.refreshToken(req, next);
            }
            return this.logoutAndRedirect(err);
          }
          if (err instanceof HttpErrorResponse && err.status === 401) {
            return this.logoutAndRedirect(err);
          }
          return throwError(err);
        })
      );
    } else {
      return next.handle(req);
    }
  }

  private logoutAndRedirect(err): Observable<HttpEvent<any>> {
    sessionStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    //this.router.navigate(['']);
    localStorage.clear();
    return throwError(err);
  }

  private addAuthorizationHeader(
    request: HttpRequest<any>,
    token: string
  ): HttpRequest<any> {
    // let urlInput = new URL(request.url);
    // if (urlInput.hostname !== 'api.main.terrain.art') {
    //   return request;
    // }
    if (token) {
      return request.clone({
        setHeaders: { Authorization: `Bearer ${token}` },
      });
    }
    return request;
  }

  private refreshToken(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (!this.refreshingInProgress) {
      this.refreshingInProgress = true;
      this.accessTokenSubject.next(null);

      return this.authService.refreshToken().pipe(
        switchMap((res) => {
          this.refreshingInProgress = false;
          this.refreshTokenCallMade = false;
          this.accessTokenSubject.next(res.accessToken);
          return next.handle(
            this.addAuthorizationHeader(request, res.accessToken)
          );
        })
      );
    } else {
      // wait while getting new token
      return this.accessTokenSubject.pipe(
        filter((token) => token !== null),
        take(1),
        switchMap((token) => {
          // repeat failed request with new token
          return next.handle(this.addAuthorizationHeader(request, token));
        })
      );
    }
  }
}
