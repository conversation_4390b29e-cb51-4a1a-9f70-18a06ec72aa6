import { CardBoxItem } from '../core/card-box/card-box.model';

export const artistCard: CardBoxItem[] = [
  {
    url_name: 'enit-maria',
    image:
      'https://register.terrain.art/artist-portal/assets/ktjxj9l2qfkck4sg?key=website-cards',
    routerLink: '',
    heading: 'Enit María',
    subheading:
      'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
  },
  {
    url_name: 'jay<PERSON>-bhat<PERSON>charya',
    image:
      'https://register.terrain.art/artist-portal/assets/7bwpm2m9wtoog8s8?key=website-cards',
    routerLink: '',
    heading: '<PERSON><PERSON>charya',
    subheading:
      '<PERSON>rem Ipsum is simply dummy text of the printing and typesetting industry.',
  },

  {
    url_name: 'nur-mahammad',
    image:
      'https://register.terrain.art/artist-portal/assets/m8hyy114mbk0s08s?key=website-cards',
    routerLink: '',
    heading: 'Nur Mahammad',
    subheading:
      'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
  },
  {
    url_name: 'shivang<PERSON>-ladha',
    image:
      'https://register.terrain.art/artist-portal/assets/nmucnqbr3j4go80s?key=website-cards',
    routerLink: '',
    heading: 'Shivangi Ladha',
    subheading:
      'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
  },
  {
    url_name: 'srinivas-mangipudi',
    image:
      'https://register.terrain.art/artist-portal/assets/b1akwpay814o4w4s?key=website-cards',
    routerLink: '',
    heading: 'Srinivas Mangipudi',
    subheading:
      'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
  },
];

export const artistCard2: CardBoxItem[] = [
  {
    url_name: '0',
    image:
      'https://register.terrain.art/artist-portal/assets/i875nremrfw44cso?key=website-cards',
    routerLink: '',
    heading: 'Abstraction',
    subheading: '',
  },
  {
    url_name: '4',
    image:
      'https://register.terrain.art/artist-portal/assets/6u3n1wrbxqsckw8s?key=website-cards',
    routerLink: '',
    heading: 'Painting',
    subheading: '',
  },

  {
    url_name: '2',
    image:
      'https://register.terrain.art/artist-portal/assets/5936oibubikos0g8?key=website-cards',
    routerLink: '',
    heading: 'Photography',
    subheading: '',
  },
  {
    url_name: '3',
    image:
      'https://register.terrain.art/artist-portal/assets/8psfvcmfxg08wso8?key=website-cards',
    routerLink: '',
    heading: 'Artist Collectives & Collaboration',
    subheading: '',
  },
  {
    url_name: '1',
    image:
      'https://register.terrain.art/artist-portal/assets/ejn6nfgakn408soc?key=website-cards',
    routerLink: '',
    heading: 'Portraiture',
    subheading: '',
  },
];
export const artistCard3: CardBoxItem[] = [
  {
    image:
      'https://register.terrain.art/artist-portal/assets/ktjxj9l2qfkck4sg?key=website-cards',
    routerLink: '',
    heading: 'Gestural Abstraction',
    subheading: '',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/7bwpm2m9wtoog8s8?key=website-cards',
    routerLink: '',
    heading: 'Minimalism',
    subheading: '',
  },

  {
    image:
      'https://register.terrain.art/artist-portal/assets/m8hyy114mbk0s08s?key=website-cards',
    routerLink: '',
    heading: 'Installation',
    subheading: '',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/nmucnqbr3j4go80s?key=website-cards',
    routerLink: '',
    heading: 'Intaglio',
    subheading: '',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/b1akwpay814o4w4s?key=website-cards',
    routerLink: '',
    heading: 'Letterpress',
    subheading: '',
  },
];
