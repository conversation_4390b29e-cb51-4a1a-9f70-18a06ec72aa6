import { BannerSliderItem } from './../core/banner-slider/banner-slider.model';
export const bannerDemo: BannerSliderItem[] = [
  {
    image:
      'https://register.terrain.art/artist-portal/assets/3zul5fiz7j408c80?key=website',
    heading: '<PERSON><PERSON><PERSON>, Concolon, 2018',
    subheading: 'Acrylic on canvas, 30 x 40 in',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/5zdqm4x5mtwcw4sk?key=website',
    heading: 'Shivangi Ladha, Oneness, 2020',
    subheading: 'Watercolor on Hahnemuhle Paper, 30.7 x 20.8 in',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/9w4lj84v8iwoko4w?key=website',
    heading: '<PERSON><PERSON> Maham<PERSON>, Freedom at Threshold, 2018',
    subheading: 'Watercolour on Paper, 89h x 137w cm | 35.04h x 53.94w in',
  },
];
export const bannerDemo2: BannerSliderItem[] = [
  {
    image:
      'https://register.terrain.art/artist-portal/assets/qnubpg2sfusgkogw?key=website',
    heading: `<a href="learn">Learn</a> about art history and art terms.
          <a href="explore">Explore</a> and buy art pieces. Under
          <a href="#">Archive</a> you can learn about an artist’s practice and
          get an in depth understanding of artworks.`,
    subheading: 'Shivangi Ladha, Becoming Tree I, 2020',
    backgroundColor: '#d9dade',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/5zdqm4x5mtwcw4sk?key=website',
    heading: `Register, verify, and purchase artworks securely stored on blockchain. Build your art collection with works from exciting young artists.`,
    subheading: 'Shivangi Ladha, Oneness, 2020',
    backgroundColor: '#d9dade',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/c8ywapkjsw84ckg8?key=website',
    heading: `<a href="learn">Learn</a> the language of art and art history with free educational videos illustrating key concepts through exemplary works of South Asian modern and contemporary art.`,
    subheading: 'Srinivas Mangipudi, Tudo de bom, 2018',
    backgroundColor: '#d9dade',
  },
];
export const bannerDemo3: BannerSliderItem[] = [
  {
    image:
      'https://register.terrain.art/artist-portal/assets/fx4g2tmolk0kk4cg?key=website',
    heading: 'What is Abstraction?',
    subheading: '',
  },
  {
    image: 'https://register.terrain.art/artist-portal/assets/ojtiz2ik6hccog04',
    heading:
      'Explore our interactive timeline on Modern & Contemporary Indian Art',
    subheading: '',
  },
  {
    image:
      'https://register.terrain.art/artist-portal/assets/55iuntz8hk84ssk8?key=website',
    heading: 'Gallery Luhring Augustine',
    subheading: '',
  },
];
