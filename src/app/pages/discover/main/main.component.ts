import {
  NavBarService,
  NavBarType,
} from './../../../shared/services/navbar.service';
import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { BannerSliderItem } from 'src/app/core/banner-slider/banner-slider.model';
import { CardBoxItem } from 'src/app/core/card-box/card-box.model';
import {
  MediaForSlider,
  SlideMediaType,
} from 'src/app/core/single-slider/single-slider.model';
import { Artwork } from '../../explore/artists/models/artwork.model';
import { ExplorePageModel } from '../../explore/models/explore-page.model';
import { ArtResourceService } from '../../explore/services/art-resource.service';
import { ArtworkService } from '../../explore/services/artwork.service';
import { ExhibitionService } from '../../explore/services/exhibition.service';
import { ExploreService } from '../../explore/services/explore.service';
import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { faCube } from '@fortawesome/free-solid-svg-icons';
import { faImage } from '@fortawesome/free-solid-svg-icons';
import { galleriesDemo } from 'src/app/demo/galleries.demo';
import { LearnPageService } from '../../learn/services/learn.service';
import { GlossaryService } from '../../learn/services/glossary.service';
import { PartnerService } from '../../learn/services/partners.service';
import { LearnPageModel } from '../../learn/models/learn-page.model';
import { ArtistInfoModel } from '../../learn/models/artists.model';
import { GlossaryModel } from '../../learn/models/glossary.model';
import { artistCard2 } from 'src/app/demo/artist-card.demo';
import { isPlatformBrowser } from '@angular/common';
import { CollectorService } from 'src/app/services/collector.service';
@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit {
  people: any[] = [{}, {}, {}];
  artistDataLearn = artistCard2;
  artistData: CardBoxItem[];
  limitedEdition: CardBoxItem[];
  galleries = galleriesDemo;
  faCube = faCube;
  faImage = faImage;
  faChevronRight = faChevronRight;
  bannerSlider: BannerSliderItem[] = [];
  // bannerSliderLearn: BannerSliderItem[] = [];
  discoverSlider: BannerSliderItem[] = [
    {
      image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1747469046004_maze%20.jpg',
      heading: 'Discover Amazing Artworks',
      subheading: 'Explore our curated collection'
    },
    {
      image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1748947575390_IMG_1341.jpeg',
      heading: 'Contemporary Art',
      subheading: 'Modern expressions and creativity'
    },
    {
      image: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1748412729400_Waltzing%20The%20Wild%204.jpg',
      heading: 'Artistic Excellence',
      subheading: 'Masterpieces from talented artists'
    }
  ];
  categories: string[] = [];
  forSaleartworks: Artwork[] = [];
  isMoreCategories = false;
  exploreData: ExplorePageModel;
  exhibionSlider2: MediaForSlider[] = [
    {
      mediaType: SlideMediaType.IMAGE,
      url: 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/cvznxu80v5kww4o0',
    },
  ];
  filteredResources = [];
  learnData: LearnPageModel;
  blogList = [];
  selectedArtists: ArtistInfoModel[] = [];
  artistsList = [];
  glossaryDatas: GlossaryModel[] = [];
  videoSeries: CardBoxItem[] = [];
  resources = [
    {
      value: 'Art Restorer',
      count: 0,
      name: 'Restorer',
      link: 'Restorer',
    },
    {
      value: 'Insurers',
      count: 0,
      name: 'Insurers',
      link: 'Insurers',
    },
    {
      value: 'Framers',
      count: 0,
      name: 'Framers',
      link: 'Framers',
    },
    {
      value: 'Shippers',
      count: 0,
      name: 'Shippers',
      link: 'Shippers',
    },
    {
      value: 'Interior Designers',
      count: 0,
      name: 'Interior Designers',
      link: 'Interior-Designers',
    },
    {
      value: 'Museums',
      count: 0,
      name: 'Museums',
      link: 'Museums',
    },
    {
      value: 'Printers',
      count: 0,
      name: 'Printers',
      link: 'Printers',
    },
    {
      value: 'Printers & Framers',
      count: 0,
      name: 'Printers & Framers',
      link: 'Printers-Framers',
    },
    {
      value: 'Shippers & Movers',
      count: 0,
      name: 'Shippers & Movers',
      link: 'Shippers-Movers',
    },
    {
      value: 'Art Storage',
      count: 0,
      name: 'Storage',
      link: 'Storage',
    },
    {
      value: 'Art Conservator',
      count: 0,
      name: 'Conservator',
      link: 'Conservator',
    },
    {
      value: 'Legal',
      count: 0,
      name: 'Legal',
      link: 'Legal',
    },
    {
      value: 'Photographer',
      count: 0,
      name: 'Photographer',
      link: 'Photographer',
    },
  ];

  exhibitonsData = [];
  artworkResource = [
    {
      routerLink: '/learn/art-resources',
      queryparam: 'Restorer',
      image:
        'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://static01.nyt.com/images/2019/10/27/multimedia/27sp-conserve-ringling/merlin_162645336_0ab0ef0f-4b1c-4c29-91e7-6d6063b92703-articleLarge.jpg?quality=75&auto=webp&disable=upscale',
      heading: 'Art Restorers',
    },
    {
      routerLink: '/learn/art-resources',
      queryparam: 'Insurers',
      image:
        'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://play-lh.googleusercontent.com/4e-a-TLYDy3wIODpQW91o6RjUJ7G8ohm77noEiTOVASeteZld0FGlC11_6sA1GA9d2c',
      heading: 'Insurers',
    },
    {
      routerLink: '/learn/art-resources',
      queryparam: 'Framers',
      image:
        'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://images.unsplash.com/flagged/photo-1551373916-bdaddbf4f881?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZnJhbWV8ZW58MHx8MHx8&auto=format&fit=crop&w=600&q=60',
      heading: 'Framers',
    },
    {
      routerLink: '/learn/art-resources',
      queryparam: 'Shippers',
      image:
        'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://pbs.twimg.com/profile_images/1410602066356015109/7AixQxJ1_400x400.jpg',
      heading: 'Shippers',
    },
  ];

  isMobile = false;
  isBrowser = false;
  pageData;
  constructor(
    private artworkService: ArtworkService,
    private exploreService: ExploreService,
    private exhibitionService: ExhibitionService,
    private artResourceService: ArtResourceService,
    private titleService: Title,
    private metaService: Meta,
    private learnPageService: LearnPageService,
    private glossaryService: GlossaryService,
    private galleryService: PartnerService,
    private navBarService: NavBarService,
    private server: CollectorService,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.isBrowser = true;
    }
  }

  ngOnInit(): void {
    this.artResourceService.getArtResources().then((data) => {
      data.forEach((a) => {
        const index = this.resources.findIndex(
          (x) => x.value === a.resource_type
        );
        this.resources[index].count++;
      });
      this.filteredResources = this.resources.filter((a) => a.count !== 0);
    });
    this.getPageData();
    if (this.isBrowser) {
      this.initData();
    }
  }

  public getPageData() {
    let url = `website?page=discover`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200 && res.data) {
        this.pageData = res.data.contents;
      }
    });
  }

  initData() {
    this.navBarService.changeNavbarType(NavBarType.TRANSPARENT);
    this.setSEOTags();
    this.artResourceService.getArtResources().then((data) => {
      data.forEach((a) => {
        const index = this.resources.findIndex(
          (x) => x.value === a.resource_type
        );
        this.resources[index].count++;
      });
      this.filteredResources = this.resources.filter((a) => a.count !== 0);
    });
    this.isMobile = this.getIsMobile();
    window.onresize = () => {
      this.isMobile = this.getIsMobile();
    };
    this.exploreService.getExploreData().then((data) => {
      this.exploreData = data;

      this.exploreData.banner1_data.sort((a, b) =>
        Number(a.index) > Number(b.index)
          ? 1
          : Number(b.index) > Number(a.index)
            ? -1
            : 0
      );
      this.bannerSlider = this.exploreData.banner1_data.map((a) => {
        return {
          image: `https://www.terrain.art/cdn-cgi/image/width=1000,quality=82/${a.image_source}?key=website`,
          heading: a.title,
          subheading: a.subtitle,
        };
      });
      this.exploreData.for_sale_artworks.forEach((a) => {
        if (a['11_artworks_id']) {
          this.forSaleartworks.push(a['11_artworks_id']);
        }
      });

      this.exploreData.medium_categories.forEach((a) => {
        this.categories.push(a['30_medium_keywords_id'].name);
      });
      this.exploreData.movement_categories.forEach((a) => {
        this.categories.push(a['32_movement_keywords_id'].name);
      });
      this.artistData = this.exploreData.select_artists.map((a) => {
        return {
          url_name: a['01_personal_info_id'].url_name,
          image: `https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/${a['01_personal_info_id'].artist_list_thumbnail.private_hash}
          ?key=website-cards`,
          routerLink:
            '/explore/limited-edition-prints/' +
            a['01_personal_info_id'].url_name,
          heading: a['01_personal_info_id'].display_name,
          subheading: '',
        };
      });

      this.limitedEdition = this.exploreData.limited_edition_select.map((a) => {
        return {
          url_name: String(a['11_artworks_id'].id),
          image: `https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/${a['11_artworks_id'].thumbnail_of_primary.private_hash}
          ?key=website-cards`,
          routerLink:
            '/explore/limited-edition-prints/' + a['11_artworks_id'].id,
          heading:
            a['11_artworks_id'].artist_name?.first_name +
            ' ' +
            a['11_artworks_id'].artist_name?.last_name,
          subheading: '',
        };
      });
    });
    this.exhibitionService.getExhibitionInfo().then((d) => {
      const data = d.reverse();
      this.exhibitonsData = [];
      for (let i = 0; i < 4; i++) {
        const exhib = data[i];
        this.exhibitonsData.push({
          routerLink:
            '/explore/exhibitions/' +
            exhib.artist_select.url_name +
            '/' +
            exhib.id,
          image:
            'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/' +
            exhib.tech_image_1.private_hash,
          heading: exhib.exhibition_title,
          subheading: exhib.artist_select.display_name,
        });
      }
    });

    this.learnPageService.getLearnData().then((data) => {
      this.learnData = data;
      let value = '';
      this.blogList = this.learnData.blog_select.map((blog) => ({
        routerLink:
          '/learn/blogs/' +
          (blog?.['70_articles_id'].blog_type == 'Articles'
            ? 'articles/'
            : 'videos/') +
          blog?.['70_articles_id'].id,
        image:
          'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/' +
          blog?.['70_articles_id'].banner_image.private_hash,
        heading: blog?.['70_articles_id'].article_title,
        subheading: '',
      }));
      this.learnData.artists_select.forEach((element) => {
        value += encodeURIComponent(element.artist_id) + ',';
      });
      this.learnPageService.getArtistData(value).then((artistDataLearn) => {
        this.selectedArtists = artistDataLearn;

        this.artistsList = this.selectedArtists.map((artist) => ({
          routerLink: '/learn/artists/' + artist?.page_url,
          image:
            'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://pricedb.terrain.art/pricedb/assets/' +
            artist?.thumbnail_image?.private_hash,
          heading: artist?.artist_name,
          subheading: artist?.demise_year
            ? artist?.birth_year + ' - ' + artist?.demise_year
            : artist?.birth_year
              ? 'b. ' + artist?.birth_year
              : '',
        }));
      });

      const gloassaries = this.learnData.glossary_data.map(
        (a) => a.glossary_id
      );

      this.glossaryService
        .getGlossariesByIds(gloassaries.join(','))
        .then((data) => {
          this.glossaryDatas = data;
        });

      const gallaries = this.learnData.gallery_data.map((a) => a.gallery_id);

      this.galleryService
        .getGalleriesByIds(gallaries.join(','))
        .then((data) => {
          data.forEach((a) => {
            this.galleries.push({
              logo: `https://pricedb.terrain.art/pricedb/assets/${a.gallery_logo.private_hash}?key=directus-large-contain`,
              artwork_title: a.gallery_name,
              medium: a.location,
              primary_image: `https://pricedb.terrain.art/pricedb/assets/${a.page_banner_image.private_hash}`,
            });
            this.galleries.push({
              logo: `https://pricedb.terrain.art/pricedb/assets/${a.gallery_logo.private_hash}?key=directus-large-contain`,
              artwork_title: a.gallery_name,
              medium: a.location,
              primary_image: `https://pricedb.terrain.art/pricedb/assets/${a.page_banner_image.private_hash}`,
            });
            this.galleries.push({
              logo: `https://pricedb.terrain.art/pricedb/assets/${a.gallery_logo.private_hash}?key=directus-large-contain`,
              artwork_title: a.gallery_name,
              medium: a.location,
              primary_image: `https://pricedb.terrain.art/pricedb/assets/${a.page_banner_image.private_hash}`,
            });
          });
        });

      this.learnData.top_banner_data.sort((a, b) =>
        Number(a.index) > Number(b.index)
          ? 1
          : Number(b.index) > Number(a.index)
            ? -1
            : 0
      );
      // this.bannerSliderLearn = this.learnData.top_banner_data.map((a) => {
      //   return {
      //     image: `https://www.terrain.art/cdn-cgi/image/width=2000,quality=52/${a.image_source}?key=website`,
      //     heading: a.text_on_image,
      //     subheading: '',
      //   };
      // });
      this.videoSeries = this.learnData.artwork_terms_select.map((a) => {
        return {
          routerLink: '/learn/education-videos/' + a['50_ed_videos_id'].page_url,
          image: `https://www.terrain.art/cdn-cgi/image/width=1000,quality=82/https://register.terrain.art/artist-portal/assets/${a['50_ed_videos_id'].thumbnail.private_hash}?key=website-cards`,
          heading: a['50_ed_videos_id'].page_title,
          subheading: '',
          url_name: a['50_ed_videos_id'].page_url,
        };
      });
    });
  }

  ngOnDestroy(): void {
    this.navBarService.changeNavbarType(NavBarType.DEFAULT);
  }

  public getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }
  public get categoriesValues(): Array<string> {
    if (this.isMoreCategories) {
      return this.categories;
    }
    return this.categories.slice(0, 10);
  }

  public get artworksList(): Artwork[] {
    const list: Artwork[] = this.forSaleartworks;
    return list;
  }
  setSEOTags() {
    this.titleService.setTitle(
      'Discover Original Artworks | Exhibitions, Prints & NFTs By Indian Artists'
    );
    this.metaService.addTags([
      {
        name: 'description',
        content:
          'Looking to purchase Artworks Online? At Terrain.art, we deal in physical & digital artworks, limited edition prints, NFTs, etc. For enquiries, call @ 9220522788.',
      },
    ]);
  }
  checkIsFullUrl(url) {
    return url.match(/^(http|https):\/\/[^\s\/]+[\s\/]+/);
  }

  hasDiscoverTitle(): boolean {
    // Check if any tile has title "Discover"
    return this.pageData?.tiles?.some(tile => tile.title === 'Discover') || false;
  }
}
