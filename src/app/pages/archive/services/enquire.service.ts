import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
@Injectable({
  providedIn: 'root',
})
export class EnquireService {
  constructor(private http: HttpClient) {}

  async sendEnquire(email: string): Promise<any> {
    await this.http
      .post<any>(`https://api.register.terrain.art/enquire/mail`, {
        email,
        type: 'archive',
      })
      .toPromise();
  }
}
