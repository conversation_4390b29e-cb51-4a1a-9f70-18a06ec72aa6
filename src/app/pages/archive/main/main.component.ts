import {
	<PERSON>mpo<PERSON>,
	<PERSON>ementR<PERSON>,
	<PERSON><PERSON><PERSON>roy,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Meta, Title } from "@angular/platform-browser";
@Component({
	selector: "app-main",
	templateUrl: "./main.component.html",
	styleUrls: ["./main.component.scss"],
})
export class MainComponent implements OnInit {
	bannerSlider = [
		{
			image:
				"https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/l416x2d77twwkgo4?key=website",
			heading: `Browse South Asian artists' oeuvres  with our comprehensive, tamper-proof archive. Painstakingly compiled and a continual work in progress, the archive aims to digitize works by artists in the public realm.`,
			subheading: "",
		},
	];
	isMobile = false;
	constructor(
		private titleService: Title,
		private metaService: Meta,
	) {}

	ngOnInit(): void {
		this.isMobile = this.getIsMobile();
		window.onresize = () => {
			this.isMobile = this.getIsMobile();
		};
		this.setSEOTags();
	}

	public getIsMobile(): boolean {
		const w = document.documentElement.clientWidth;
		const breakpoint = 768;
		if (w <= breakpoint) {
			this.isMobile = true;
			return true;
		} else {
			this.isMobile = false;
			return false;
		}
	}

	setSEOTags() {
		this.titleService.setTitle(
			"South Asian Artists & Artworks | Tamper-Proof Blockchain Technology",
		);
		this.metaService.addTags([
			{
				name: "keywords",
				content: "art collection, indian artists, art history, art terminology",
			},
			{
				name: "description",
				content:
					"Browse South Asian artists' oeuvres with our comprehensive, tamper-proof archive. Interested in learning more about the Archive? Leave us a message.",
			},
			{ name: "og:image", content: "https://test.terrain.art/assets/fav.png" },
			{ name: "og:title", content: "Archive | Terrain.Art" },
			{
				name: "og:description",
				content:
					"Build your art collection from an exciting range of Indian artists, Learn South Asian art history and art terminology, Explore archives of South Asian artists.",
			},
		]);
	}
}
