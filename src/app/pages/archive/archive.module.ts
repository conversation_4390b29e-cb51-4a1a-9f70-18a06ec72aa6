import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { ArchiveRoutingModule } from './archive-routing.module';
import { MainComponent } from './main/main.component';
import { BannerSliderModule } from 'src/app/core/banner-slider/banner-slider.module';
import { EnquireComponent } from './enquire/enquire.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EnquireService } from './services/enquire.service';

@NgModule({
  declarations: [MainComponent, EnquireComponent],
  imports: [
    CommonModule,
    ArchiveRoutingModule,
    SharedModule,
    BannerSliderModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  providers: [EnquireService],
})
export class ArchiveModule {}
