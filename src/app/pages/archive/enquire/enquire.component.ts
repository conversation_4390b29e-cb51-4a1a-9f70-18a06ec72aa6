import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Location } from '@angular/common';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { EnquireService } from '../services/enquire.service';
declare let gtag: Function;
@Component({
  selector: 'enquire-popup',
  templateUrl: './enquire.component.html',
  styleUrls: ['./enquire.component.scss'],
})
export class EnquireComponent implements OnInit, AfterViewInit {
  @ViewChild('recaptcha', { static: true }) recaptchaElement: ElementRef;

  enquireForm: FormGroup;
  Haserror: boolean = false;
  Submitted: boolean = false;
  ErroMsg: string;
  isMobile = false;

  constructor(
    private location: Location,
    private formBuilder: FormBuilder,
    private enquireService: EnquireService
  ) {
    this.enquireForm = this.formBuilder.group({
      email: new FormControl('', [
        Validators.required,
        Validators.pattern(
          /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        ),
      ]),
    });
  }
  ngAfterViewInit(): void {
    this.addRecaptchaScript();
  }

  async ngOnInit(): Promise<void> {
    this.isMobile = this.getIsMobile();
    window.onresize = () => {
      this.isMobile = this.getIsMobile();
    };
  }

  public getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  addRecaptchaScript() {
    window['grecaptchaCallback'] = () => {
      this.renderReCaptcha();
    };

    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['grecaptchaCallback']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src =
        'https://www.google.com/recaptcha/api.js?onload=grecaptchaCallback&render=explicit';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'recaptcha-jssdk', this);
  }

  renderReCaptcha() {
    window['grecaptcha'].render(this.recaptchaElement.nativeElement, {
      sitekey: '6LeBSv0ZAAAAAByosZw1-W6q_qN-CjevjjnSQq7E',
      callback: (response) => {
        console.log(response);
      },
    });
  }
  ClickedOut(event) {
    console.log(event);

    if (
      event.target.className ===
      'd-flex align-items-center justify-content-center h-100 w-100 form-container'
    ) {
      this.location.back();
    }
  }
  async onSubmit() {
    this.Haserror = false;
    this.Submitted = false;
    if (this.enquireForm.controls.email.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Please provide a valid email address.';
    } else if (window['grecaptcha'].getResponse() == '') {
      this.Haserror = true;
      this.ErroMsg = 'reCAPTCHA is not verified!!';
    } else {
      gtag('event', 'submiting_archive_enquire', {
        event_category: 'archive_enquire',
        event_label: 'archive_enquire',
        user_email: this.enquireForm.controls.email,
      });
      this.enquireService
        .sendEnquire(this.enquireForm.controls.email.value)
        .then(() => {
          this.Submitted = true;
          setTimeout(() => {
            this.location.back();
          }, 1500);
        })
        .catch((e) => {
          this.Submitted = false;
          this.Haserror = true;
          this.ErroMsg =
            'Something went wrong. Apologies for the inconvenience, please try again.!!';
        });
    }
  }
  closeAlert() {
    this.Haserror = false;
  }
}
