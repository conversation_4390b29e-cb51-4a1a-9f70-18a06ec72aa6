.section {
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.8);
  .popup-close {
    position: absolute;
    right: 1vw;
    top: 1vw;
    font-size: 2.4vw;
    a {
      color: white;
      img {
        width: 2.22vw;
        height: 2.22vw;
      }
    }
  }
}

.form-enquire {
  padding: 3vw 2vw;
  position: relative;
  width: 40vw;
  background-color: white;
  border-radius: 0.45vw;
  .input-field {
    input::-webkit-input-placeholder {
      font-weight: 500;
    }
    input::-moz-placeholder {
      font-weight: 500;
    }
    input:-ms-input-placeholder {
      font-weight: 500;
    }
    input::-ms-input-placeholder {
      font-weight: 500;
    }
    input::placeholder {
      font-weight: 500;
    }
  }
}
.button {
  border: none;
  cursor: pointer;
  padding: 0.7vw 4.37vw;
  background-color: #004ddd;
  color: #ffffff;
  border-radius: 0.45vw;
}

@media (max-width: 768px) {
  .section {
    .popup-close {
      right: 2.42vw;
      top: 2.42vw;
      font-size: 4.35vw;
      a {
        img {
          width: 5.8vw;
          height: 5.8vw;
        }
      }
    }
  }
  .form-enquire {
    font-size: 3.86vw;
    width: 80vw;
    border-radius: 1.6vw;
    max-height: 80vh;
    overflow: hidden;
    overflow-y: scroll;
  }
  .button {
    padding: 2.42vw 9.66vw;
    border-radius: 1.6vw;
  }
}
