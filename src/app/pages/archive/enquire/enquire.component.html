<div class="section" (click)="ClickedOut($event)">
  <div class="section-inner h-100">
    <div
      class="d-flex align-items-center justify-content-center h-100 w-100 form-container"
    >
      <div class="form-enquire">
        <form [formGroup]="enquireForm" (ngSubmit)="onSubmit()">
          <div class="d-flex justify-content-center" [ngStyle]="{'margin-top' : isMobile ? 4.84+'vw' : 1+'vw'}">
            <div class="input-field" style="width: 60%">
              <!-- <label class="form-control-label">Email</label> -->
              <input
                formControlName="email"
                type="text"
                class="form-control"
                placeholder="Enter email address"
              />
            </div>
          </div>

          <div class="d-flex justify-content-center"  [ngStyle]="{'margin-top' : isMobile ? 4.84+'vw' : 1+'vw'}">
            <div #recaptcha></div>
          </div>
          <div
            class="message alert alert-danger alert-dismissible mt-2"
            *ngIf="Haserror"
          >
            <a
              (click)="closeAlert()"
              class="close"
              data-dismiss="alert"
              aria-label="close"
              >&times;</a
            >
            <strong>Error!</strong> {{ ErroMsg }}
          </div>
          <div
            class="message alert alert-success alert-dismissible mt-2"
            *ngIf="Submitted"
          >
            <a
              (click)="closeAlert()"
              class="close"
              data-dismiss="alert"
              aria-label="close"
              >&times;</a
            >
            <strong>Success!</strong> Message sent successfully!
          </div>

          <div class="d-flex justify-content-center"  [ngStyle]="{'margin-top' : isMobile ? 4.84+'vw' : 1+'vw'}">
            <button type="submit" class="button">Submit</button>
          </div>
        </form>
        <div class="popup-close">
          <a routerLink="../">
            <img src="assets/images/close.png" />
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
