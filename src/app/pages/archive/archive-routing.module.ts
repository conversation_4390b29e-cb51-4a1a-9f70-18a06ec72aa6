import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { EnquireComponent } from './enquire/enquire.component';
import { MainComponent } from './main/main.component';

const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    data: {
      hidebread: true,
    },
    children: [
      {
        path: 'enquire',
        component: EnquireComponent,
        data: {
          hidebread: true,
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ArchiveRoutingModule {}
