import { SeoComponent } from './add-art-resource/seo/seo.component';
import { MainComponent as MainContent } from './add-art-resource/main/main.component';
import { AddArtResourceComponent } from './add-art-resource/add-art-resource.component';
import { MainComponent } from './main/main.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    component: MainComponent
  },
  {
    path: 'add',
    component: AddArtResourceComponent,
    data: {
      hidebread: true,
    },
    children: [
      {
        path: '',
        component: MainContent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'seo',
        component: SeoComponent,
        data: {
          hidebread: true,
        },
      },]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ArtResourcesRoutingModule { }
