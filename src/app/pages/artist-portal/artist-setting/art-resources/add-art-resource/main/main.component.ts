import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, FormArray } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
  isArtworkPopupOpen;
  isFilterDropdownOpen=false;
  isFilterDropdownOpen1=false;
  isDropDownOpen = Array(10).fill(false);
  isDropDownOpen2 = Array(10).fill(false);
  isDropDownOpen3 = Array(10).fill(false);
  isDropDownOpen4 = Array(10).fill(false);

  selectedFiles: File[][] = [[], [], [], [], [], [], [], [], [], [], [], [], [],];

  form: FormGroup;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
  ) { }




  ngOnInit(): void {
    this.isArtworkPopupOpen=false;
    this.form = this.formBuilder.group({
      resourceType:new FormControl(),
      filter: new FormControl({value:'ID',disabled:true}),
      sort: new FormControl({value: "Sort" ,disabled:true}),
      socialMedia: this.formBuilder.array([this.formBuilder.group({})]),
      additionalMedia: this.formBuilder.array([this.formBuilder.group({mediaType: new FormControl('Media Type')})],),
      // selectArtwork: this.formBuilder.array([this.formBuilder.group({})]),
      selectExhibition: this.formBuilder.array([this.formBuilder.group({})]),
      upcomingEvent: this.formBuilder.array([this.formBuilder.group({})]),
      showVideo: new FormControl(false),
      showLogo:new FormControl(false),
      showAdditionalMedia: new FormControl(false),
      // showArtworks: new FormControl(false),
      // showExhibition: new FormControl(false),
      // showEvent: new FormControl(false),
      // showMap: new FormControl(false),
    });
  }



  // defThumbnail: new FormControl(false)

  get additionalMedia(): FormArray {
    return <FormArray>this.form.get('additionalMedia');
  }
  get socialMedia(): FormArray {
    return <FormArray>this.form.get('socialMedia');
  }
  // get selectArtwork(): FormArray {
  //   return <FormArray>this.form.get('selectArtwork');
  // }
  get selectExhibition(): FormArray {
    return <FormArray>this.form.get('selectExhibition');
  }
  get upcomingEvent(): FormArray {
    return <FormArray>this.form.get('upcomingEvent');
  }


  async onFileSelect(index: number, files: FileList) {
    console.log(index);

    if (files[0].size < 2100000) {
      this.selectedFiles[index].push(files[0]);
    }
    console.log(this.selectedFiles);
  }
  removeItem(index: number, item) {
    this.selectedFiles[index].splice(item, 1);
  }
  addRepeater() {

    this.socialMedia.push(
      this.formBuilder.group({})
    );
  }
  addRepeater1() {
    this.additionalMedia.push(
      this.formBuilder.group({mediaType: new FormControl('Media Type')})
    );
  }
  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen2[index] = false;
    }, 200);
  }
  changeFocusRepeater(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen3[index] = false;
    }, 200);
  }

  // addRepeater2() {
  //   this.selectArtwork.push(
  //     this.formBuilder.group({})
  //   );
  // }
  addRepeater3() {
    this.selectExhibition.push(
      this.formBuilder.group({
      })
    );
  }
  addRepeater4() {
    this.upcomingEvent.push(
      this.formBuilder.group({
      })
    );
  }
  onSubmit() {
  }
}