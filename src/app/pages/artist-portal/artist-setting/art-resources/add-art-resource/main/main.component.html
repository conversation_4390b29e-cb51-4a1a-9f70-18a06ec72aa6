<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form [formGroup]="form">
                    <!-- <div style="margin-top: 3.47vw; font-size: 1.25vw">Banner Image</div>
            <div class="field-value flex-block">
              <div class="input-container file-upload">
                <input
                  class="upload-input"
                  type="file"
                  (change)="onFileSelect(0, $event.target.files)"
                  accept="image/*"
                />
                <div *ngIf="selectedFiles[0].length <= 0" class="icon">
                  <img src="assets/icons/<EMAIL>" />
                </div>
                <div *ngIf="selectedFiles[0].length <= 0" class="text-content">
                  <div class="title">You can upload or drop your file here.</div>
                  <div class="sub-title">Maximum upload size: 2 MB</div>
                </div>
                <ng-container *ngIf="selectedFiles[0].length > 0">
                  <ng-container
                    *ngFor="let file of selectedFiles[0]; let i = index"
                  >
                    <div class="text-content">
                      <div class="title">
                        {{ file.name }}
                      </div>
                      <div class="sub-title">
                        File size: {{ (file.size / 1048576).toFixed(2) }} MB
                      </div>
                      <div (click)="removeItem(0, i)" class="close">
                        <img src="assets/icons/close.png" />
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
  
                <div class="button-container">
                  <div class="button">
                    {{ selectedFiles[0].length > 0 ? "Add file" : "Choose file" }}
                  </div>
                </div>
              </div>
              <div class="input-info">
                Provide the banner image to be used on the profile page (Image
                will be centered and cropped to 1440x246 px for Desktop & 414x277
                px for Mobile)
              </div>
            </div> -->




                    <div class="upload-head">Banner Image</div>

                    <div class="field-value flex-block">
                        <image-upload [accept]="'all'" [fileSize]="2147483648" [placeholder]=""></image-upload>
                        <div class="input-info">
                            Provide the banner image to be used on the profile page (Image will be centered and cropped to 1440x246 px for Desktop & 414x277 px for Mobile).
                        </div>
                    </div>











                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Logo :</div>
                            <label class="switch">
                  <input type="checkbox" formControlName="showLogo" />
                  <span class="slider round"></span>
                </label>
                        </div>
                        <div class="input-info">
                            Select whether to show/hide the logo on the page
                        </div>
                    </div>
                    <!-- <div style="margin-top: 3.47vw; font-size: 1.25vw">Resource Logo</div>
            <div class="field-value flex-block">
              <div class="input-container file-upload">
                <input
                  class="upload-input"
                  type="file"
                  (change)="onFileSelect(0, $event.target.files)"
                  accept="image/*"
                />
                <div *ngIf="selectedFiles[0].length <= 0" class="icon">
                  <img src="assets/icons/<EMAIL>" />
                </div>
                <div *ngIf="selectedFiles[0].length <= 0" class="text-content">
                  <div class="title">You can upload or drop your file here.</div>
                  <div class="sub-title">Maximum upload size: 2 MB</div>
                </div>
                <ng-container *ngIf="selectedFiles[0].length > 0">
                  <ng-container
                    *ngFor="let file of selectedFiles[0]; let i = index"
                  >
                    <div class="text-content">
                      <div class="title">
                        {{ file.name }}
                      </div>
                      <div class="sub-title">
                        File size: {{ (file.size / 1048576).toFixed(2) }} MB
                      </div>
                      <div (click)="removeItem(0, i)" class="close">
                        <img src="assets/icons/close.png" />
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
  
                <div class="button-container">
                  <div class="button">
                    {{ selectedFiles[0].length > 0 ? "Add file" : "Choose file" }}
                  </div>
                </div>
              </div>
              <div class="input-info">
                Upload the logo file (PNG or JPEG format), cropped to content,
                with no additional background.
              </div>
            </div> -->
                    <div class="upload-head" style="margin-top: 2vw;">Resource Logo</div>

                    <div class="field-value flex-block">
                        <image-upload [accept]="'all'" [fileSize]="2147483648" [placeholder]=""></image-upload>
                        <div class="input-info">
                            Upload the logo file (PNG or JPEG format), cropped to content, with no additional background.
                        </div>
                    </div>

                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <div class="input-container" (focusout)="changeFocus(0)">
                                <input type="text" class="selection" placeholder="Resource Type" formControlName="resourceType" [readonly]="true" (focus)="isDropDownOpen2[0] = true" />
                                <div class="placeholder">Resource Type</div>
                                <button (click)="isDropDownOpen2[0] = !isDropDownOpen2[0]" type="button">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                                <div [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen2[0],
                      'dropdown-visible': isDropDownOpen2[0]
                    }">
                                    <ul>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Restorers')">
                                            <div class="country-name">Restorers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Insurers')">
                                            <div class="country-name">Insurers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Framers')">
                                            <div class="country-name">Framers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Shippers')">
                                            <div class="country-name">Shippers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Interior Designers')">
                                            <div class="country-name">Interior Designers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Museums')">
                                            <div class="country-name">Museums</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Printers')">
                                            <div class="country-name">Printers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Printers & Framers')">
                                            <div class="country-name">Printers & Framers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Shippers & Movers')">
                                            <div class="country-name">Shippers & Movers</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Storage')">
                                            <div class="country-name">Storage</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Conservators')">
                                            <div class="country-name">Conservators</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Legal')">
                                            <div class="country-name">Legal</div>
                                        </li>
                                        <li (click)="isDropDownOpen2[0] = false;form.get('resourceType').setValue('Photographers')">
                                            <div class="country-name">Photographers</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the type of art resource from the drop-down list.
                            </div>
                        </div>
                        <div class="field-value">
                            <input type="text" placeholder="Resource Name" />
                            <div class="placeholder">Resource Name</div>
                            <div class="input-info">
                                Provide the full name of the art resource. If there are multiple locations/branches, create separate entries.
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 3.47vw; font-size: 1.25vw">Intro Text</div>
                    <div class="field-value">
                        <angular-editor id="editor3"></angular-editor>
                        <div class="input-info">
                            Provide the text/description for the art resource. Between 100-150 words (approx. 1000 chars with spaces).
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Url" />
                            <div class="placeholder">Website</div>
                            <div class="input-info">
                                Provide the art resource's complete website URL. e.g.: https://www.intach.org/.
                            </div>
                        </div>
                        <div class="field-value">
                            <input type="text" placeholder="Location" />
                            <div class="placeholder">Location</div>
                            <div class="input-info">
                                Provide the location of the art resource (city and country). e.g.: Mumbai, India.
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 3.47vw; font-size: 1.25vw">
                        Resource Address
                    </div>
                    <div class="field-value doted">
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Address 1" />
                                <div class="placeholder">Address Line 1</div>
                            </div>
                            <div class="field-value">
                                <input type="text" placeholder="Address 2" />
                                <div class="placeholder">Address Line 2</div>
                            </div>
                        </div>
                        <div class="input-info">Provide the physical location address.</div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="email" placeholder="Email" />
                            <div class="placeholder">Email Address</div>
                            <div class="input-info">
                                Provide the contact email for general enquiries.
                            </div>
                        </div>
                        <div class="field-value">
                            <input type="text" placeholder="Number" />
                            <div class="placeholder">Telephone Number</div>
                            <div class="input-info">
                                Provide the contact number, with Area code.
                            </div>
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Url" />
                            <div class="placeholder">Page URL</div>
                            <div class="input-info">
                                Provide the URL to be used for the art resource on the site. This needs to be a unique entry. e.g.: prudent-insurance.
                            </div>
                        </div>
                    </div>
                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Publish Profile :</div>
                            <label class="switch">
                  <input type="checkbox" formControlName="showProfile" />
                  <span class="slider round"></span>
                </label>
                        </div>
                        <div class="input-info">
                            Choose whether to publish the artist information on the website.
                        </div>
                    </div>
                    <!-- Show Video -->
                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Video :</div>
                            <label class="switch">
                  <input type="checkbox" formControlName="showVideo" />
                  <span class="slider round"></span>
                </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show intro video on the profile page
                        </div>
                    </div>
                    <div class="videoSection" *ngIf="form.get('showVideo').value">
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Link" />
                                <div class="placeholder">Intro Video</div>
                                <div class="input-info">
                                    Provide the Vimeo player link for the video to be featured on the page. e.g.: https://www.player.vimeo.com/12342143
                                </div>
                            </div>
                        </div>
                        <div style="margin-top: 3.47vw; font-size: 1.11vw">Video Text</div>
                        <div class="field-value">
                            <angular-editor id="editor4"></angular-editor>
                            <div class="input-info">
                                Provide the text to be displayed on the Video thumbnail. e.g.: Director talking about the current exhibition.
                            </div>
                        </div>
                    </div>

                    <!-- Show Additional -->
                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Additional Media :</div>
                            <label class="switch">
                  <input type="checkbox" formControlName="showAdditionalMedia" />
                  <span class="slider round"></span>
                </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show media content (videos, images, documents) on the profile page.
                        </div>
                    </div>
                    <div *ngIf="form.get('showAdditionalMedia').value">
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Section Title" />
                                <div class="placeholder">Media Section Title</div>
                                <div class="input-info">
                                    Provide the title for the media section. e.g.: Related Media.
                                </div>
                            </div>
                        </div>
                        <div style="margin-top: 3.47vw; font-size: 1.25vw">
                            Media Details
                            <img class="plus-icon" (click)="addRepeater1()" src="assets/images/load-more.png" style="width: 1.9vw; margin-left: 2vw" />
                        </div>
                        <div class="field-value doted">
                            <div formArrayName="additionalMedia" *ngFor="let item of additionalMedia.controls; let i = index" class="field-value doted" style="padding-bottom: 1vw">
                                <div [formGroupName]="i">
                                    <div class="splitter">
                                        <div class="field-value" style="padding-right: 0.5vw">
                                            <input type="text" placeholder="Index Number" />
                                            <div class="placeholder">Index Number</div>
                                            <!-- <div class="input-info">
                      Provide the art resource's complete website URL. e.g.: https://www.intach.org/.
                    </div> -->
                                        </div>
                                        <div class="field-value">
                                            <input type="text" placeholder="Media Title" />
                                            <div class="placeholder">Media Title</div>
                                            <!-- <div class="input-info">
                      Provide the location of the art resource (city and country). e.g.: Mumbai, India.
                    </div> -->
                                        </div>
                                    </div>
                                    <div class="splitter">
                                        <div class="field-value" style="padding-right: 0.5vw">
                                            <input type="text" placeholder="Url" />
                                            <div class="placeholder">Media Thumbnail URL</div>
                                            <!-- <div class="input-info">
                      Provide the art resource's complete website URL. e.g.: https://www.intach.org/.
                    </div> -->
                                        </div>
                                        <div class="field-value">
                                            <input type="text" placeholder="Media Text" />
                                            <div class="placeholder">Media Text</div>
                                            <!-- <div class="input-info">
                      Provide the location of the art resource (city and country). e.g.: Mumbai, India.
                    </div> -->
                                        </div>
                                    </div>
                                    <div class="splitter">
                                        <div class="field-value" style="padding-right: 0.5vw">
                                            <div class="input-container" (focusout)="changeFocusRepeater(i)">
                                                <input type="text" class="selection" placeholder="Media Type" formControlName="mediaType" [readonly]="true" (focus)="isDropDownOpen3[i] = true" />
                                                <div class="placeholder">Media Type</div>
                                                <button (click)="isDropDownOpen3[i] = !isDropDownOpen3[i]" type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                                                <div [ngClass]="{
                              'dropdown-hidden': !isDropDownOpen3[i],
                              'dropdown-visible': isDropDownOpen3[i]
                            }">
                                                    <ul>
                                                        <li (click)="
                                  isDropDownOpen3[i] = false;
                                  additionalMedia.controls[i]
                                    .get('mediaType')
                                    .setValue('Document')
                                ">
                                                            <div class="country-name">Document</div>
                                                        </li>
                                                        <li (click)="
                                  isDropDownOpen3[i] = false;
                                  additionalMedia.controls[i]
                                    .get('mediaType')
                                    .setValue('Image')
                                ">
                                                            <div class="country-name">Image</div>
                                                        </li>
                                                        <li (click)="
                                  isDropDownOpen3[i] = false;
                                  additionalMedia.controls[i]
                                    .get('mediaType')
                                    .setValue('Video')
                                ">
                                                            <div class="country-name">Video</div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field-value">
                                            <input type="text" placeholder="Url" />
                                            <div class="placeholder">Media URL</div>
                                            <!-- <div class="input-info">
                      Provide the location of the art resource (city and country). e.g.: Mumbai, India.
                    </div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose 3 to 6 artists to feature on the Gallery page
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>