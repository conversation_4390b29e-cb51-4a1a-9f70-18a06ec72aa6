import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
import { Subscription } from 'rxjs';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-add-art-resource',
  templateUrl: './add-art-resource.component.html',
  styleUrls: ['./add-art-resource.component.scss'],
})
export class AddArtResourceComponent implements OnInit, OnDestroy {
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  routerObserver: Subscription;
  activeMenu = 'main';
  faArrowAltCircleLeft=faArrowAltCircleLeft;
  ngOnDestroy(): void {
    // this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    // this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/art-resources/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/art-resources/add/seo':
        this.activeMenu = 'seo';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }
}
