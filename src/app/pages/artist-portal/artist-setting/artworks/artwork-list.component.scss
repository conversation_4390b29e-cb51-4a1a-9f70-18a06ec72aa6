@import url("//use.fontawesome.com/releases/v5.7.2/css/all.css");
.filter {
  color: var(--quaternary-font-color);
  font-size: var(--default-font-size);
  input[type="text"] {
    padding: 0.7vw 1.32vw 0.7vw 1vw;
    -o-object-fit: contain;
    object-fit: contain;
    outline: none;
    width: 100%;
    font-family: Arial, FontAwesome;
    border-radius: 0.5vw;
    font-weight: 500;
    // margin-bottom: 2.08vw;
    color: var(--quaternary-font-color);
    height: 2.8vw;
    border: solid 0.07vw var(--secondary-font-color);
    text-indent: 1.73vw;
    background-position: 0 50%;
    background-repeat: no-repeat;
    background-position-x: 1.04vw;
    background-size: 1.04vw;
    background-image: url("../../../../../assets/icons/search/<EMAIL>");
  }
  input[type="text"]:focus {
    background-image: none;
    text-indent: 0px;
  }
}
.filter-div-section {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 1vw;
  .rightside-section {
    display: flex;
    gap: 1vw;
  }
}
.heading {
  margin-bottom: 1vw;
  position: relative;
  .head {
    // width: 82.5%;
    font-size: 1.66vw;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    // align-items: center;
    img {
      width: 2.29vw;
      height: 2.29vw;
      margin-right: 1.08vw;
    }
  }
  .sub {
    font-size: var(--default-font-size);
    font-family: var(--secondary-font);
    margin-top: 0.42vw;
    padding-left: 3.33vw;
  }
}
.flex-block {
  display: flex !important;
}
.fa-icon-list {
  fa-icon {
    margin-right: 0.1vw;
  }
}
.blockchainIcon {
  // width: 1.25vw;
  // height: 1.25vw !important;
  width: 1vw;
  height: 1.4vw !important;
  padding-bottom: 0.4vw;

  image-rendering: -moz-crisp-edges;
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  -ms-interpolation-mode: nearest-neighbor;
}
.grpOpn {
  margin-left: 0.5vw;
  width: 1vw;
  height: 1.4vw;
  padding-bottom: 0.4vw;
  object-fit: cover;
}
.blockchainIconList {
  width: auto !important;
  height: 1vw;
}

// style="color: #bfbfbf"
.actions-fa-icon {
  color: #bfbfbf;
  fa-icon {
    margin-right: 1vw;
  }
  fa-icon:hover {
    color: #004ddd;
  }
}
.actions-fa-icon:hover {
  color: #004ddd;
}
.collector-form {
  .search-input {
    font-size: var(--default-font-size);
    margin-top: 4.16vw;
    position: relative;
    input[type="text"] {
      width: 100%;
      padding: 0.34vw 0;
      border: none;
      text-indent: 2.08vw;
      color: var(--quaternary-font-color);
      border-bottom: 0.138vw solid var(--timeline-color);
      &:focus {
        outline: none;
      }
    }
    img {
      position: absolute;
      left: 0;
      top: 50%;
      height: 0.97vw;
      width: 0.97vw;
      transform: translate(0, -50%);
    }
  }
  .interests {
    width: auto;
    margin-left: 0;
    margin-top: 1.74vw;
    overflow-y: auto;
    //   height: calc(100vh - 44.75vw);
    padding-bottom: 8vw;
    .contents {
      // display: flex;
      // justify-content: space-between;
      // flex-wrap: wrap;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5vw;
      .artist {
        width: 15.06vw;
        padding: 1.74vw 0;
        .artworkImggg {
          width: 14.93vw;
          height: 10.69vw;
          min-height: 10.69vw;
          -o-object-fit: cover;
          object-fit: cover;
          border-radius: 0.41vw;
          cursor: pointer;
        }
        .wrappp {
          line-height: 1.1;
          .name {
            font-size: 0.9027vw;
            margin-top: 0.23vw;
          }
          &.active {
            color: var(--tertiary-font-color);
            img {
              border: solid 0.013vw var(--tertiary-font-color);
            }
          }
        }
      }
      // .artwork-list {
      //   display: flex;
      //   flex-direction: row;
      //   .list-contain {
      //     display: flex;
      //     flex-direction: row;

      //     .artwork-img {
      //       width: 10%;
      //     }
      //     .artwork-indicators {
      //       width: 10%;
      //     }
      //     .artwork-title {
      //       width: 20%;
      //     }
      //     .artist-name {
      //       width: 20%;
      //     }
      //     .sale-price {
      //       width: 20%;
      //     }
      //     .nft-id {
      //       width: 20%;
      //     }
      //   }
      // }
      .icon-indicators {
        padding-top: 0.5vw;
        display: flex;

        align-items: center;
        fa-icon {
          margin-right: 0.1vw;
        }
      }
    }
    .contentList {
      justify-content: space-between;
      flex-wrap: wrap;

      .Table {
        display: flex;
        flex-direction: column;
        border: 1px solid #f2f2f2;
        font-size: 1rem;
        // margin: 0.5rem;
        justify-content: space-between;
      }

      .Table-header {
        font-weight: 700;
        background-color: #defdec;
      }

      .Table-row {
        display: box;
        display: flex;
        // flex-flow: row nowrap;
        font-size: 0.9027vw;
        border-bottom: 1px solid #f2f2f2;
        height: 2.777vw;
        // overflow: hidden;
        line-height: 1;
      }

      .Table-row-item {
        display: flex;
        // flex-grow: 0;
        width: 10%;
        padding: 0.5em;
        align-items: center;

        word-break: break-word;
        text-align: center;
        .img-item {
          padding: 0.5em;
          justify-content: start;
        }
        .img-artwork-thumbnail {
          background-color: #80808054;
          width: 50px;
          height: 40px;
          -o-object-fit: contain;
          object-fit: contain;
          border-radius: 0.14vw;
        }

        .edit_icon {
          position: relative;
        }

        .edit_icon::before {
          font-family: "Font Awesome 5 Free";
          // color: #356ad2;
          // content: "\f040";
          // display: inline-block;
          // // padding-right: 3px;
          // font-size: 18px;
          // vertical-align: middle;
          // font-weight: 900;
          content: "\f023";
          // font-family: FontAwesome;
          font-style: normal;
          font-weight: normal;
          text-decoration: inherit;
          /*--adjust as necessary--*/
          color: rgb(255, 5, 5);
          font-size: 18px;
          padding-right: 0.5em;
          position: absolute;
        }
      }
      // .Table-row-item.title-art {
      //   // font-weight: 600;
      // }
      .u-Flex-grow2 {
        width: 20%;
      }

      .u-Flex-grow3 {
        width: 30%;
      }

      // .u-Flex-grow3 {
      //   -webkit-flex-grow: 3;
      //   -moz-flex-grow: 3;
      //   flex-grow: 3;
      //   -ms-flex-positive: 3;
      // }

      // table Close
    }
  }

  .buttonList {
    margin-top: 3.47vw;
    .save {
      outline: none;
      font-size: 1.25vw;
      // min-width: 20.56vw;
      background-color: transparent;
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
      margin-right: 1.5vw;
    }
    .save:hover {
      font-weight: 600;
    }
    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: transparent;
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }
    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }
}
.saveBtn {
  // display: block;
  //width: 4vw;
  outline: none;
  font-size: 1.25vw;
  // width: 20.56vw;
  margin-left: 2vw;
  background-color: transparent;
  color: var(--tertiary-font-color);
  padding: 0.333333vw 0.89vw;
  border: 0.069vw solid var(--tertiary-font-color);
  border-radius: 1.46vw;
  @media (max-width: 768px) {
    font-size: 3.86vw;
    margin-bottom: 4.83vw;
    border-radius: 10.1vw;
    padding: 3.45vw 6.89vw;
  }
}

.switch {
  position: relative;
  display: inline-block;
  width: 2.6vw;
  height: 1.11vw;
  margin-bottom: 0 !important;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--tertiary-font-color);
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 1vw;
  width: 1vw;
  left: 0.06vw;
  bottom: 0.06vw;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--tertiary-font-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked + .slider:before {
  transform: translateX(1.4vw);
}

/* Rounded sliders */
.slider.round {
  border-radius: 3vw;
}

.slider.round:before {
  border-radius: 50%;
}
.text-before {
  font-size: 1.111vw;
  margin-right: 0.5vw;
  font-weight: 500;
}

.published-dot,
.unpublished-dot,
.status-dot,
.minted,
.not-minted,
.aserd,
.edit_grid {
  text-decoration: none;
  margin: 0 0.2vw;
}
.edition {
  font-weight: 600;
  font-size: 1.1vw;
  // margin-right: 0.2vw;
}
.editionList {
  font-weight: 600;
  font-size: 1vw;
  // margin-right: 0.2vw;
}
.edit_grid {
  position: absolute;
  top: 2vw;
  right: 0.7vw;
  // padding: 0.5vw;
  background-color: white;
  padding: 0.1vw;
  border-radius: 18vw;
}
.edit_grid:hover {
  color: grey;
}
.edit_grid::before {
  font-family: "Font Awesome 5 Free";
  content: "\f142";
  color: #004ddd;
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
}
.aserd::before {
  font-family: "Font Awesome 5 Free";
  content: "\f304";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
}
.minted::before {
  font-family: "Font Awesome 5 Free";
  content: "\f023";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
}
.not-minted::before {
  font-family: "Font Awesome 5 Free";
  content: "\f3c1";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
}
.unpublished-dot::before {
  font-family: "Font Awesome 5 Free";
  content: "\f057";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
  background: -webkit-linear-gradient(#ebb1b1, #ee2828);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.published-dot::before {
  font-family: "Font Awesome 5 Free";
  content: "\f058";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
  // color: rgb(44, 135, 255);
  background: -webkit-linear-gradient(#5a8eff, #356ad2);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.status-dot::before {
  font-family: "Font Awesome 5 Free";
  content: "\f111";
  display: inline-block;
  // padding-right: 3px;
  vertical-align: middle;
  font-weight: 900;
}

.sidenav {
  box-sizing: border-box;
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1;
  top: 0;
  right: 0;
  background: linear-gradient(#dbe7ff, #fff);
  overflow: hidden;
  transition: 0.5s;
  // padding-top:px;

  .sidenav-content {
    padding: 0 20px 60px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    // position: relative;
    .closebtn {
      text-align: right;
      display: inline-block;
      font-size: 36px;
      transition: 0.3s;
      cursor: pointer;
    }
    .closebtn:hover {
      color: var(--secondarary-font-color);
    }
    .head-wrapper {
      // min-height: 20.833vw;
      // max-height: 34.722vw;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .text-side {
        padding: 0.5vw;
        font-size: 1.111vw;
        text-align: left;
        .description {
          max-height: 4vw;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .img-side {
        display: flex;
        align-items: center;
        img {
          width: 100%;
          -o-object-fit: contain;
          object-fit: contain;
        }
      }
    }
    .divider {
      width: 100%;
      border-bottom: 0.0694vw solid var(--timeline-color);
    }
  }
}
.filter-sidenav {
  box-sizing: border-box;
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 0;
  background: linear-gradient(#dbe7ff, #fff);
  overflow: hidden;
  transition: 0.5s;
  // padding-top:px;

  .sidenav-content {
    padding: 0.3vw 0.5vw 0.5vw 0.8vw;
    height: 100%;
    display: flex;
    flex-direction: column;
    // position: relative;
    .closebtn {
      text-align: right;
      display: inline-block;
      font-size: 36px;
      transition: 0.3s;
      cursor: pointer;
    }
    .closebtn:hover {
      color: var(--secondarary-font-color);
    }
    .head-wrapper {
      // min-height: 20.833vw;
      // max-height: 34.722vw;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .text-side {
        padding: 0.5vw;
        font-size: 1.111vw;
        text-align: left;
        .description {
          max-height: 4vw;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .img-side {
        display: flex;
        align-items: center;
        img {
          width: 100%;
          -o-object-fit: contain;
          object-fit: contain;
        }
      }
    }
    .divider {
      width: 100%;
      border-bottom: 0.0694vw solid var(--timeline-color);
    }
  }
}
// .sidenav-content a {
//   // padding: 8px 8px 8px 32px;
//   text-decoration: none;
//   font-size: 25px;
//   color: #818181;
//   display: inline;
//   // transition: 0.3s;
//   position: relative;
//   // margin-bottom: 25px;
// }

// .sidenav a:hover {
//   color: #f1f1f1;
// }

#mainContent {
  overflow-y: scroll;
  // height: 100%;
  height: 90%;
  // transition: margin-right 0.5s;
  // padding: 16px;
}
.dropdown-container {
  display: block;
  height: auto;
  position: absolute;
  top: 2.9vw;
  right: 1.9vw;

  z-index: 20;
}
.dropdown-content {
  background-color: white;
  // width: 8vw;
  width: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 20;
}

.footer-buttons {
  display: none;
  width: 100%;
  // position: absolute;
  left: 0;
  right: 15px;
  bottom: 0;
  z-index: 100;
  .aui {
    // min-height: 2.847vw;
    margin-top: 0.5vw;
    // margin-bottom: .5vw;
    button {
      margin-right: 1vw;
    }
    .button-type {
      display: inline-block;
      text-align: center;
      min-width: 4.444vw;
      background: transparent;
      font-size: 0.833vw;
      /* font-weight: 600; */
      text-transform: uppercase;
      letter-spacing: 0.0694vw;
      color: #555;
      padding: 0.486vw 0.833vw;
      border: 0.0694vw solid #c8c8c8;
      border-radius: 1.527vw;
      margin-top: 0.2083vw;
      margin-bottom: 0.2083vw;
    }
    .simpe-type {
      display: inline-block;
      text-align: center;
      min-width: 4.444vw;
      background: transparent;
      font-size: 0.833vw;
      /* font-weight: 600; */
      text-transform: uppercase;
      letter-spacing: 0.0694vw;
      letter-spacing: 1px;
      color: #888;
      border: none;
      padding: 0.486vw 0.833vw;
      margin-top: 0.2083vw;
      margin-bottom: 0.2083vw;
    }
  }
}
.button-type {
  display: inline-block;
  text-align: center;
  min-width: 4.444vw;
  background: transparent;
  font-size: 0.833vw;
  /* font-weight: 600; */
  text-transform: uppercase;
  letter-spacing: 0.0694vw;
  color: #555;
  padding: 0.486vw 0.833vw;
  border: 0.0694vw solid #c8c8c8;
  border-radius: 1.527vw;
  margin-top: 0.2083vw;
  margin-bottom: 0.2083vw;
}
.simpe-type {
  display: inline-block;
  text-align: center;
  min-width: 4.444vw;
  background: transparent;
  font-size: 0.833vw;
  /* font-weight: 600; */
  text-transform: uppercase;
  letter-spacing: 0.0694vw;
  letter-spacing: 1px;
  color: #888;
  border: none;
  padding: 0.486vw 0.833vw;
  margin-top: 0.2083vw;
  margin-bottom: 0.2083vw;
}

.btnn {
  margin-top: 2vw;
  display: flex;
  justify-content: flex-end;
}
.dropdown-menu-arrow {
  top: -1.5vw;
  left: 25%;
  // top: 3.6vw;
  // left: 18%;
  // width: 0;
  // height: 0;
  position: absolute;
  // span {
  // transform: rotate(180deg);
  // -ms-transform: rotate(180deg); /*  for IE  */

  /* 	for browsers supporting webkit (such as chrome, firefox, safari etc.). */
  // -webkit-transform: rotate(180deg);
  display: inline-block;
  // }
}
.dropdown-menu-arrow:before,
.dropdown-menu-arrow:after {
  content: "";
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-width: 0.486vw 0.555vw;
  border-style: solid;
  border-color: transparent;
  z-index: 1001;
}
.dropdown-menu-arrow:after {
  bottom: -1.25vw;
  right: -0.555vw;
  border-bottom-color: #fff;
}
.dropdown-menu-arrow:before {
  bottom: -1.18vw;
  right: -0.555vw;
  border-bottom-color: rgba(0, 0, 0, 0.15);
}
.openUp {
  font-size: 1.042vw;
  position: absolute;
  background-color: white;
  height: 100%;
  width: 10.5vw;
  text-align: start;
  z-index: 100;
  // top: -3.2vw;
  // left: 10vw;
  left: 3vw;
  bottom: -3.2vw;
  // bottom: -3.8vw;
  ul {
    padding: 0;
    border: 0.0694vw solid var(--timeline-color);
    list-style-type: none;
    background-color: white;
    margin-bottom: 0;
    li {
      padding: 0.5vw;
      border-bottom: 0.06vw solid var(--timeline-color);
    }
    li:hover {
      background-color: rgba(136, 136, 136, 0.493);
    }
  }
}
.modelForCreate {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: 12vw;
  height: 100%;
  z-index: 1006;
  background: rgba(255, 255, 255, 0.76);
  overflow: hidden;

  .Content-Box {
    width: 29.861vw;
    // height: 26vw;
    background-color: white;
    box-shadow: rgba(0, 0, 0, 0.24) 1px 3px 8px;

    margin: auto;
    padding: 1vw;
    .modHead {
      font-size: 1.388vw;
      margin-top: 0;
      letter-spacing: 0.3px;
      padding-bottom: 0.555vw;
      text-transform: none;
      margin-bottom: 0;
      line-height: 1.1;
    }

    .sort-section {
      margin-top: 0.5vw;
      margin-bottom: 0.5vw;
      .Choise {
        font-weight: 600;
      }
      .choiceClick {
        color: var(--tertiary-font-color);
        margin-left: 1vw;
        position: relative;
        cursor: pointer;
      }
    }
    .booxDot {
      border: 1px dashed grey;
      padding: 2vw;
      p {
        margin: auto;
      }
    }
  }
}

.field-value {
  margin-top: 2.08vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;
  input[type="text"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type="number"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type="date"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    color: var(--quaternary-font-color);
    //   ::placeholder{
    // color: var(--quaternary-font-color);
    // }
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  input[type="password"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  .input-container {
    width: 100%;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    .text-before {
      @media (max-width: 768px) {
        font-size: 4.34vw;
      }
    }
    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }
    button {
      outline: none;
      background: none;
      border: none;
    }
    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 2.08vw;
      top: 0;
      bottom: 0;
      margin: auto 0;
      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }
    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }
    input[type="text"] {
      // background: transparent;
      font-family: var(--secondary-font);
      border: 0.069vw solid var(--timeline-color);
      padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      border-radius: 0.14vw;
      height: 3.47vw;
      width: 100%;
      z-index: 0;
      @media (max-width: 768px) {
        height: 11.35vw;
      }
      &.selection {
        padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }
    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;
      @media (max-width: 768px) {
        top: 10.47vw;
      }
      ul {
        list-style: none;
        padding: 0 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;
        @media (max-width: 768px) {
          padding: 1.69vw 0;
        }
        li {
          padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;
          @media (max-width: 768px) {
            padding: 1.69vw 1.04vw;
          }
          .country-name {
            margin-left: 0.69vw;
            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }
        li:hover {
          background-color: var(--timeline-color);
        }
      }
      ul::-webkit-scrollbar {
        display: none;
      }
    }
    .dropdown-hidden {
      display: none;
    }
  }
  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }
  .placeholder {
    position: absolute;
    top: -0.4vw;
    left: 1.04vw;
    font-size: 0.6944vw;
    color: var(--quaternary-font-color);
    padding: 0 0.3vw;
    background-color: var(--primary-background-color);
    // background-color: #ededf1;
    @media (max-width: 768px) {
      top: -1.8vw;
      left: 2.04vw;
      font-size: 3.38vw;
    }
  }
  .send {
    margin-left: 2.08vw;
    color: var(--tertiary-font-color);
  }
}
.filter-value {
  // margin-top: 2.08vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;
  input[type="text"] {
    background: transparent;
    // font-family: var(--secondary-font);
    border: none !important;
    // border: 0.069vw solid var(--timeline-color);
    // border-radius: 0.14vw;
    // padding: 1.04vw 1.11vw;
    width: 100%;
    // height: 3.47vw;
    @media (max-width: 768px) {
      // height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input::placeholder {
    color: var(--primary-font-color);
  }
  textarea:focus,
  input:focus {
    outline: none;
  }
  .input-container {
    // width: 100%;
    width: 8.3vw;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;

    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }
    button {
      outline: none;
      background: none;
      border: none;
    }
    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 1.5vw;
      top: 0;
      bottom: 0;
      margin: auto 0;
      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }
    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }
    input[type="text"] {
      // background: transparent;
      // font-family: var(--secondary-font);
      // border: 0.069vw solid var(--timeline-color);
      // padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      // border-radius: 0.14vw;
      // height: 3.47vw;
      width: 100%;
      z-index: 0;
      @media (max-width: 768px) {
        // height: 11.35vw;
      }
      &.selection {
        // padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }
    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;
      @media (max-width: 768px) {
        top: 10.47vw;
      }
      ul {
        list-style: none;
        padding: 0vw 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;
        @media (max-width: 768px) {
          padding: 1.69vw 0;
        }
        li {
          padding: 0.4vw 0.5vw;
          // padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;
          @media (max-width: 768px) {
            padding: 1.69vw 1.04vw;
          }
          .country-name {
            // margin-left: 0.69vw;
            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }
        li:hover {
          background-color: var(--timeline-color);
        }
      }
      ul::-webkit-scrollbar {
        display: none;
      }
    }
    .dropdown-hidden {
      display: none;
    }
  }
  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }
}
.sort-option-div {
  position: absolute;
  width: 10vw;
  height: 7vw;
  top: 2vw;
  left: 0;
  overflow-y: scroll;
  ul {
    background-color: white;
    list-style: none;
    padding: 0;
    li {
      font-size: 1.042vw;
      padding: 0.5vw;
      border-bottom: 0.06944vw solid var(--timeline-color);
    }
    li:hover {
      background-color: rgba(128, 128, 128, 0.493);
    }
  }
}

.filter-div-for-flag {
  position: absolute;
  width: 10vw;
  height: 15vw;
  top: 2vw;
  right: 0;
  // overflow-y: scroll;
  z-index: 1001;
  ul {
    background-color: white;
    list-style: none;
    padding: 0;
    border: 0.06944vw solid var(--timeline-color);
    margin-bottom: 0;
    li {
      font-size: 1.042vw;
      padding: 0.5vw;
      border-bottom: 0.02vw solid var(--timeline-color);
    }
    li:hover {
      background-color: rgba(128, 128, 128, 0.493);
    }
  }
}

.flgSpan {
  margin-right: 0.2vw;
  cursor: pointer;
  display: flex;
  gap: 0.2vw;
}

.dropdown-container-list {
  display: block;
  height: auto;
  position: absolute;
  // top: 2.9vw;
  // right: 1.9vw;
  top: 1.9vw;
  right: 3.9vw;
  z-index: 100;
  background-color: white;
  // z-index: 20;
  width: 8vw;
  // width: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  // z-index: 20;
}
.list-hover:hover {
  background-color: rgba(128, 128, 128, 0.493);
}
.drop-for-category {
  position: absolute;
  width: 9vw;
  top: 2vw;
  right: -8.2vw;
  text-align: left;
  z-index: 1000;
  animation: stick 0.15s ease;
  font-size: 0.902vw;
  box-shadow: 0px 0.4166vw 0.833vw 0px rgb(50 50 50 / 7%);

  background: #ffffff;
  border: 0.06944vw solid #dadada;
  padding: 0;
  margin-bottom: 0;
  max-height: 12vw;
  overflow: auto;
  overflow-x: hidden;
  .drop-for-category-item {
    border-bottom: 0.06944vw solid var(--timeline-color);
    padding: 0.5vw;
    cursor: pointer;
  }
}
.popuptext {
  visibility: hidden;
  width: 8vw;
  background-color: #555;
  color: #fff;
  // text-align: center;
  border-radius: 6px;
  padding: 0.5vw;
  position: absolute;
  z-index: 1;
  display: flex;
  justify-content: center;
  // bottom: 125%;
  // left: 50%;
  right: 125%;
  top: 0;
  margin-left: -80px;
  ul {
    li {
      padding: 0.3vw 0;
    }
  }
}
// .popuptext::after {
//   content: "";
//   position: absolute;
//   top: 100%;
//   left: 50%;
//   // left: 100%;
//   // left: 50%;
//   margin-left: -5px;
//   border-width: 5px;
//   border-style: solid;
//   border-color: #555 transparent transparent transparent;
// }
.show {
  visibility: visible;
  -webkit-animation: fadeIn 1s;
  animation: fadeIn 1s;
}

/* Add animation (fade in the popup) */
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.popup {
  position: relative;
  display: inline-block;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.checkbox_style {
  margin-right: 0.5vw;
}
.filter_action {
  outline: none;
  font-size: 1.11vw;
  // min-width: 20.56vw;
  height: fit-content;
  background-color: transparent;
  color: var(--tertiary-font-color);
  padding: 0.6vw 0.84vw;
  border: 0.069vw solid var(--tertiary-font-color);
  border-radius: 1.46vw;
  // margin-right: 1.5vw;
}
.label-class {
  display: flex;
  margin-bottom: 0;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}
.circle {
  display: inline-flex;
  // border: 1px solid; /* Adjust as needed */
  border-radius: 50%; /* This makes the border circular */
  width: 1.2vw;
  height: 1.2vw;
  text-align: center;
  font-family: Arial, sans-serif;
  font-size: 0.85vw;
  color: white;
  font-weight: 600;
  justify-content: center;
  align-items: center;
}
li fa-icon {
  font-size: 1.1vw;
}
.filterInput {
  background: transparent;
  font-family: var(--secondary-font);
  border: none;
  border: 0.069vw solid var(--timeline-color) !important;
  border-radius: 0.5vw !important;
  padding: 0.4vw 0.4vw;
  font-size: 1vw;
  width: 50%;
  height: 2.47vw;

  @media (max-width: 768px) {
    height: 11.35vw;
    font-size: 3.86vw;
    padding: 1.04vw 2.11vw;
  }
}
.action_Btn {
  background-color: black;
  color: white;
  border: 0.06944vw solid var(--timeline-color);
  padding: 0.5vw 1vw;
  border-radius: 2vw;
  display: inline-flex;
  // font-weight: 600;
  // gap: .5vw;
  width: max-content;
  justify-content: center;
  align-items: center;
}
