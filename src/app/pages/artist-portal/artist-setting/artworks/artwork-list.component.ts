import { FooterService } from './../../../../shared/services/footer.service';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnInit,
  PLATFORM_ID,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import {
  faAngleDown,
  faCheckCircle,
  faCircle,
  faEllipsisV,
  faFlag as SolidFlag,
  faLock,
  faPencilAlt,
  faQuestionCircle,
  faTimesCircle,
  faUnlock as faUnlockAlt,
  faAngleLeft,
  faAngleRight,
} from '@fortawesome/free-solid-svg-icons';
import { faFlag as OutlineFlag } from '@fortawesome/free-regular-svg-icons';
import { faCheckSquare as faCheck } from '@fortawesome/free-solid-svg-icons';
import { faArrowAltCircleDown as faArrow } from '@fortawesome/free-solid-svg-icons';
import { isPlatformBrowser } from '@angular/common';
import { ToastService } from 'src/app/core/toast/toast.service';
declare let Swal: any;

faEllipsisV;
@Component({
  selector: 'app-artwork-list',
  templateUrl: './artwork-list.component.html',
  styleUrls: ['./artwork-list.component.scss'],
  host: {
    '(document:click)': 'onClick($event)',
  },
})
export class ArtworkListComponent implements OnInit {
  helpPopUp = false;
  Category = 'All';
  categoryDrop = false;
  statusDrop = false;
  status;
  artGroupDrop = false;
  artGroup;
  faQuestionCircle = faQuestionCircle;
  privateTitle: any = '';
  faArrow = faArrow;
  faPencilAlt = faPencilAlt;
  faCartArrowDown = faAngleDown;
  faEllipsisV = faEllipsisV;
  faLock = faLock;
  faUnlockAlt = faUnlockAlt;
  faTimesCircle = faTimesCircle;
  faCheckCircle = faCheckCircle;
  faCircle = faCircle;
  IsFindFlagged = false;
  IsFindUnFlagged = false;
  faCheck = faCheck;
  totalRecords = 0;
  isFlagFilter = false;
  flaggedArtworkArray = [];
  showListView = false;
  Solidflag = SolidFlag;
  OutlineFlag = OutlineFlag;
  isSortChoice = false;
  isCreateDrop = false;
  isPVModalOpen = false;
  list_selection: number;
  searchValue = '';
  noOfArtPerPage = 12;
  index = 0;
  lastIndex = 0;
  currentWorks: any[] = [];
  current: any[] = [];
  currentOrignal: any[] = [];
  isSidemenuOpen = false;
  isFilterMenuOpen = false;
  totalRecordsOrginal = 0;
  openStyle = {
    width: '250px',
  };
  priceShowMore = false;
  showAdvancedText = false;
  isFilterDropDownOpen = Array(10).fill(false);
  form: FormGroup;
  artworks;
  emptyDiv = [];
  temporary = {
    status: 'Sold',
    artwork_type: 'Edition',
    published: false,
    minted: false,
    network: 'Etherum',
    createdBy: 'Ashuthosh NagarWala',
    NFT_id: 'Terrain.Art_289',
  };
  offset: any = 1;
  limit: any = 15;
  total: number = 0;
  isLoading = false;
  temporary2 = [{}, {}, {}, {}, {}, {}, {}, {}];
  userDetailsObj: any = {};
  showMenu = Array(1000).fill(false);
  showMenuList = Array(1000).fill(false);
  permissionsObj: any = {};
  permissionsObj2: any = {};
  Toast;
  flaggedArtworks;
  publish_artworks = false;
  // type_of_work = [null, null];
  // multiple_sale_options = [null, null, null, null,null];
  // multiple_group_options = [null, null];
  type_of_work = [];
  multiple_sale_options = [];
  multiple_group_options = [];
  work_type = [];
  medium_options = [];
  fromPriceRange: number = 0;
  toPriceRange: number = 0;
  artwork_title = '';
  location_keyword = '';
  sale_options = '';
  artwork_group = '';

  filterInput = Array(30).fill(false);

  viewOptions = {
    display_name: true,
    sale_price: true,
    dimensions: true,
    location: true,
    medium: true,
    type_of_work: false,
    sale_info: true,
    edition: true,
    condition: true
  };

  faAngleLeft = faAngleLeft;
  totalPage = 0;
  faAngleRight = faAngleRight;

  deleteArtwork = {
    item: null,
    popup: false,
  };
  duplicateArtwork = {
    item: null,
    popup: false,
    data: {
      artwork_title: '',
      with_images: true,
      with_price: true,
      with_shipping: true
    },
  };

  allArtworks = [];
  gotoPage;
  priceFrom;
  priceTo;
  minPrice;
  maxPrice

  dimensionFrom;
  dimensionTo;
  minDimension;
  maxDimension;

  heightFrom;
  heightTo;
  minHeight;
  maxHeight;
  widthFrom;
  widthTo;
  minWidth;
  maxWidth;

  isViewDemoDrop;

  @ViewChild('artTypeDropDown') artTypeDropDown: ElementRef;
  @ViewChild('artStatusDropDown') artStatusDropDown: ElementRef;
  @ViewChild('artGrpDropDown') artGrpDropDown: ElementRef;
  @ViewChild('artGrpDropDown2') artGrpDropDown2: ElementRef;
  @ViewChild('artViewDropDown') artViewDropDown: ElementRef;
  @ViewChild('priceViewDropDown') priceViewDropDown: ElementRef;
  @ViewChild('dimensionViewDropDown') dimensionViewDropDown: ElementRef;
  @ViewChild('mediumDropDown') mediumDropDown: ElementRef;
  dropDownFirstClick = Array(30).fill(false);

  resetFlagFilter() {
    this.IsFindFlagged = false;
    this.IsFindUnFlagged = false;
  }
  async searchResult() {
    this.offset = 1;
    this.current = [];
    sessionStorage.setItem('artworkListSearch', this.searchValue);
    sessionStorage.setItem('artworkListSearch2', this.location_keyword);
    this.getArtworks();
    // let url = apiUrl.getArtwork;
    // this.isLoading = true;
    // let userId =
    //   this.userDetailsObj?.role == 'SUPERADMIN' ? '' : this.userDetailsObj?._id;
    // let data = {
    //   offset: this.offset,
    //   limit: this.limit,
    //   sort: 0,
    //   userId,
    //   multiple_type_of_work: this.type_of_work,
    //   multiple_sale_options: this.multiple_sale_options,
    //   multiple_artwork_group: this.multiple_group_options,
    //   publish_artworks: this.publish_artworks,
    //   priceMin: this.fromPriceRange,
    //   priceMax: this.toPriceRange,
    //   sale_options: this.sale_options,
    //   search: this.searchValue,
    //   location: this.location_keyword,
    //   artwork_title: this.artwork_title,
    //   artwork_group: this.artwork_group,
    //   artist: [],
    //   medium: [],
    //   subject: [],
    //   movement: [],
    //   tags: [],
    //   platform: 'cms',
    //   adminAccess: this.permissionsObj['admin'] || false,
    // };
    // console.log(data);
    // console.log(this.type_of_work);
    // if (this.IsFindFlagged) {
    //   data['flag'] = true;
    //   this.current = [];
    //   this.currentOrignal = [];
    // }
    // this.server.showSpinner();
    // this.server.postApi(url, data).subscribe((res) => {
    //   this.server.hideSpinner();
    //   this.isLoading = false;
    //   if (res.statusCode == 200) {
    //     this.currentOrignal = [];
    //     this.current = [];
    //     this.artworks = data;
    //     this.totalRecords = res.fiter_totalCount;
    //     this.totalRecordsOrginal = res.total;

    //     const reminder = res.data?.length % 4;
    //     this.current.push(...res.data);
    //     this.currentOrignal.push(...res.data);
    //     if (reminder > 0) {
    //       this.emptyDiv = Array(reminder).fill(0);
    //     }
    //     this.updateFlagged();
    //   }
    // });
    // console.log(this.searchValue + ' here');
    // this.currentWorks = [];
    // this.currentWorks = this.current;
    // // this.current = null;
    // // this.currentWorks = [];
    // // this.currentWorks = this.artworks;
    // if (this.searchValue !== '') {
    //   console.log(this.searchValue);
    //   const newArray = this.currentWorks.filter((art) =>
    //     art?.artwork_title
    //       ? art.artwork_title
    //           .toLowerCase()
    //           .includes(this.searchValue.toLowerCase())
    //       : false
    //   );
    //   this.currentWorks = newArray;
    //   console.log(newArray);
    // } else {
    //   this.currentWorks = this.artworks;
    // }
    // this.updateCurrent();
  }

  updateCurrent(): void {
    this.current = [];

    for (
      let i = this.index * this.noOfArtPerPage;
      i < (this.index + 1) * this.noOfArtPerPage;
      i++
    ) {
      const element = this.currentWorks[i];
      if (element === undefined) {
        break;
      }
      this.current.push(element);
    }
  }

  MintlockColor(status) {
    if (status) {
      return {
        background: '-webkit-linear-gradient(#004ddd, #004ddd)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    } else {
      return {
        background: '-webkit-linear-gradient(#9e9e9e, #9e9e9e)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    }
  }

  blockchainType(type) {
    switch (type) {
      case 'Ethereum':
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
      case 'BSC':
        return '../../../../../assets/icons/blockchain-type/binance-icon.png';
        break;
      case 'Polygon':
        return '../../../../../assets/icons/blockchain-type/polygon-icon.png';
        break;
      case 'Solana':
        return '../../../../../assets/icons/blockchain-type/solana-icon.png';
        break;

      default:
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
    }
  }

  lockColor(status) {
    switch (status) {
      case 'Ropsten':
        return {
          background: '-webkit-linear-gradient(#eecda3, #ef629f)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Private':
        return {
          background: '-webkit-linear-gradient(#f87bff, #ffa9cb)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Main Net':
        return {
          background: '-webkit-linear-gradient(#86fde8,#acb6e5)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Non-Terrain':
        return {
          background: '-webkit-linear-gradient(#92eab0, #6eff7a)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      default:
        break;
    }
  }

  dotColor(status) {
    switch (status) {
      case 'ForSale':
        return '#00FF00';
        break;
      case 'Reserved':
        return '#FFFF00';
        break;
      case 'OnReserve':
        return '#FFFF00';
        break;
      case 'Sold':
        return '#FF0000';
        break;
      case 'NotForSale':
        return '#101010';
        break;
      default:
        break;
    }
  }

  openNav(i) {
    console.log(i);
    if (this.list_selection == i) {
      this.isSidemenuOpen = !this.isSidemenuOpen;
    } else {
      this.list_selection = i;
      this.isSidemenuOpen = true;
    }
  }

  closeNav() {
    this.isSidemenuOpen = false;
  }

  constructor(
    private artistInfoService: ArtistInfoService,
    private server: CollectorService,
    private router: Router,
    public formBuilder: FormBuilder,
    private footerService: FooterService,
    @Inject(PLATFORM_ID) private platformId: any,
    private cdr: ChangeDetectorRef,
    private toastService: ToastService
  ) { }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.offset = Number(localStorage.getItem('artworkCMSoffset2'));
      if (this.offset <= 1) {
        this.offset = 1;
      }
      let priceFrom = Number(localStorage.getItem('priceFrom2'));
      if (priceFrom) {
        this.priceFrom = priceFrom;
      }
      let priceTo = Number(localStorage.getItem('priceTo2'));
      if (priceTo) {
        this.priceTo = priceTo;
      }
      let heightFrom = Number(localStorage.getItem('heightFrom2'));
      if (heightFrom) {
        this.heightFrom = heightFrom;
      }
      let heightTo = Number(localStorage.getItem('heightTo2'));
      if (heightTo) {
        this.heightTo = heightTo;
      }
      let widthFrom = Number(localStorage.getItem('widthFrom2'));
      if (widthFrom) {
        this.widthFrom = widthFrom;
      }
      let widthTo = Number(localStorage.getItem('widthTo2'));
      if (widthTo) {
        this.widthTo = widthTo;
      }
      this.minPrice = Number(this.priceFrom) || undefined;
      this.maxPrice = Number(this.priceTo) || undefined;
      this.minHeight = Number(this.heightFrom) * 2.54 || undefined;
      this.maxHeight = Number(this.heightTo) * 2.54 || undefined;
      this.minWidth = Number(this.widthFrom) * 2.54 || undefined;
      this.maxWidth = Number(this.widthTo) * 2.54 || undefined;

      this.Toast = Swal.mixin({
        toast: true,
        position: 'top-right',
        iconColor: 'white',
        customClass: {
          popup: 'colored-toast',
        },
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
      });
      const viewOptions = localStorage.getItem('artworkCMSViewOptions2');
      if (viewOptions) {
        this.viewOptions = JSON.parse(viewOptions);
      } else {
        localStorage.setItem(
          'artworkCMSViewOptions2',
          JSON.stringify(this.viewOptions)
        );
      }
      const filterOptions = localStorage.getItem('artworkCMSFilterOptions2');
      if (filterOptions) {
        this.filterInput = JSON.parse(filterOptions);
      } else {
        localStorage.setItem(
          'artworkCMSFilterOptions2',
          JSON.stringify(this.filterInput)
        );
      }
      const artType = localStorage.getItem('artworkCMSArtType2');
      if (artType) {
        this.type_of_work = JSON.parse(artType);
      } else {
        localStorage.setItem(
          'artworkCMSArtType2',
          JSON.stringify(this.type_of_work)
        );
      }
      const artGrp = localStorage.getItem('artworkCMSArtGrp2');
      if (artGrp) {
        this.multiple_group_options = JSON.parse(artGrp);
      } else {
        localStorage.setItem(
          'artworkCMSArtGrp22',
          JSON.stringify(this.multiple_group_options)
        );
      }
      const artGrp2 = localStorage.getItem('artworkCMSArtGrp22');
      if (artGrp2) {
        this.work_type = JSON.parse(artGrp2);
      } else {
        localStorage.setItem(
          'artworkCMSArtGrp22',
          JSON.stringify(this.work_type)
        );
      }
      const artSale = localStorage.getItem('artworkCMSArtSale2');
      if (artSale) {
        this.multiple_sale_options = JSON.parse(artSale);
      } else {
        localStorage.setItem(
          'artworkCMSArtSale2',
          JSON.stringify(this.multiple_sale_options)
        );
      }
    }
    // console.log(this.server.personalDetailsObj);
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.form = this.formBuilder.group({
      // showListView: new FormControl(false),
    });
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Artworks'
    );
    this.permissionsObj2 = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Preview Links'
    );
    this.userDetailsObj = JSON.parse(decode);
    if (this.userDetailsObj?.role == 'SUPERADMIN') {
      this.permissionsObj2 = { read: true, };
    }

    //console.log(this.userDetailsObj);

    if (sessionStorage.getItem('artworkListSearch')) {
      this.searchValue = sessionStorage.getItem('artworkListSearch');
    }
    if (sessionStorage.getItem('artworkListSearch2')) {
      this.location_keyword = sessionStorage.getItem('artworkListSearch2');
    }

    this.getArtworks();
  }

  UnFlagAll() {
    this.resetFlagFilter();

    this.flaggedArtworkArray = [];
    this.totalRecords = this.totalRecordsOrginal;
    this.current = [...this.currentOrignal];
    console.log(this.current);
    console.log(this.currentOrignal);
    alert('Unflagged !');
    // for (let index = 0; index < this.current.length; index++) {
    //   const element = this.current[index];
    //   this.flaggedArtworkArray.push(element._id);
    // }
  }

  getAllArtworks() {
    let url = apiUrl.getArtwork;
    let userId =
      this.userDetailsObj?.role == 'SUPERADMIN' ? '' : this.userDetailsObj?._id;
    let data = {
      offset: this.offset,
      limit: 10000,
      sort: 0,
      userId,
      multiple_type_of_work: this.type_of_work,
      multiple_sale_options: this.multiple_sale_options,
      multiple_artwork_group: this.multiple_group_options,
      publish_artworks: this.publish_artworks,
      priceMin: this.minPrice ? this.minPrice : 0,
      priceMax: this.maxPrice ? this.maxPrice : 0,
      sale_options: this.sale_options,
      heightMin: this.minHeight ? this.minHeight - 1 : 0,
      heightMax: this.maxHeight ? this.maxHeight + 1 : 0,
      widthMin: this.minWidth ? this.minWidth - 1 : 0,
      widthMax: this.maxWidth ? this.maxWidth + 1 : 0,
      search: this.searchValue,
      location: this.location_keyword,
      artwork_title: this.artwork_title,
      artwork_group: this.artwork_group,
      artist: [],
      medium: [],
      subject: [],
      movement: [],
      tags: [],
      platform: 'cms',
      adminAccess: this.permissionsObj['admin'] || false,
      work_type: this.work_type
    };
    if (this.IsFindFlagged) {
      data['flag'] = true;
    }
    this.server.postApi(url, data).subscribe((res) => {
      if (res.statusCode == 200) {
        this.allArtworks = res.data;
        this.flaggedArtworkArray = this.allArtworks.map((a) => a._id);
        let req = {
          artworkIds: this.flaggedArtworkArray,
        };
        this.setFlag('add', req);
      }
    });
  }
  // to manage flag action
  manageFlag(type) {
    if (type == 'add') {
      let req = {
        artworkIds: this.flaggedArtworkArray,
      };
      this.setFlag('add', req);
    } else if (type == 'remove') {
      let req = {
        artworkIds: this.flaggedArtworkArray,
      };
      this.setFlag('remove', req);
    } else if (type == 'removeAll') {
      let req = {
        artworkIds: [],
      };
      this.setFlag('removeAll', req);
    } else if (type == 'flagAll') {
      this.getAllArtworks();
    }
  }

  // to set flag
  setFlag(action, req) {
    let url = apiUrl.artworks.setFlag + `?action=${action}`;
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        // alert(res.message);
        //this.offset = 1;
        // this.updateFlagged();
        let url = apiUrl.getArtwork;
        let userId =
          this.userDetailsObj?.role == 'SUPERADMIN' ? '' : this.userDetailsObj?._id;
        let data = {
          offset: 1,
          limit: 10000,
          sort: 0,
          userId,
          publish_artworks: false,
          priceMin: 0,
          priceMax: 0,
          sale_options: '',
          search: '',
          artwork_group: '',
          artist: [],
          medium: [],
          subject: [],
          movement: [],
          tags: [],
          platform: 'cms',
          flag: true,
          adminAccess: this.permissionsObj['admin'] || false,
        };
        // this.flaggedArtworks = [];
        // this.flaggedArtworkArray = [];
        this.server.postApi(url, data).subscribe((res) => {
          if (res.statusCode == 200) {
            this.flaggedArtworks = res.data;
            this.flaggedArtworkArray = this.flaggedArtworks
              .filter((x) => x?.flagUsers?.includes(this.userDetailsObj?._id))
              .map((x) => x._id);
            //console.log(this.flaggedArtworkArray);
            // setTimeout(() => {
            this.cdr.detectChanges();
            if (action == 'add') {
              this.Toast.fire({
                icon: 'success',
                title: 'Added to flags!',
              });
            } else {
              this.Toast.fire({
                icon: 'success',
                title: 'Removed from flags!',
              });
            }
            // }, 200);
          }
        });

      }
    });
  }

  Flagged(id) {
    if (this.flaggedArtworkArray.includes(id)) {
      let req = {
        artworkIds: [id],
      };
      this.setFlag('remove', req);
      return 'false';
    } else {
      let req = {
        artworkIds: [id],
      };
      this.setFlag('add', req);
      return true;
    }
  }

  isFlagged(id) {
    //console.log(id);

    return this.flaggedArtworkArray.includes(id);
  }

  // to get artworks
  getArtworks() {
    localStorage.setItem(
      'artworkCMSFilterOptions2',
      JSON.stringify(this.filterInput)
    );
    localStorage.setItem(
      'artworkCMSArtType2',
      JSON.stringify(this.type_of_work)
    );
    localStorage.setItem(
      'artworkCMSArtGrp2',
      JSON.stringify(this.multiple_group_options)
    );
    localStorage.setItem(
      'artworkCMSArtGrp22',
      JSON.stringify(this.work_type)
    );
    localStorage.setItem(
      'artworkCMSArtSale2',
      JSON.stringify(this.multiple_sale_options)
    );
    localStorage.setItem('artworkCMSoffset2', String(this.offset));
    localStorage.setItem('priceFrom2', String(this.priceFrom));
    localStorage.setItem('priceTo2', String(this.priceTo));
    localStorage.setItem('heightFrom2', String(this.heightFrom));
    localStorage.setItem('heightTo2', String(this.heightTo));
    localStorage.setItem('widthFrom2', String(this.widthFrom));
    localStorage.setItem('widthTo2', String(this.widthTo));
    let url = apiUrl.getArtwork;
    this.isLoading = true;
    this.current = [];
    this.currentOrignal = [];
    let userId =
      this.userDetailsObj?.role == 'SUPERADMIN' ? '' : this.userDetailsObj?._id;
    this.gotoPage = this.offset;
    let data = {
      offset: this.offset,
      limit: this.limit,
      sort: 0,
      userId,
      multiple_type_of_work: this.type_of_work,
      multiple_sale_options: this.multiple_sale_options,
      multiple_artwork_group: this.multiple_group_options,
      publish_artworks: this.publish_artworks,
      priceMin: this.minPrice ? this.minPrice : 0,
      priceMax: this.maxPrice ? this.maxPrice : 0,
      heightMin: this.minHeight ? this.minHeight - 1 : 0,
      heightMax: this.maxHeight ? this.maxHeight + 1 : 0,
      widthMin: this.minWidth ? this.minWidth - 1 : 0,
      widthMax: this.maxWidth ? this.maxWidth + 1 : 0,
      sale_options: this.sale_options,
      search: this.searchValue,
      location: this.location_keyword,
      artwork_title: this.artwork_title,
      artwork_group: this.artwork_group,
      artist: [],
      medium: this.medium_options,
      subject: [],
      movement: [],
      tags: [],
      platform: 'cms',
      adminAccess: this.permissionsObj['admin'] || false,
      work_type: this.work_type,
    };
    console.log(data);
    console.log(this.type_of_work);
    console.log(this.IsFindFlagged);
    if (this.IsFindFlagged) {
      data['flag'] = true;
      this.current = [];
      this.currentOrignal = [];
    }
    this.server.showSpinner();
    this.server.postApi(url, data).subscribe((res) => {
      this.current = [];
      this.currentOrignal = [];
      this.server.hideSpinner();
      this.isLoading = false;
      if (res.statusCode == 200) {
        //this.current = res.data;
        this.artworks = data;
        this.totalRecords = res.fiter_totalCount;
        this.totalRecordsOrginal = res.total;
        this.totalPage = Math.ceil(this.totalRecords / this.limit);
        localStorage.setItem('artworkCMStotalPage2', String(this.totalPage));
        const reminder = res.data?.length % 4;
        this.current.push(...res.data);
        this.currentOrignal.push(...res.data);
        if (reminder > 0) {
          this.emptyDiv = Array(reminder).fill(0);
        }
        this.updateFlagged();
      }
    });
  }

  updateFlagged() {
    let url = apiUrl.getArtwork;
    let userId =
      this.userDetailsObj?.role == 'SUPERADMIN' ? '' : this.userDetailsObj?._id;
    let data = {
      offset: 1,
      limit: 10000,
      sort: 0,
      userId,
      publish_artworks: false,
      priceMin: 0,
      priceMax: 0,
      sale_options: '',
      search: '',
      artwork_group: '',
      artist: [],
      medium: [],
      subject: [],
      movement: [],
      tags: [],
      platform: 'cms',
      flag: true,
      adminAccess: this.permissionsObj['admin'] || false,
    };
    // this.flaggedArtworks = [];
    // this.flaggedArtworkArray = [];
    this.server.postApi(url, data).subscribe((res) => {
      if (res.statusCode == 200) {
        this.flaggedArtworks = res.data;
        this.flaggedArtworkArray = this.flaggedArtworks
          .filter((x) => x?.flagUsers?.includes(this.userDetailsObj?._id))
          .map((x) => x._id);
        //console.log(this.flaggedArtworkArray);
        // setTimeout(() => {
        this.cdr.detectChanges();
        // }, 200);
      }
    });
  }

  navigateTo() {
    localStorage.removeItem('artworkID');
    localStorage.removeItem('artworkArtistID');
    this.router.navigate(['artist-portal/settings/artwork/add']);
  }

  // to edit artwork
  editArtWork(item) {
    localStorage.removeItem('artworkArtistID');
    localStorage.setItem('artworkID', item['_id']);
    this.router.navigate(['artist-portal/settings/artwork/add']);
  }

  // to manage pagination
  managePagination(page) {
    // this.offset = page;
    // this.total = 0;

    // this.offset = page;
    switch (page) {
      case 'next':
        this.offset = this.offset + 1;
        break;

      default:
        if (this.offset > 1) {
          this.offset = this.offset - 1;
        }
        break;
    }
    this.getArtworks();
  }
  onGotoPage() {
    this.offset = Number(this.gotoPage);
    if (!this.offset) {
      this.offset = 1;
    }
    this.getArtworks();
  }

  // @HostListener('window:scroll', [])
  // onScroll(): void {
  //   if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
  //     if (!this.isLoading) {
  //       this.offset = this.offset + this.limit;
  //       this.getArtworks();
  //     }

  //     //this.temporary.push(...[{}, {}, {}, {}]);
  //   }
  // }

  // @HostListener('window:scroll', ['$event'])
  // scrollHandler(event): void {
  //   if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
  //     console.log('hey sdfsdf');
  //     if (!this.isLoading) {
  //       console.log('sdfsdf');

  //       // this.offset = this.offset + this.limit;
  //       this.offset = this.offset + 1;
  //       this.getArtworks();
  //     }

  //     //this.temporary.push(...[{}, {}, {}, {}]);
  //   }
  // }
  onElementScroll(event) {
    // if (
    //   event.target.offsetHeight + event.target.scrollTop >=
    //   event.target.scrollHeight
    // ) {
    //   console.log('hey sdfsdf2');
    //   if (!this.isLoading) {
    //     console.log('sdfsdf2');
    //     // this.offset = this.offset + this.limit;
    //     this.offset = this.offset + 1;
    //     this.getArtworks();
    //   }
    // }
  }

  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.
    //this.footerService.changeFooterType(FooterType.DEFAULT);
  }

  publishArtWork(item) {
    let url = apiUrl.addArtwork;
    let req = {
      publish_artworks: true,
      artworkId: item['_id'],
    };
    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('artwork published successfully!');
      }
    });
  }

  changeFocus() {
    // console.log('in in ');
    setTimeout(() => {
      this.isCreateDrop = false;
    }, 200);
  }
  flagfilter() {
    setTimeout(() => {
      this.isFlagFilter = false;
    }, 200);
  }
  FindFlagged() {
    const checkFlagged = this.IsFindFlagged;
    this.resetFlagFilter();

    this.IsFindFlagged = !checkFlagged;
    this.offset = 1;
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
    // if (this.flaggedArtworkArray.length != 0) {
    //   this.current = [...this.currentOrignal];
    //   this.IsFindFlagged = true;
    //   this.IsFindUnFlagged = false;
    //   this.current = this.currentOrignal.filter((a) =>
    //     this.flaggedArtworkArray.includes(a._id)
    //   );

    //   console.log(this.current);

    //   this.totalRecords = this.flaggedArtworkArray.length;
    // }
  }
  FindUnflagged() {
    if (this.flaggedArtworkArray.length != 0) {
      this.IsFindUnFlagged = true;
      this.current = [...this.currentOrignal];

      // for (let index = 0; index < this.flaggedArtworkArray.length; index++) {
      //   const FlagID = this.flaggedArtworkArray[index];
      //   for (let index1 = 0; index1 < this.current.length; index1++) {
      //     const element = this.current[index1];
      //     if (this.flaggedArtworkArray.includes(element._id)) {
      //       this.current.splice(index1, 1);
      //     }

      //   }

      // }

      this.current = this.currentOrignal.filter(
        (a) => !this.flaggedArtworkArray.includes(a._id)
      );

      this.totalRecords =
        this.totalRecordsOrginal - this.flaggedArtworkArray.length;
    }
  }

  //to get no. of flagged artworks with primary image

  get getFlaggedArtworksWithImages() {
    return this.flaggedArtworks
      ?.filter((a) => a?.primary_image?.[0]?.url)
      .map((a) => a._id);
  }

  // to create private view
  createPrivateLink() {
    let req = {
      title: this.privateTitle,
      artworkIds: this.flaggedArtworkArray,
      contents: [
        {
          component: 'artwork settings',
          model: 'model-1',
          data: {
            showAvailability: true,
            showMedium: true,
            showDimensions: true,
            showSalePrice: true,
            showCatalogId: false,
            showDescription: false,
            showProvenance: false,
            showLiterature: false,
            showExhibitions: false,
            showPublications: false,
            showCopyrightLine: false,
            showPhotoCredit: false,
            showBanner: false,
            darkTheme: false,
            displayImageFiles: false,
            excludeSecondaryImages: false,
            showArtworks: false,
            installationViews: false,
            imageAlignHorizontal: 'Left',
            imageAlignVertical: 'Middle',
            synchronize: false,
            grid: 3,
          },
        },
      ],
      privateViewLink: `https://preview.terrain.art/urls/gallery?artist_id=${this.server.personalDetailsObj['_id']}&private_id=`,
    };
    let url = apiUrl.artworks.createPrivate;
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        this.toastService.createToast('Preview Link created successfully');
        this.isPVModalOpen = false;
        this.router.navigate([
          `/artist-portal/settings/artwork/private_view/edit/${res?.data?._id}`,
        ]);
      }
    });
  }

  // to get Gallery Preview list
  navigateToPrivateList() {
    this.router.navigate(['artist-portal/settings/artwork/private_view/list']);
  }

  public get deActivateLinks(): boolean {
    const hideRoles = [
      '61dfce935f1c8a1939f250e1',
      '61dfceb75f1c8a1939f250e6',
      '61df024613450872ca0b0b59',
    ];

    return hideRoles.some((a) => a == this.userDetailsObj?.role_id?._id);
  }

  //while selecting status
  onStatusChange() {
    this.sale_options = '';
    this.publish_artworks = false;
    switch (this.status) {
      case 'Published':
        this.publish_artworks = true;
        break;
      case 'Unpublished':
        break;
      case 'Sold':
        this.sale_options = 'Sold';
        break;
      case 'ForSale':
        this.sale_options = 'ForSale';
        break;
      case 'Reserved':
        this.sale_options = 'Reserved';
        break;
      default:
        break;
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }

  onArtGroupChange() {
    this.artwork_group = this.artGroup.toLowerCase();
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }

  changeFilterFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isFilterDropDownOpen[index] = false;
    }, 200);
  }

  closeAdvancedSearch() {
    setTimeout(() => {
      this.showAdvancedText = false;
    }, 200);
  }
  changeTypeOgWork(item, selected) {
    if (selected == true) this.type_of_work.push(item);
    else {
      let index = this.type_of_work.indexOf(item);
      this.type_of_work.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }
  SetSaleFilter(status, selected) {
    if (selected == true) this.multiple_sale_options.push(status);
    else {
      let index = this.multiple_sale_options.indexOf(status);
      this.multiple_sale_options.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }
  SetAdvSaleFilter(status, selected) {
    if (selected == true) this.multiple_sale_options.push(status);
    else {
      let index = this.multiple_sale_options.indexOf(status);
      this.multiple_sale_options.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.multiple_sale_options = this.multiple_sale_options.filter(
      (x, i, a) => a.indexOf(x) == i
    );

    this.getArtworks();
  }
  SetArtworkGroupFilter(status, selected) {
    if (selected == true) this.multiple_group_options.push(status);
    else {
      let index = this.multiple_group_options.indexOf(status);
      this.multiple_group_options.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }
  SetArtworkGroupFilter2(status, selected) {
    if (selected == true) this.work_type.push(status);
    else {
      let index = this.work_type.indexOf(status);
      this.work_type.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }

  SetMediumFilter(mediumId, selected, mediumName) {
    // Store the medium ID for API request
    if (selected == true) this.medium_options.push(mediumId);
    else {
      let index = this.medium_options.indexOf(mediumId);
      this.medium_options.splice(index, 1);
    }
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }

  advancedSearch() {
    this.current = [];
    this.currentOrignal = [];
    this.getArtworks();
  }

  setViewOptions(item, checked) {
    this.viewOptions[item] = checked;
    localStorage.setItem(
      'artworkCMSViewOptions2',
      JSON.stringify(this.viewOptions)
    );
  }
  SetArtworkPrice() {
    this.minPrice = Number(this.priceFrom) || undefined;
    this.maxPrice = Number(this.priceTo) || undefined;
    this.getArtworks();
  }

  SetArtworkDimension() {
    this.minHeight = Number(this.heightFrom) * 2.54 || undefined;
    this.maxHeight = Number(this.heightTo) * 2.54 || undefined;
    this.minWidth = Number(this.widthFrom) * 2.54 || undefined;
    this.maxWidth = Number(this.widthTo) * 2.54 || undefined;
    this.getArtworks();
  }

  deleteArt(item) {
    this.deleteArtwork.item = item;
    this.deleteArtwork.popup = true;
    // localStorage.setItem('artworkID', item['_id']);
    // this.router.navigate(['artist-portal/settings/artwork/add']);
  }
  duplicateArt(item) {
    this.duplicateArtwork.item = item;
    this.duplicateArtwork.popup = true;
    this.duplicateArtwork.data.artwork_title = item.artwork_title;
  }

  deleteArtCall() {
    let url = `artwork/delete/${this.deleteArtwork.item['_id']}`;
    this.server.postApi(url, {}).subscribe(
      (res) => {
        if (res.statusCode == 200 && res.data) {
          this.deleteArtwork.popup = false;
          this.toastService.createToast('Deleted Successfully !');
          setTimeout(() => {
            this.getArtworks();
          }, 500);
        } else {
        }
      },
      (e) => {
        alert(e.error.message);
        // this.toastService.createToast(e.error.message);
      }
    );
  }
  duplicateArtCall() {
    let url = `artwork/duplicate/${this.duplicateArtwork.item['_id']}`;
    this.server.postApi(url, this.duplicateArtwork.data).subscribe(
      (res) => {
        if (res.statusCode == 200 && res.data) {
          this.duplicateArtwork.popup = false;
          this.duplicateArtwork.data.artwork_title = '';
          this.duplicateArtwork.data.with_images = true;
          this.duplicateArtwork.data.with_price = true;
          this.duplicateArtwork.data.with_shipping = true;
          this.toastService.createToast('Duplicated Successfully !');
          setTimeout(() => {
            localStorage.setItem('artworkID', res.data['_id']);
            this.router.navigate(['artist-portal/settings/artwork/add']);
          }, 800);
        } else {
          alert(res.message);
          this.toastService.createToast('Something Went Wrong !');
        }
      },
      (e) => {
        alert(e.error.message);

        //this.toastService.createToast(e.error.message);
      }
    );
  }
  onClick(event) {
    if (
      this.isFilterDropDownOpen[0] &&
      !this.artTypeDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[0]) {
        this.isFilterDropDownOpen[0] = false;
        this.dropDownFirstClick[0] = false;
      } else {
        this.dropDownFirstClick[0] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[1] &&
      !this.artStatusDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[1]) {
        this.isFilterDropDownOpen[1] = false;
        this.dropDownFirstClick[1] = false;
      } else {
        this.dropDownFirstClick[1] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[2] &&
      !this.artGrpDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[2]) {
        this.isFilterDropDownOpen[2] = false;
        this.dropDownFirstClick[2] = false;
      } else {
        this.dropDownFirstClick[2] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[4] &&
      !this.artGrpDropDown2.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[4]) {
        this.isFilterDropDownOpen[4] = false;
        this.dropDownFirstClick[4] = false;
      } else {
        this.dropDownFirstClick[4] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[3] &&
      !this.artViewDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[3]) {
        this.isFilterDropDownOpen[3] = false;
        this.dropDownFirstClick[3] = false;
      } else {
        this.dropDownFirstClick[3] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[5] &&
      !this.priceViewDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[5]) {
        this.isFilterDropDownOpen[5] = false;
        this.dropDownFirstClick[5] = false;
      } else {
        this.dropDownFirstClick[5] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[6] &&
      !this.dimensionViewDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[6]) {
        this.isFilterDropDownOpen[6] = false;
        this.dropDownFirstClick[6] = false;
      } else {
        this.dropDownFirstClick[6] = true;
      }
    }
    if (
      this.isFilterDropDownOpen[7] &&
      !this.mediumDropDown.nativeElement.contains(event.target)
    ) {
      if (this.dropDownFirstClick[7]) {
        this.isFilterDropDownOpen[7] = false;
        this.dropDownFirstClick[7] = false;
      } else {
        this.dropDownFirstClick[7] = true;
      }
    }
  }

  clearFilters() {
    this.priceFrom = null;
    this.priceTo = null;
    this.minPrice = null;
    this.maxPrice = null;

    this.heightFrom = null;
    this.heightTo = null;
    this.minHeight = null;
    this.maxHeight = null;
    this.widthFrom = null;
    this.widthTo = null;
    this.minWidth = null;
    this.maxWidth = null;

    this.multiple_group_options = [];
    this.work_type = [];
    this.multiple_sale_options = [];
    this.type_of_work = [];
    this.medium_options = [];
    this.filterInput = Array(40).fill(false);

    this.getArtworks();
  }
  getSaleInformation(art): string {
    switch (art.sale_options) {
      case 'NotForSale':
        return `${art?.extras?.notForSaleReason} - ${art?.extras?.notForSaleDate}`;
      case 'Sold':
        return `${art?.extras?.soldTo} - ${art?.extras?.soldDate} - ${art?.extras?.soldBy}`;
      case 'Reserved':
        return `${art?.extras?.reservedFor} - ${art?.extras?.reservedDate} - ${art?.extras?.reservedBy}`;
      case 'ReturnedToArtist':
        return `${art?.extras?.returnedToArtistReason} - ${art?.extras?.returnedDate}`;
      case 'OnLoan':
        return `${art?.extras?.loanDetails} - From:${art?.extras?.startDate} - To:${art?.extras?.endDate}`;
      default:
        break;
    }
    return '';
  }
  downloadImage(url: string, filename: string): void {
    if (!filename) {
      filename = 'download.jpg'
    }
    this.server.showSpinner();
    fetch(url).then(response => response.blob()).then(blob => {
      this.server.hideSpinner();
      this.toastService.createToast('Image Downloaded!!');
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      link.click();

      // Clean up
      URL.revokeObjectURL(link.href);
    }).catch(e => console.error(e));
  }
  changeDropDown() {
    setTimeout(() => {
      this.isViewDemoDrop = !this.isViewDemoDrop;
    }, 200);
  }
}

// some((a) => a.value == item.value)
// includes(element._id)
