<div class="heading">
  <div class="head">
    <div>
      Artworks
      <a (click)="navigateTo()"
        ><button class="saveBtn">Add Artworks +</button></a
      >
      <ng-container *ngIf="permissionsObj2?.read">
        <a (click)="navigateToPrivateList()"
          ><button class="saveBtn">Preview Links</button></a
        >
        <button
          class="saveBtn"
          (click)="isCreateDrop = !isCreateDrop"
          style="position: relative; margin-left: 1vw"
        >
          Download
          <fa-icon style="color: #004ddd; margin-left: 0.2vw" [icon]="faArrow">
          </fa-icon>
          <div
            class="openUp"
            [hidden]="!isCreateDrop"
            (mouseleave)="isCreateDrop = false"
          >
            <ul>
              <a routerLink="/artist-portal/settings/report"
                ><li>Document/Reports...</li></a
              >
              <li (click)="isPVModalOpen = true">Create a Preview Link...</li>
            </ul>
          </div>
        </button>
        <button
          class="saveBtn"
          (click)="changeDropDown()"
          style="position: relative; margin-left: 1vw"
        >
          View Demo
          <fa-icon style="color: #004ddd; margin-left: 0.2vw" [icon]="faArrow">
          </fa-icon>
          <div
            class="openUp"
            [hidden]="!isViewDemoDrop"
            (mouseleave)="changeDropDown()"
          >
            <ul>
              <a href="" target="_blank"><li>Publishing Artworks</li></a>
              <a href="https://player.vimeo.com/video/886400889" target="_blank"
                ><li>Create Preview Links</li></a
              >
              <a href="https://player.vimeo.com/video/886404391" target="_blank"
                ><li>Edit Preview Links</li></a
              >
              <a href="https://player.vimeo.com/video/886412732" target="_blank"
                ><li>Updating Sales</li></a
              >
              <a href="" target="_blank"><li>Pulling Reports</li></a>
            </ul>
          </div>
        </button>
      </ng-container>
    </div>
    <div class="filter" style="margin-right: 0.3vw; width: 9vw">
      <input
        type="text"
        placeholder="Location"
        name="location"
        [(ngModel)]="location_keyword"
        [ngModelOptions]="{ standalone: true }"
        (keyup)="searchResult()"
      />
    </div>
    <div class="filter" style="width: 14vw">
      <input
        type="text"
        placeholder="Search by artwork"
        name="search"
        [(ngModel)]="searchValue"
        [ngModelOptions]="{ standalone: true }"
        (keyup)="searchResult()"
        (focus)="showAdvancedText = true"
        (focusout)="closeAdvancedSearch()"
      />
      <p
        *ngIf="showAdvancedText"
        (click)="isFilterMenuOpen = true"
        style="
          cursor: pointer;
          text-align: right;
          font-size: 0.6944vw;
          font-weight: 500;
          margin-top: 0.2vw;
          margin-bottom: unset;
        "
      >
        Advanced search
      </p>
    </div>
  </div>
  <div class="filter-div-section">
    <div class="d-flex">
      <div class="filter-value">
        <!-- (focusout)="changeFilterFocus(0)" -->
        <div class="input-container" style="width: 9vw">
          <input
            type="text"
            class="selection"
            placeholder="Artwork Type"
            (focus)="isFilterDropDownOpen[0] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[0] = !isFilterDropDownOpen[0]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw"
            [hidden]="!isFilterDropDownOpen[0]"
            #artTypeDropDown
          >
            <ul>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Physical</div>
                <input
                  type="checkbox"
                  value="physical"
                  [(ngModel)]="filterInput[0]"
                  (change)="
                    changeTypeOgWork('physical', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Digital</div>
                <input
                  type="checkbox"
                  value="digital"
                  [(ngModel)]="filterInput[1]"
                  (change)="
                    changeTypeOgWork('digital', $event.currentTarget.checked)
                  "
                />
              </li>
            </ul>
          </div>
        </div>
      </div>
      <!-- <span class="with-carret d-flex" style="margin-right: 2vw">
        <div [innerHtml]="Category" style="margin-right: 0.2vw">All</div>
        Artworks
        <div style="position: relative; margin-left: 0.5vw">
          <fa-icon
            [icon]="faCartArrowDown"
            (click)="categoryDrop = !categoryDrop"
            style="cursor: pointer"
          >
          </fa-icon>
          <div
            class="drop-for-category"
            [hidden]="!categoryDrop"
            (mouseleave)="categoryDrop = false"
          >
            <div class="drop-for-category-item" (click)="Category = 'All'">
              All
            </div>
            <div class="drop-for-category-item" (click)="Category = 'Unique'">
              Unique
            </div>
            <div
              class="drop-for-category-item"
              (click)="Category = 'Prints and Editions'"
            >
              Prints and Editions
            </div>
            <div class="drop-for-category-item" (click)="Category = 'Main'">
              Main
            </div>
            <div
              class="drop-for-category-item"
              (click)="Category = 'Published'"
            >
              Published
            </div>
          </div>
        </div>
      </span> -->
      <!-- (focusout)="changeFilterFocus(1)" -->
      <div class="filter-value">
        <div class="input-container" style="width: 6vw">
          <input
            type="text"
            class="selection"
            placeholder="Status"
            (focus)="isFilterDropDownOpen[1] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[1] = !isFilterDropDownOpen[1]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw; width: 180%"
            [hidden]="!isFilterDropDownOpen[1]"
            #artStatusDropDown
          >
            <ul>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Published
                  <input
                    [(ngModel)]="filterInput[2]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('Published', $event.currentTarget.checked)
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Not Published
                  <input
                    type="checkbox"
                    [(ngModel)]="filterInput[3]"
                    (click)="
                      SetSaleFilter(
                        'NotPublished',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Sold
                  <input
                    [(ngModel)]="filterInput[4]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('Sold', $event.currentTarget.checked)
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >For Sale
                  <input
                    [(ngModel)]="filterInput[5]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('ForSale', $event.currentTarget.checked)
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Not For Sale
                  <input
                    [(ngModel)]="filterInput[6]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('NotForSale', $event.currentTarget.checked)
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Reserved
                  <input
                    [(ngModel)]="filterInput[7]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('Reserved', $event.currentTarget.checked)
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Returned to Artist
                  <input
                    [(ngModel)]="filterInput[8]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter(
                        'ReturnedToArtist',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >On Loan
                  <input
                    [(ngModel)]="filterInput[9]"
                    type="checkbox"
                    (click)="
                      SetSaleFilter('OnLoan', $event.currentTarget.checked)
                    "
                /></label>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <!-- <span class="with-carret d-flex" style="margin-right: 2vw">
        {{ status ? status : "Status" }}
        <div style="position: relative; margin-left: 0.5vw">
          <fa-icon
            [icon]="faCartArrowDown"
            (click)="statusDrop = !statusDrop"
            style="cursor: pointer"
          >
          </fa-icon>
          <div
            class="drop-for-category"
            [hidden]="!statusDrop"
            (mouseleave)="statusDrop = false"
          >
            <div
              class="drop-for-category-item"
              (click)="status = 'Published'; onStatusChange()"
            >
              Published
            </div>
            <div
              class="drop-for-category-item"
              (click)="status = 'Sold'; onStatusChange()"
            >
              Sold
            </div>
            <div
              class="drop-for-category-item"
              (click)="status = 'ForSale'; onStatusChange()"
            >
              ForSale
            </div>
            <div
              class="drop-for-category-item"
              (click)="status = 'Reserved'; onStatusChange()"
            >
              Reserved
            </div>
          </div>
        </div>
      </span> -->
      <!-- (focusout)="changeFilterFocus(2)" -->
      <div class="filter-value">
        <div class="input-container" style="width: 8vw">
          <input
            type="text"
            class="selection"
            placeholder="Work Type"
            (focus)="isFilterDropDownOpen[4] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[4] = !isFilterDropDownOpen[4]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw; width: 150%"
            [hidden]="!isFilterDropDownOpen[4]"
            #artGrpDropDown2
          >
            <ul>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Unique
                  <input
                    [(ngModel)]="filterInput[20]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter2(
                        'unique',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Unique - Diptych
                  <input
                    [(ngModel)]="filterInput[21]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter2(
                        'Unique - Diptych',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Unique - Triptych
                  <input
                    [(ngModel)]="filterInput[22]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter2(
                        'Unique - Triptych',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Unique - Set of works
                  <input
                    [(ngModel)]="filterInput[23]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter2(
                        'Unique - Set of works',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Edition
                  <input
                    [(ngModel)]="filterInput[24]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter2(
                        'edition',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="filter-value">
        <div class="input-container" style="width: 9.5vw">
          <input
            type="text"
            class="selection"
            placeholder="Artwork Group"
            (focus)="isFilterDropDownOpen[2] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[2] = !isFilterDropDownOpen[2]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw"
            [hidden]="!isFilterDropDownOpen[2]"
            #artGrpDropDown
          >
            <ul>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Open
                  <input
                    [(ngModel)]="filterInput[8]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter(
                        'open',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Curated
                  <input
                    [(ngModel)]="filterInput[9]"
                    type="checkbox"
                    (click)="
                      SetArtworkGroupFilter(
                        'curated',
                        $event.currentTarget.checked
                      )
                    "
                /></label>
              </li>
              <!-- <li
                style="
                 cursor: pointer;
                  width: 100%;
                "
              >
                <label class="country-name label-class">Gallery
                <input type="checkbox" (click)="SetArtworkGroupFilter('Gallery',$event.currentTarget.checked)" /></label>
              </li> -->
              <!-- <li
              style="
               cursor: pointer;
                width: 100%;
              "
            >
              <label class="country-name label-class">Archive
              <input type="checkbox" (click)="SetArtworkGroupFilter('Archive',$event.currentTarget.checked)" /></label>
            </li> -->
            </ul>
          </div>
        </div>
      </div>
      <!-- <span class="with-carret d-flex" style="margin-right: 2vw">
        {{ artGroup ? artGroup : "Artwork Group" }}
        <div style="position: relative; margin-left: 0.5vw">
          <fa-icon
            [icon]="faCartArrowDown"
            (click)="artGroupDrop = !artGroupDrop"
            style="cursor: pointer"
          >
          </fa-icon>
          <div
            class="drop-for-category"
            [hidden]="!artGroupDrop"
            (mouseleave)="artGroupDrop = false"
          >
            <div
              class="drop-for-category-item"
              (click)="artGroup = 'Curated'; onArtGroupChange()"
            >
              Curated
            </div>

            <div
              class="drop-for-category-item"
              (click)="artGroup = 'Open'; onArtGroupChange()"
            >
              Open
            </div>
            <div
              class="drop-for-category-item"
              (click)="artGroup = 'Gallery'; onArtGroupChange()"
            >
              Gallery
            </div>
            <div
              class="drop-for-category-item"
              (click)="artGroup = 'Archive'; onArtGroupChange()"
            >
              Archive
            </div>
          </div>
        </div>
      </span> -->

      <div class="filter-value">
        <div class="input-container" style="width: 9vw">
          <input
            type="text"
            class="selection"
            placeholder="View Options"
            (focus)="isFilterDropDownOpen[3] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[3] = !isFilterDropDownOpen[3]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw"
            [hidden]="!isFilterDropDownOpen[3]"
            #artViewDropDown
          >
            <ul>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Artist Name</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.display_name"
                  (click)="
                    setViewOptions('display_name', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Sale Price</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.sale_price"
                  (click)="
                    setViewOptions('sale_price', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Dimensions</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.dimensions"
                  (click)="
                    setViewOptions('dimensions', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Location</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.location"
                  (click)="
                    setViewOptions('location', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Medium</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.medium"
                  (click)="
                    setViewOptions('medium', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Type of work</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.type_of_work"
                  (click)="
                    setViewOptions('type_of_work', $event.currentTarget.checked)
                  "
                />
              </li>

              <li
                *ngIf="
                  userDetailsObj.role != 'REPRESENTED ARTIST' &&
                  userDetailsObj.role != 'GALLERY ARTIST'
                "
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">More Info</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.sale_info"
                  (click)="
                    setViewOptions('sale_info', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Edition</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions.edition"
                  (click)="
                    setViewOptions('edition', $event.currentTarget.checked)
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                "
              >
                <div class="country-name">Condition</div>
                <input
                  type="checkbox"
                  [checked]="viewOptions?.condition"
                  (click)="
                    setViewOptions('condition', $event.currentTarget.checked)
                  "
                />
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="filter-value">
        <div class="input-container" style="width: 5vw">
          <input
            type="text"
            class="selection"
            placeholder="Price"
            (focus)="isFilterDropDownOpen[5] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[5] = !isFilterDropDownOpen[5]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw; width: 250%"
            [hidden]="!isFilterDropDownOpen[5]"
            #priceViewDropDown
          >
            <ul>
              <div
                style="margin-top: 0.5vw; font-size: 0.8vw; margin-left: 1vw"
              >
                INR
              </div>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  width: 100%;
                  align-items: center;
                  padding: 0.69vw 0.4vw;
                "
              >
                <input
                  [(ngModel)]="priceFrom"
                  type="text"
                  class="filterInput"
                  placeholder="Min"
                  style="
                    margin-left: 0.5vw;
                    border: 0.069vw solid var(--timeline-color) !important;
                  "
                />
              </li>
              <li
                style="
                  cursor: pointer;
                  display: flex;
                  justify-content: space-between;
                  flex-direction: row;
                  width: 100%;
                  align-items: center;
                  padding: 0.69vw 0.4vw;
                "
              >
                <input
                  [(ngModel)]="priceTo"
                  type="text"
                  class="filterInput"
                  placeholder="Max"
                  style="
                    margin-left: 0.5vw;
                    border: 0.069vw solid var(--timeline-color) !important;
                  "
                />
              </li>
              <li>
                <button
                  class="action_Btn"
                  style="
                    border-radius: 1px;
                    border-style: solid !important;
                    background-color: black;
                    color: white;
                    border: 0.06944vw solid var(--timeline-color);
                    padding: 0.5vw 1vw;
                    border-radius: 2vw;
                    display: inline-flex;
                    width: max-content;
                    justify-content: center;
                    align-items: center;
                  "
                  (click)="SetArtworkPrice()"
                >
                  Apply
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="filter-value">
        <div class="input-container" style="width: 8vw">
          <input
            type="text"
            class="selection"
            placeholder="Medium"
            (focus)="isFilterDropDownOpen[7] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[7] = !isFilterDropDownOpen[7]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw; width: 150%"
            [hidden]="!isFilterDropDownOpen[7]"
            #mediumDropDown
          >
            <ul>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Acrylic
                  <input
                    [(ngModel)]="filterInput[30]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29994fa47fa55069814be',
                        $event.currentTarget.checked,
                        'Acrylic'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Canvas
                  <input
                    [(ngModel)]="filterInput[31]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29996fa47fa5506981768',
                        $event.currentTarget.checked,
                        'Canvas'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Mixed Media
                  <input
                    [(ngModel)]="filterInput[32]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        6,
                        $event.currentTarget.checked,
                        'Mixed Media'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Oil
                  <input
                    [(ngModel)]="filterInput[33]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29995fa47fa55069815ba',
                        $event.currentTarget.checked,
                        'Oil'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Paper
                  <input
                    [(ngModel)]="filterInput[34]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29994fa47fa55069814d5',
                        $event.currentTarget.checked,
                        'Paper'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Print
                  <input
                    [(ngModel)]="filterInput[35]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        5,
                        $event.currentTarget.checked,
                        'Print'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Textile
                  <input
                    [(ngModel)]="filterInput[36]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29995fa47fa5506981558',
                        $event.currentTarget.checked,
                        'Textile'
                      )
                    "
                /></label>
              </li>
              <li style="cursor: pointer; width: 100%">
                <label class="country-name label-class"
                  >Water
                  <input
                    [(ngModel)]="filterInput[37]"
                    type="checkbox"
                    (click)="
                      SetMediumFilter(
                        '61d29994fa47fa55069814d6',
                        $event.currentTarget.checked,
                        'Water'
                      )
                    "
                /></label>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="filter-value">
        <div class="input-container">
          <input
            type="text"
            class="selection"
            placeholder="Dimensions"
            (focus)="isFilterDropDownOpen[6] = true"
            readonly
          />
          <button (click)="isFilterDropDownOpen[6] = !isFilterDropDownOpen[6]">
            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
          </button>
          <div
            class="dropdown-visible"
            style="top: 2.1vw; width: 140%"
            [hidden]="!isFilterDropDownOpen[6]"
            #dimensionViewDropDown
          >
            <ul>
              <div
                style="margin-top: 0.5vw; font-size: 0.8vw; margin-left: 1vw"
              >
                Height ( Inch)
              </div>

              <div style="display: flex; justify-content: space-between">
                <li
                  style="
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    width: 50%;
                    align-items: center;
                  "
                >
                  <input
                    [(ngModel)]="heightFrom"
                    type="text"
                    class="filterInput"
                    placeholder="Min"
                    style="
                      margin-left: 0.5vw;
                      border: 0.069vw solid var(--timeline-color) !important;
                    "
                  />
                </li>
                <li
                  style="
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    flex-direction: row;
                    width: 50%;
                    align-items: center;
                  "
                >
                  <input
                    [(ngModel)]="heightTo"
                    type="text"
                    class="filterInput"
                    placeholder="Max"
                    style="
                      margin-left: 0.5vw;
                      border: 0.069vw solid var(--timeline-color) !important;
                    "
                  />
                </li>
              </div>
              <div
                style="margin-top: 0.5vw; font-size: 0.8vw; margin-left: 1vw"
              >
                Width ( Inch)
              </div>

              <div style="display: flex; justify-content: space-between">
                <li
                  style="
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    width: 50%;
                    align-items: center;
                  "
                >
                  <input
                    [(ngModel)]="widthFrom"
                    type="text"
                    class="filterInput"
                    placeholder="Min"
                    style="
                      margin-left: 0.5vw;
                      border: 0.069vw solid var(--timeline-color) !important;
                    "
                  />
                </li>
                <li
                  style="
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    flex-direction: row;
                    width: 50%;
                    align-items: center;
                  "
                >
                  <input
                    [(ngModel)]="widthTo"
                    type="text"
                    class="filterInput"
                    placeholder="Max"
                    style="
                      margin-left: 0.5vw;
                      border: 0.069vw solid var(--timeline-color) !important;
                    "
                  />
                </li>
              </div>
              <li>
                <button
                  class="action_Btn"
                  style="
                    border-radius: 1px;
                    border-style: solid !important;
                    background-color: black;
                    color: white;
                    border: 0.06944vw solid var(--timeline-color);
                    padding: 0.5vw 1vw;
                    border-radius: 2vw;
                    display: inline-flex;
                    width: max-content;
                    justify-content: center;
                    align-items: center;
                  "
                  (click)="SetArtworkDimension()"
                >
                  Apply
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="rightside-section">
      <ng-container *ngIf="permissionsObj2?.read">
        <div style="position: relative">
          <div
            class="flgSpan"
            tabindex="1"
            (click)="isFlagFilter = !isFlagFilter"
          >
            Flag Options
            <fa-icon style="color: #004ddd" [icon]="Solidflag"> </fa-icon>
          </div>

          <div
            class="filter-div-for-flag"
            [hidden]="!isFlagFilter"
            (mouseleave)="flagfilter()"
          >
            <ul>
              <li (click)="FindFlagged()">
                Find Flagged
                <fa-icon
                  style="color: var(--tertiary-font-color); float: right"
                  [icon]="faCheck"
                  [hidden]="!IsFindFlagged"
                >
                </fa-icon>
              </li>
              <li (click)="manageFlag('flagAll')">Flag all</li>
              <li (click)="manageFlag('removeAll')">Unflag all</li>
              <li (click)="manageFlag('add')">Add to flagged</li>
              <li (click)="FindUnflagged()">
                Find unflagged
                <fa-icon
                  style="color: var(--tertiary-font-color); float: right"
                  [icon]="faCheck"
                  [hidden]="!IsFindUnFlagged"
                >
                </fa-icon>
              </li>
              <li>Replace flagged</li>
              <li (click)="manageFlag('remove')">Subtract from flagged</li>
            </ul>
          </div>
        </div>
      </ng-container>

      <div class="input-container" style="display: flex; float: right">
        <div
          class="text-before"
          [innerHtml]="showListView ? 'List view' : 'Grid view'"
        ></div>
        <label class="switch">
          <input type="checkbox" [(ngModel)]="showListView" />
          <span class="slider round"></span>
        </label>
      </div>
      <span class="popup">
        <fa-icon
          [icon]="faQuestionCircle"
          (click)="helpPopUp = !helpPopUp"
          style="color: var(--tertiary-font-color)"
        ></fa-icon>
        <div class="popuptext" [ngClass]="{ show: helpPopUp }">
          <ul
            style="
              list-style: none;
              margin-bottom: 0;
              padding: 0;
              font-size: 0.6944vw;
            "
          >
            <li>
              <fa-icon style="color: #004ddd" [icon]="faCheckCircle"></fa-icon>
              - Published
            </li>
            <li>
              <fa-icon style="color: red" [icon]="faTimesCircle"></fa-icon> -
              Not Published
            </li>
            <li>
              <fa-icon style="color: #00ff00" [icon]="faCircle"></fa-icon> - For
              Sale
            </li>
            <li>
              <fa-icon style="color: #ffff00" [icon]="faCircle"></fa-icon> -
              Reserved
            </li>
            <li>
              <fa-icon style="color: #101010" [icon]="faCircle"></fa-icon> - Not
              For Sale
            </li>
            <li>
              <fa-icon style="color: #ff0000" [icon]="faCircle"></fa-icon> -
              Sold
            </li>
            <li>
              <fa-icon style="color: #004ddd" [icon]="faLock"></fa-icon> -
              Minted
            </li>
            <li>
              <fa-icon style="color: #bfbfbf" [icon]="faUnlockAlt"></fa-icon> -
              Not Minted
            </li>
            <li>
              <span class="circle" style="background-color: rgb(236, 112, 10)"
                >R</span
              >
              - Returned To Artist
            </li>
            <li>
              <span class="circle" style="background-color: rgb(236, 228, 10)"
                >L</span
              >
              - On Loan
            </li>
          </ul>
        </div>
      </span>
    </div>
  </div>
  <div
    class="d-flex justify-content-between view-actions"
    style="flex-wrap: nowrap; margin-top: 1vw"
  >
    <span style="display: flex; justify-content: end; align-items: end"
      >Showing {{ (offset - 1) * limit + 1 }} -
      {{ (offset - 1) * limit + current.length }} artworks of {{ totalRecords
      }}<span *ngIf="flaggedArtworkArray.length != 0"
        >, {{ flaggedArtworkArray.length }} Flagged
      </span>
      <div
        *ngIf="
          multiple_group_options.length > 0 ||
          multiple_sale_options.length > 0 ||
          type_of_work.length > 0 ||
          work_type.length > 0 ||
          this.minPrice ||
          this.maxPrice ||
          this.minHeight ||
          this.maxHeight ||
          this.minWidth ||
          this.maxWidth
        "
        style="padding-left: 0.5vw; cursor: pointer"
      >
        <a (click)="clearFilters()"
          ><button class="saveBtn" style="font-size: 0.8vw; margin-left: 0.2vw">
            Clear filters
          </button></a
        >
      </div>
    </span>
    <div class="d-flex" style="gap: 2vw">
      <!--
      <button (click)="onGotoPage()">Go To</button> -->
      <span class="d-flex" style="gap: 0.7vw; align-items: center">
        <fa-icon
          [icon]="faAngleLeft"
          (click)="managePagination('prev')"
        ></fa-icon>
        Page
        <input
          [(ngModel)]="gotoPage"
          [ngModelOptions]="{ standalone: true }"
          type="text"
          min="1"
          style="width: 2vw; text-align: center"
          (focusout)="onGotoPage()"
          pattern="\d*"
          (keydown.enter)="onGotoPage()"
        />
        <!-- <input
        style="height: 1vw; width: 1vw"
        type="number"
        [max]="totalPage"
        [value]="offset"
        min="1"
        name="pager"
        id="pager"
      /> -->
        Of {{ totalPage }}
        <fa-icon
          [icon]="faAngleRight"
          (click)="managePagination('next')"
        ></fa-icon>
      </span>
    </div>
  </div>
</div>
<div (scroll)="onElementScroll($event)" id="mainContent" [formGroup]="form">
  <div class="collector-form">
    <div class="interests">
      <div class="contents" *ngIf="!showListView">
        <div
          *ngFor="let art of current; let i = index"
          class="artist"
          style="position: relative"
        >
          <img
            (click)="editArtWork(art)"
            class="artworkImggg"
            [src]="
              art?.primary_image.length > 0
                ? 'https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
                  art?.primary_image[0]?.url
                : ''
            "
          />
          <a (click)="showMenu[i] = !showMenu[i]">
            <div class="edit_grid"></div>
            <div *ngIf="showMenu[i]" class="dropdown-container">
              <div class="dropdown-content">
                <a (click)="editArtWork(art)"
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Edit
                  </div></a
                >
                <a (click)="duplicateArt(art)"
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Duplicate
                  </div></a
                >
                <a (click)="deleteArt(art)"
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Delete
                  </div></a
                >
                <a
                  (click)="
                    downloadImage(
                      art?.primary_image[0]?.url,
                      art?.primary_image[0]?.name
                    )
                  "
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Download Thumbnail ({{
                      (art?.primary_image[0].size / 1048576).toFixed(2) || "--"
                    }}
                    MB)
                  </div></a
                >
                <a
                  (click)="
                    downloadImage(
                      art?.original_artwork_file[0]?.url,
                      art?.original_artwork_file[0]?.name
                    )
                  "
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Download Original File ({{
                      (art?.original_artwork_file[0].size / 1048576).toFixed(
                        2
                      ) || "--"
                    }}
                    MB)
                  </div></a
                >
                <!-- <a (click)="publishArtWork(art)"
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Publish
                  </div></a
                >
                <a
                  ><div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Duplicate
                  </div></a
                > -->
              </div>
            </div>
          </a>
          <div class="icon-indicators">
            <fa-icon
              [style.color]="art?.publish_artworks ? '#004ddd' : 'red'"
              [icon]="art?.publish_artworks ? faCheckCircle : faTimesCircle"
            ></fa-icon>
            <span
              *ngIf="art?.sale_options == 'ReturnedToArtist'"
              class="circle"
              style="background-color: rgb(236, 112, 10)"
              >R</span
            >
            <span
              *ngIf="art?.sale_options == 'OnLoan'"
              class="circle"
              style="background-color: rgb(236, 228, 10)"
              >L</span
            >
            <fa-icon
              *ngIf="
                art?.sale_options != 'OnLoan' &&
                art?.sale_options != 'ReturnedToArtist'
              "
              [style.color]="dotColor(art?.sale_options)"
              [icon]="faCircle"
            ></fa-icon>
            <fa-icon
              [style.color]="art?.nft?.id ? '#004ddd' : '#bfbfbf'"
              [icon]="art?.nft?.id ? faLock : faUnlockAlt"
            ></fa-icon>
            <!--   <div
              [ngClass]="art?.publish_artworks ? 'published-dot' : 'unpublished-dot'"
            ></div>
            <div *ngIf="temporary.artwork_type == 'Edition'" class="edition">
              E
            </div>
            <div
              class="status-dot"
              [ngStyle]="dotColor(art?.sale_options)"
            ></div>
            <div
              [ngClass]="art?.minted ? 'minted' : 'not-minted'"
              [ngStyle]="MintlockColor(art?.minted)"
            ></div>
            <img
              class="blockchainIcon"
              *ngIf="temporary.minted"
              [src]="blockchainType(temporary.network)"
              alt="Blockchain Type"
            />-->
          </div>
          <div class="wrappp">
            <div class="name" style="line-height: 0.7vw">
              <i
                >{{
                  art?.artwork_title.length > 19
                    ? art.artwork_title.slice(0, 19) + "..."
                    : art.artwork_title
                }}, &nbsp;
                <span style="font-style: normal"> {{ art?.year }}</span>
                <img
                  class="grpOpn"
                  *ngIf="art?.artwork_group == 'open'"
                  src="../../../../../assets/icons/TOBadges.png"
                  alt="TerrainOG"
                />
                <img
                  class="grpOpn"
                  *ngIf="art?.artwork_group == 'curated'"
                  src="../../../../../assets/icons/TcBadges (1).png"
                  alt="TerrainOG"
                />
              </i>
            </div>
            <div *ngIf="viewOptions.display_name" class="name">
              {{ art?.artist_id?.display_name }}
            </div>
            <div [hidden]="!art?.minted" class="name" style="margin-top: 0">
              NFT id : {{ art?.nft_id }}
              <img
                class="blockchainIcon"
                [src]="blockchainType(temporary.network)"
                alt="Blockchain Type"
              />
            </div>
            <div *ngIf="viewOptions.dimensions" class="name">
              {{ art?.dimensions }}
            </div>
            <div *ngIf="viewOptions.location" class="name">
              {{ art?.location }}
            </div>
            <div
              *ngIf="
                art?.work_type != 'edition' &&
                viewOptions.location &&
                art?.extras?.storageAddress &&
                art?.extras?.isGallerWearhouse == 1
              "
              class="name"
            >
              {{ art?.extras?.storageAddress }}
            </div>
             <div
              *ngIf="
                art?.work_type == 'edition' &&
                viewOptions.location &&
                art?.editionDetails[0]?.extras?.storageAddress &&
                art?.editionDetails[0]?.isGallerWearhouse == 1
              "
              class="name"
            >
              {{ art?.editionDetails[0]?.extras?.storageAddress }}
            </div>
            <div
              *ngIf="
                art?.work_type != 'edition' &&
                viewOptions.location &&
                art?.extras?.artistAddress?.title &&
                art?.extras?.isGallerWearhouse == 2
              "
              class="name"
            >
              {{ art?.extras?.artistAddress?.title }}
            </div>
            <div
              *ngIf="
                art?.work_type == 'edition' &&
                viewOptions.location &&
                art?.editionDetails[0]?.extras?.artistAddress?.title &&
                art?.editionDetails[0]?.isGallerWearhouse == 2
              "
              class="name"
            >
              {{ art?.editionDetails[0]?.extras?.artistAddress?.title }}
            </div>
            <div *ngIf="viewOptions.medium" class="name">{{ art?.medium }}</div>
            <div *ngIf="viewOptions.type_of_work" class="name">
              {{ art?.physicalOrDigital }}
            </div>
            <div
              *ngIf="viewOptions.sale_price"
              class="name"
              style="display: flex; justify-content: space-between"
            >
              ₹ {{ art?.sale_price | number : "1.0" }}
            </div>
            <ng-container
              *ngIf="
                userDetailsObj.role != 'REPRESENTED ARTIST' &&
                userDetailsObj.role != 'GALLERY ARTIST'
              "
            >
              <div *ngIf="viewOptions.sale_info" class="name">
                {{ art?.sold_date }}
              </div>
            </ng-container>

            <div *ngIf="viewOptions.edition" class="name">
              {{ art?.edition_details }}
            </div>
            <div *ngIf=" art?.work_type != 'edition' && viewOptions.condition" class="name">
              Condition: {{ art?.current_state }}
            </div>
            <div *ngIf=" art?.work_type == 'edition' && viewOptions.condition" class="name">
              Condition: {{ art?.editionDetails[0]?.extras?.currentState }}
            </div>
            <div
              *ngIf="art?.work_type != 'edition' && viewOptions.condition && art?.extras?.packaging"
              class="name"
            >
              Packaging: {{ art?.extras?.packaging }}
            </div>
            <div
              *ngIf="art?.work_type == 'edition' && viewOptions.condition && art?.editionDetails[0]?.extras?.packaging"
              class="name"
            >
              Packaging: {{ art?.editionDetails[0]?.extras?.packaging }}
            </div>
            <div
              *ngIf="art?.work_type != 'edition' && viewOptions.condition && art?.extras?.installState"
              class="name"
            >
              Install State: {{ art?.extras?.installState }}
            </div>
            <div
              *ngIf="art?.work_type == 'edition' && viewOptions.condition && art?.editionDetails[0]?.extras?.installState"
              class="name"
            >
              Install State: {{ art?.editionDetails[0]?.extras?.installState }}
            </div>
            <ng-container
              *ngIf="
                userDetailsObj.role != 'REPRESENTED ARTIST' &&
                userDetailsObj.role != 'GALLERY ARTIST'
              "
            >
              <div
                [innerHTML]="getSaleInformation(art)"
                *ngIf="viewOptions.sale_info"
                class="name"
              ></div>
            </ng-container>
            <div
              class="name"
              style="display: flex; justify-content: space-between"
            >
              <div></div>
              <div style="display: none">{{ isFlagged(art._id) }}</div>
              <fa-icon
                *ngIf="isFlagged(art._id)"
                [style.color]="'#004ddd'"
                style="margin-right: 0.1vw"
                [icon]="Solidflag"
                (click)="Flagged(art._id)"
              ></fa-icon>

              <fa-icon
                *ngIf="!isFlagged(art._id)"
                [style.color]="'#bfbfbf'"
                style="margin-right: 0.1vw"
                [icon]="OutlineFlag"
                (click)="Flagged(art._id)"
              ></fa-icon>
            </div>
          </div>
          <!-- <div class="wrappp">
            <div class="name">
              <i>{{ art?.artwork_title }}</i>, {{ art?.startYear }}
            </div>
            <div class="name">{{ art?.artist_id?.display_name }}</div>
            <div class="name">₹ {{ art?.sale_price }}</div>
            <div class="name">NFT id : {{ temporary?.NFT_id }}</div>
          </div> -->
        </div>
        <ng-container *ngIf="isLoading">
          <div *ngFor="let item of temporary2" class="artist">
            <app-placeholder height="10.69vw" width="14.93vw">
            </app-placeholder>
          </div>
        </ng-container>
      </div>

      <div class="contentList" *ngIf="showListView">
        <div class="Table">
          <div
            class="Table-row"
            *ngFor="let art of current; let i = index"
            (click)="openNav(i)"
            [ngStyle]="
              list_selection == i
                ? { 'background-color': '#b0c7dda3', color: '#fff' }
                : {}
            "
          >
            <div class="Table-row-item img-item" data-header="Header1">
              <img
                class="img-artwork-thumbnail"
                [src]="
                  art?.thumbnail_of_primary
                    ? art?.thumbnail_of_primary
                    : art?.primary_image.length > 0
                    ? art?.primary_image[0]?.url
                    : ''
                "
                alt="thumbnail"
              />
            </div>
            <div class="Table-row-item fa-icon-list" data-header="Header2">
              <fa-icon
                [style.color]="art?.publish_artworks ? '#004ddd' : 'red'"
                [icon]="art?.publish_artworks ? faCheckCircle : faTimesCircle"
              ></fa-icon>

              <fa-icon
                [style.color]="dotColor(art?.sale_options)"
                [icon]="faCircle"
              ></fa-icon>
              <fa-icon
                [style.color]="art?.minted ? '#004ddd' : '#bfbfbf'"
                [icon]="art?.minted ? faLock : faUnlockAlt"
              ></fa-icon>
            </div>
            <div
              class="Table-row-item title-art"
              [ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
              data-header="Header3"
            >
              <i
                >{{
                  art?.artwork_title.length > 19
                    ? art.artwork_title.slice(0, 19) + "..."
                    : art.artwork_title
                }}, &nbsp;
                <span style="font-style: normal"> {{ art?.year }}</span>
                <img
                  class="grpOpn"
                  *ngIf="art?.artwork_group == 'open'"
                  src="../../../../../assets/icons/TOBadges.png"
                  alt="TerrainOG"
                />
                <img
                  class="grpOpn"
                  *ngIf="art?.artwork_group == 'curated'"
                  src="../../../../../assets/icons/TcBadges (1).png"
                  alt="TerrainOG"
                />
              </i>
            </div>
            <div
              class="Table-row-item"
              [ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
              data-header="Header4"
            >
              {{ art?.artist_id?.display_name }}
            </div>
            <div
              class="Table-row-item"
              *ngIf="!isSidemenuOpen"
              data-header="Header5"
            >
              ₹{{ art?.sale_price }}
            </div>

            <div [hidden]="!art?.minted" class="Table-row-item">
              <div class="name" style="margin-top: 0">
                {{ art?.nft_id }}
                <img
                  class="blockchainIcon"
                  [src]="blockchainType(temporary.network)"
                  alt="Blockchain Type"
                />
              </div>
            </div>
            <div
              class="Table-row-item actions-fa-icon"
              style="position: relative"
            >
              <fa-icon
                (click)="editArtWork(art)"
                [icon]="faPencilAlt"
              ></fa-icon>
              <fa-icon
                [ngStyle]="{
                  color: isFlagged(art._id) ? '#004ddd' : 'inherit'
                }"
                [icon]="isFlagged(art._id) ? Solidflag : OutlineFlag"
                (click)="Flagged(art._id)"
              ></fa-icon>
              <!-- <div style="position: relative;"> -->
              <fa-icon
                [style.color]="showMenuList[i] ? '#004ddd' : 'inherit'"
                [icon]="faEllipsisV"
                (click)="showMenuList[i] = !showMenuList[i]"
              ></fa-icon>
              <div [hidden]="!showMenuList[i]" class="dropdown-container-list">
                <!-- <div class="dropdown-content"> -->
                <a (click)="editArtWork(art)">
                  <div style="padding: 0.5vw; cursor: pointer; color: black">
                    Edit
                  </div>
                </a>
                <a (click)="publishArtWork(art)">
                  <div style="padding: 0.5vw; cursor: pointer; color: black">
                    Publish
                  </div>
                </a>
                <a>
                  <div style="padding: 0.5vw; cursor: pointer; color: black">
                    Duplicate
                  </div>
                </a>
                <!-- </div> -->
              </div>
              <!-- </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- <div class="footer-buttons" >
  <div class="aui">
    <button id="new-record" class="button-type" onclick="">New record</button>

    <button
      class="simpe-type"
      (click)="isCreateDrop = !isCreateDrop"
      style="position: relative"
      (focusout)="changeFocus()"
    >
      Create...
      <div class="openUp" [hidden]="!isCreateDrop">
        <span class="dropdown-menu-arrow"></span>
        <ul>
          <li>Document/Reports...</li>
          <li (click)="isPVModalOpen = true">Create a PrivateView...</li>
        </ul>
      </div>
    </button>
    <button class="simpe-type" onclick="">SELL/OFFER</button>
    <button class="simpe-type" onclick="">UPDATE/DELETE MULTIPLE</button>
    <button class="simpe-type" onclick="">SAVE SEARCH</button>
  </div>
</div> -->
<div
  id="mySidenav"
  class="sidenav"
  [style.width]="isSidemenuOpen ? '400px' : 0"
>
  <div class="sidenav-content">
    <div class="closebtn" (click)="closeNav()">&times;</div>
    <div class="head-wrapper">
      <div class="img-side">
        <img [src]="current[list_selection]?.thumbnail_of_primary" />
      </div>
      <div class="text-side">
        <div class="text-container" style="line-height: 1">
          <b> {{ current[list_selection]?.artist_id?.display_name }} </b>
        </div>
        <div class="text-container">
          {{ current[list_selection]?.artwork_title }},
          {{ current[list_selection]?.startYear }}
        </div>

        <div class="text-container">
          {{ current[list_selection]?.medium }}
        </div>
        <div class="text-container">
          {{ current[list_selection]?.dimensions }}
        </div>
        <div class="text-container description">
          {{ current[list_selection]?.description }}
        </div>
        <div class="text-container">
          {{ temporary?.NFT_id }}
        </div>
        <div class="text-container">
          ₹ {{ current[list_selection]?.sale_price }}
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modelForCreate" [hidden]="!isPVModalOpen">
  <div class="Content-Box">
    <span class="modHead">Create a Preview Link from flagged records </span>
    <!-- <div class="input-info" style="margin-bottom: 0vw; margin-top: 0.5vw">
      {{ flaggedArtworkArray.length }} Flagged
    </div> -->
    <div class="field-value" style="padding-right: 0.5vw">
      <input
        type="text"
        [(ngModel)]="privateTitle"
        placeholder="Preview Links Title"
      />
      <div class="placeholder">Enter a Title</div>
      <div class="input-info" style="margin-top: 0.5vw">
        {{ flaggedArtworkArray.length }} Flagged
      </div>
    </div>
    <!-- <div class="sort-section">
      <span class="as">Sorted by:</span><span class="Choise">Artist,year</span>
      <span class="choiceClick" (click)="isSortChoice = !isSortChoice"
        >Change...
        <div class="sort-option-div" [hidden]="!isSortChoice">
          <ul>
            <li>Artist,stock no</li>
            <li>Artist,stock no</li>
            <li>Artist,stock no</li>
            <li>Artist,stock no</li>
            <li>Artist,stock no</li>
            <li>Artist,stock no</li>
          </ul>
        </div>
      </span>
    </div> -->
    <!-- <div class="booxDot">
      <p>
        WARNING: The number of found records exceeds the maximum number of
        records for a Gallery Preview (200 records). Only the first 200 records
        will be added.
      </p>
    </div> -->
    <div class="btnn">
      <button
        id="new-record"
        class="simpe-type"
        (click)="isPVModalOpen = false"
      >
        Cancel
      </button>
      <button id="new-record" class="button-type" (click)="createPrivateLink()">
        Create
      </button>
    </div>
  </div>
</div>

<div class="filter-sidenav" [style.width]="isFilterMenuOpen ? '400px' : 0">
  <div class="sidenav-content">
    <div class="closebtn" (click)="isFilterMenuOpen = false">&times;</div>
    <div class="head-wrapper">
      <p>Filter Results</p>
      <div style="display: flex; flex-direction: column; height: 100%">
        <div class="filter-content-div" style="height: 80%; overflow-y: auto">
          <div class="field-value">
            <input
              type="text"
              [(ngModel)]="artwork_title"
              style="width: unset; height: 2.47vw"
            />
            <div class="placeholder">Artwork</div>
            <!-- <div class="input-info">

              </div> -->
          </div>
          <div class="field-value">
            <input
              type="text"
              [(ngModel)]="location_keyword"
              style="width: unset; height: 2.47vw"
            />
            <div class="placeholder">Location</div>
            <!-- <div class="input-info">

              </div> -->
          </div>
          <section style="display: flex; flex-direction: column">
            <span style="margin: 1vw 0 0.5vw 0">Status</span>
            <label
              ><input
                type="checkbox"
                (click)="
                  SetAdvSaleFilter('Published', $event.currentTarget.checked)
                "
                class="checkbox_style"
              />
              Published</label
            >
            <label
              ><input
                type="checkbox"
                (click)="
                  SetAdvSaleFilter('NotPublished', $event.currentTarget.checked)
                "
                class="checkbox_style"
              />
              Not Published</label
            >
            <label
              ><input
                type="checkbox"
                (click)="SetAdvSaleFilter('Sold', $event.currentTarget.checked)"
                class="checkbox_style"
              />
              Sold</label
            >
            <label
              ><input
                type="checkbox"
                (click)="
                  SetAdvSaleFilter('ForSale', $event.currentTarget.checked)
                "
                class="checkbox_style"
              />
              For Sale</label
            >
            <label
              ><input
                type="checkbox"
                (click)="
                  SetAdvSaleFilter('NotForSale', $event.currentTarget.checked)
                "
                class="checkbox_style"
              />Not For Sale</label
            >
            <label
              ><input
                type="checkbox"
                (click)="
                  SetAdvSaleFilter('Reserved', $event.currentTarget.checked)
                "
                class="checkbox_style"
              />
              Reserved</label
            >
          </section>
          <section style="display: flex; flex-direction: column">
            <span style="margin: 1vw 0 0 0">Price Range</span>
            <div class="field-value">
              <input
                type="number"
                [(ngModel)]="fromPriceRange"
                style="width: unset; height: 2.47vw"
              />
              <div class="placeholder">From</div>
            </div>
            <div class="field-value">
              <input
                type="number"
                [(ngModel)]="toPriceRange"
                style="width: unset; height: 2.47vw"
              />
              <div class="placeholder">To</div>
            </div>
            <!-- <label><input type="checkbox" class="checkbox_style" /> No price entered</label>
        <label><input type="checkbox" class="checkbox_style"/> Up to 1,000</label>
        <label><input type="checkbox" class="checkbox_style"/> 1,000 to 2,000</label>
        <label><input type="checkbox" class="checkbox_style"/> 2,000 to 3,000</label>
        <label><input type="checkbox" class="checkbox_style"/> 3,000-5,000</label>

        <ng-container #name *ngIf="priceShowMore">
        <label><input type="checkbox" class="checkbox_style"/>5,000-10,000</label>
        <label><input type="checkbox" class="checkbox_style"/>10,000-20,000</label>
        <label><input type="checkbox" class="checkbox_style"/>20,000-30,000</label>
        <label><input type="checkbox" class="checkbox_style"/>30,000-50,000</label>
        <label><input type="checkbox" class="checkbox_style"/>50,000-100,000</label>
        <label><input type="checkbox" class="checkbox_style"/>100,000-200,000</label>
        <label><input type="checkbox" class="checkbox_style"/>200,000-300,000</label>
        <label><input type="checkbox" class="checkbox_style"/>300,000-500,000</label>
        <label><input type="checkbox" class="checkbox_style"/>500,000-1,000,000</label>
        <label><input type="checkbox" class="checkbox_style"/>1,000,000-2,000,000</label>
        <label><input type="checkbox" class="checkbox_style"/>2,000,000-3,000,000</label>
        <label><input type="checkbox" class="checkbox_style"/>3,000,000-5,000,000</label>
        <label><input type="checkbox" class="checkbox_style"/>5,000,000-10,000,000</label>
        <label><input type="checkbox" class="checkbox_style"/>over 10,000,000</label>
      </ng-container> -->
            <p
              style="
                cursor: pointer;
                color: #004ddd;
                margin-top: 0.3vw;
                font-size: 1.0452vw;
              "
              (click)="priceShowMore = !priceShowMore"
            >
              {{ priceShowMore ? "show less" : "show more" }}
            </p>
          </section>
        </div>
        <div
          class="filter-actions"
          style="
            height: 20%;
            display: flex;
            justify-content: space-around;
            align-items: center;
          "
        >
          <button class="filter_action" (click)="isFilterMenuOpen = false">
            Cancel
          </button>
          <button class="filter_action" (click)="advancedSearch()">
            Advanced Search
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modelForCreate" [hidden]="!deleteArtwork.popup">
  <div class="Content-Box">
    <span class="modHead"
      >Delete
      <i
        >{{
          deleteArtwork.item?.artwork_title.length > 19
            ? deleteArtwork.item?.artwork_title.slice(0, 19) + "..."
            : deleteArtwork.item?.artwork_title
        }}, &nbsp;
        <span style="font-style: normal"> {{ deleteArtwork.item?.year }}</span>
      </i>
      !!?
    </span>

    <div class="btnn">
      <button class="simpe-type" (click)="deleteArtwork.popup = false">
        Cancel
      </button>
      <button class="button-type" (click)="deleteArtCall()">Confirm</button>
    </div>
  </div>
</div>

<div class="modelForCreate" [hidden]="!duplicateArtwork.popup">
  <div class="Content-Box">
    <span class="modHead"
      >Duplicate
      <i
        >{{
          duplicateArtwork.item?.artwork_title.length > 19
            ? duplicateArtwork.item?.artwork_title.slice(0, 19) + "..."
            : duplicateArtwork.item?.artwork_title
        }}, &nbsp;
        <span style="font-style: normal">
          {{ duplicateArtwork.item?.year }}</span
        >
      </i>
      !!?
    </span>
    <div class="field-value" style="padding-right: 0.5vw">
      <input
        type="text"
        [(ngModel)]="duplicateArtwork.data.artwork_title"
        placeholder="Gallery Preview Title"
      />
      <div class="placeholder">Artwork Title</div>
      <div style="margin-top: 0.5vw; margin-bottom: 0.5vw">
        <input
          type="checkbox"
          [(ngModel)]="duplicateArtwork.data.with_images"
          class="checkbox_style"
        />
        With Images
      </div>
      <div style="margin-top: 0.5vw; margin-bottom: 0.5vw">
        <input
          type="checkbox"
          [(ngModel)]="duplicateArtwork.data.with_price"
          class="checkbox_style"
        />
        With Price Data
      </div>
      <div style="margin-top: 0.5vw; margin-bottom: 0.5vw">
        <input
          type="checkbox"
          [(ngModel)]="duplicateArtwork.data.with_shipping"
          class="checkbox_style"
        />
        With Shipping Data
      </div>
    </div>

    <div class="btnn">
      <button class="simpe-type" (click)="duplicateArtwork.popup = false">
        Cancel
      </button>
      <button class="button-type" (click)="duplicateArtCall()">Confirm</button>
    </div>
  </div>
</div>
