import {
	Component,
	ElementRef,
	NgModule,
	OnInit,
	ViewChild,
} from '@angular/core';
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
@Component({
	selector: 'app-artworks',
	templateUrl: './artworks.component.html',
	styleUrls: ['./artworks.component.scss'],
})
export class AddArtworkComponent implements OnInit {
	mobile = '(*************';
	flag = flagData[0].flag;
	code = flagData[0].dial_code;
	number = flagData[0].code;
	flagData = flagData;
	isCancel = false;
	isDropDown = false;
	isDropDown2 = false;
	form: FormGroup;
	id;
	selectedFiles: File[] = [];

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private formBuilder: FormBuilder,
		private artistInfoService: ArtistInfoService
	) { }

	ngOnInit(): void {
		this.route.paramMap.subscribe((params) => {
			console.log(params);

			if (params.get('id')) {
				this.id = params.get('id');
				this.artistInfoService.getArtwork().subscribe((data) => {
					const art = data.find((a) => a.id == this.id);
					this.form.patchValue(art);
					this.form.patchValue({ priceList: art.priceList[0].price });
					this.selectedFiles.push(
						new File([''], art.primaryImage.originalname)
					);
				});
			} else {
				this.id = null;
			}
		});
		this.form = this.formBuilder.group({
			title: new FormControl('', [Validators.required]),
			year: new FormControl('', [Validators.required]),
			dimensions: new FormControl('', [Validators.required]),
			medium: new FormControl('', [Validators.required]),
			height: new FormControl('', [Validators.required]),
			width: new FormControl('', [Validators.required]),
			depth: new FormControl('', [Validators.required]),
			type: new FormControl('', [Validators.required]),
			priceList: new FormControl('', [Validators.required]),
		});
	}

	async onFileSelect(files: FileList) {
		if (files[0].size < 2100000) {
			this.selectedFiles.push(files[0]);
		}
	}
	removeItem(index) {
		this.selectedFiles.splice(index, 1);
	}

	select(i) {
		switch (i) {
			case 1:
				this.form.get('type').setValue('3D');
				break;
			case 2:
				this.form.get('type').setValue('NFTs');
				break;
			default:
				break;
		}
	}
	submit() {
		const data = this.form.value;
		data.priceList = [{ price: data.priceList }];
		console.log(data);
		this.artistInfoService.addArtwork(data, this.selectedFiles).subscribe();
	}
}
