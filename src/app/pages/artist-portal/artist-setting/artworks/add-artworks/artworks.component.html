<div class="container__main">
	<p class="main__heading">
		{{ id ? 'Edit Artworks' : 'Add Artworks' }}
	</p>
	<div class="content__section">
		<div class="profile-field">
			<div class="w-100 field-contents">
				<form [formGroup]="form">
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								type="text"
								formControlName="title"
								placeholder="Artwork Title"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Artwork Title</div>
						</div>
						<div class="field-value" style="padding-left: 0.5vw">
							<input
								type="text"
								formControlName="year"
								placeholder="Year"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Year</div>
						</div>
					</div>
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								type="text"
								formControlName="dimensions"
								placeholder="Dimensions"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Dimensions</div>
						</div>
						<div class="field-value" style="padding-left: 0.5vw">
							<input
								type="text"
								formControlName="medium"
								placeholder="Medium"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Medium</div>
						</div>
					</div>
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								type="text"
								formControlName="height"
								placeholder="Height in CM"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Height</div>
						</div>
						<div class="field-value" style="padding-left: 0.5vw">
							<input
								type="text"
								formControlName="width"
								placeholder="Width in CM"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Width</div>
						</div>
					</div>
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								type="text"
								formControlName="depth"
								placeholder="Depth in CM"
								style="width: 17.1vw"
							/>
							<div class="placeholder">Depth</div>
						</div>
						<div class="field-value" style="padding-left: 0.5vw">
							<input
								type="text"
								formControlName="priceList"
								placeholder="Artist List Price"
							/>
							<div class="placeholder">Artist List Price</div>
						</div>
					</div>
					<div class="field-value">
						<div class="input-container">
							<input
								type="text"
								class="selection"
								formControlName="type"
								placeholder="Artwork Type"
							/>
							<button (click)="isDropDown2 = !isDropDown2">
								<img src="assets/icons/arrow-down.png" class="flag-arrow" />
							</button>
							<div
								[ngClass]="{
									'dropdown-hidden': !isDropDown2,
									'dropdown-visible': isDropDown2
								}"
							>
								<ul>
									<li (click)="select(1)">
										<div class="country-name">3D</div>
									</li>
									<li (click)="select(2)">
										<div class="country-name">NFTs</div>
									</li>
								</ul>
							</div>
						</div>
					</div>

					<div style="margin-top: 3.47vw; font-size: 1.25vw">Primary Image</div>
					<div class="field-value flex-block">
						<div class="input-container file-upload">
							<input
								class="upload-input"
								type="file"
								(change)="onFileSelect($event.target.files)"
								accept="image/*"
							/>
							<div *ngIf="selectedFiles.length <= 0" class="icon">
								<img src="assets/images/<EMAIL>" />
							</div>
							<div *ngIf="selectedFiles.length <= 0" class="text-content">
								<div class="title">You can upload or drop your file here.</div>
								<div class="sub-title">Maximum upload size: 2 MB</div>
							</div>
							<ng-container *ngIf="selectedFiles.length > 0">
								<ng-container *ngFor="let file of selectedFiles; let i = index">
									<div class="text-content">
										<div class="title">
											{{ file.name }}
										</div>
										<div class="sub-title">
											File size: {{ (file.size / 1048576).toFixed(2) }} MB
										</div>
										<div (click)="removeItem(i)" class="close">
											<img src="assets/icons/close.png" />
										</div>
									</div>
								</ng-container>
							</ng-container>

							<div class="button-container">
								<div class="button">
									{{ selectedFiles.length > 0 ? 'Add file' : 'Choose file' }}
								</div>
							</div>
						</div>
					</div>
				</form>
				<div class="buttonList">
					<button (click)="submit()" class="save">Save</button>
				</div>
			</div>
		</div>
	</div>
</div>
