input::placeholder {
  color: var(--primary-font-color);
}
.container__main {
  padding-right: calc(36.11vw - var(--page-default-margin));
  padding-bottom: 6.3vw;
  @media (max-width: 768px) {
    padding-right: calc(6.11vw - var(--page-default-margin));
    padding-bottom: 22.3vw;
  }
  .main__heading {
    font-size: 1.805vw;
    margin-bottom: 2.083vw;
    @media (max-width: 768px) {
      font-size: 5.79vw;
      margin-bottom: 7.24vw;
    }
    font-weight: 600;
  }
  .address-section {
    margin-top: 2.083vw;
    @media (max-width: 768px) {
      margin-top: 9.66vw;
    }
    &-heading {
      font-size: 1.25vw;
      font-weight: 500;
      @media (max-width: 768px) {
        font-size: 4.34vw;
        margin-bottom: 7.24vw;
      }
    }
  }

  .profile-field {
    width: 35vw;

    .placeholder-institution {
      position: absolute;
      left: 1vw;
      top: 50%;
      width: 2vw;
      height: 2vw;
      transform: translate(0, -50%);

      @media (max-width: 425px) {
        margin-left: 1vw;
        position: absolute;
        left: 1.041vw;
        top: 54%;
        width: 4.25vw;
        height: 4.25vw;
        transform: translate(0, -50%);
      }

      @media (max-width: 768px) and (min-width: 550px) {
        margin-left: 1vw;
        position: absolute;
        left: 1.041vw;
        top: 54%;
        width: 3.25vw;
        height: 3.25vw;
        transform: translate(0, -50%);
      }
    }

    .placeholder1 {
      margin-top: 1vw;

      @media (max-width: 768px) {
        font-size: 4vw;
        margin-top: 5vw;
      }

      @media (max-width: 768px) and (min-width: 550px) {
        font-size: 2vw;
        margin-top: 2vw;
      }
    }

    .shipping-form {
      @media (max-width: 425px) {
        position: relative;
        z-index: 100;
        margin: -60vw 0vw 0vw -6vw;
        padding-left: 6vw;
        background-color: white;
      }

      @media (min-width: 500px) and(max-width:768px) {
        position: relative;
        z-index: 100;
        margin: -38vw 0vw 0vw -6vw;
        padding-left: 6vw;
        background-color: white;
      }
    }

    // @media (max-width: 768px) {
    //   width: 100%;
    //   margin-top: 80vw;
    // }

    .flex-block {
      display: flex !important;
    }

    .addAddress {
      background: none;
      margin: 2.08vw 0;
      border: 0;
      outline: none;

      img {
        height: 2.29vw;
      }
    }

    .address-info {
      margin-bottom: 2.08vw;
      position: relative;

      .hr-line {
        margin-top: 2.08vw;
        height: 0.07vw;
        width: 100%;
        background-color: var(--hr-primary-color);
      }

      h1 {
        margin: 0;
        font-size: 1.25vw;
        font-family: var(--secondary-font);
        font-weight: 500;
      }

      p {
        margin: 0;
        margin-top: 1.04vw;
        font-family: var(--secondary-font);
      }

      .sub {
        color: var(--quaternary-font-color);
      }

      .edit-icon {
        position: absolute;
        right: 0;
        top: 0;

        img {
          margin-left: 1.04vw;
          cursor: pointer;
          width: 2.85vw;
          height: 2.85vw;
        }
      }
    }

    .field-contents {
      .field-head {
        margin-top: 2.78vw;
        margin-bottom: 0;
        font-family: var(--secondary-font);
        font-size: 1.67vw;

        @media (max-width: 320px) {
          margin-top: 70.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 5.5vw;
          font-weight: 900;
          padding-top: 6vw;
        }

        @media (min-width: 330px) and (max-width: 375px) {
          margin-top: 66.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 5.5vw;
          font-weight: 900;
          padding-top: 10vw;
        }

        @media (min-width: 380px) and (max-width: 425px) {
          margin-top: 65.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 5.5vw;
          font-weight: 900;
          padding-top: 10vw;
        }

        @media (min-width: 500px) and (max-width: 768px) {
          margin-top: 45vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 3vw;
          font-weight: 900;
          padding-top: 0vw;
        }
      }

      .field-sub {
        margin-top: 1.04vw;
        margin-bottom: 1.38vw;
        font-family: var(--secondary-font);
        color: var(--quaternary-font-color);
        font-size: 1.25vw;

        @media (max-width: 425px) {
          margin-top: 5.04vw;
          margin-bottom: 1.38vw;
          font-family: var(--secondary-font);
          color: var(--quaternary-font-color);
          font-size: 4.5vw;
          padding-right: 17vw;
          line-height: 6.5vw;
        }

        @media (min-width: 500px) and(max-width:768px) {
          margin-top: 3.04vw;
          margin-bottom: 3vw;
          font-family: var(--secondary-font);
          color: var(--quaternary-font-color);
          font-size: 2.5vw;
          padding-right: 17vw;
          line-height: 4vw;
        }
      }

      .add-addr {
        margin-top: 2.08vw;
        margin-bottom: 1.38vw;
        height: 2.29vw;

        @media (max-width: 425px) {
          margin-top: 7.08vw;
          margin-bottom: 1.38vw;
          height: 9vw;
        }

        @media (min-width: 500px) and(max-width:768px) {
          margin-top: 0vw;
          margin-bottom: 1.38vw;
          height: 4.5vw;
        }
      }

      .hr-line {
        height: 0.07vw;
        width: 100%;
        background-color: var(--hr-primary-color);
      }

      .field {
        padding: 2.08vw 0;

        @media (max-width: 425px) {
          padding: 7.08vw 0;
          line-height: 5vw;
        }

        @media (min-width: 500px) and(max-width:768px) {
          padding: 1.08vw 0;
          line-height: 5vw;
        }

        .field-addr {
          position: relative;
          display: flex;

          @media screen and(max-width:768px) {
            position: relative;
            display: flex;
          }

          .edit {
            position: absolute;
            right: 0;
            top: 0;
            width: 2.78vw;
            height: 2.78vw;

            @media (max-width: 425px) {
              position: absolute;
              right: 8vw;
              top: 0;
              width: 10.78vw;
              height: 10.78vw;
            }

            @media (min-width: 500px) and(max-width:768px) {
              position: absolute;
              right: 9vw;
              top: 0;
              width: 5.78vw;
              height: 5.78vw;
            }
          }

          .check {
            width: 1.25vw;
            height: 1.25vw;

            @media (max-width: 425px) {
              width: 3vw;
              height: 3vw;
            }

            @media (min-width: 500px) and(max-width:768px) {
              width: 3vw;
              height: 3vw;
            }
          }

          .check-content {
            padding-left: 0.83vw;

            @media (max-width: 425px) {
              padding-left: 5.83vw;
              line-height: 6.5vw;
            }

            @media (min-width: 500px) and(max-width:768px) {
              padding-left: 5.83vw;
              line-height: 2.5vw;
            }

            h2 {
              margin: 0;
              font-family: var(--secondary-font);
              font-size: 1.25vw;
              font-weight: 500;
              padding-bottom: 0.35vw;

              @media (max-width: 425px) {
                margin: 0;
                font-family: var(--secondary-font);
                font-size: 4.8vw;
                font-weight: 900;
                padding-bottom: 3.35vw;
              }

              @media (min-width: 500px) and(max-width:768px) {
                margin: 0;
                font-family: var(--secondary-font);
                font-size: 2.5vw;
                font-weight: 900;
                padding-bottom: 1.35vw;
                padding-top: 1vw;
              }
            }

            p {
              margin: 0;
              padding-top: 0.7vw;
              font-family: var(--secondary-font);
              font-size: var(--default-font-size);

              @media (max-width: 425px) {
                margin: 0;
                padding-top: 0.7vw;
                font-family: var(--secondary-font);
                font-size: 4.1vw;
              }

              @media (min-width: 500px) and(max-width:768px) {
                margin: 0;
                padding-top: 0.7vw;
                font-family: var(--secondary-font);
                font-size: 2vw;
              }
            }

            .sub {
              margin-top: 1vw;
              margin-bottom: 0vw;
              color: var(--quaternary-font-color);

              @media (max-width: 425px) {
                margin-top: 3vw;
                margin-bottom: 3vw;
                color: var(--quaternary-font-color);
              }

              @media (min-width: 500px) and(max-width:768px) {
                margin-top: 3vw;
                margin-bottom: 0vw;
                color: var(--quaternary-font-color);
              }
            }

            .para {
              margin: 0;
              padding-top: 1.04vw;
              font-size: 0.97vw;

              @media (max-width: 425px) {
                margin: 0;
                padding-top: 1.04vw;
                font-size: 0.97vw;
              }

              @media (min-width: 500px) and(max-width:768px) {
                margin: 0;
                padding-top: 1.04vw;
                font-size: 0.97vw;
              }
            }
          }
        }

        .buttonList {
          margin-top: 2.08vw;
          padding-left: 0.83vw;

          @media (max-width: 425px) {
            margin-top: 2.08vw;
            padding-left: 0.83vw;
          }

          @media (min-width: 500px) and(max-width:768px) {
            margin-top: 2.08vw;
            padding-left: 0.83vw;
          }

          .save {
            outline: none;
            font-size: 1.25vw;
            width: 20.56vw;
            background-color: var(--primary-background-color);
            color: var(--tertiary-font-color);
            padding: 0.83vw 1.04vw;
            border: 0.069vw solid var(--tertiary-font-color);
            border-radius: 1.46vw;

            @media (max-width: 425px) {
              outline: none;
              font-size: 4.5vw;
              width: 83vw;
              background-color: var(--primary-background-color);
              color: var(--tertiary-font-color);
              padding: 3.3vw 1.04vw;
              border: 0.069vw solid var(--tertiary-font-color);
              border-radius: 5.46vw;
              margin-top: 5vw;
              margin-left: 2vw;
            }

            @media (min-width: 500px) and(max-width:768px) {
              outline: none;
              font-size: 3vw;
              width: 83vw;
              background-color: var(--primary-background-color);
              color: var(--tertiary-font-color);
              padding: 1vw 1.04vw;
              border: 0.069vw solid var(--tertiary-font-color);
              border-radius: 5.46vw;
              margin-top: 5vw;
              margin-left: 2vw;
              margin-bottom: 2vw;
            }
          }

          .save:hover {
            font-weight: 600;

            @media screen and(max-width:768px) {
              font-weight: 600;
            }
          }
        }
      }

      .field-heading {
        margin-top: 2.78vw;
        margin-bottom: 0;
        font-family: var(--secondary-font);
        font-size: 1.25vw;

        @media (max-width: 425px) {
          margin-top: 7.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 5vw;
          font-weight: bolder;
        }

        @media (min-width: 500px) and (max-width: 768px) {
          margin-top: 2vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 3vw;
          font-weight: bolder;
        }
      }

      .double {
        width: 35vw;

        .input-double {
          width: 17.01vw !important;

          @media (max-width: 768px) {
            margin-right: 4vw;
            width: 42vw !important;
          }
        }
      }

      button {
        outline: none;
        background: none;
      }

      .flag-arrow {
        position: absolute;
        max-width: 0.69vw;
        height: auto;
        right: 2.08vw;

        @media (max-width: 425px) {
          position: absolute;
          max-width: 2.98vw;
          height: auto;
          right: 15.08vw;
        }

        @media (min-width: 500px) and (max-width: 768px) {
          position: absolute;
          max-width: 1.8vw;
          height: auto;
          right: 15.08vw;
        }
      }

      .field-value {
        margin-top: 2.08vw;
        position: relative;
        display: flex;
        justify-content: start;
        align-items: center;

        @media (max-width: 768px) {
          ::placeholder {
            color: rgba(209, 207, 207, 0.801);
          }

          margin-top: 3.25vw;
          position: relative;
          display: flex;
          justify-content: start;
          align-items: center;
        }

        input[type="text"] {
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;

          @media (max-width: 768px) {
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 3.04vw 2.11vw;
            width: 94vw;
            height: 13.47vw;
            font-size: 4vw;
          }
        }

        .input-container {
          position: relative;
          display: inline-flex;
          justify-content: start;
          align-items: center;

          @media (max-width: 768px) {
            position: relative;
            display: inline-flex;
            justify-content: start;
            align-items: center;
          }

          .flag-icon {
            position: absolute;
            left: 1.04vw;

            @media (max-width: 768px) {
              position: absolute;
              left: 1.04vw;
            }
          }

          button {
            outline: none;
            background: none;
            border: none;

            @media (max-width: 768px) {
            }
          }

          .flag-arrow {
            position: absolute;
            max-width: 1.3vw;
            height: auto;
            right: 2.08vw;

            @media (max-width: 425px) {
              position: absolute;
              max-width: 2.98vw;
              height: auto;
              right: 8.08vw;
            }

            @media (min-width: 500px) and (max-width: 768px) {
              position: absolute;
              max-width: 1.6vw;
              height: auto;
              right: 5.08vw;
            }
          }
        }

        .division {
          position: absolute;
          width: 0.069vw;
          height: 1.04vw;
          background-color: var(--timeline-color);
          left: 3.33vw;

          @media (max-width: 425px) {
            position: absolute;
            width: 0.069vw;
            height: 6.04vw;
            background-color: var(--timeline-color);
            left: 7vw;
          }

          @media (min-width: 500px) and (max-width: 768px) {
            position: absolute;
            width: 0.069vw;
            height: 3.04vw;
            background-color: var(--timeline-color);
            left: 6.33vw;
          }
        }

        input[type="text"] {
          font-family: var(--secondary-font);
          border: 0.069vw solid var(--timeline-color);
          padding: 1.04vw 1.11vw 1.04vw 4.2vw;
          border-radius: 0.14vw;
          height: 3.47vw;
          width: 35vw;
          z-index: 0;

          @media (max-width: 425px) {
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 3.04vw 2.11vw 3.04vw 7.2vw;
            border-radius: 0.14vw;
            height: 13.47vw;
            width: 88vw;
            z-index: 0;
            font-size: 4vw;
          }

          @media (min-width: 500px) and(max-width: 768px) {
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 3.04vw 2.11vw 3.04vw 7.2vw;
            border-radius: 0.14vw;
            height: 6.47vw;
            width: 88vw;
            z-index: 0;
            font-size: 2.5vw;
          }
        }

        .dropdown-visible {
          background-color: var(--primary-background-color);
          visibility: visible;
          position: absolute;
          top: 3.47vw;
          z-index: 1;
          box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);

          @media (max-width: 425px) {
            top: 12.47vw;
          }

          @media (min-width: 500px) and (max-width: 768px) {
            top: 6.47vw;
          }

          ul {
            list-style: none;
            padding: 0.69vw 0;
            max-height: 8.97vw;
            margin: 0;
            overflow: hidden;
            overflow-y: scroll;

            @media (max-width: 768px) {
              list-style: none;
              padding: 0.69vw 0;
              max-height: 27.97vw;
              margin: 0;
              overflow: hidden;
              overflow-y: scroll;
            }

            li {
              padding: 0.69vw 1.04vw;
              width: 34.9vw;
              display: flex;

              @media (max-width: 425px) {
                font-size: 4.38vw;
                width: 87.8vw;
                padding: 2vw 3.08vw;
              }

              @media (min-width: 500px) and (max-width: 768px) {
                font-size: 2.38vw;
                width: 87.8vw;
                padding: 2vw 3.08vw;
              }

              .country-name {
                margin-left: 0.69vw;

                @media (max-width: 768px) {
                  margin-left: 0.69vw;
                }
              }
            }

            li:hover {
              background-color: var(--timeline-color);

              @media (max-width: 768px) {
                background-color: var(--timeline-color);
              }
            }
          }

          ul::-webkit-scrollbar {
            display: none;

            @media (max-width: 768px) {
              display: none;
            }
          }
        }

        .dropdown-hidden {
          display: none;

          @media (max-width: 768px) {
            display: none;
          }
        }
      }

      .ph-flag {
        height: 1.25vw;
        padding-right: 0.62vw;

        // border-right: solid 0.09vw var(--quaternary-font-color);
        @media (max-width: 768px) {
          height: 2.65vw;
          font-size: 4.2vw;
          margin-top: -1.5vw;
          padding-left: 1vw;
        }
      }

      .placeholder {
        position: absolute;
        top: -0.4vw;
        left: 1.04vw;
        font-size: 0.69vw;
        color: var(--quaternary-font-color);
        padding: 0 0.3vw;
        background-color: var(--primary-background-color);

        @media (max-width: 425px) {
          position: absolute;
          top: -1vw;
          left: 1.04vw;
          font-size: 3vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
        }

        @media (min-width: 500px) and (max-width: 768px) {
          position: absolute;
          top: -1vw;
          left: 1.04vw;
          font-size: 2vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
        }
      }

      .send {
        margin-left: 2.08vw;
        color: var(--tertiary-font-color);
      }
    }

    .last-changed {
      margin-top: 1.04vw;
      font-size: 0.97vw;
      color: var(--quaternary-font-color);
    }
  }

  .buttonList {
    margin-top: 3.47vw;

    .save {
      outline: none;
      font-size: 1.25vw;
      width: 20.56vw;
      background-color: var(--primary-background-color);
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }

    .save:hover {
      font-weight: 600;
    }

    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: var(--primary-background-color);
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }

    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }

  @media (max-width: 768px) {
    .profile-field {
      width: 94vw;

      .field-contents {
        .hr-line {
          height: 0.07vw;
          width: 100%;
          background-color: var(--hr-primary-color);
        }

        // .field-heading {
        //   margin-top: 7.78vw;
        //   margin-bottom: 0;
        //   font-family: var(--secondary-font);
        //   font-size: 5vw;
        //   font-weight: bolder;
        // }

        .double {
          width: 35vw;
        }

        .last-changed {
          margin-top: 1.04vw;
          font-size: 0.97vw;
          color: var(--quaternary-font-color);
        }
      }

      .buttonList {
        margin-top: 3.47vw;

        .save {
          outline: none;
          width: 20.56vw;
          background-color: var(--primary-background-color);
          color: var(--tertiary-font-color);
          border: 0.069vw solid var(--tertiary-font-color);
          font-size: 5.25vw;
          width: 88vw;
          padding: 4.3vw 1.04vw;
          border-radius: 8.46vw;
          margin-top: 10vw;
          margin-bottom: 10vw;

          @media (max-width: 768px) {
            margin-bottom: 5vw;
          }

          @media (min-width: 500px) and (max-width: 768px) {
            outline: none;
            width: 20.56vw;
            background-color: var(--primary-background-color);
            color: var(--tertiary-font-color);
            border: 0.069vw solid var(--tertiary-font-color);
            font-size: 3.25vw;
            width: 88vw;
            padding: 2.3vw 1.04vw;
            border-radius: 8.46vw;
            margin-top: 3vw;
            margin-bottom: 10vw;
          }

          // margin-left: -2vw;
        }

        .save:hover {
          font-weight: 600;
        }

        .cancel {
          outline: none;
          font-size: 1.25vw;
          background-color: var(--primary-background-color);
          color: var(--quaternary-font-color);
          padding: 0.83vw 2.78vw;
          border: none;
        }

        .cancel:hover {
          font-weight: 600;
          // color: var(--tertiary-font-color);
        }
      }
    }
  }
}
::placeholder {
  color: rgb(185, 182, 182) !important;
}
.back-b {
  cursor: pointer;
  font-size: 1.25vw;
  padding-bottom: 0.34vw;
  margin-right: 2.77vw;
  color: var(--tertiary-font-color);
  &.active {
    border-bottom: 2px solid var(--tertiary-font-color);
    color: var(--primary-font-color);
  }
  @media (max-width: 768px) {
    font-size: 3.86vw;
    padding-bottom: 1.34vw;
    margin-right: 3.86vw;
  }
}
