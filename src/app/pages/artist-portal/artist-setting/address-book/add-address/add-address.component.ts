import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { faArrowAltCircleLeft } from '@fortawesome/free-solid-svg-icons';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { AuthService } from 'src/app/services/auth.service';
import { CollectorService } from 'src/app/services/collector.service';

@Component({
  selector: 'app-add-address',
  templateUrl: './add-address.component.html',
  styleUrls: ['./add-address.component.scss'],
})
export class AddAddressComponent implements OnInit {
  isCancel = false;
  isDropDown = false;
  isDropDown1 = false;
  country_selected = false;
  form;
  flagData = flagData;
  faArrowAltCircleLeft = faArrowAltCircleLeft;
  selectCountry() {
    this.country_selected = true;
  }
  constructor(private authService: AuthService, private route: Router, private server: CollectorService,) { }

  ngOnInit(): void {
    this.form = new FormGroup({
      name: new FormControl(''),
      phone: new FormControl(''),
      address_line_1: new FormControl(''),
      address_line_2: new FormControl(''),
      city: new FormControl(''),
      state: new FormControl(''),
      country: new FormControl('India'),
      post_code: new FormControl(''),
      title: new FormControl(''),
      defaultShipping: new FormControl(''),
      defaultBilling: new FormControl(''),
    });
    if (localStorage.getItem('addressID')) {
      this.getExhibitionById();
    }
  }

  onFlagInputChange() {
    this.flagData = flagData.filter((a) => { return a.name.toLowerCase()?.includes(this.form.getRawValue()?.country.toLowerCase()) })
  }
  getExhibitionById() {
    let data = JSON.parse(localStorage.getItem('addressIDData'));

    if (data) {
      this.form.patchValue({
        name: data?.name,
        phone: data?.phone,
        address_line_1: data?.address_line_1,
        address_line_2: data?.address_line_2,
        city: data?.city,
        state: data?.state,
        country: data?.country,
        post_code: data?.post_code,
        title: data?.title,
      });
      if (data?.type == "billing") {
        this.form.patchValue({
          defaultShipping: false,
          defaultBilling: true,
        })
      } else if (data?.type == "shipping") {
        this.form.patchValue({
          defaultShipping: true,
          defaultBilling: false,
        })
      } else if (data?.type == "both") {
        this.form.patchValue({
          defaultShipping: true,
          defaultBilling: true,
        })
      } else {
        this.form.patchValue({
          defaultShipping: false,
          defaultBilling: false,
        })
      }
    }


  }
  getCountryFlagFromName() {
    let b = this.flagData.find((a) => {
      return a.name === this.form.getRawValue().country
    });
    return b.flag

  }
  saveAddress() {
    const body: any = {
      name: this.form.value.name,
      phone: this.form.value.phone,
      address_line_1: this.form.value.address_line_1,
      address_line_2: this.form.value.address_line_2,
      city: this.form.value.city,
      state: this.form.value.state,
      country: this.form.value.country,
      post_code: this.form.value.post_code,
      title: this.form.value.title,
    };
    if (this.form.value.defaultShipping && this.form.value.defaultBilling) {
      body['type'] = 'both';
    } else if (this.form.value.defaultShipping && !this.form.value.defaultBilling) {
      body['type'] = 'shipping';
    } else if (!this.form.value.defaultShipping && this.form.value.defaultBilling) {
      body['type'] = 'billing';
    } else {
      body['type'] = 'none';
    }
    if (localStorage.getItem('addressID')) {
      let url = `api/addresses/${localStorage.getItem('addressID')}`;
      this.server.patchApi(url, body).subscribe((res) => {
        if (res.statusCode == 200) {
          alert('success')
        }
      })
    } else {
      this.authService.addAddress({ ...body, type: 'none' }).subscribe(
        (data) => {
          this.route.navigate(['/artist-portal/settings/address-book']);
        },
        (err) => { }
      );
    }

  }
}
