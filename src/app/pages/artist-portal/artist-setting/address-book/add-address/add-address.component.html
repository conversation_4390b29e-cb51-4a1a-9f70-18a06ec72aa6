<div class="container__main">
  <div class="section left-container" style="width: 100%">
    <div class="section-inner">
      <div class="d-flex justify-content-start slider-header">
        <a routerLink="/artist-portal/settings/address-book">
          <h3 class="back-b">
            <fa-icon [icon]="faArrowAltCircleLeft"></fa-icon>
          </h3>
        </a>
        <h3>
          <p class="main__heading">Add address</p>
        </h3>
      </div>
    </div>
  </div>
  <div class="profile-field">
    <div class="w-100 field-contents">
      <form [formGroup]="form" class="shipping-form">
        <div class="field-head"></div>
        <div class="field-value">
          <input type="text" formControlName="name" placeholder="Full Name" />
          <div class="placeholder">Full Name</div>
        </div>
        <div class="field-value">
          <input
            type="text"
            formControlName="phone"
            placeholder="Phone Number"
          />
          <div class="placeholder">Phone Number</div>
        </div>
        <div class="field-value">
          <div class="input-container">
            <input
              type="text"
              formControlName="country"
              placeholder="Country"
              (focus)="isDropDown = true"
              (input)="onFlagInputChange()"
            />
            <div class="placeholder-institution">
              <div
                style="
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  width: 100%;
                  height: 100%;
                "
              >
                {{ getCountryFlagFromName() }}
              </div>
            </div>

            <div class="division"></div>
            <button (click)="isDropDown = !isDropDown">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>
            <div class="division"></div>
            <div
              #dropDown
              [ngClass]="{
                'dropdown-hidden': !isDropDown,
                'dropdown-visible': isDropDown
              }"
            >
              <ul>
                <li
                  *ngFor="let item of flagData"
                  (click)="
                    form.get('country').setValue(item.name); isDropDown = false
                  "
                >
                  {{ item.flag }}
                  <div class="country-name">{{ item.name }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="placeholder">Country</div>
        </div>
        <div class="field-heading">Address</div>
        <div class="field-value">
          <input
            type="text"
            formControlName="address_line_1"
            placeholder="Address Line 1"
          />
          <div class="placeholder">Address Line 1</div>
        </div>
        <div class="field-value">
          <input
            type="text"
            formControlName="address_line_2"
            placeholder="Address Line 2"
          />
          <div class="placeholder">Address Line 2</div>
        </div>
        <div class="d-flex justify-content-between double">
          <div class="field-value">
            <input
              class="input-double"
              type="text"
              formControlName="city"
              placeholder="City"
            />
            <div class="placeholder">City</div>
          </div>
          <div class="field-value">
            <input
              class="input-double"
              type="text"
              formControlName="post_code"
              placeholder="Postal Code"
            />
            <div class="placeholder">Postal Code</div>
          </div>
        </div>
        <div class="field-value">
          <input
            type="text"
            formControlName="state"
            placeholder="State/province"
          />
          <div class="placeholder">State</div>
        </div>

        <div class="field-value">
          <input
            type="text"
            formControlName="title"
            placeholder="Eg: Artist Studio, New Delhi"
          />
          <div class="placeholder">Address Label</div>
        </div>
        <div style="margin-top: 0.5vw">
          <input formControlName="defaultShipping" type="checkbox" />
          <label class="form-control-label"> Default Shipping</label>
        </div>
        <div>
          <input formControlName="defaultBilling" type="checkbox" />
          <label class="form-control-label"> Default Billing</label>
        </div>
        <!-- <div class="field-value">
        <input type="text" formControlName="state" placeholder=" State/province " />
        <div class="placeholder">State</div>
        <button>
          <img src="assets/icons/arrow-down.png" class="flag-arrow" />
        </button>
      </div> -->
        <div class="flex-block buttonList">
          <button (click)="saveAddress()" class="save">Save</button>
        </div>
      </form>
    </div>
  </div>
</div>
