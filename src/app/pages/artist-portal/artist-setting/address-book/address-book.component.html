<div id="mainContent">
  <div class="heading">
    <div class="head">
      <div>
        Address
        <a (click)="navigateTo()"><button class="saveBtn">Add +</button></a>
      </div>
      <div class="filter"></div>
    </div>
  </div>

  <div class="collector-form">
    <div class="interests">
      <div class="contents">
        <div
          *ngFor="let art of addressData; let i = index"
          class="artist"
          style="position: relative"
        >
          <div
            class="artworkImggg"
            style="
              display: flex;
              justify-content: center;
              align-items: center;
              flex-direction: column;
            "
          >
            <div>{{ art?.name }}</div>
            <div>{{ art?.address_line_1 }}</div>
            <div>{{ art?.address_line_2 }}</div>
            <div>{{ art?.city }} - {{ art?.post_code }}</div>
            <div>Phone: {{ art?.phone }}</div>
          </div>
          <a (click)="showMenu[i] = !showMenu[i]" (focusout)="changeFocus(0)">
            <div class="edit_grid"></div>
            <div *ngIf="showMenu[i]" class="dropdown-container">
              <div class="dropdown-content">
                <a (click)="editArtWork(art)">
                  <div style="padding: 0.6vw; cursor: pointer; color: black">
                    Edit
                  </div>
                </a>
              </div>
            </div>
          </a>

          <div
            class="wrappp"
            style="
              display: flex;
              justify-content: space-between;
              flex-direction: row;
              align-items: center;
            "
          >
            <div class="name" style="width: 90%">
              <i>{{ art?.title }}</i>
            </div>
          </div>
        </div>
        <ng-container *ngIf="isLoading">
          <div *ngFor="let item of temporary2" class="artist">
            <app-placeholder height="10.69vw" width="14.93vw">
            </app-placeholder>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>
