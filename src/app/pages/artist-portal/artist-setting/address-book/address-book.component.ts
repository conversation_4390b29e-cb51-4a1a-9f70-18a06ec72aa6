import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-address-book',
  templateUrl: './address-book.component.html',
  styleUrls: ['./address-book.component.scss'],
})
export class AddressBookComponent implements OnInit {
  addressData: any[] = [];
  isLoading = true;
  showMenu = Array(1000).fill(false);
  constructor(private authService: AuthService, private router: Router,) { }

  ngOnInit(): void {
    this.authService.getAddress().subscribe((data) => {
      this.isLoading = false;
      this.addressData = data.data[0].addresses;
    });
  }
  navigateTo() {
    localStorage.removeItem('addressID');
    this.router.navigate(['artist-portal/settings/add-address']);
  }
  editArtWork(item) {
    localStorage.setItem('addressID', item['_id']);
    localStorage.setItem('addressIDData', JSON.stringify(item));
    this.router.navigate(['artist-portal/settings/add-address']);
  }
}
