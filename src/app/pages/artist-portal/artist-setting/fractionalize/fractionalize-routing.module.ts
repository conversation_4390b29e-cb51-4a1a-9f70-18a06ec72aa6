import { StepsComponent } from './steps/steps.component';
import { MethodComponent } from './method/method.component';
import { CreateVaultComponent } from './create-vault/create-vault.component';
import { MainComponent } from './main/main.component';

import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

const routes: Routes = [{
  path: '',
  component: MainComponent,
  data: {
    hidebread: true,
  }
},
{
  path: 'method',
  component: MethodComponent,
  data: {
    hidebread: true,
  }
},
{
  path: 'create-vault',
  component: CreateVaultComponent,
  data: {
    hidebread: true,
  }
},
{
  path: 'steps',
  component: StepsComponent,
  data: {
    hidebread: true,
  }
}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FractionalizeRoutingModule { }
