import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
	list_selection: number;
	searchValue = '';
	noOfArtPerPage = 12;
	index = 0;
	lastIndex = 0;
	currentWorks: any[] = [];
	current: any[] = [];
	isSidemenuOpen = false;
	openStyle = {
		'width': "250px"
	}
	form: FormGroup;
	artworks;
	emptyDiv = [];

	searchResult(): void {
		console.log(this.searchValue + " here")
		this.current = null;
		this.currentWorks = [];
		this.currentWorks = this.artworks;
		if (this.searchValue !== '') {
			console.log(this.searchValue)
			const newArray = this.currentWorks.filter(
				(art) =>
				(art?.title
					? art.title.toLowerCase().includes(this.searchValue.toLowerCase())
					: false)
			);
			this.currentWorks = newArray;
			console.log(newArray)
		}
		this.updateCurrent();
	}

	updateCurrent(): void {
		this.current = [];

		for (
			let i = this.index * this.noOfArtPerPage;
			i < (this.index + 1) * this.noOfArtPerPage;
			i++
		) {
			const element = this.currentWorks[i];
			if (element === undefined) {
				break;
			}
			this.current.push(element);
		}



	}



	constructor(
		public formBuilder: FormBuilder) { }
	ngOnInit(): void {
		this.form = this.formBuilder.group({
			showListView: new FormControl(false),
		})
	}
}
