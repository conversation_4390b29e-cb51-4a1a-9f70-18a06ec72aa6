<!-- /artist-portal/settings/partner-profile/add -->

<div id="mainContent" [formGroup]="form">
	<div class="heading">
	  <div class="head">
		<div>
			Fractionalize
		  <a routerLink="/artist-portal/settings/fractionalize/method"
			><button class="saveBtn">Create Vault +</button></a
		  >
		</div>
		<div class="filter">
		  <input
			type="text"
			placeholder="Search by NFT "
			name="search"
			[(ngModel)]="searchValue"
			[ngModelOptions]="{ standalone: true }"
			(keyup)="searchResult()"
		  />
		  <div
			class="input-container"
			style="display: flex; float: right; margin-top: 1.5vw"
		  >
			<div
			  class="text-before"
			  [innerHtml]="
				form.get('showListView').value ? 'List view' : 'Grid view'
			  "
			></div>
			<label class="switch">
			  <input type="checkbox" formControlName="showListView" />
			  <span class="slider round"></span>
			</label>
			<!-- <div class="text-before">&nbsp; &nbsp;List view</div> -->
		  </div>
		</div>
	  </div>
	</div>

	<div class="collector-form">
	  <div class="interests">
		<div class="contents" *ngIf="!form.get('showListView').value">
		  <div class="artist" style="position: relative">
			<img
			  src="https://www.terrain.art/cdn-cgi/image/width=500,quality=52/https://images.unsplash.com/photo-1592347093417-0e95eb5851aa?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2072&q=80"
			/>

			<div class="wrappp">
			  <div class="name">
				<i>Title</i>
				<!-- <i>{{ art?.title }}</i
				  >, {{ art?.year }} -->
			  </div>
			  <!-- <div class="name">{{ temporary?.createdBy }}</div>
				<div class="name">₹{{ art?.originalPrice }}</div>
				<div class="name">NFT id : {{ temporary?.NFT_id }}</div> -->
			</div>
		  </div>
		  <div *ngFor="let item of emptyDiv" class="artist"></div>
		</div>
		<div class="contentList" *ngIf="form.get('showListView').value">
		  <div class="list_info" style="font-size: 0.833vw; margin-bottom: 0.5vw">
			FOUND 0 RECORDS
		  </div>
		  <div class="Table">
			<div
			  class="Table-row"
			  [ngStyle]="
				list_selection == i
				  ? { 'background-color': '#b0c7dda3', color: '#fff' }
				  : {}
			  "
			>
			  <div class="Table-row-item img-item" data-header="Header1">
				<img
				  class="img-artwork-thumbnail"
				  src="https://www.terrain.art/cdn-cgi/image/width=500,quality=52/https://images.unsplash.com/photo-1592347093417-0e95eb5851aa?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2072&q=80"
				  alt="thumbnail"
				/>
			  </div>
			  <div
				class="Table-row-item title-art"
				[ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
				data-header="Header3"
			  >
				<i>Title</i>
			  </div>
			  <div
				class="Table-row-item"
				[ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
				data-header="Header4"
			  >
				Details
			  </div>
			  <div
				class="Table-row-item"
				*ngIf="!isSidemenuOpen"
				data-header="Header5"
			  >
				Details
			  </div>
			  <div
				class="Table-row-item u-Flex-grow2"
				*ngIf="!isSidemenuOpen"
				data-header="Header6"
			  >
				Details
			  </div>
			  <div class="Table-row-item">
				<div class="aserd"></div>
			  </div>
			</div>
		  </div>
		</div>
	  </div>
	</div>
  </div>
  <!-- </div> -->
