import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FractionalizeRoutingModule } from './fractionalize-routing.module';
import { MainComponent } from './main/main.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CreateVaultComponent } from './create-vault/create-vault.component';
import { MethodComponent } from './method/method.component';
import { StepsComponent } from './steps/steps.component';


@NgModule({
  declarations: [MainComponent, CreateVaultComponent, MethodComponent, StepsComponent],
  imports: [
    FormsModule,
		ReactiveFormsModule,
    CommonModule,
    FractionalizeRoutingModule
  ]
})
export class FractionalizeModule { }
