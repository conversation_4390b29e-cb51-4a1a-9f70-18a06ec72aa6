.card-wrapper {
  width: 100%;
  // height: 60vw;
  display: flex;
  justify-content: center;
  align-items: center;
  .card-pack {
    // margin: auto;
    margin: 5vw 0;
    // margin-top: 5vw;
    width: 40vw;
    padding:2vw 1.5vw;
    // height: 40vw;
    box-shadow: 2px 2px 8px #00000096;
    border-radius: 1vw;
    .head-pack {
      display: flex;
      justify-content: space-between;
      .for_text {
        font-size: 1.666vw;
        font-weight: 600;
      }
      .for_icon {
        height: 2vw;
        width: 2vw;
      }
    }
    .content-pack {
      display: flex;
      width: 100%;
      margin-top: 1vw;
      .icon-section {
        // margin: 1vw 0;
        width: 30%;
        display: flex;
        //   justify-content: center;
        // text-align: center;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        gap: .5vw;
        flex-direction: column;
        img {
          width: 4vw;
          height: 4vw;
          // margin: auto;
        }
        .line {
          // margin: .5vw auto;
          // height: 4.5vw;
          height: 3.7vw;
          background-color: rgb(151, 151, 151);
          width: 0.13vw;
        }
      }
      .text-section {
        width: 70%;
        .section-pack {
            padding-top: .5vw;
            // height: 10vw;
            height: 8vw;
            padding-right: 1.5vw;
          span {
            font-weight: 600;
          }
          p {

            margin-top: .3vw;
            color: var(--quaternary-font-color);
            // font-size: 0.6944vw;
            margin-bottom: .5vw;
          }
          .active {
              width: 100%;
              padding: .5vw 0;
              border-radius: 2vw;
            //   font-weight: 600;
            color: white;
              border-color: transparent;
              // background-color:rgb(179, 255, 27);
              background-color:var(--tertiary-font-color) ;
          }
          .disabled{
              background-color:rgb(149, 150, 147);
          }
        }
      }
    }
  }
}
