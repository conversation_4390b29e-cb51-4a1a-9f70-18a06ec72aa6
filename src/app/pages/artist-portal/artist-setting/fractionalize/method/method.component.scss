.wrapper {
  width: 100%;
  //   background-color: rgba(155, 143, 143, 0.103);
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  // height: 45vw;
  // height: 100%;
  .popup-card {
    box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
    background-color: white;
    // width: 25vw;
    width: 44vw;
    // height: 40vw;
    height: auto;
    padding: 2.5vw 2vw;
    border-radius: 2vw;
    overflow: hidden;
    .popup-card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2vw;
      .for_text {
        font-size: 1.666vw;
        font-weight: 600;
      }
      .for_icon {
        height: 2vw;
        width: 2vw;
      }
    }
    .popup-card-sub-header {
      display: flex;
      flex-wrap: wrap;
      font-size: 1.111vw;
      margin-bottom: 1vw;
    }
    .popup-card-content {
      display: flex;
      flex-direction: column;

      .popup-card-content-option {
        display: flex;
        margin-bottom: 1vw;
        input[type="radio"] {
          // border: 0px;
          // width: 100%;
          // height: 2vw;
        }
        .For-des {
          display: flex;
          flex-direction: column;

          .method-name {
            font-weight: 600;
            margin-bottom: 0.25vw;
          }
          .method-description {
            font-size: 0.9944vw;
            display: flex;
            flex-wrap: wrap;
            color: var(--quaternary-font-color);
            margin-bottom: unset;
          }
        }
      }
    }
    .button-section {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      a {
        width: 100%;
        .continue {
          border:  0.13888vw solid var(--tertiary-font-color);
          //border:  0.13888vw solid rgb(186, 252, 56);
          //  border: 0;
          padding: 1vw 2vw;
          // background-color: white;
          color: white;
          width: 100%;
          border-radius: 4vw;
          margin-bottom: 0.5vw;
          font-weight: 600;
          // background-color: rgb(186, 252, 56);
          background-color: var(--tertiary-font-color) ;

          border-color: transparent;
        }
        .continue:hover,
        .cancel:hover {
        }
        .cancel {
          padding: 1vw 2vw;
          background-color: white;
          width: 100%;
          // border: 0.13888vw solid rgb(186, 252, 56);
          border: 0.13888vw solid var(--tertiary-font-color) ;
          border-radius: 4vw;
          font-weight: 600;
        }
      }
    }
  }
}
