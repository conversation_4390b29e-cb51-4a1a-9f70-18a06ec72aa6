<div class="wrapper">
  <div class="popup-card">
    <div class="popup-card-header">
      <span class="for_text">
        Fractionalization <br />
        method</span
      >
      <img
        src="../../../../../../assets/icons/<EMAIL>"
        alt="Close"
        hidden
        class="for_icon"
      />
    </div>
    <span class="popup-card-sub-header">
      Select a tokenization standard for the vault to determine how owners will
      be able to view and handle their fractions. Don't worry, you'll still be
      able to change your mind on the next page.
    </span>
    <div class="popup-card-content">
      <div class="popup-card-content-option">
        <input
          type="radio"
          name="ERC"
          id="ERC-20"
          (change)="onItemChange($event.target.value)"
          value="ERC20"
          style="height: 3vw; width: 8vw; margin-right: 1vw"
        />

        <div class="For-des">
          <span class="method-name">Tokenize the vault (ERC-20)</span>
          <p class="method-description">
            Create fungible, divisible fractions. In wallets, each fraction will
            display as a token symbol and its numeric amount, alongside any
            other ERC-20 tokens
          </p>
        </div>
      </div>
      <div class="popup-card-content-option">
        <input
          type="radio"
          (change)="onItemChange($event.target.value)"
          name="ERC"
          value="ERC1155"
          id="ERC-1155"
          disabled
          title="Launching soon"
          style="height: 3vw; width: 10vw; margin-right: 1vw"
        />
        <div class="For-des">
          <span class="method-name">Mint new NFTs (ERC-1155)</span>
          <p class="method-description">
            Create indivisible, non-fungible fractions. In wallets each fraction
            will display as an image of the vaults contents, plus a standard
            "Fractional border". alongside any other ERC-721 and ERC-1155 NFTs.
          </p>
        </div>
      </div>
    </div>
    <div class="button-section">
      <a routerLink="/artist-portal/settings/fractionalize/create-vault">
        <button [disabled]="!selected" class="continue" type="button">
          Continue
        </button></a
      >
      <a href="#">
        <button class="cancel" type="button">Cancel</button>
      </a>
    </div>
  </div>
</div>
