.filter {
  color: var(--quaternary-font-color);
  font-size: var(--default-font-size);
  margin-bottom: 2.222vw;
  max-width: 24.444vw;
}
.container-cover {
  display: flex;
  padding-bottom: 4.166vw;
  // padding-top: 3.888vw;
  form {
    display: flex;
  }
  .left {
    width: 60%;
    h2 {
      font-weight: 600;
      margin-bottom: 1.666vw;
    }
    .desp {
      font-size: 1.042vw;
      margin-bottom: 1.666vw;
      // padding-right: 4vw;
      padding-right: 6vw;
    }
  }
  .right {
    width: 40%;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    // align-items: flex-start;

    .card_cover {
      margin-top: 2vw;
      //  box-shadow: 2px 2px 8px black;
      box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
      background-color: rgba(244, 245, 246, 0.6);
      // width: 24.722vw;
      width: 100%;
      // height: 34.722vw;
      margin: unset auto;
      border-radius: 2vw;
      padding: 1.666vw;
      h4 {
        font-weight: 700;
        font-size: 0.916vw;
        line-height: 1.388vw;
        margin-bottom: 1.388vw;
      }
      .card-section {
        margin-bottom: 1.666vw;
        color: rgba(143, 151, 163, 1);
        p {
          font-size: 0.805vw;
          font-weight: 700;
          margin-bottom: 0.5vw;
        }
        span {
          display: flex;
          justify-content: space-between;
        }
      }
      .split {
        display: flex;
        gap: 0.5vw;
      }
      .selected-nft {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.388vw;
        .head {
          font-weight: 700;
          font-size: 1.111vw;
          line-height: 1.388vw;
          .countBadge {
            margin: -0.5vw 0 0 0.2vw;
            padding: 0.2vw 0.5vw;
            border-color: transparent;
            border-radius: 50%;
            background-color: rgb(178, 248, 37);
            color: white;
            font-size: 00.69444vw;
          }
        }
        .unselect {
          color: var(--tertiary-font-color);
          font-size: 1.111vw;
          cursor: pointer;
        }
      }
      .thumbnail-NFT {
        display: flex;
        flex-wrap: wrap;
        img {
          margin: 0.654vw;
          height: 4vw;
          width: 4vw;
          object-fit: cover;
          border-radius: 0.5vw;
        }
      }
      .content-desciption-erc1155 {
        display: flex;
        flex-direction: column;
        .headerz {
          font-weight: 600;
          margin: 1.5vw 0;
        }
        .desc-text {
          color: var(--quaternary-font-color);
        }
      }
    }
  }
}

input[type="text"] {
  padding: 0.7vw 1.32vw 0.7vw 1vw;
  -o-object-fit: contain;
  object-fit: contain;
  outline: none;
  width: 100%;
  font-family: Arial, FontAwesome;
  border-radius: 0.5vw;
  font-weight: 500;
  // margin-bottom: 2.08vw;
  color: var(--quaternary-font-color);
  height: 2.8vw;
  border: solid 0.07vw var(--secondary-font-color);
  text-indent: 1.73vw;
  background-position: 0 50%;
  background-repeat: no-repeat;
  background-position-x: 1.04vw;
  background-size: 1.04vw;
  background-image: url("../../../../../../assets/icons/search/<EMAIL>");
}
input[type="text"]:focus {
  background-image: none;
  text-indent: 0px;
}
input[type="range"] {
  padding: 0.7vw 1.32vw 0.7vw 1vw;
  -o-object-fit: contain;
  object-fit: contain;
  outline: none;
  width: 100%;
  font-family: Arial, FontAwesome;
  border-radius: 0.5vw;
  font-weight: 500;
  // margin-bottom: 2.08vw;
  color: var(--quaternary-font-color);
  height: 2.8vw;
  border: solid 0.07vw var(--secondary-font-color);
  text-indent: 1.73vw;
  background-position: 0 50%;
  background-repeat: no-repeat;
  background-position-x: 1.04vw;
  background-size: 1.04vw;
  background-image: url("../../../../../../assets/icons/search/<EMAIL>");
}
.continue {
  width: 100%;
  cursor: not-allowed;
  padding: 1rem 2.5rem;
  background-color: white;
  color: var(--tertiary-font-color);
  //  padding: 0.533333vw 0.89vw;
  border: 0.069vw solid var(--tertiary-font-color);
  border-radius: 1.46vw;
}
.continue:hover {
  background-color: var(--tertiary-font-color);
  color: white;
  border: 0.069vw solid black;
}
.nft-section {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  gap: 2vw;
  padding-right: 7vw;
  padding-left: 0.5vw;
  .card-wrapper {
    display: flex;
    flex-direction: column;
    .nft-card {
      // border: 0.0694vw solid var(--primary-font-color);
      border-radius: .6vw;
      // box-shadow: rgb(6 24 44 / 40%) 0px 0px 0px 0.1px,
      //   rgb(6 24 44 / 65%) 0px 4px 6px -1px,
      //   rgb(255 255 255 / 8%) 0px 1px 0px inset;
      width: 15vw;
       box-shadow:0px 0px 0px 1px rgba(0,0,0,0.20)  ;
      // padding: 1.5vw 1vw;
      height: 22vw;
      // margin-bottom: 2vw;
      overflow: hidden;
      // overflow: hidden;
      .nft-img {
        position: relative;
        display: flex;
        justify-content: center;
        width: 100%;
        // margin: auto;
        // overflow: hidden;
        margin-bottom: 1vw;
        span {
          position: absolute;
          padding: 0.3vw;
          z-index: 1;
          background-color: rgba(194, 185, 185, 0.404);
          border-radius: 0.2vw;
          top: 1.3vw;
          left: 1vw;
          color: white;
          font-size: 0.66vw;
        }
        img {
          width: 100%;
          // overflow: hidden;
          object-fit: cover;
          height: 14.5vw;
          // border-top-right-radius: 1.5vw;
          // border-top-left-radius: 1.5vw;
        }
      }
      .nft-title {
        font-weight: 600;
        padding: 0.5vw 1vw;
        float: left;
      }
      text-align: center;
      .selection {
        width: 90%;

        background-color: white;
        border: 0.0694vw solid var(--quaternary-font-color);
        border-radius: 1vw;
        padding: 0.5vw 0;
        font-size: 0.69444vw;
        // font-weight: 600;
        margin: auto auto;
      }
      .active-selected {
        color: white;
        background-color: rgba(0, 0, 0, 0.596);
        border: 0.0694vw solid rgba(0, 0, 0, 0.596);
        font-size: 0.696vw;
        font-weight: 600;
      }
      .selection:hover {
        // width: 100%;
        color: white;
        background-color: black;
        border: 0.0694vw solid white;
        // border-radius: 2vw;
        // padding: .5vw 2vw;
        font-size: 0.696vw;
        //
      }
    }
    .copies_section {
      border-radius: 1vw;
      width: 15vw;
      height: 5.5vw;
      box-shadow: rgb(6 24 44 / 40%) 0px 0px 0px 0.1px,
        rgb(6 24 44 / 65%) 0px 4px 6px -1px,
        rgb(255 255 255 / 8%) 0px 1px 0px inset;
      margin-bottom: 1vw;
      padding: 1vw;
      position: relative;
      .top-section {
        font-size: 0.6944vw;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        .gray {
          color: var(--quaternary-font-color);
        }
      }
      .input-section {
        input[type="number"] {
          height: 2.5vw;
          width: 100%;
          padding-right: 3vw;
          border: 1px solid var(--quaternary-font-color);
          border-radius: 0.2vw;
          padding-left: 1vw;
        }
        .applyChange {
          position: absolute;
          top: 2.22vw;
          right: 1.35vw;
          background-color: white;
          border: 1px solid rgb(183, 255, 39);
          // border-radius: 0.5vw;
          font-size: 0.555vw;
        }
      }
    }
  }
}

.div-for-switch {
}

.--switch {
  position: relative;
  display: inline-block;
  // width: 95%;
  width: 100%;
  height: 2.43vw;
}

.--switch input {
  display: none;
}

.--slider .fa-check {
  z-index: 2;
  color: black;
  position: absolute;
  left: 4vw;
  font-size: 1.25vw;
  font-weight: 600;
  /*   display:none; */
}
.--slider .fa-times {
  color: black;
  position: absolute;
  z-index: 2;
  font-weight: 600;

  font-size: 1.25vw;
  right: 4vw;
}

.--slider {
  position: absolute;
  cursor: pointer;
  display: flex;
  align-items: center;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: lightgray;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  // border-radius:50px;
  z-index: 0;
}

.--slider:before {
  position: absolute;
  content: "";
  height: 1.736vw;
  width: 48%;
  left: 0.555vw;
  // left: 3.7vw;
  bottom: 0.347vw;
  // border-radius:40%;
  z-index: 1;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.--switch input:checked + .--slider .fa-check {
  display: block;
}

.--switch input:checked + .--slider .fa-times {
  /*   display:none; */
}

.--switch input:checked + .--slider {
  /*   background-color: lightblue; */
}

.--switch input:focus + .--slider {
  box-shadow: 0 0 1px #52b69a;
}

.--switch input:checked + .--slider:before {
  -webkit-transform: translateX(15vw);
  -ms-transform: translateX(15vw);
  transform: translateX(15vw);
}
