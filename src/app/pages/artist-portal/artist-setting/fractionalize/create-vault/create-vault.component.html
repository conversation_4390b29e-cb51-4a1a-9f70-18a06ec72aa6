<div class="container-cover">
  <form [formGroup]="form">
    <div class="left">
      <h2>Select NFTs to Fractionalize</h2>
      <p class="desp">
        Choose the NFT(s) to send to a new vault, select your desired fraction
        type, set your vault’s details, then continue to fractionalize. Once
        complete, all fractions will appear in your wallet. Be aware, you cannot
        add to the NFTs in a vault once created.
        <a href="#">Read our guides</a> for more information.
      </p>
      <div class="filter">
        <input
          type="text"
          placeholder="Search your NFTs "
          name="search"
          [(ngModel)]="searchValue"
          [ngModelOptions]="{ standalone: true }"
          (keyup)="searchResult()"
        />
      </div>
      <!-- <p>Please click <a href="#"> here</a> and connect your wallet.</p> -->

      <div class="nft-section">
        <div class="card-wrapper" *ngFor="let item of items">
          <div class="nft-card">
            <div class="nft-img">
              <span>14 COPIES</span>
              <img [src]="item.download_url" alt="BAYC" />
            </div>
            <p class="nft-title">{{ item.author }}</p>
            <button
              class="selection"
              (click)="selectNFT(item)"
              [ngClass]="{ 'active-selected': isNFTSelected(item) }"
              [innerHTML]="isNFTSelected(item) ? 'Selected' : 'Select'"
            ></button>
          </div>
          <div class="copies_section" [hidden]="!selectedNFTs.includes(item)" >
           <div class="top-section">
            <span class="gray"># OF COPIES TO VAULT</span> <span>AVAILABLE : 36</span>
           </div>
           <div class="input-section">
             <input type="number" name="copies" id="copies">
             <button class="applyChange">APPLY<br>CHANGES</button>
           </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="div-for-switch">
        <label class="--switch">
          <input formControlName="ERC" type="checkbox" />
          <span class="--slider">
            <span class="fa-check">ERC-20</span>
            <span class="fa-times">ERC-1155</span>
          </span>
        </label>
      </div>

      <div class="card_cover" *ngIf="!form.get('ERC').value">
        <h4>Vault Details</h4>
        <div class="card-section">
          <p>NAME</p>
          <input
            type="text"
            placeholder="CryptoPunk Frenzie"
            name="search"
            [(ngModel)]="searchValue"
            [ngModelOptions]="{ standalone: true }"
            (keyup)="searchResult()"
          />
        </div>
        <div class="split">
          <div class="card-section">
            <p>SUPPLY</p>
            <input
              type="text"
              placeholder="10000"
              name="search"
              [(ngModel)]="searchValue"
              [ngModelOptions]="{ standalone: true }"
              (keyup)="searchResult()"
            />
          </div>
          <div class="card-section">
            <p>SYMBOL</p>
            <input
              type="text"
              placeholder="CPF"
              name="search"
              [(ngModel)]="searchValue"
              [ngModelOptions]="{ standalone: true }"
              (keyup)="searchResult()"
            />
          </div>
        </div>
        <div class="card-section">
          <p>RESERVE PRICE</p>
          <input
            type="text"
            placeholder="0.0"
            name="search"
            [(ngModel)]="searchValue"
            [ngModelOptions]="{ standalone: true }"
            (keyup)="searchResult()"
          />
        </div>
        <div class="card-section" hidden>
          <p>ANNUAL MANAGEMENT FEE</p>
          <input
            type="range"
            name="ageInputName"
            id="ageInputId"
            value="24"
            min="1"
            max="10"
            oninput="ageOutputId.value = ageInputId.value"
          />
          <span>
            <p>0%</p>
            <p><output name="ageOutputName" id="ageOutputId"></output>%</p>

            <p>10%</p>
          </span>
        </div>
        <button class="continue">Continue</button>
      </div>
      <div class="card_cover" *ngIf="form.get('ERC').value">
        <div class="div" style="width: 100%;height: 24.0277vw;display: flex;justify-content: center;align-items: center;">
        Launching Soon
        </div>
        <!-- <div class="selected-nft">
          <span class="head"
            >Selected NFTs
            <span class="countBadge" [innerHTML]="selectedNFTid.length"></span>
          </span>
          <span class="unselect" (click)="unselectall()">unselect all</span>
        </div>
        <div class="thumbnail-NFT">
          <img
            *ngFor="let item of selectedNFTs"
            [src]="item?.download_url"
            alt="NFT Thumbnail"
          />
        </div>
        <div class="content-desciption-erc1155">
          <span class="headerz">Vault details</span>
          <p class="desc-text">
            The ERC-1155 will have a fixed supply of 10,000 fractions.The
            ERC-1155 will have a fixed supply of 10,000 fractions.The ERC-1155
            will have a fixed supply of 10,000 fractions.<a href="#"
              >Read more</a
            >
          </p>
        </div>
        <a routerLink="/artist-portal/settings/fractionalize/steps"><button class="continue">Continue</button></a>  -->
      </div>
    </div>
  </form>
</div>
