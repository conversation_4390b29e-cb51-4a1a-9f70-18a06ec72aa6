<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <!--SEO section-->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            *ngIf="showPage != 'List'"
          >
            <button class="btn-edit" (click)="onBack()">Back</button>
          </div>
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            *ngIf="showPage == 'List'"
          >
            <button class="btn-edit" (click)="onAddingUser()">
              Invite User
            </button>
            <!-- <button class="mySlider shiftRight">
              <label class="switch">
                <input id="status" type="checkbox">
              <span class="slider round"></span>
            </label>
          </button> -->
            <span class="side-btn">ACTIVE</span>
            <label class="switch">
              <input type="checkbox" checked (change)="onToggle()" />
              <span class="slider round"></span>
            </label>
            <span class="side-btn">INACTIVE</span>
          </div>
          <div class="field-value doted">
            <div class="splitter" *ngIf="showPage != 'List'">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="first_name"
                  placeholder="First Name"
                />
                <div class="placeholder">First Name</div>
                <div class="input-info">Provide the first name to be used.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="last_name"
                  placeholder="Last Name"
                />
                <div class="placeholder">Last Name</div>
                <div class="input-info">Provide the last name to be used.</div>
              </div>
            </div>

            <div class="splitter" *ngIf="showPage != 'List'">
              <div class="field-value">
                <div class="input-container" (click)="showdrop = !showdrop">
                  <input
                    type="text"
                    class="selection"
                    formControlName="role"
                    placeholder="Select Role"
                    readonly
                  />
                  <button type="button">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="
                      showdrop ? 'dropdown-visible' : 'dropdown-hidden'
                    "
                  >
                    <ul>
                      <li
                        (click)="selectRole(option)"
                        *ngFor="let option of roleList"
                      >
                        <div class="country-name">{{ option?.role }}</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info" *ngIf="!showdrop">
                  Choose which role to assign to the user.
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="email"
                  placeholder="Email"
                />
                <div class="placeholder">Email</div>
                <div class="input-info">Provide the email to be used.</div>
              </div>
            </div>
            <div *ngIf="showPage != 'List' && form.value.role">
              <table class="table">
                <thead>
                  <tr>
                    <th class="font-head text-center">Modules</th>
                    <th class="font-head text-center">Download</th>
                    <th class="font-head text-center">Create</th>
                    <th class="font-head text-center">Read</th>
                    <th class="font-head text-center">Delete</th>
                    <th class="font-head text-center">Update</th>
                    <!-- <th class="font-head text-center">Select All</th> -->
                  </tr>
                </thead>

                <tbody>
                  <tr *ngFor="let item of modulesArr; let i = index">
                    <td>{{ item?.name }}</td>
                    <td class="text-center">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.download"
                        [ngModelOptions]="{ standalone: true }"
                        disabled
                      />
                    </td>
                    <td class="text-center">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.create"
                        [ngModelOptions]="{ standalone: true }"
                        disabled
                      />
                    </td>
                    <td class="text-center">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.read"
                        [ngModelOptions]="{ standalone: true }"
                        disabled
                      />
                    </td>
                    <td class="text-center">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.delete"
                        [ngModelOptions]="{ standalone: true }"
                        disabled
                      />
                    </td>
                    <td class="text-center">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.update"
                        [ngModelOptions]="{ standalone: true }"
                        disabled
                      />
                    </td>
                    <!-- <td class="text-center"><input type="checkbox" [(ngModel)]="item.selectAll" [ngModelOptions]="{standalone: true}" (click)="manageSelection('selectAll',item.selectAll,i)" readonly></td> -->
                  </tr>
                </tbody>
              </table>
            </div>

            <div *ngIf="showPage == 'List'">
              <div>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Email</th>
                      <th class="text-center">Role</th>
                      <th class="text-center">Action</th>
                    </tr>
                  </thead>

                  <tbody id="MyTable">
                    <tr
                      *ngFor="
                        let item of userList
                          | paginate
                            : {
                                itemsPerPage: limit,
                                currentPage: currentPage(),
                                totalItems: total
                              };
                        let i = index
                      "
                    >
                      <td (click)="onUserView(item, i)">{{ item?.email }}</td>
                      <td class="text-center" (click)="onUserView(item, i)">
                        {{ item?.role }}
                      </td>
                      <td class="text-center">
                        <button
                          class="btn-edit"
                          (click)="onUserEdit(item, i)"
                          *ngIf="status == 'ACTIVE'"
                        >
                          Edit
                        </button>
                        <button
                          class="btn-cancel"
                          (click)="
                            isPopupOpen = true;
                            selectedUserId = item['_id'];
                            showPage = 'List'
                          "
                          *ngIf="status == 'ACTIVE'"
                        >
                          Deactivate
                        </button>
                        <button
                          class="btn-success"
                          (click)="
                            isDeactivatePopupOpen = true;
                            selectedUserId = item['_id'];
                            showPage = 'List'
                          "
                          *ngIf="status == 'BLOCK'"
                        >
                          Activate
                        </button>
                      </td>
                    </tr>
                    <tr *ngIf="userList.length == 0">
                      <td class="text-center" colspan="3">No record found!</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div *ngIf="showPage == 'List'" class="pagination">
            <pagination-controls
              (pageChange)="managePagination($event)"
            ></pagination-controls>
          </div>
          <div
            class="text-center mt10"
            *ngIf="showPage == 'Update' || showPage == 'Add'"
          >
            <button
              class="btn-edit"
              (click)="showPage == 'Update' ? updateUser() : addUser()"
            >
              {{ showPage }} User
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div
  id="myModal"
  class="modal"
  [style.display]="isPopupOpen ? 'block' : 'none'"
>
  <div class="modal-content">
    <p>Are you sure you wish to deactivate the user?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button
      ><button
        type="button"
        (click)="isPopupOpen = false; confirmDeactivate()"
        class="btnn"
      >
        Confirm
      </button>
    </div>
  </div>
</div>
<div
  id="myModal"
  class="modal"
  [style.display]="isDeactivatePopupOpen ? 'block' : 'none'"
>
  <div class="modal-content">
    <p>Are you sure you wish to activate the user?</p>
    <div class="buttonKeeper">
      <button
        type="button"
        class="btnn"
        (click)="isDeactivatePopupOpen = false"
      >
        Cancel</button
      ><button
        type="button"
        (click)="isDeactivatePopupOpen = false; confirmActivate()"
        class="btnn"
      >
        Confirm
      </button>
    </div>
  </div>
</div>
