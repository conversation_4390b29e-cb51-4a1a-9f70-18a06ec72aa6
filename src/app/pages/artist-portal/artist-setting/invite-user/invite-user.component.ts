import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../services/artist.service';

@Component({
  selector: 'app-invite-user',
  templateUrl: './invite-user.component.html',
  styleUrls: ['./invite-user.component.scss'],
})
export class InviteUserComponent implements OnInit {
  form: FormGroup;
  showdrop: boolean = false;
  limit: any = 10;
  offset: any = 0;
  userList: any = [];
  showPage: any = 'List';
  roleList: any = [];
  isPopupOpen: boolean = false;
  isDeactivatePopupOpen: boolean = false;
  selectedUserId: any;
  modulesArr: any = [];
  selectedRoleId: any;
  total: any = 0;
  status: any = 'ACTIVE';
  isChecked: boolean = true;

  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService
  ) {}

  ngOnInit(): void {
    this.initialiseForm();
    this.getRoles();
    this.getUsers();
    this.resetModules();
  }

  // to initialise form
  initialiseForm() {
    this.form = this.formBuilder.group({
      first_name: new FormControl(''),
      last_name: new FormControl(''),
      email: new FormControl(''),
      role: new FormControl(''),
    });
  }

  // to get roles
  getRoles() {
    let url =
      apiUrl.addRole +
      `?limit=${this.limit}&offset=${this.offset}&status=ACTIVE`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.roleList = res.data;
      }
    });
  }

  // to get users
  getUsers() {
    this.userList = [];
    let url =
      apiUrl.addUser +
      `?limit=${this.limit}&offset=${this.offset}&status=${this.status}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.userList = res.data;
        this.total = res.total;
      }
    });
  }

  onUserEdit(item, index) {
    this.selectedUserId = item['_id'];
    this.showPage = 'Update';
    this.patchUser(item);
  }

  // to view role
  onUserView(item, index) {
    this.selectedUserId = item['_id'];
    this.showPage = 'View';
    this.patchUser(item);
  }

  // to patch Role value on edit and view
  patchUser(item) {
    this.form.patchValue({
      first_name: item['first_name'],
      last_name: item['last_name'],
      role: item.role,
      email: item.email,
    });
    this.form.controls['email'].disable();
    let index = this.roleList.findIndex((x) => x.role == item.role);
    if (index != -1) {
      this.modulesArr = this.roleList[index].permissions;
      this.selectedRoleId = this.roleList[index]['_id'];
    }
  }

  // to confirm deactivate user
  confirmDeactivate() {
    let url = apiUrl.deactivateUser;
    let data = {
      user_id: this.selectedUserId,
      status: 'BLOCK',
    };
    // this.isPopupOpen = false;
    this.server.showSpinner();
    this.server.postApi(url, data).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert('User blocked successfully!');
          this.offset = 0;
          this.getUsers();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }

  // to confirm activate user
  confirmActivate() {
    let url = apiUrl.activateUser;
    let data = {
      user_id: this.selectedUserId,
      status: 'ACTIVE',
    };
    // this.isDeactivatePopupOpen = false;
    this.server.showSpinner();
    this.server.postApi(url, data).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert('User id activated successfully!');
          this.offset = 0;
          this.getUsers();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }

  //on clicking back button
  onBack() {
    this.showPage = 'List';
    this.limit = 10;
    this.offset = 0;
    this.status = 'ACTIVE';
    this.getUsers();
  }

  // on clicking adding role buuton
  onAddingUser() {
    this.showPage = 'Add';
    this.form.reset();
    this.resetModules();
  }

  // to reset
  resetModules() {
    this.modulesArr = apiUrl.modules;
  }

  // on selecting role
  selectRole(item) {
    console.log(item);
    this.form.patchValue({ role: item.role });
    this.modulesArr = item.permissions;
    this.selectedRoleId = item['_id'];
  }

  // add user api
  addUser() {
    if (this.form.invalid) {
      alert('Form invalid!');
      return;
    }
    let url = apiUrl.addUser;
    let data = {
      first_name: this.form.value.first_name,
      last_name: this.form.value.last_name,
      fullName: this.form.value.first_name + ' ' + this.form.value.last_name,
      email: this.form.value.email,
      role_id: this.selectedRoleId,
      cmsUrl: this.server.cmsUrl,
    };
    this.server.showSpinner();
    this.server.postApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Email verfication link sent !');
        this.showPage = 'List';
        this.offset = 0;
        this.limit = 10;
        this.getUsers();
      }
    });
  }

  // update user api
  updateUser() {
    if (this.form.invalid) {
      alert('Form invalid!');
      return;
    }
    let url = apiUrl.addUser;
    let data = {
      first_name: this.form.value.first_name,
      last_name: this.form.value.last_name,
      role_id: this.selectedRoleId,
      user_id: this.selectedUserId,
    };
    this.server.showSpinner();
    this.server.putApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.showPage = 'List';
        this.offset = 0;
        this.limit = 10;
        this.getUsers();
      }
    });
  }

  // to manage pagination
  managePagination(page) {
    this.offset = (Number(page) - 1) * this.limit;
    this.total = 0;
    this.getUsers();
  }

  // on toggle
  onToggle() {
    this.isChecked = !this.isChecked;
    if (this.isChecked) {
      this.status = 'ACTIVE';
    } else {
      this.status = 'BLOCK';
    }
    this.limit = 10;
    this.offset = 0;
    this.getUsers();
  }

  currentPage() {
    return Math.ceil(this.offset / this.limit) + 1;
  }
}
