<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="field-value" [hidden]="!showObj?.publishProfile">
            <div
              class="input-container"
              style="
                flex-direction: column;
                justify-content: unset;
                align-items: unset;
              "
            >
              <div class="text-before" style="margin-bottom: 0.5vw">
                Publish Profile :
              </div>
              <label class="switch">
                <input type="checkbox" formControlName="publishProfile" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to publish the profile on the website
            </div>
          </div>

          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                type="text"
                formControlName="displayName"
                placeholder="Name as to be used on profile"
                (change)="onDisplaychange()"
              />
              <div class="placeholder">Display Name</div>
              <div class="input-info">
                Provide the display name to be used for your profile on the
                website. Use full name, mononyms, pseudonyms/alias as preferred.
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.url"
              style="padding-right: 0.5vw"
            >
              <input
                type="text"
                placeholder="e.g.: john-smith"
                formControlName="url"
                [readOnly]="
                  userDetails?.role !== 'SUPERADMIN' &&
                  userDetails?.role !== 'TERRAIN CURATOR' &&
                  userDetails?.role !== 'ARCHIVIST'
                "
              />
              <div class="placeholder">Profile URL</div>
              <div class="input-info">
                Provide the exact details to give in URL name for the artist
                (preferrably contents from the Display Name field, with hyphen
                replacing any spaces)
              </div>
            </div>
            <div
              *ngIf="this.selectedArtistObj?.role !== 'THOUGHT-LEADER'"
              class="field-value"
            >
              <input
                type="text"
                placeholder="e.g.: JSMITH"
                formControlName="catalogue_code"
                [readOnly]="
                  userDetails?.role !== 'SUPERADMIN' &&
                  userDetails?.role !== 'TERRAIN CURATOR' &&
                  userDetails?.role !== 'ARCHIVIST'
                "
              />
              <div class="placeholder">Catalogue Code</div>
              <div class="input-info"></div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div
                style="margin-top: 3.47vw; font-size: 1.25vw"
                [hidden]="!showObj?.banner_image"
              >
                Banner Image
              </div>
              <div class="flex-block" [hidden]="!showObj?.thumbnail_image">
                <image-upload
                  [accept]="'image/*'"
                  [selectedData]="selectedFilesObj?.bannerImageArr"
                  (onFileChange)="onFileSelect($event, 'bannerImageArr')"
                  [fileSize]="8388608"
                  [placeholder]="placeholderObj.bannerImage"
                  [hideAltText]="true"
                ></image-upload>
                <div class="input-info">
                  Provide the banner image to be used on the artist profile page
                  (one of the artworks, cropped as per aspect ratio approx
                  1440x246 px).
                </div>
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div
                style="margin-top: 3.47vw; font-size: 1.25vw"
                [hidden]="!showObj?.thumbnail_image"
              >
                Profile Thumbnail
              </div>

              <div class="flex-block" [hidden]="!showObj?.thumbnail_image">
                <image-upload
                  [accept]="'image/*'"
                  [selectedData]="selectedFilesObj?.thumbImageArr"
                  (onFileChange)="onFileSelect($event, 'thumbImageArr')"
                  [fileSize]="8388608"
                  [placeholder]="placeholderObj.thumbImage"
                  [hideAltText]="true"
                ></image-upload>
                <div class="input-info">
                  Provide the cropped image to be used on the artist listing
                  page (cropped as per aspect ratio 1:1). Images uploaded will
                  be centered as per the aspect ratio automatically.
                </div>
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw; width: 35vw">
              <div
                style="margin-top: 3.47vw; font-size: 1.25vw"
                [hidden]="!showObj?.artistBio"
              >
                Bio / Artist Note
              </div>
              <div class="" [hidden]="!showObj?.artistBio">
                <angular-editor
                  *ngIf="htmlcheckBox[0]"
                  [placeholder]="'Bio / Artist Note'"
                  formControlName="artistBio"
                  id="editor36"
                  rows="18"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[0]"
                  [placeholder]="'Bio / Artist Note'"
                  formControlName="artistBio"
                  rows="18"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide a short bio (less than 700 characters with spaces) to
                  be used on the profile page of the creator.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[0]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw; width: 35vw">
              <div
                style="margin-top: 3.47vw; font-size: 1.25vw"
                [hidden]="!showObj?.narrativeBio"
              >
                Narrative Bio
              </div>
              <div class="" [hidden]="!showObj?.narrativeBio">
                <angular-editor
                  *ngIf="htmlcheckBox[1]"
                  id="editor38"
                  [placeholder]="'Artist Note'"
                  formControlName="narrativeBio"
                  id="editor37"
                  rows="18"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[1]"
                  [placeholder]="'Artist Note'"
                  formControlName="narrativeBio"
                  rows="18"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide a short narrative bio with the practice highlights of
                  the creator (~500 characters with spaces) to be used on the
                  profile page.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[1]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            *ngIf="this.selectedArtistObj.role !== 'THOUGHT-LEADER'"
            class="splitter"
          >
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.yearFrom"
            >
              <input
                type="number"
                placeholder="Year"
                formControlName="yearFrom"
                oninput="this.value = Math.max(0,Math.round(this.value));"
              />
              <div class="placeholder">Active From</div>
              <div class="input-info">
                Select the year the creator started creating works, or the year
                of their first exhibition
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.yearTo">
              <input
                type="number"
                placeholder="Year"
                formControlName="yearTo"
                oninput="this.value = Math.max(0,Math.round(this.value));"
              />
              <div class="placeholder">Last Active</div>
              <div class="input-info">
                Select the year of the creator's last known exhibition/work
                (applicable only if the creator isn't creating works anymore)
              </div>
            </div>
          </div>
          <div
            class="sub-head"
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.socialMedia"
          >
            <p>Website URLs</p>

            <div class="input-info" style="margin-bottom: 1vw">
              <!-- Upload additional images (installation/detail shots), videos (pan, instalation videos, in process videos) of the artwork -->
              Provide the website URLs to use on the profile page (Social Media
              profile links, personal websites, etc). Please ensure the complete
              URL is provided, and not just the handles.
            </div>
          </div>

          <forms-dynamic-table
            [enableDrag]="true"
            [columns]="tableColumns"
            [formArray]="form.get('socialMedia')"
          ></forms-dynamic-table>

          <div class="field-value">
            <multi-input
              placeholder="Choose exhibitions from the list"
              [itemsArray]="exhibition_select"
              [previosItems]="selected_exhibition"
              [newEntry]="false"
              [disableSearch]="true"
              (searchEvent)="exhibition_search = $event; getArtistsExhibition()"
              (selectedItem)="selected_exhibition = $event"
            ></multi-input>
            <div class="placeholder">Select Exhibitions</div>
            <div class="input-info"></div>
          </div>
          <ng-container
            *ngIf="
              userDetails?.role === 'SUPERADMIN' ||
              userDetails?.role === 'TERRAIN CURATOR' ||
              userDetails?.role === 'ARCHIVIST'
            "
          >
            <div
              class="splitter"
              *ngIf="this.selectedArtistObj.role !== 'THOUGHT-LEADER'"
            >
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  formControlName="consignmentStartDate"
                  placeholder="Consignment Start Date"
                  [attr.disabled]="isConsignmentEditable ? null : true"
                />
                <div class="placeholder">Consignment Start Date</div>
                <div class="input-info"></div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  formControlName="consignmentEndDate"
                  placeholder="Consignment End Date"
                  [attr.disabled]="isConsignmentEditable ? null : true"
                />
                <div class="placeholder">Consignment End Date</div>
                <div class="input-info"></div>
              </div>
            </div>
            <div class="addbutton-container">
              <div class="button" (click)="movetoConsignmentHisPop = true">
                Move to Consignment History
              </div>
            </div>
            <div *ngIf="this.selectedArtistObj.role !== 'THOUGHT-LEADER'">
              <div class="sub-head">
                <p>Consignment History</p>
                <forms-dynamic-table
                  [columns]="ConsignmentHistoryColumns"
                  [formArray]="form.get('consignmentDetails')"
                  [readOnly]="true"
                  [expandedMode]="false"
                ></forms-dynamic-table>
              </div>
            </div>
          </ng-container>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="updateProfile()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
