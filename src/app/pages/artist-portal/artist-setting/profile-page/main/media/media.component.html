<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div
            *ngIf="
              this.selectedArtistObj.role !== 'THOUGHT-LEADER' &&
              !this.isGalleryEdit
            "
            class="sub-head"
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.quoteAudio"
          >
            <p>Quotes and Audio</p>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="input-container">
                  <input
                    type="text"
                    [(ngModel)]="quoteAudioObj.audio_title"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Audio Title"
                  />

                  <div class="placeholder">Audio Title</div>
                </div>
                <div class="input-info">
                  Provide the title of the clip to use on the site
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  [(ngModel)]="quoteAudioObj.audio_by"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Audio By"
                />
                <div class="placeholder">Audio By</div>
                <div class="input-info">
                  Provide the name(s) of the person/people in the clip.
                </div>
              </div>
            </div>
            <div class="">
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="input-container">
                  <angular-editor
                    id="editor39"
                    [(ngModel)]="quoteAudioObj.quote"
                    [ngModelOptions]="{ standalone: true }"
                    [placeholder]="'Quote'"
                  ></angular-editor>
                  <div class="placeholder">Quote</div>
                </div>
                <div class="input-info">
                  Provide the Quote to use next to the Audio clip on the site.
                </div>
              </div>
            </div>

            <div class="">
              <div class="field-value" style="padding-right: 0.5vw">
                <image-upload
                  [accept]="'audio/*'"
                  [selectedData]="selectedFilesObj?.audioClipArr"
                  (onFileChange)="onFileSelect($event, 'audioClipArr')"
                  [fileSize]="8388608"
                  [placeholder]="placeholderObj.audioClip"
                ></image-upload>
              </div>
            </div>
            <div class="input-info" style="margin-bottom: 1vw">
              Upload the audio clip to use on the site. Filetypes mp3, m4a
              supported (mp3 preferred).
            </div>
            <p (click)="addRepeater()">
              <span
                ><img class="plus-icon" src="assets/images/load-more.png"
              /></span>
              <span class="sub-text">Add to quotes and audio</span>
            </p>
          </div>
          <div
            class="outer-box"
            *ngIf="
              quoteAudio.controls.length > 0 &&
              this.selectedArtistObj.role !== 'THOUGHT-LEADER' &&
              !this.isGalleryEdit
            "
          >
            <div class="row">
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head ml-35">Quote</label>
              </div>
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Audio Title</label>
              </div>
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Audio By</label>
              </div>

              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Image Url</label>
              </div>
            </div>
            <div
              formArrayName="quoteAudio"
              [dragula]="'task-group'"
              [(dragulaModel)]="quoteAudio.controls"
            >
              <div
                class="row mb-15 inner-box"
                formGroupName="{{ i }}"
                *ngFor="let item of quoteAudio.controls; let i = index"
              >
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <span class="cross-icon" style="cursor: grab !important">
                    <i class="fa fa-ellipsis-v mr2"></i>
                    <i class="fa fa-ellipsis-v"></i>
                  </span>
                  <input formControlName="quote" type="text" disabled />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <input formControlName="audio_title" type="text" disabled />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <input formControlName="audio_by" type="text" disabled />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <input
                    formControlName="audio_file_url"
                    type="text"
                    style="width: 70%"
                    disabled
                  />
                  <button (click)="quoteAudio.removeAt(i, 1)" class="rm-btn">
                    <img src="assets/icons/grey-close.png" alt="Close" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="sub-head" style="margin-top: 3.47vw; font-size: 1.25vw">
            <p>Introduction Video</p>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="introductionVideoUrl"
                  placeholder="Video Url"
                />
                <div class="placeholder">Video URL</div>
                <div class="input-info">
                  Provide the Vimeo player link for the video, the URL from the
                  Embed Code field in Vimeo, to be displayed in the best
                  possible manner on the website. e.g.:
                  https://player.vimeo.com/video/708552591?h=c3ba00e766.
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="introductionVideoTitle"
                  placeholder="Video Title"
                />
                <div class="placeholder">Video Title</div>
                <div class="input-info">
                  Provide a one-line title description for the video
                </div>
              </div>
            </div>
          </div>
          <div
            *ngIf="isValidUrl(form.controls.introductionVideoUrl.value)"
            style="width: 48%"
          >
            <div style="padding: 56.25% 0 0 0; position: relative">
              <iframe
                [src]="
                  form.controls.introductionVideoUrl.value
                    | safe : 'resourceUrl'
                "
                style="
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                "
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </div>
          <div
            *ngIf="
              form.controls.introductionVideoUrl.value &&
              !isValidUrl(form.controls.introductionVideoUrl.value)
            "
            style="width: 48%; color: red"
          >
            Invalid URL
          </div>
          <div
            *ngIf="
              userDetails?.role !== 'REPRESENTED ARTIST' &&
              userDetails?.role !== 'GALLERY ARTIST' &&
              this.selectedArtistObj.role !== 'THOUGHT-LEADER'
            "
            class="sub-head"
            style="margin-top: 3.47vw; font-size: 1.25vw"
          >
            <p>Process Video</p>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="processVideoUrl"
                  placeholder="Video Url"
                />
                <div class="placeholder">Video URL</div>
                <div class="input-info">
                  Provide the Vimeo player link for the video, the URL from the
                  Embed Code field in Vimeo, to be displayed in the best
                  possible manner on the website. e.g.:
                  https://player.vimeo.com/video/708552591?h=c3ba00e766.
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="processVideoTitle"
                  placeholder="Video Title"
                />
                <div class="placeholder">Video Title</div>
                <div class="input-info">
                  Provide a one-line title description for the video
                </div>
              </div>
            </div>
          </div>
          <div
            *ngIf="
              userDetails?.role !== 'REPRESENTED ARTIST' &&
              userDetails?.role !== 'GALLERY ARTIST' &&
              this.selectedArtistObj.role !== 'THOUGHT-LEADER' &&
              !this.isGalleryEdit
            "
            class="sub-head"
            style="margin-top: 3.47vw; font-size: 1.25vw"
          >
            <p>Additional Video</p>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="additionalVideoUrl"
                  placeholder="Video Url"
                />
                <div class="placeholder">Video URL</div>
                <div class="input-info">
                  Provide the Vimeo player link for the video, the URL from the
                  Embed Code field in Vimeo, to be displayed in the best
                  possible manner on the website. e.g.:
                  https://player.vimeo.com/video/708552591?h=c3ba00e766.
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="additionalVideoTitle"
                  placeholder="Video Title"
                />
                <div class="placeholder">Video Title</div>
                <div class="input-info">
                  Provide a one-line title description for the video
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="updateProfile()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
