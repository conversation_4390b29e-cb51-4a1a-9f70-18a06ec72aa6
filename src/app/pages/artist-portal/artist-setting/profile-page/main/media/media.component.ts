import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
  FormArray,
} from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/internal/operators/debounceTime';
import { distinctUntilChanged } from 'rxjs/internal/operators/distinctUntilChanged';
import { Subject } from 'rxjs/internal/Subject';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-media',
  templateUrl: './media.component.html',
  styleUrls: ['./media.component.scss'],
})
export class MediaComponent implements OnInit {
  selectedArtistObj;
  form: FormGroup;
  selected_exhibition = [];
  exhibition_select = [];
  extras = {};
  isConsignmentEditable = false;
  selectedFilesObj: any = {
    bannerImageArr: [],
    thumbImageArr: [],
    bannerImage: '',
    thumbImage: '',
    audioClipArr: [],
    audioClip: '',
  };
  quoteAudioObj: any = {
    quote: '',
    audio_title: '',
    audio_by: '',
  };
  socialObj: any = {
    url: '',
    type: '',
  };
  exhibition_search = '';
  exhibition_limit = 12;

  userDetails;
  permissionsObj;
  showObj: any = {};

  placeholderObj: any = {
    thumbImage: '',
    audioClip: '',
    bannerImage: '',
  };
  htmlcheckBox = Array(10).fill(false);
  isGalleryEdit = false;
  tableColumns = [
    {
      name: 'URL Type',
      type: 'select',
      control: 'type',
      data: [
        {
          label: 'Instagram',
          value: 'Instagram',
        },
        { label: 'Facebook', value: 'Facebook' },
        { label: 'Twitter', value: 'Twitter' },
        { label: 'Youtube', value: 'Youtube' },
        { label: 'Behance', value: 'Behance' },
        { label: 'Website', value: 'Website' },
        { label: 'Vimeo', value: 'Vimeo' },
        { label: 'LinkedIn', value: 'LinkedIn' },
        { label: 'Flickr', value: 'Flickr' },
        { label: 'Other', value: 'Other' },
      ],
    },
    { name: 'URL', type: 'text', control: 'url' },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private router: Router
  ) {}

  onDisplaychange() {
    if (
      this.userDetails?.role !== 'SUPERADMIN' &&
      this.userDetails?.role !== 'TERRAIN CURATOR' &&
      this.userDetails?.role !== 'ARCHIVIST' &&
      this.selectedArtistObj['url']
    ) {
    } else {
      if (this.form.controls.displayName.value.trim()) {
        if (!this.form.controls.url.value.trim()) {
          let displayName = this.form.controls.displayName.value.trim();
          let urlName = displayName.toLowerCase().replace(/\s+/g, '-');
          urlName = urlName.replace(/[^a-z0-9-]/g, '');
          if (!this.selectedArtistObj['url_name']) {
            this.form.controls.url.setValue(urlName);
          }
        }
        let displayName = this.form.controls.displayName.value
          .trim()
          .toUpperCase();
        let nameArray = displayName.split(' ');
        let catName = '';
        for (let index = 0; index < nameArray.length; index++) {
          const element = nameArray[index];
          if (index + 1 == nameArray.length) {
            catName += element;
          } else {
            catName += element[0];
          }
        }
        if (!this.selectedArtistObj['catalogue_code']) {
          this.form.controls.catalogue_code.setValue(catName);
        }
      }
    }
  }

  ngOnInit(): void {
    this.initForm();
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );

    this.userDetails = JSON.parse(decode);
    this.permissionsObj = JSON.parse(decode)
      ['role_id']['permissions'].find((x) => x.name == 'Artist Bio')
      .tabsArr.find((x) => x.name == 'Main');

    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = JSON.parse(ele.show);
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled'] =
          ele.disabled == 'true' || ele.disabled == true ? true : false;
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
      }
    });

    this.server.selectedArtist$.subscribe((res) => {
      this.selectedArtistObj = res;
      this.patchFormValues();
    });

    this.isGalleryEdit = false;
    if (localStorage.getItem('bioArtistID')) {
      this.isGalleryEdit = true;
    }
  }

  initForm() {
    this.form = this.formBuilder.group({
      artistBio: new FormControl(''),
      narrativeBio: new FormControl(''),
      displayName: new FormControl(''),
      yearTo: new FormControl(''),
      yearFrom: new FormControl('', [Validators.required]),
      url: new FormControl(''),
      catalogue_code: new FormControl(''),
      featured: new FormControl(''),
      showFilter: new FormControl(''),
      publishProfile: new FormControl(''),
      type: new FormControl(''),
      quoteAudio: this.formBuilder.array([]),
      socialMedia: this.formBuilder.array([]),
      additionalVideoTitle: new FormControl(''),
      additionalVideoUrl: new FormControl(''),
      introductionVideoUrl: new FormControl(''),
      introductionVideoTitle: new FormControl(''),
      processVideoUrl: new FormControl(''),
      processVideoTitle: new FormControl(''),
      openToNetworkEnable: new FormControl(false),
      openToNetworkEmail: new FormControl(''),
      openToNetworkPhone: new FormControl(''),
      consignmentStartDate: new FormControl(''),
      consignmentEndDate: new FormControl(''),
      consignmentDetails: this.formBuilder.array([]),
    });
  }
  patchFormValues() {
    this.selected_exhibition = [];
    this.selectedArtistObj?.collection_ids?.forEach((a) => {
      this.selected_exhibition.push({ id: a._id, name: a.title });
    });
    this.getArtistsExhibition();
    this.form.patchValue({
      artistBio: this.selectedArtistObj['artist_bio'] || '',
      displayName: this.selectedArtistObj['display_name'] || '',
      narrativeBio: this.selectedArtistObj['narrative_bio'] || '',
      yearFrom: this.selectedArtistObj['active_from'] || '',
      yearTo: this.selectedArtistObj['active_till'] || '',
      featured: this.selectedArtistObj['featured'] || '',
      publishProfile:
        this.selectedArtistObj['publish'] == 'true' ? true : false,
      showFilter: this.selectedArtistObj['show_filter'] || '',
      url: this.selectedArtistObj['url_name'] || '',
      catalogue_code: this.selectedArtistObj['catalogue_code'],
      // quote: this.selectedArtistObj['quote'] || '',
      // indexPrice: this.selectedArtistObj['indexPrice'] || '',
      // audioTitle: this.selectedArtistObj['audio_title'] || '',
      // audioBy: this.selectedArtistObj['audio_by'] || '',
      additionalVideoTitle:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '2';
        })?.[0]?.video_title || '',
      additionalVideoUrl:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '2';
        })?.[0]?.url || '',
      introductionVideoUrl:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '1';
        })?.[0]?.url || '',
      introductionVideoTitle:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '1';
        })?.[0]?.video_title || '',
      processVideoUrl:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '3';
        })?.[0]?.url || '',
      processVideoTitle:
        this.selectedArtistObj['intro_videos']?.filter((a) => {
          return a.index_number === '3';
        })?.[0]?.video_title || '',
      openToNetworkEnable: this.selectedArtistObj['opentonetwork']?.enable,
      openToNetworkEmail: this.selectedArtistObj['opentonetwork']?.email,
      openToNetworkPhone: this.selectedArtistObj['opentonetwork']?.phone,
      consignmentStartDate:
        this.selectedArtistObj?.extras?.consignmentStartDate,
      consignmentEndDate: this.selectedArtistObj?.extras?.consignmentEndDate,
    });
    console.log(this.selectedArtistObj?.extras);

    if (this.selectedArtistObj?.extras) {
      this.extras = this.selectedArtistObj?.extras;
    } else {
      this.extras = {};
    }

    this.selectedArtistObj?.extras?.consignmentDetails?.forEach(
      (ele, index) => {
        (<FormArray>this.form.get('consignmentDetails')).push(
          this.formBuilder.group({
            consignmentStartDate: new FormControl(ele.consignmentStartDate),
            consignmentEndDate: new FormControl(ele.consignmentEndDate),
          })
        );
      }
    );

    if (
      this.form.get('consignmentStartDate').value !==
        this.form.get('consignmentDetails').value?.at(-1)
          ?.consignmentStartDate &&
      this.form.get('consignmentEndDate').value !==
        this.form.get('consignmentDetails').value?.at(-1)?.consignmentEndDate
    ) {
      this.isConsignmentEditable = false;
    } else {
      this.isConsignmentEditable = true;
    }

    if (
      !this.form.get('consignmentDetails').value?.at(-1)?.consignmentStartDate
    ) {
      this.isConsignmentEditable = true;
    }

    if (this.selectedArtistObj['banner_image']) {
      this.selectedFilesObj.bannerImageArr.push({
        url: this.selectedArtistObj['banner_image'],
        size: '',
        name: 'File',
        type: 'image',
        file_event: '',
        filedata: '',
      });
    }

    if (this.selectedArtistObj['banner_image']) {
      this.selectedFilesObj.thumbImageArr.push({
        url: this.selectedArtistObj['thumbnail_image'],
        size: '',
        name: 'File',
        type: 'image',
        file_event: '',
        filedata: '',
      });
    }

    this.selectedArtistObj.quotes.forEach((ele, index) => {
      this.addRepeater();
      this.form['controls'].quoteAudio['controls'][index]
        .get('quote')
        .setValue(ele.quote);
      this.form['controls'].quoteAudio['controls'][index]
        .get('audio_title')
        .setValue(ele.audio_title);
      this.form['controls'].quoteAudio['controls'][index]
        .get('audio_file_url')
        .setValue(ele.audio_file_url);
      this.form['controls'].quoteAudio['controls'][index]
        .get('audio_by')
        .setValue(ele.audio_by);
    });
    this.selectedArtistObj.socialUrl.forEach((ele, index) => {
      this.addRepeater2();
      this.form['controls'].socialMedia['controls'][index]
        .get('url')
        .setValue(ele.url);
      this.form['controls'].socialMedia['controls'][index]
        .get('type')
        .setValue(ele.type);
    });
  }
  getArtistsExhibition() {
    let url = `api/exhibition/artist/${this.selectedArtistObj['_id']}?limit=${this.exhibition_limit}&search=${this.exhibition_search}&offset=1`;
    this.server.getApi(url).subscribe((res) => {
      this.exhibition_select = res.data.docs.map((a) => {
        return {
          id: a._id,
          name: a.title,
        };
      });
      console.log(this.exhibition_select);
    });
  }
  addRepeater() {
    this.quoteAudio.push(
      this.formBuilder.group({
        quote: new FormControl(this.quoteAudioObj.quote),
        audio_title: new FormControl(this.quoteAudioObj.audio_title),
        audio_file_url: new FormControl(
          this.selectedFilesObj.audioClipArr.length > 0
            ? this.selectedFilesObj.audioClipArr[0].url
            : ''
        ),
        audio_by: new FormControl(this.quoteAudioObj.audio_by),
      })
    );
  }

  addRepeater2() {
    this.socialMedia.push(
      this.formBuilder.group({
        url: new FormControl(this.socialObj.url),
        type: new FormControl(this.socialObj.type),
      })
    );
  }
  get quoteAudio(): FormArray {
    return <FormArray>this.form.get('quoteAudio');
  }

  get socialMedia(): FormArray {
    return <FormArray>this.form.get('socialMedia');
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }
  validateEmail(email) {
    var regex = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;
    return regex.test(email);
  }
  validatePhoneNumber(phoneNumber) {
    var regex = /^\+?[\d\s]+$/;
    return regex.test(phoneNumber);
  }

  updateProfile() {
    // if (this.form.invalid) {
    //   alert('Form not valid.Please fill form correctly !');
    //   return;
    // }
    if (this.selectedFilesObj.bannerImageArr.length == 0) {
      alert('Please upload banner image!');
      return;
    }
    if (this.selectedFilesObj.thumbImageArr.length == 0) {
      alert('Please upload thumbnail image!');
      return;
    }
    if (!this.form.value.displayName) {
      alert('Please enter display name!');
      return;
    }
    // if (!this.form.value.url) {
    //   alert('Please enter url name!');
    //   return;
    // }
    if (!this.form.value.artistBio) {
      alert('Please enter bio!');
      return;
    }

    if (this.form.value.openToNetworkEnable) {
      if (
        !this.form.value.openToNetworkEmail ||
        !this.validateEmail(this.form.value.openToNetworkEmail)
      ) {
        alert('Please enter valid email!');
        return;
      }
      if (
        !this.form.value.openToNetworkPhone ||
        !this.validatePhoneNumber(this.form.value.openToNetworkPhone)
      ) {
        alert('Please enter valid phone no.!');
        return;
      }
    }

    if (
      this.permissionsObj.fields.find((x) => x.name == 'banner_image')
        .mandatory &&
      this.selectedFilesObj.bannerImageArr.length == 0
    ) {
      alert('Please upload banner image!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'thumbnail_image')
        .mandatory &&
      this.selectedFilesObj.thumbImageArr.length == 0
    ) {
      alert('Please upload thumbnail image!');
      return;
    }
    // if (
    //   this.permissionsObj.fields.find((x) => x.name == 'quoteAudio')
    //     .mandatory &&
    //   this.form.value.quoteAudio.length == 0
    // ) {
    //   alert('Please add quote and audio!');
    //   return;
    // }
    // if (
    //   this.permissionsObj.fields.find((x) => x.name == 'socialMedia')
    //     .mandatory &&
    //   this.form.value.socialMedia.length == 0
    // ) {
    //   alert('Please add social media url!');
    //   return;
    // }
    let intro_videos = [
      {
        index_number: '1',
        newItem: true,
        short_description: '',
        url: this.form.value.introductionVideoUrl?.trim(),
        video_title: this.form.value.introductionVideoTitle?.trim(),
      },
      {
        index_number: '2',
        newItem: true,
        short_description: '',
        url: this.form.value.additionalVideoUrl?.trim(),
        video_title: this.form.value.additionalVideoTitle?.trim(),
      },
      {
        index_number: '3',
        newItem: true,
        short_description: '',
        url: this.form.value.processVideoUrl?.trim(),
        video_title: this.form.value.processVideoTitle?.trim(),
      },
    ];
    this.extras['consignmentStartDate'] = this.form.value.consignmentStartDate;
    this.extras['consignmentDetails'] =
      this.form.getRawValue().consignmentDetails;
    this.extras['consignmentEndDate'] = this.form.value.consignmentEndDate;

    let req = {
      banner_image:
        this.selectedFilesObj.bannerImageArr.length > 0
          ? this.selectedFilesObj.bannerImageArr[0].url
          : '',
      thumbnail_image:
        this.selectedFilesObj.thumbImageArr.length > 0
          ? this.selectedFilesObj.thumbImageArr[0].url
          : '',
      artist_id: this.selectedArtistObj['_id'],
      artist_bio: this.form.value.artistBio?.trim(),
      display_name: this.form.value.displayName?.trim(),
      narrative_bio: this.form.value.narrativeBio?.trim(),
      active_from: this.form.value.yearFrom,
      active_till: this.form.value.yearTo,
      featured: this.form.value.featured,
      publish: this.form.value.publishProfile,
      show_filter: this.form.value.showFilter,
      url_name: this.form.value.url?.toLowerCase()?.trim(),
      catalogue_code: this.form.value.catalogue_code,
      email: this.selectedArtistObj.email,
      quotes: this.form.value.quoteAudio,
      socialUrl: this.form.value.socialMedia.map((a) => {
        a.url = a.url?.trim();
        return a;
      }),
      artist_type: this.selectedArtistObj?.role_id?.artist_type,
      intro_videos,
      opentonetwork: {
        enable: this.form.value.openToNetworkEnable,
        email: this.form.value.openToNetworkEmail,
        phone: this.form.value.openToNetworkPhone,
      },
      collection_ids: this.selected_exhibition.map((a) => a.id),
      extras: this.extras,
      // addresses: [
      //   {
      //     name: this.selectedArtistObj.fullName,
      //   },
      // ],
    };

    let url = apiUrl.signup;
    this.server.showSpinner();
    this.server.putApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode) {
        alert('Profile updated successfully!');
        if (window.location.href.includes('personal')) {
          // this.router.navigate(['/artist-portal/settings']);
          this.server.getUserDetails();
        } else {
          if (this.isGalleryEdit) {
            this.router.navigate(['/artist-portal/settings/invite-artist']);
          } else {
            // this.showPage = 'List';
            // this.getUsers();
          }
        }
      }
    });
  }

  isValidUrl(url) {
    if (!url || !url.match(/^https?:\/\/.*/)) {
      return false;
    }
    if (
      url.indexOf('javascript:') !== -1 ||
      url.indexOf('vbscript:') !== -1 ||
      url.indexOf('data:') !== -1
    ) {
      return false;
    }

    return true;
  }
}
