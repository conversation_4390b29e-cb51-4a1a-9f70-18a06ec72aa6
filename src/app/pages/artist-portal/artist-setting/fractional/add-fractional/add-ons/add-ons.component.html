<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="dynamicForm" novalidate autocomplete="off">
          <div class="splitter">
            <div class="field-value">
              <!-- readonly" -->
              <div
                class="input-container"
                (click)="showdrop = !showdrop"
                (focusout)="chooseDrop()"
              >
                <input
                  [(ngModel)]="componentSelector"
                  [ngModelOptions]="{ standalone: true }"
                  type="text"
                  class="selection"
                  [placeholder]="componentSelector"
                  [readonly]="true"
                />
                <button type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="showdrop ? 'dropdown-visible' : 'dropdown-hidden'"
                >
                  <ul>
                    <li
                      (click)="
                        onComponentSelect('textComponent', 'Text Component')
                      "
                    >
                      <div class="country-name">Text Component</div>
                    </li>
                    <li
                      (click)="onComponentSelect('introVideo', 'Intro Video')"
                    >
                      <div class="country-name">Intro Video</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('artworkSelect', 'Artwork Select')
                      "
                    >
                      <div class="country-name">Artwork Select</div>
                    </li>
                    <li (click)="onComponentSelect('quote', 'Quote')">
                      <div class="country-name">Quote</div>
                    </li>
                    <li
                      (click)="onComponentSelect('quoteMedia', 'Quote Media')"
                    >
                      <div class="country-name">Quote Media</div>
                    </li>
                    <li (click)="onComponentSelect('audio', 'Audio')">
                      <div class="country-name">Audio</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('artistProfile1', 'Artist Profile-1')
                      "
                    >
                      <div class="country-name">Artist Profile-1</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('artistProfile2', 'Artist Profile-2')
                      "
                    >
                      <div class="country-name">Artist Profile-2</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('bannerDivide', 'Banner Divide')
                      "
                    >
                      <div class="country-name">Banner Divide</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('artworkFeature', 'Artwork Feature')
                      "
                    >
                      <div class="country-name">Artwork Feature</div>
                    </li>
                  </ul>
                </div>
              </div>
              <!-- <div class="dropdown-collapse" *ngIf="showdrop">
                <p id="option" >Description</p>
                <p id="option" >Intro Video</p>
                <p id="option" >Artwork Select</p>
                <p id="option" >Quote</p>
                <p id="option" >Quote Media</p>
                <p id="option" >Audio</p>
                <p id="option" >Artist Profile-1</p>
                <p id="option" >Artist Profile-2</p>
                <p id="option" >Banner Divide</p>
              </div> -->

              <div class="input-info" *ngIf="!showdrop">
                Choose which component to use in the collection page.
              </div>
            </div>
            <div class="field-value">
              <button class="save" (click)="addNewComponent()">Add +</button>
            </div>
          </div>
          <div
            formArrayName="mainArr"
            [dragula]="'task-group'"
            [(dragulaModel)]="mainArr.controls"
          >
            <div
              [formGroupName]="i"
              class="outer-border"
              *ngFor="let item of mainArr.controls; let i = index"
            >
              <div
                class="textComponent"
                *ngIf="item.controls.compType.value == 'textComponent'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Text Component</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- <div class="field-value" [hidden]="!item.controls.collapse.value">
                  <div class="input-container">
                    <div class="text-before" (click)="value(item)">Show Description :</div>
                    <label class="switch">
                      <input type="checkbox" formControlName="showDescriptiona" />
                      <span class="slider round"></span>
                    </label>
                  </div>
                  <div class="input-info">
                    Choose whether to show the Exhibition Note.
                  </div>
                </div> -->
                <div [hidden]="!item.controls.collapse.value">
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Description
                  </div>
                  <div class="field-value">
                    <angular-editor
                      id="editor12"
                      formControlName="descriptionText"
                    >
                    </angular-editor>
                    <div class="input-info">
                      Provide the exhibition note to be shown on the page.
                    </div>
                  </div>
                  <div class="field-value">
                    <div class="input-container">
                      <div class="text-before">Enable Read More :</div>
                      <label class="switch">
                        <input
                          type="checkbox"
                          formControlName="EnableReadMore"
                        />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <div class="input-info">
                      Choose whether to show complete exhibition note, or hide
                      behind Read More.
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="item.controls.compType.value == 'introVideo'">
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Intro Video</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- <div class="field-value" [hidden]="!item.controls.collapse.value">
                  <div class="input-container">
                    <div class="text-before">Show Video :</div>
                    <label class="switch">
                      <input type="checkbox" formControlName="showVideoa" />
                      <span class="slider round"></span>
                    </label>
                  </div>
                  <div class="input-info">
                    Choose whether to show a video on the exhibition page.
                  </div>
                </div> -->
                <div
                  class="videoSection"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        formControlName="exhibitionVideo"
                        placeholder="Link"
                      />
                      <div class="placeholder">Exhibition Video</div>
                      <div class="input-info">
                        Provide the Vimeo player link for the video to be
                        featured on the exhibition page. e.g.:
                        https://www.player.vimeo.com/12342143.
                      </div>
                    </div>
                  </div>
                  <div style="margin-top: 2.08vw; font-size: 1.11vw">
                    Video Text
                  </div>
                  <div class="field-value">
                    <angular-editor
                      id="editor13"
                      formControlName="video_description"
                    >
                    </angular-editor>
                    <div class="input-info">
                      Provide the text to be displayed on the Video thumbnail.
                      e.g.: Artist XYZ talking about their works.
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="item.controls.compType.value == 'artworkSelect'">
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Artwork Select</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div
                  class="field-value"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div style="padding: 1rem; margin: 1.2rem 0; cursor: default">
                    <!-- /* border: 0.069vw solid var(--timeline-color); */ -->
                    <div>
                      <!-- <div class="field-value">
                        <div class="input-container">
                          <div class="text-before">Show Artworks :</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showArtworks"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show artworks on the Exhibition
                          page.
                        </div>
                      </div> -->
                      <div class="splitter">
                        <div class="field-value">
                          <div
                            tabindex="-1"
                            class="input-container"
                            (click)="
                              isArtworkDropdownOpen1[i] =
                                !isArtworkDropdownOpen1[i]
                            "
                            (focusout)="
                              changeFocus('isArtworkDropdownOpen1', i)
                            "
                          >
                            <input
                              type="text"
                              class="selection"
                              formControlName="artworkGrid"
                              placeholder="Artwork Grid"
                              [readonly]="true"
                            />
                            <div class="placeholder">Artwork Grid</div>
                            <button type="button">
                              <img
                                src="assets/icons/arrow-down.png"
                                class="flag-arrow"
                              />
                            </button>
                            <div
                              [ngClass]="
                                isArtworkDropdownOpen1[i]
                                  ? 'dropdown-visible'
                                  : 'dropdown-hidden'
                              "
                            >
                              <ul>
                                <li
                                  (click)="
                                    item.controls.artworkGrid.setValue('Single')
                                  "
                                >
                                  <div class="country-name">Single</div>
                                </li>
                                <li
                                  (click)="
                                    item.controls.artworkGrid.setValue('2x1')
                                  "
                                >
                                  <div class="country-name">2x1</div>
                                </li>
                                <li
                                  (click)="
                                    item.controls.artworkGrid.setValue('3x1')
                                  "
                                >
                                  <div class="country-name">3x1</div>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="input-info">
                            Choose the grid to display the artworks in.
                          </div>
                        </div>
                        <div class="field-value">
                          <div
                            class="input-container"
                            (click)="
                              isArtworkDropdownOpen3[i] =
                                !isArtworkDropdownOpen3[i]
                            "
                            (focusout)="isArtworkDropdownOpen3[i] = false"
                          >
                            <input
                              type="text"
                              class="selection"
                              formControlName="artworkInterior"
                              placeholder=""
                              readonly
                            />
                            <div class="placeholder">Artwork Interior</div>
                            <button type="button">
                              <img
                                src="assets/icons/arrow-down.png"
                                class="flag-arrow"
                              />
                            </button>
                            <div
                              [ngClass]="
                                isArtworkDropdownOpen3[i]
                                  ? 'dropdown-visible'
                                  : 'dropdown-hidden'
                              "
                            >
                              <ul>
                                <li
                                  (click)="
                                    item.controls.artworkInterior.setValue(
                                      'Artwork Page'
                                    )
                                  "
                                >
                                  <div class="country-name">Artwork Page</div>
                                </li>
                                <li
                                  (click)="
                                    item.controls.artworkInterior.setValue(
                                      'View In Room'
                                    )
                                  "
                                >
                                  <div class="country-name">View In Room</div>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="input-info">
                            Choose whether to link View In Room or Artwork
                            detail page for the artworks.
                          </div>
                        </div>
                      </div>
                      <div style="margin-top: 2.08vw; font-size: 1.25vw">
                        Artwork Select
                      </div>
                      <div class="contain_">
                        <div class="selection_container">
                          <div class="selection_headings">
                            <div class="property_title box">ID</div>
                            <div class="property_title box2">Primary Image</div>
                            <div class="property_title box2">Artist Name</div>
                            <div class="property_title box2">Artwork Title</div>
                            <div class="property_title box2">Year</div>
                            <div class="property_title box">Action</div>
                          </div>
                          <div class="selection_data">
                            <div
                              class="artwork_details"
                              *ngFor="
                                let option of selectedArtworksArr;
                                let j = index
                              "
                            >
                              <div class="properties box">
                                {{ option?._id }}
                              </div>
                              <div class="properties box2">
                                <img
                                  [src]="
                                    option?.primary_image.length > 0
                                      ? option?.primary_image[0].url
                                      : 'https://picsum.photos/200/300'
                                  "
                                  alt="Artwork Thumbanail"
                                />
                              </div>
                              <div class="properties box2">
                                {{ option?.artist_id?.display_name }}
                              </div>
                              <div class="properties box2">
                                {{ option?.artwork_title }}
                              </div>
                              <div class="properties box2">
                                {{ option?.endYear }}
                              </div>
                              <div class="properties box">
                                <p
                                  class="close_icon"
                                  (click)="selectedArtworksArr.splice(j, 1)"
                                >
                                  &times;
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                        <button class="select" (click)="openArtworks()">
                          Select Existing
                        </button>
                      </div>
                      <div class="input-info">
                        Choose the artworks to be shown in the artwork grid
                      </div>
                      <div class="splitter">
                        <div class="field-value">
                          <div class="input-container">
                            <div class="text-before">Show Price :</div>
                            <label class="switch">
                              <input
                                type="checkbox"
                                formControlName="showPrice"
                              />
                              <span class="slider round"></span>
                            </label>
                          </div>
                          <div class="input-info">
                            Choose whether to show artwork price and Add to Cart
                            button on the page.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="input-info">
                    Choose 4 to 8 artworks to feature on the Exhibitions page
                  </div>
                </div>
              </div>

              <div
                class="Quote"
                *ngIf="item.controls.compType.value == 'quote'"
              >
                <div>
                  <div>
                    <div class="row">
                      <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                        <span
                          class="cross-icon"
                          style="cursor: grab !important"
                        >
                          <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                          <span style="margin-left: 1vw">{{ i + 1 }}</span>
                        </span>
                      </div>
                      <div class="col-md-4 col-lg-4 col-sm-4">
                        <label class="main-title">Quote</label>
                      </div>
                      <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                        <div class="SwitchInBar">
                          {{
                            item.controls.publish.value ? "Publish" : "Draft"
                          }}
                          <label class="switch">
                            <input type="checkbox" formControlName="publish" />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <button type="button" class="eye-icon">
                          <span
                            *ngIf="item.controls.collapse.value == true"
                            (click)="
                              collapseForm(item.controls.collapse.value, i)
                            "
                          >
                            <fa-icon [icon]="faEye"></fa-icon>
                          </span>
                          <span
                            *ngIf="item.controls.collapse.value == false"
                            (click)="
                              collapseForm(item.controls.collapse.value, i)
                            "
                          >
                            <fa-icon [icon]="faEyeSlash"></fa-icon>
                          </span>
                          <span
                            *ngIf="item.controls.collapse.value == false"
                            (click)="deleteRow(i)"
                          >
                            <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                          </span>
                        </button>
                      </div>
                    </div>
                    <div
                      class="field-value doted"
                      style="position: relative"
                      [hidden]="!item.controls.collapse.value"
                    >
                      <!-- <div class="field-value">
                        <div class="input-container">
                          <div class="text-before">Show Quote :</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showQuote"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show the quote on page.
                        </div>
                      </div> -->
                      <div style="margin-top: 2.08vw; font-size: 1.25vw">
                        Quote Text
                      </div>
                      <div class="field-value">
                        <angular-editor
                          id="editor14"
                          formControlName="quoteText"
                        >
                        </angular-editor>
                        <div class="input-info">
                          Provide the artist quote to be displayed on the page.
                        </div>
                      </div>
                      <!-- <img
                        (click)="setDeleteSelection('quote', i)"
                        src="assets/icons/grey-close.png"
                        title="Delete this "
                        style="
                          cursor: pointer;
                          width: 1.9vw;
                          right: 0.5vw;
                          top: 0.5vw;
                          position: absolute;
                        "
                      /> -->
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="item.controls.compType.value == 'quoteMedia'">
                <div>
                  <div class="row">
                    <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                      <span class="cross-icon" style="cursor: grab !important">
                        <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                        <span style="margin-left: 1vw">{{ i + 1 }}</span>
                      </span>
                    </div>
                    <div class="col-md-4 col-lg-4 col-sm-4">
                      <label class="main-title">Quote Media</label>
                    </div>
                    <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                      <div class="SwitchInBar">
                        {{ item.controls.publish.value ? "Publish" : "Draft" }}
                        <label class="switch">
                          <input type="checkbox" formControlName="publish" />
                          <span class="slider round"></span>
                        </label>
                      </div>
                      <button type="button" class="eye-icon">
                        <span
                          *ngIf="item.controls.collapse.value == true"
                          (click)="
                            collapseForm(item.controls.collapse.value, i)
                          "
                        >
                          <fa-icon [icon]="faEye"></fa-icon>
                        </span>
                        <span
                          *ngIf="item.controls.collapse.value == false"
                          (click)="
                            collapseForm(item.controls.collapse.value, i)
                          "
                        >
                          <fa-icon [icon]="faEyeSlash"></fa-icon>
                        </span>
                        <span
                          *ngIf="item.controls.collapse.value == false"
                          (click)="deleteRow(i)"
                        >
                          <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                        </span>
                      </button>
                    </div>
                  </div>
                  <div
                    class="field-value"
                    [hidden]="!item.controls.collapse.value"
                  >
                    <div style="margin-top: 2.08vw; font-size: 1.25vw">
                      Quote
                    </div>
                    <div class="field-value">
                      <angular-editor
                        id="editor15"
                        formControlName="quoteData"
                      ></angular-editor>
                      <div class="input-info">
                        Provide the artist quote to be displayed on the page
                      </div>
                    </div>
                    <div style="margin-top: 2.08vw; font-size: 1.25vw">
                      Quote Source
                    </div>
                    <div class="field-value">
                      <angular-editor
                        id="editor16"
                        formControlName="quoteSrc"
                      ></angular-editor>
                      <div class="input-info">
                        Provide the source of the quote to be displayed on the
                        page
                      </div>
                    </div>

                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <div
                          class="input-container"
                          (click)="
                            isQuoteMediaDropdownOpen1[i] =
                              !isQuoteMediaDropdownOpen1[i]
                          "
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="quoteMediaPosition"
                            placeholder="-Select-"
                            [readonly]="readonly"
                          />
                          <div class="placeholder">Quote Media Position</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="
                              isQuoteMediaDropdownOpen1[i]
                                ? 'dropdown-visible'
                                : 'dropdown-hidden'
                            "
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.quoteMediaPosition.setValue(
                                    'Left'
                                  )
                                "
                              >
                                <div class="country-name">Left</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.quoteMediaPosition.setValue(
                                    'Right'
                                  )
                                "
                              >
                                <div class="country-name">Right</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">
                          Choose where to show the media
                        </div>
                      </div>
                      <div class="field-value">
                        <div
                          class="input-container"
                          (click)="
                            isQuoteMediaDropdownOpen2[i] =
                              !isQuoteMediaDropdownOpen2[i]
                          "
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="quoteMedia"
                            placeholder="-Select-"
                            [readonly]="readonly"
                          />
                          <div class="placeholder">Quote Media</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="
                              isQuoteMediaDropdownOpen2[i]
                                ? 'dropdown-visible'
                                : 'dropdown-hidden'
                            "
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.quoteMedia.setValue('Image')
                                "
                              >
                                <div class="country-name">Image</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.quoteMedia.setValue('Video')
                                "
                              >
                                <div class="country-name">Video</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.quoteMedia.setValue('GIF')
                                "
                              >
                                <div class="country-name">GIF</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">Choose quote media type</div>
                      </div>
                    </div>
                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="number"
                          class="selection"
                          formControlName="quoteSize"
                          placeholder="size"
                          style="width: 97%"
                        />
                        <div class="placeholder">Quote Size</div>

                        <div class="input-info">
                          Provide the font size for the Quote (between 1 and 2)
                        </div>
                      </div>
                      <div class="field-value">
                        <div
                          class="input-container"
                          (click)="
                            isQuoteMediaDropdownOpen3[i] =
                              !isQuoteMediaDropdownOpen3[i]
                          "
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="quoteMediaBackground"
                            placeholder="-Select-"
                            [readonly]="readonly"
                          />
                          <div class="placeholder">Quote Media Background</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="
                              isQuoteMediaDropdownOpen3[i]
                                ? 'dropdown-visible'
                                : 'dropdown-hidden'
                            "
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.quoteMediaBackground.setValue(
                                    'No Background'
                                  )
                                "
                              >
                                <div class="country-name">No Background</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.quoteMediaBackground.setValue(
                                    'Grey'
                                  )
                                "
                              >
                                <div class="country-name">Grey</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">
                          Choose whether to have background colour for the quote
                          component
                        </div>
                      </div>
                    </div>
                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          class="selection"
                          formControlName="quoteLinkOrText"
                          placeholder="Text or Link"
                          style="width: 97%"
                        />
                        <div class="placeholder">Quote Media URL</div>

                        <div class="input-info">
                          Upload the media or provide the Vimeo player URL
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="playAudio"
                *ngIf="item.controls.compType.value == 'audio'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Audio</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>

                <div *ngIf="item.controls.collapse.value">
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        formControlName="audioLevel"
                        placeholder="Audio level"
                      />
                      <div class="placeholder">Audio Level</div>
                      <div class="input-info">
                        Provide the audio level in (in percentage). e.g.: 20.
                      </div>
                    </div>
                  </div>
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Audio File
                  </div>
                  <div class="field-value flex-block">
                    <image-upload
                      [accept]="'audio/*'"
                      [selectedData]="selectedFilesObj?.audioArr"
                      (onFileChange)="onFileSelect($event, 'audioArr')"
                      [fileSize]="8388608"
                      [placeholder]="placeholder"
                    ></image-upload>

                    <div class="input-info">
                      Import the mp3 audio file. Provide file size less than 3
                      mb..
                    </div>
                  </div>
                  <div class="field-value">
                    <div class="input-container">
                      <div class="text-before">Audio Loop :</div>
                      Loop &nbsp;
                      <label class="switch">
                        <input type="checkbox" formControlName="audioLoop" />
                        <span class="slider round"></span>
                      </label>
                      &nbsp; Play Once
                    </div>
                    <div class="input-info">
                      Choose whether to play the audio in loop, or one time.
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="ArtistProfile1"
                *ngIf="item.controls.compType.value == 'artistProfile1'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Artist Profile-1</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- <div
                  class="field-value"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div class="input-container">
                    <div class="text-before">Show Artist Profile:</div>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="showArtistProfile1"
                      />
                      <span class="slider round"></span>
                    </label>
                  </div>
                  <div class="input-info">
                    Choose whether to display the artist profile on the
                    exhibition page.
                  </div>
                </div> -->
                <div [hidden]="!item.controls.collapse.value">
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Artist Profile
                  </div>
                  <div class="field-value">
                    <angular-editor
                      id="editor17"
                      formControlName="profileText"
                    ></angular-editor>
                    <div class="input-info">
                      Provide the Artist Profile text to be used.
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        placeholder="profileUrl"
                        formControlName="profileUrl"
                      />
                      <div class="placeholder">Profile URL</div>
                      <div class="input-info">
                        Provide the complete URL for the artist's profile page.
                      </div>
                    </div>
                    <div class="field-value">
                      <div
                        class="input-container"
                        (click)="artistMedia1[i] = !artistMedia1[i]"
                        (focusout)="artistMedia1[i] = false"
                      >
                        <input
                          type="text"
                          class="selection"
                          formControlName="artistMedia"
                          placeholder="item.controls.artistMedia.value"
                          readonly
                        />
                        <div class="placeholder">Artist Media Type</div>
                        <button type="button">
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !artistMedia1[i],
                            'dropdown-visible': artistMedia1[i]
                          }"
                        >
                          <ul>
                            <li
                              (click)="
                                item.controls.artistMedia.setValue('Image');
                                artistMediaType = 'image/*'
                              "
                            >
                              <div class="country-name">Image</div>
                            </li>
                            <li
                              (click)="
                                item.controls.artistMedia.setValue('Video');
                                artistMediaType = 'video/*'
                              "
                            >
                              <div class="country-name">Video</div>
                            </li>
                            <li
                              (click)="
                                item.controls.artistMedia.setValue('GIF');
                                artistMediaType = 'image/*'
                              "
                            >
                              <div class="country-name">GIF</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div class="input-info">
                        Choose the type of Banner to use
                      </div>
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        placeholder="Button Text"
                        formControlName="btnText"
                      />
                      <div class="placeholder">Button Text</div>
                      <div class="input-info">
                        Provide the text to be used on the Button.
                      </div>
                    </div>
                  </div>
                  <div
                    class=""
                    *ngIf="
                      item.controls.artistMedia.value === 'Image' ||
                      item.controls.artistMedia.value === 'GIF'
                    "
                  >
                    <div class="field-value" style="padding-right: 0.5vw">
                      <image-upload
                        [accept]="artistMediaType"
                        [selectedData]="selectedFilesObj?.artistMediaTypeArr"
                        (onFileChange)="
                          onFileSelect($event, 'artistMediaTypeArr')
                        "
                        [fileSize]="8388608"
                        [placeholder]="placeholder"
                      ></image-upload>
                    </div>
                  </div>

                  <div
                    class="video-type"
                    *ngIf="item.controls.artistMedia.value == 'Video'"
                  >
                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          placeholder="URL"
                          formControlName="mediaUrl"
                        />
                        <div class="placeholder">Artist Profile Media URL</div>
                        <div class="input-info">
                          Provide the Vimeo player link for the video to be
                          featured as title banner. e.g.:
                          https://www.player.vimeo.com/12342143.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="field-value">
                    <div class="input-container">
                      <div class="text-before">Show Profile Link:</div>
                      <label class="switch">
                        <input
                          type="checkbox"
                          formControlName="showProfileLink"
                        />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <div class="input-info">
                      Choose whether to link the Artist's profile page.
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="ArtistProfile2"
                *ngIf="item.controls.compType.value == 'artistProfile2'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Artist Profile-2</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <!-- <div
                  class="field-value"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div class="input-container">
                    <div class="text-before">Show Artist Profile:</div>
                    <label class="switch">
                      <input
                        type="checkbox"
                        formControlName="showArtistProfile2"
                      />
                      <span class="slider round"></span>
                    </label>
                  </div>
                  <div class="input-info">
                    Choose whether to display the artist profile on the
                    exhibition page.
                  </div>
                </div> -->
                <div
                  [hidden]="!item.controls.collapse.value"
                  style="cursor: default"
                >
                  <div style="margin-top: 3.47vw; font-size: 1.25vw">
                    Artist Profile
                  </div>
                  <div class="field-value">
                    <angular-editor
                      id="editor18"
                      formControlName="artistProfileText"
                    >
                    </angular-editor>
                    <div class="input-info">
                      Provide a short bio for the Gallery (less than 700
                      characters with spaces) to be used on the profile page.
                    </div>
                  </div>

                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Artist Media
                    <img
                      class="plus-icon"
                      (click)="addIntoProfileArr(i)"
                      src="assets/images/load-more.png"
                      style="width: 1.9vw; margin-left: 2vw"
                    />
                  </div>
                  <div class="field-value">
                    <div
                      formArrayName="artistProfileArr"
                      *ngFor="
                        let sub_item of item.controls.artistProfileArr.controls;
                        let j = index
                      "
                    >
                      <div
                        [formGroupName]="j"
                        style="
                          padding: 1rem;
                          border: 0.069vw solid var(--timeline-color);
                          margin: 1.2rem 0;
                          position: relative;
                        "
                      >
                        <div style="margin-top: 1.08vw; font-size: 12px;margin-bottom:10px">
                          Profile Image
                        </div>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr0" (onFileChange)="onFileSelect($event, 'profileImageArr0')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==0"></image-upload>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr1" (onFileChange)="onFileSelect($event, 'profileImageArr1')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==1"></image-upload>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr2" (onFileChange)="onFileSelect($event, 'profileImageArr2')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==2"></image-upload>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr3" (onFileChange)="onFileSelect($event, 'profileImageArr3')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==3"></image-upload>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr4" (onFileChange)="onFileSelect($event, 'profileImageArr4')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==4"></image-upload>
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.profileImageArr5" (onFileChange)="onFileSelect($event, 'profileImageArr5')" [fileSize]="8388608" [placeholder]="placeholder" *ngIf="j==5"></image-upload>

                        <div class="splitter">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Artist Name" formControlName="artistName"/>
                            <div class="placeholder">Artist Name</div>
                            <div class="input-info">
                              Provide the Artist Name for the Artist Profile
                              Page .
                            </div>
                          </div>
                        </div>

                        <div class="field-value">
                          <div class="input-container">
                            <div class="text-before" (click)="value(sub_item)">
                              Show URL :
                            </div>
                            <label class="switch">
                              <input
                                type="checkbox"
                                formControlName="showUrl"
                              />
                              <span class="slider round"></span>
                            </label>
                          </div>
                        </div>
                        <div
                          
                        >
                          <div class="splitter">
                            <div
                              class="field-value"
                              style="padding-right: 0.5vw"
                            >
                              <input
                                type="text"
                                placeholder="Profile Url"
                                formControlName="profileUrl"
                              />
                              <div class="placeholder">Profile URL</div>
                              <div class="input-info">
                                Provide the url for the Artist Profile Page .
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="splitter">
                          <div class="field-value">
                            <div
                              class="input-container"
                              (click)="postion[j] = !postion[j]"
                            >
                              <input
                                type="text"
                                class="selection"
                                placeholder="Position"
                                formControlName="position"
                              />
                              <div class="placeholder">Position</div>
                              <button type="button">
                                <img
                                  src="assets/icons/arrow-down.png"
                                  class="flag-arrow"
                                />
                              </button>
                              <div
                                [ngClass]="{
                                  'dropdown-hidden': !postion[j],
                                  'dropdown-visible': postion[j]
                                }"
                              >
                                <ul>
                                  <li
                                    (click)="
                                      item.controls.artistProfileArr.controls[
                                        j
                                      ].controls.position.setValue('Left')
                                    "
                                  >
                                    <div class="country-name">Left</div>
                                  </li>
                                  <li
                                    (click)="
                                      item.controls.artistProfileArr.controls[
                                        j
                                      ].controls.position.setValue('Right')
                                    "
                                  >
                                    <div class="country-name">Right</div>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                        <img
                          (click)="setDeleteSelection('artistProfileMedia', i)"
                          src="assets/icons/grey-close.png"
                          title="Delete this"
                          style="
                            cursor: pointer;
                            width: 1.9vw;
                            float: right;
                            right: 0vw;
                            top: 0vw;
                            position: absolute;
                          "
                        />
                      </div>
                    </div>
                    <div class="input-info">
                      Provide the image and text to be used with the profile
                      details
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="Banner_section"
                *ngIf="item.controls.compType.value == 'bannerDivide'"
              >
                <!-- <div class="field-value doted"> -->
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Banner Divide</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div
                  class="field-value"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div>
                    <!-- <div class="field-value">
                      <div class="input-container">
                        <div class="text-before">Show Banner :</div>
                        <label class="switch">
                          <input type="checkbox" formControlName="showBanner" />
                          <span class="slider round"></span>
                        </label>
                      </div>
                      <div class="input-info">
                        Choose whether to show the banner video/image.
                      </div>
                    </div> -->

                    <div class="splitter">
                      <div class="field-value">
                        <div
                          class="input-container"
                          (click)="bannerType1[i] = !bannerType1[i]"
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="bannerType"
                            placeholder="Banner Type"
                            readonly
                          />
                          <div class="placeholder">Banner Type</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="{
                              'dropdown-hidden': !bannerType1[i],
                              'dropdown-visible': bannerType1[i]
                            }"
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.bannerType.setValue('Image');
                                  accept = 'image/*'
                                "
                              >
                                <div class="country-name">Image</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.bannerType.setValue('Video');
                                  accept = 'video/*'
                                "
                              >
                                <div class="country-name">Video</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.bannerType.setValue('GIF');
                                  accept = 'image/*'
                                "
                              >
                                <div class="country-name">GIF</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">
                          Choose the type of Banner to use
                        </div>
                      </div>
                    </div>

                    <div
                      class=""
                      *ngIf="
                        item.controls.bannerType.value === 'Image' ||
                        item.controls.bannerType.value === 'GIF'
                      "
                    >
                      <div class="field-value" style="padding-right: 0.5vw">
                        <image-upload
                          [accept]="accept"
                          [selectedData]="selectedFilesObj?.bannerArr"
                          (onFileChange)="onFileSelect($event, 'bannerArr')"
                          [fileSize]="8388608"
                          [placeholder]="placeholder"
                        ></image-upload>
                      </div>
                    </div>

                    <div
                      class="video-type"
                      *ngIf="item.controls.bannerType.value == 'Video'"
                    >
                      <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                          <input
                            type="text"
                            placeholder="URL"
                            formControlName="videoUrlBanner"
                          />
                          <div class="placeholder">
                            Artist Profile Media URL
                          </div>
                          <div class="input-info">
                            Provide the Vimeo player link for the video to be
                            featured as title banner. e.g.:
                            https://www.player.vimeo.com/12342143.
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <div class="input-container">
                          <div class="text-before">Show Title</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showTitle"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show title text on the banner.
                        </div>
                      </div>
                    </div>
                    <div class="splitter" *ngIf="item.controls.showTitle.value">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          placeholder="Title Text"
                          formControlName="titleText"
                        />
                        <div class="placeholder">Title Text</div>
                        <div class="input-info">
                          Provide the Title text for the banner.
                        </div>
                      </div>
                    </div>

                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <div class="input-container">
                          <div class="text-before">Show Buttons</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showButtons"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show buttons on the banner.
                        </div>
                      </div>
                    </div>
                    <div
                      class="splitter"
                      *ngIf="item.controls.showButtons.value"
                    >
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          placeholder="Button 1 Text"
                          formControlName="button1Text"
                        />
                        <div class="placeholder">Button 1 Text</div>
                        <div class="input-info">
                          Provide the text for Button 1.
                        </div>
                      </div>
                      <div class="field-value">
                        <input
                          type="text"
                          placeholder="Button 1 URL"
                          formControlName="button1Url"
                        />
                        <div class="placeholder">Button 1 URL</div>
                        <div class="input-info">
                          Provide the complete URL for Button 1.
                        </div>
                      </div>
                    </div>
                    <!-- button 2 -->
                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <div class="input-container">
                          <div class="text-before">Show Button 2</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showButton2"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show button 2 .
                        </div>
                      </div>
                    </div>
                    <div
                      class="splitter"
                      *ngIf="item.controls.showButton2.value"
                    >
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          placeholder="Button 2 Text"
                          formControlName="button2Text"
                        />
                        <div class="placeholder">Button 2 Text</div>
                        <div class="input-info">
                          Provide the text for Button 2.
                        </div>
                      </div>
                      <div class="field-value">
                        <input
                          type="text"
                          placeholder="Button 2 URL"
                          formControlName="button2Url"
                        />
                        <div class="placeholder">Button 2 URL</div>
                        <div class="input-info">
                          Provide the complete URL for Button 2.
                        </div>
                      </div>
                    </div>
                    <!-- button 3 -->
                    <div class="splitter">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <div class="input-container">
                          <div class="text-before">Show Button 3</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showButton3"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show button 3 .
                        </div>
                      </div>
                    </div>
                    <div
                      class="splitter"
                      *ngIf="item.controls.showButton3.value"
                    >
                      <div class="field-value" style="padding-right: 0.5vw">
                        <input
                          type="text"
                          placeholder="Button 3 Text"
                          formControlName="button3Text"
                        />
                        <div class="placeholder">Button 3 Text</div>
                        <div class="input-info">
                          Provide the text for Button 3.
                        </div>
                      </div>
                      <div class="field-value">
                        <input
                          type="text"
                          placeholder="Button 3 URL"
                          formControlName="button3Url"
                        />
                        <div class="placeholder">Button 3 URL</div>
                        <div class="input-info">
                          Provide the complete URL for Button 3.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- </div> -->
              </div>

              <!-- =========================================================================================== -->

              <div
                class="Artwork Feature"
                *ngIf="item.controls.compType.value == 'artworkFeature'"
              >
                <!-- <div class="field-value doted"> -->
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Artwork Feature</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEye"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                      >
                        <fa-icon [icon]="faEyeSlash"></fa-icon>
                      </span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                      >
                        <fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div
                  class="field-value"
                  [hidden]="!item.controls.collapse.value"
                >
                  <div>
                    <!-- <div class="field-value">
                    <div class="input-container">
                      <div class="text-before">Show Banner :</div>
                      <label class="switch">
                        <input type="checkbox" formControlName="showBanner" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                    <div class="input-info">
                      Choose whether to show the banner video/image.
                    </div>
                  </div> -->

                    <div class="splitter">
                      <div class="field-value">
                        <div
                          class="input-container"
                          (click)="imageTypeDrop[i] = !imageTypeDrop[i]"
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="imageType"
                            placeholder="Image Type"
                            readonly
                          />
                          <div class="placeholder">Image Type</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="{
                              'dropdown-hidden': !imageTypeDrop[i],
                              'dropdown-visible': imageTypeDrop[i]
                            }"
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.imageType.setValue(
                                    'Single Image'
                                  )
                                "
                              >
                                <div class="country-name">Single Image</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.imageType.setValue('Grid')
                                "
                              >
                                <div class="country-name">Grid</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">
                          Choose whether to use a single image file, or grid of
                          images
                        </div>
                      </div>
                    </div>

                    <div class="upload-head">Image upload</div>

                    <div class="field-value flex-block">
                      <image-upload
                        [accept]="'image/*'"
                        [selectedData]="selectedFilesObj?.imageUrls"
                        (onFileChange)="onFileSelect($event, 'imageUrls')"
                        [fileSize]="2147483648"
                        [placeholder]="placeholder"
                      ></image-upload>

                      <div class="input-info">
                        Upload a high res file or provide link to the high res
                        file. This will be sent to the buyer upon confirmation
                        of payment.
                      </div>
                    </div>

                    <div class="splitter">
                      <div class="field-value">
                        <div
                          class="input-container"
                          (click)="griddrop1[i] = !griddrop1[i]"
                        >
                          <input
                            type="text"
                            class="selection"
                            formControlName="griddimensions"
                            placeholder="Grid dimensions"
                            readonly
                          />
                          <div class="placeholder">Grid dimensions</div>
                          <button type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                          <div
                            [ngClass]="{
                              'dropdown-hidden': !griddrop1[i],
                              'dropdown-visible': griddrop1[i]
                            }"
                          >
                            <ul>
                              <li
                                (click)="
                                  item.controls.griddimensions.setValue('3x3')
                                "
                              >
                                <div class="country-name">3x3</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.griddimensions.setValue('9x9')
                                "
                              >
                                <div class="country-name">9x9</div>
                              </li>
                              <li
                                (click)="
                                  item.controls.griddimensions.setValue('12x12')
                                "
                              >
                                <div class="country-name">12x12</div>
                              </li>
                            </ul>
                          </div>
                        </div>
                        <div class="input-info">
                          Provide the grid dimensions (3x3, 9x9, 12x12, etc)
                        </div>
                      </div>
                    </div>

                    <div class="upload-head">Grid image upload</div>

                    <div class="field-value flex-block">
                      <image-upload
                        [accept]="'image/*'"
                        [selectedData]="selectedFilesObj?.gridUrls"
                        (onFileChange)="onFileSelect($event, 'gridUrls')"
                        [fileSize]="2147483648"
                        [placeholder]="placeholder"
                      ></image-upload>

                      <div class="input-info">
                        Upload the images as per the grid selected
                      </div>
                    </div>

                    <div style="margin-top: 2.08vw; font-size: 1.25vw">
                      Text Section
                      <img
                        class="plus-icon"
                        (click)="addIntotextArr(i)"
                        src="assets/images/load-more.png"
                        style="width: 1.9vw; margin-left: 2vw"
                      />
                    </div>
                    <div class="field-value">
                      <div
                        formArrayName="textArr"
                        *ngFor="
                          let sub_item of item.controls.textArr.controls;
                          let j = index
                        "
                      >
                        <div
                          [formGroupName]="j"
                          style="
                            padding: 1rem;
                            border: 0.069vw solid var(--timeline-color);
                            margin: 1.2rem 0;
                            position: relative;
                          "
                        >
                          <img
                            (click)="DeleteText(i, j)"
                            src="assets/icons/grey-close.png"
                            title="Delete this"
                            style="
                              cursor: pointer;
                              width: 1.9vw;
                              float: right;
                              right: 0vw;
                              top: 0vw;
                              position: absolute;
                            "
                          />
                          <div class="splitter">
                            <div
                              class="field-value"
                              style="padding-right: 0.5vw"
                            >
                              <div class="input-container">
                                <div class="text-before">Show Text</div>
                                <label class="switch">
                                  <input
                                    type="checkbox"
                                    formControlName="showText"
                                  />
                                  <span class="slider round"></span>
                                </label>
                              </div>
                              <div class="input-info">
                                Choose whether to show text on the Artwork
                                Feature.
                              </div>
                            </div>
                          </div>
                          <div
                            class="nothing"
                            [hidden]="
                              !item.controls.textArr.controls[j].controls
                                .showText.value
                            "
                          >
                            <div style="margin-top: 2.08vw; font-size: 1.25vw">
                              Text
                            </div>
                            <div class="field-value">
                              <angular-editor
                                id="editor19"
                                formControlName="showTextData"
                              >
                              </angular-editor>
                              <div class="input-info">
                                Provide the text to show on the image.
                              </div>
                            </div>
                            <div class="splitter">
                              <div
                                class="field-value"
                                style="padding-right: 0.5vw"
                              >
                                <div
                                  class="input-container"
                                  (click)="textPosition[j] = !textPosition[j]"
                                >
                                  <input
                                    type="text"
                                    class="selection"
                                    formControlName="textPosition"
                                    placeholder="Text position"
                                    readonly
                                  />
                                  <div class="placeholder">Text position</div>
                                  <button type="button">
                                    <img
                                      src="assets/icons/arrow-down.png"
                                      class="flag-arrow"
                                    />
                                  </button>
                                  <div
                                    [ngClass]="{
                                      'dropdown-hidden': !textPosition[j],
                                      'dropdown-visible': textPosition[j]
                                    }"
                                  >
                                    <ul>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Top Left'
                                          )
                                        "
                                      >
                                        <div class="country-name">Top Left</div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Top Center'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Top Center
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Top Right'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Top Right
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Middle Left'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Middle Left
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Middle Center'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Middle Center
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Middle Right'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Middle Right
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Bottom Left'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Bottom Left
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Bottom Center'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Bottom Center
                                        </div>
                                      </li>
                                      <li
                                        (click)="
                                          item.controls.textArr.controls[
                                            j
                                          ].controls.textPosition.setValue(
                                            'Bottom Right'
                                          )
                                        "
                                      >
                                        <div class="country-name">
                                          Bottom Right
                                        </div>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="input-info">
                                  Choose the position to display the text
                                </div>
                              </div>
                              <div class="field-value">
                                <input
                                  type="number"
                                  placeholder="X Axis"
                                  formControlName="xAxis"
                                />
                                <div class="placeholder">X Axis</div>
                                <div class="input-info">
                                  Provide the center X Axis value
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="splitter">
                            <div
                              class="field-value"
                              style="padding-right: 0.5vw"
                            >
                              <input
                                type="number"
                                formControlName="zoomLevel"
                                placeholder="Zoom level"
                              />
                              <div class="placeholder">Zoom level</div>
                              <div class="input-info">
                                Provide the zoom level to use on the images
                              </div>
                            </div>
                            <div class="field-value">
                              <input
                                type="number"
                                placeholder="Y Axis"
                                formControlName="yAxis"
                              />
                              <div class="placeholder">Y Axis</div>
                              <div class="input-info">
                                Provide the center Y Axis value
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- </div> -->
              </div>
            </div>
          </div>

          <!-- artwork select mopdal -->
          <div
            class="artworkModal"
            [style.display]="isArtworkPopupOpen ? 'block' : 'none'"
          >
            <!-- Modal content -->
            <div class="artwork-modal-content">
              <div class="Addselection_container">
                <div class="Addtitle_bar">
                  Select Existing
                  <p class="Addclose_icon" (click)="isArtworkPopupOpen = false">
                    &times;
                  </p>
                </div>
                <div class="Addsearch_bar">
                  <div class="search_container">
                    <div class="fieldd-value">
                      <input
                        type="text"
                        placeholder="Search"
                        [(ngModel)]="searchKey"
                        [ngModelOptions]="{ standalone: true }"
                        (keyup)="searchResult($event)"
                      />
                    </div>
                  </div>
                  <div class="filter_container">
                    <div class="fieldd-value">
                      <div
                        class="input-container"
                        style="width: 12vw !important"
                      >
                        <input
                          [ngModelOptions]="{ standalone: true }"
                          [(ngModel)]="filter"
                          [placeholder]="filter"
                          type="text"
                          class="selection"
                          readonly
                        />
                        <button
                          (click)="isFilterDropdownOpen = !isFilterDropdownOpen"
                          type="button"
                        >
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !isFilterDropdownOpen,
                            'dropdown-visible': isFilterDropdownOpen
                          }"
                        >
                          <ul>
                            <!-- <li (click)="
                                isFilterDropdownOpen = false;
                                dynamicForm
                                  .get('filter')
                                  .setValue('Artist Name')
                              ">
                                                            <div class="country-name">Artist Name</div>
                                                        </li> -->
                            <li
                              (click)="
                                isFilterDropdownOpen = false;
                                filter = 'Artwork Title'
                              "
                            >
                              <div class="country-name">Artwork Title</div>
                            </li>
                            <!-- <li (click)="
                                isFilterDropdownOpen = false;
                                dynamicForm.get('filter').setValue('ID')
                              ">
                                                            <div class="country-name">ID</div>
                                                        </li> -->
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="fieldd-value">
                      <div
                        class="input-container"
                        style="width: 7vw !important"
                      >
                        <input
                          [(ngModel)]="sort"
                          [ngModelOptions]="{ standalone: true }"
                          placeholder="sort"
                          type="text"
                          class="selection"
                          readonly
                        />
                        <button
                          (click)="
                            isFilterDropdownOpen1 = !isFilterDropdownOpen1
                          "
                          type="button"
                        >
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !isFilterDropdownOpen1,
                            'dropdown-visible': isFilterDropdownOpen1
                          }"
                        >
                          <ul>
                            <li
                              (click)="
                                isFilterDropdownOpen1 = false; sort = 'Desc'
                              "
                            >
                              <div class="country-name">&uarr;</div>
                            </li>
                            <li
                              (click)="
                                isFilterDropdownOpen1 = false; sort = 'Asc'
                              "
                            >
                              <div class="country-name">&darr;</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="Addselection_headings">
                  <div class="Addproperty_title Addbox">
                    <input type="checkbox" name="title" id="title" disabled />
                  </div>
                  <div class="Addproperty_title Addbox">ID</div>
                  <div class="Addproperty_title Addbox2">Primary Image</div>
                  <div class="Addproperty_title Addbox2">Artist Name</div>
                  <div class="Addproperty_title Addbox2">Artwork Title</div>
                </div>
                <div class="Addselection_data" *ngFor="let item of artworksArr">
                  <div class="Addartwork_details">
                    <div class="Addproperties Addbox">
                      <input
                        type="checkbox"
                        [(ngModel)]="item.selected"
                        [ngModelOptions]="{ standalone: true }"
                      />
                    </div>
                    <div class="Addproperties Addbox">
                      {{ item?._id | slice: 0:10 }}
                    </div>
                    <div class="Addproperties Addbox2">
                      <img
                        [src]="
                          item?.primary_image.length > 0
                            ? item?.primary_image[0].url
                            : 'https://picsum.photos/200/300'
                        "
                        alt="Artwork Thumbanail"
                      />
                    </div>
                    <div class="Addproperties Addbox2">
                      {{ item?.artist_id?.display_name }}
                    </div>
                    <div class="Addproperties Addbox2">
                      {{ item?.artwork_title }}
                    </div>
                  </div>
                </div>
                <div class="Addfooter_bar" *ngIf="artworksArr.length != 0">
                  <button
                    class="Addselect"
                    (click)="isArtworkPopupOpen = false"
                  >
                    Cancel
                  </button>
                  <button
                    class="Addselect"
                    style="margin-left: 10px"
                    (click)="manageSelectedArtworks()"
                  >
                    Done
                  </button>

                  <span class="d-flex" style="gap: 2vw; float: left">
                    <span class="records"
                      >found {{ totalRecords }} records</span
                    >
                    <span class="d-flex" style="gap: 0.7vw">
                      <fa-icon
                        [icon]="faAngleLeft"
                        (click)="managePagination('prev')"
                      ></fa-icon>
                      Page
                      <input
                        style="height: 1vw; width: 1vw"
                        type="number"
                        [max]="totalPage"
                        [(ngModel)]="offset"
                        min="1"
                        name="pager"
                      />
                      Of {{ totalPage }}
                      <fa-icon
                        [icon]="faAngleRight"
                        (click)="managePagination('next')"
                      ></fa-icon>
                    </span>
                    <span style="cursor: pointer" (click)="findAll()">
                      Find ALL</span
                    >
                  </span>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav" *ngIf="mainArr.controls.length != 0">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  id="myModal"
  class="modal"
  [style.display]="isPopupOpen ? 'block' : 'none'"
>
  <div class="modal-content">
    <p>Are you sure you wish to remove the component?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button
      ><button type="button" (click)="confirmDelete()" class="btnn">
        Confirm
      </button>
    </div>
  </div>
</div>
