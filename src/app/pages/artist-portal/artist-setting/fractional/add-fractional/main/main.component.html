<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form [formGroup]="form">
                    <div class="mandatory_elements">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Publish Exhibition :</div>
                                <label class="switch">
                                    <input type="checkbox" formControlName="publishExhibition" />
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to publish the Exhibition on the website.
                            </div>
                        </div>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Url" formControlName="pageUrl" (keypress)="server.toCheckSpace($event,form.value.pageUrl)" />
                                <div class="placeholder">Page URL</div>
                                <div class="input-info">
                                    Exact details to give in URL name for the collection (max two words).
                                </div>
                            </div>
                        </div>
                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                            Exhibition Thumbnail
                        </div>
                        <div class="field-value flex-block">
                            <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.thumbnailArr" (onFileChange)="onFileSelect($event, 'thumbnailArr')" [fileSize]="1048576" [placeholder]="placeholder"></image-upload>

                            <div class="input-info">
                                Upload image or GIF to be used as Exhibition thumbnail.
                            </div>
                        </div>
                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                            Exhibition Banner Image
                        </div>
                        <div class="field-value flex-block">
                            <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.bannerImageArr" (onFileChange)="onFileSelect($event, 'bannerImageArr')" [fileSize]="1048576" [placeholder]="placeholder"></image-upload>

                            <div class="input-info">
                                Upload image or GIF to be used as banner on the website to represent the exhibition. The image will be centered to aspect ratio 1440x300 px on Desktop pages.
                            </div>
                        </div>

                        <div class="title_section">
                            <div class="splitter">
                                <div class="field-value">
                                    <div class="input-container" (focusout)="changeFocus(0)">
                                        <input (focusin)="isDropDownOpen[0] = true" type="text" class="selection" placeholder="Title Banner Type" formControlName="bannerType" [readonly]="true" />
                                        <div class="placeholder">Title Banner Type</div>
                                        <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]" type="button">
                                            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                                        </button>
                                        <div [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[0],
                        'dropdown-visible': isDropDownOpen[0]
                      }">
                                            <ul>
                                                <li (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form
                              .get('bannerType')
                              .setValue('Fullscreen Image')
                          ">
                                                    <div class="country-name">Fullscreen Image</div>
                                                </li>
                                                <li (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'video/*';
                            form.get('bannerType').setValue('Video')
                          ">
                                                    <div class="country-name">Video</div>
                                                </li>
                                                <li (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('GIF')
                          ">
                                                    <div class="country-name">GIF</div>
                                                </li>
                                                <li (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form
                              .get('bannerType')
                              .setValue('Artwork on wall')
                          ">
                                                    <div class="country-name">Artwork on wall</div>
                                                </li>
                                                <li (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('Half-Banner')
                          ">
                                                    <div class="country-name">Half-Banner</div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="input-info">Choose the type of Banner to use</div>
                                </div>
                                <div class="field-value">
                                    <div class="input-container" (focusout)="changeFocus(2)">
                                        <input (focusin)="isDropDownOpen[2] = true" type="text" class="selection" placeholder="Header Colour" formControlName="headerColour" [readonly]="true" />
                                        <div class="placeholder">Header Colour</div>
                                        <button (click)="isDropDownOpen[2] = !isDropDownOpen[2]" type="button">
                                            <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                                        </button>
                                        <div [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[2],
                        'dropdown-visible': isDropDownOpen[2]
                      }">
                                            <ul>
                                                <li (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Normal')
                          ">
                                                    <div class="country-name">Normal</div>
                                                </li>
                                                <li (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Inverted')
                          ">
                                                    <div class="country-name">Inverted</div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="input-info">
                                        Choose whether to use regular logo with black font, or inverted logo with white font (depending on the Banner Media chosen)
                                    </div>
                                </div>
                            </div>

                            <div class="image-type" *ngIf="form.value.bannerType">
                                <!-- Banner-->
                                <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                    Title Banner media
                                </div>
                                <div class="field-value flex-block">
                                    <image-upload [accept]="acceptType" [selectedData]="selectedFilesObj?.bannerMediaArr" (onFileChange)="onFileSelect($event, 'bannerMediaArr')" [fileSize]="1048576" [placeholder]="placeholder"></image-upload>
                                    <div class="input-info">
                                        Provide the media to be used in the banner.
                                    </div>
                                </div>
                            </div>

                            <div class="video-type" *ngIf="form.value.bannerType">
                                <div class="splitter">
                                    <div class="field-value" style="padding-right: 0.5vw">
                                        <input type="text" placeholder="Banner Title" formControlName="bannerTitle" />
                                        <div class="placeholder">Title Banner Video</div>
                                        <div class="input-info">
                                            Provide the Vimeo player link for the video to be featured as title banner. e.g.: https://www.player.vimeo.com/12342143.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="form.get('bannerType').value == 'Artwork on wall'">
                                <div class="upload-head">Banner Background Image</div>

                                <div class="field-value flex-block">
                                    <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.bannerBackgroundArr" (onFileChange)="onFileSelect($event, 'bannerBackgroundArr')" [fileSize]="1048576" [placeholder]="placeholder"></image-upload>
                                    <div class="input-info">
                                        Upload a wall background image to place the artwork on (a gallery wall image)
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Title-->
                            <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                Exhibition Title
                            </div>
                            <div class="field-value">
                                <angular-editor id="editor20" formControlName="title" (keypress)="server.toCheckSpace($event,form.value.title)"></angular-editor>
                                <div class="input-info">
                                    Provide the Exhibition title to be used in the banner. Maximum 25 characters.
                                </div>
                            </div>
                            <div class="splitter">
                                <div class="field-value" style="padding-right: 0.5vw">
                                    <input type="text" formControlName="titleSize" placeholder="Text-Size" />
                                    <div class="placeholder">Title Size</div>
                                    <div class="input-info">
                                        Provide the font size for the Exhibition title on the banner (between 1 and 3).
                                    </div>
                                </div>
                                <div class="field-value" style="position: relative">
                                    <input type="color" formControlName="bannerTextColor" placeholder="HEX code" />
                                    <!-- <div class="forHex"></div> -->
                                    <div class="placeholder">Banner Text Colour</div>
                                    <div class="input-info">
                                        Provide the HEX code for the banner text colours (eg: #6fa8dc).
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Above Subtitle :</div>
                                <label class="switch">
                                    <input type="checkbox" formControlName="showAboveSubtitle" />
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show description text.
                            </div>
                        </div>

                    </div>
                    <div *ngIf="form.get('showAboveSubtitle').value">
                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                            Above Subtitle text
                        </div>
                        <div class="field-value">
                            <angular-editor id="editor21" formControlName="aboveSubText"></angular-editor>
                            <div class="input-info">Provide the subtitle text to use above the title.</div>
                        </div>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="number" placeholder="Font-Size" formControlName="aboveSubSize" />
                                <div class="placeholder">Above Subtitle size</div>
                                <div class="input-info">
                                    Provide the font size for the subtitle (decimal values between 1.0 and 2.0).
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Below Subtitle :</div>
                            <label class="switch">
                                <input type="checkbox" formControlName="showBelowSubtitle" />
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show a subtitle.
                        </div>
                    </div>
                    <div *ngIf="form.get('showBelowSubtitle').value">
                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                            Below Subtitle text
                        </div>
                        <div class="field-value">
                            <angular-editor id="editor22" formControlName="belowSubText"></angular-editor>
                            <div class="input-info">Provide the subtitle text to use below the title.</div>
                        </div>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="number" placeholder="Font-Size" formControlName="belowSubSize" />
                                <div class="placeholder">Below Subtitle size</div>
                                <div class="input-info">
                                    Provide the font size for the subtitle (decimal values between 1.0 and 2.0).
                                </div>
                            </div>
                            <!-- <div class="field-value">
                                <div class="input-container" (focusout)="changeFocus(1)">
                                    <input type="text" class="selection" formControlName="position" placeholder="Subtitle Position" [readonly]="true" />
                                    <div class="placeholder">Subtitle Position</div>
                                    <button (click)="isDropDownOpen[1] = !isDropDownOpen[1]" type="button">
                                        <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                                    </button>
                                    <div [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[1],
                      'dropdown-visible': isDropDownOpen[1]
                    }">
                                        <ul>
                                            <li (click)="isDropDownOpen[1] = false">
                                                <div class="country-name">Above Title</div>
                                            </li>
                                            <li (click)="isDropDownOpen[1] = false">
                                                <div class="country-name">Below Title</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="input-info">
                                    Choose the position of the subtitle.
                                </div>
                            </div> -->
                        </div>
                    </div>

                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Artist Name :</div>
                            <label class="switch">
                                <input type="checkbox" formControlName="showArtistName" />
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show the artist name on the banner.
                        </div>
                    </div>
                    <div *ngIf="form.get('showArtistName').value">
                        <div style="margin-top: 2.08vw; font-size: 1.111vw">
                            Artist Name
                        </div>
                        <div class="field-value">
                            <angular-editor id="editor23" formControlName="artistName"></angular-editor>
                            <div class="input-info">
                                Provide the artist details to be displayed on the banner.
                            </div>
                        </div>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="number" placeholder="Font-Size" formControlName="artistFontSize" />
                                <div class="placeholder">Artist Name Size</div>
                                <div class="input-info">
                                    Provide the font size for the Artist Name (between 1 and 2).
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Dates:</div>
                            <label class="switch">
                                <input type="checkbox" formControlName="showDate" />
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show the exhibition dates.
                        </div>
                    </div>
                    <div *ngIf="form.get('showDate').value">
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="date" placeholder="Exhibition Date" formControlName="exhibitionDateStart" />
                                <div class="placeholder">Exhibition Start Date</div>
                                <div class="input-info">Provide the exhibition start date.</div>
                            </div>
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="date" placeholder="Exhibition Date" formControlName="exhibitionDateEnd" />
                                <div class="placeholder">Exhibition End Date</div>
                                <div class="input-info">Provide the exhibition end date.</div>
                            </div>
                        </div>
                        <div class="">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Exhibition Note" formControlName="exhibitionDateNote" />
                                <div class="placeholder">Exhibition Note</div>
                                <div class="input-info">Provide the exhibition note.</div>
                            </div>
                            
                        </div>
                    </div>
                </form>
                <div class="footer-nav">
                    <div class="button-group">
                        <div (click)="onSubmit()" class="next">Save</div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>