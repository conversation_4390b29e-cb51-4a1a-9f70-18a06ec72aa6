<div>
  <div class="d-flex">
    <div class="section left-container" style="width: 100%">
      <div class="section-inner">
        <div class="d-flex justify-content-start slider-header">
          <a routerLink="..">
            <h3 style="color: var(--tertiary-font-color)">
              <fa-icon [icon]="faArrowAltCircleLeft"></fa-icon>
            </h3>
          </a>
          <a
            (click)="
              navigateTo('artist-portal/settings/fractional/add', 'main')
            "
          >
            <h3
              [ngClass]="{
                active: activeMenu === 'main'
              }"
            >
              Main
            </h3>
          </a>
          <a
            (click)="
              navigateTo(
                'artist-portal/settings/fractional/add/add-on',
                'add-on'
              )
            "
          >
            <h3
              [ngClass]="{
                active: activeMenu === 'add-on'
              }"
            >
              Add-Ons
            </h3>
          </a>
          <a
            (click)="
              navigateTo('artist-portal/settings/fractional/add/seo', 'seo')
            "
          >
            <h3
              [ngClass]="{
                active: activeMenu === 'seo'
              }"
            >
              SEO
            </h3>
          </a>
        </div>
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</div>
