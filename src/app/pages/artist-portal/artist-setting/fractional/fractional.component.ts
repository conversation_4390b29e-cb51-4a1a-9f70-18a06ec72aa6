import {
  FooterService,
  FooterType,
} from './../../../../shared/services/footer.service';
import { Component, HostListener, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-fractional',
  templateUrl: './fractional.component.html',
  styleUrls: ['./fractional.component.scss'],
})
export class FractionalComponent implements OnInit {
  list_selection: number;
  searchValue = '';
  noOfArtPerPage = 12;
  index = 0;
  lastIndex = 0;
  currentWorks: any[] = [];
  current: any[] = [];
  isSidemenuOpen = false;
  openStyle = {
    width: '250px',
  };
  form: FormGroup;
  artworks;
  emptyDiv = [];
  temporary = {
    // 'status': 'ForSale',
    // 'status': 'Not For Sale',
    status: 'Sold',
    artwork_type: 'Edition',
    published: false,
    // 'status': 'Reserved',
    minted: false,
    // 'network': 'BSC',
    network: 'Etherum',
    // 'network': 'Solana',
    // 'network': 'Polygon',
    createdBy: 'Ashuthosh NagarWala',
    NFT_id: 'Terrain.Art_289',
  };
  offset: any = 1;
  limit: any = 12;
  total: number = 0;
  isLoading = false;
  temporary2 = [{}, {}, {}, {}, {}, {}, {}, {}];
  userDetailsObj: any = {};
  showMenu = Array(1000).fill(false);
  permissionsObj: any = {};
  async searchResult() {
    this.current = [];
    await this.getArtworks();
    // console.log(this.searchValue + ' here');
    // this.currentWorks = [];
    // this.currentWorks = this.current;
    // // this.current = null;
    // // this.currentWorks = [];
    // // this.currentWorks = this.artworks;
    // if (this.searchValue !== '') {
    //   console.log(this.searchValue);
    //   const newArray = this.currentWorks.filter((art) =>
    //     art?.artwork_title
    //       ? art.artwork_title
    //           .toLowerCase()
    //           .includes(this.searchValue.toLowerCase())
    //       : false
    //   );
    //   this.currentWorks = newArray;
    //   console.log(newArray);
    // } else {
    //   this.currentWorks = this.artworks;
    // }
    // this.updateCurrent();
  }

  updateCurrent(): void {
    this.current = [];

    for (
      let i = this.index * this.noOfArtPerPage;
      i < (this.index + 1) * this.noOfArtPerPage;
      i++
    ) {
      const element = this.currentWorks[i];
      if (element === undefined) {
        break;
      }
      this.current.push(element);
    }
  }

  MintlockColor(status) {
    if (status == true) {
      return {
        background: '-webkit-linear-gradient(#004ddd, #004ddd)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    } else {
      return {
        background: '-webkit-linear-gradient(#9e9e9e, #9e9e9e)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    }
  }

  blockchainType(type) {
    switch (type) {
      case 'Ethereum':
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
      case 'BSC':
        return '../../../../../assets/icons/blockchain-type/binance-icon.png';
        break;
      case 'Polygon':
        return '../../../../../assets/icons/blockchain-type/polygon-icon.png';
        break;
      case 'Solana':
        return '../../../../../assets/icons/blockchain-type/solana-icon.png';
        break;

      default:
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
    }
  }

  lockColor(status) {
    switch (status) {
      case 'Ropsten':
        return {
          background: '-webkit-linear-gradient(#eecda3, #ef629f)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Private':
        return {
          background: '-webkit-linear-gradient(#f87bff, #ffa9cb)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Main Net':
        return {
          background: '-webkit-linear-gradient(#86fde8,#acb6e5)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Non-Terrain':
        return {
          background: '-webkit-linear-gradient(#92eab0, #6eff7a)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      default:
        break;
    }
  }

  dotColor(status) {
    switch (status) {
      case 'ForSale':
        return {
          background: '-webkit-linear-gradient(#03ff26, #0ae729)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Reserved':
        return {
          background: '-webkit-linear-gradient(#f5f54f, #ffff00)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Sold':
        return {
          background: '-webkit-linear-gradient(#fc5c5c, #ee2828)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Not For Sale':
        return {
          background: '-webkit-linear-gradient(#eebdfd, #df06f9)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      default:
        break;
    }
  }

  openNav(i) {
    console.log(i);
    if (this.list_selection == i) {
      this.isSidemenuOpen = !this.isSidemenuOpen;
    } else {
      this.list_selection = i;
      this.isSidemenuOpen = true;
    }
  }

  closeNav() {
    this.isSidemenuOpen = false;
  }

  constructor(
    private server: CollectorService,
    private router: Router,
    public formBuilder: FormBuilder
  ) {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Exhibitions'
    );
    console.log(this.permissionsObj);
  }
  ngOnInit(): void {
    // this.footerservive.changeFooterType(FooterType.HIDE)
    this.form = this.formBuilder.group({
      showListView: new FormControl(false),
    });
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetailsObj = JSON.parse(decode);
    this.getArtworks();

    // this.current = [
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    //   { published: true, title: 'Test data', originalPrice: 500, year: 2022 },
    // ];

    // this.artistInfoService.getArtworkMain().subscribe((data) => {
    // 	console.log(data);

    // 	this.artworks = data;
    // 	this.current=data;
    // 	const reminder = data?.length % 4;
    // 	if (reminder > 0) {
    // 		this.emptyDiv = Array(reminder).fill(0);
    // 	}
    // });
  }
  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.
    //this.footerservive.changeFooterType(FooterType.DEFAULT);
  }
  // to get artworks
  getArtworks() {
    let url =
      apiUrl.exhibitions.getExhibitions +
      `?offset=${this.offset}&limit=${this.limit}&cms=true&adminAccess=${this.permissionsObj['admin']}`;
    this.isLoading = true;

    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      this.isLoading = false;
      if (res.statusCode == 200) {
        this.artworks = res.data;
        const reminder = res.data?.length % 4;
        this.current.push(...res.data);
        if (reminder > 0) {
          this.emptyDiv = Array(reminder).fill(0);
        }
      }
    });
  }

  navigateTo() {
    if (!this.permissionsObj.create) {
      alert('Permissions denied!');
      return;
    }
    console.log('called navigate');
    localStorage.removeItem('exhibitionIdID');
    this.router.navigate(['artist-portal/settings/fractional/add']);
  }

  // to edit artwork
  editArtWork(item) {
    localStorage.setItem('exhibitionIdID', item['_id']);
    this.router.navigate(['artist-portal/settings/fractional/add']);
  }

  // to manage pagination
  managePagination(page) {
    this.offset = page;
    this.total = 0;
    this.getArtworks();
  }

  @HostListener('window:scroll', [])
  onScroll(): void {
    if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
      if (!this.isLoading) {
        this.offset = this.offset + this.limit;
        this.getArtworks();
      }

      //this.temporary.push(...[{}, {}, {}, {}]);
    }
  }
  onElementScroll(event) {
    if (
      event.target.offsetHeight + event.target.scrollTop >=
      event.target.scrollHeight
    ) {
      if (!this.isLoading) {
        this.offset = this.offset + this.limit;
        this.getArtworks();
      }
    }
  }
  publishArtWork(item) {
    let url =
      apiUrl.exhibitions.getExhibitions +
      `?status=${item['status']}&id=${item['_id']}`;

    this.server.showSpinner();
    this.server.postApi(url, {}).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Exhibition published successfully');
      }
    });
  }

  changeFocus(i) {
    setTimeout(() => {
      this.showMenu[i] = false;
    }, 2000);
    console.log(this.showMenu[i]);
  }
}
