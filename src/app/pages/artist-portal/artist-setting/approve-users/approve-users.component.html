<div class="container__main">
    <div class="content__section">
      <div class="profile-field">
        <div class="w-100 field-contents">
          <form >
           
           <div class="field-value doted">
            
             <div>
              <div>
                <table class="table">
                  <thead>
                    <tr>
                  <th>Email</th>
                  <th class="text-center">Role</th>
                  <th class="text-center">Action</th>
                  </tr>
 
                  </thead>
 
                  <tbody id="MyTable">
                    <tr *ngFor="let item of userList | paginate: { itemsPerPage: limit, currentPage: offset,totalItems: total };let i=index">
                      <td (click)="onUserView(item,i)">{{item?.email}}</td>
                      <td class="text-center" (click)="onUserView(item,i)">{{item?.role}}</td>
                      <td class="text-center">
                        <button class="btn-success" (click)="isPopupOpen = true;selectedUserId=item['_id']">Approve</button>
                      </td>
                    </tr>
                    <tr   *ngIf="userList.length == 0">
                      <td class="text-center" colspan="3">No record found!</td>
                     </tr>
                 </tbody>
                 
 
                </table>
              </div>
             </div>
            
           </div>
           <div  class="pagination">
            <pagination-controls (pageChange)="managePagination($event)"></pagination-controls>
          </div>
          
        </form>
      </div>
    </div>
  </div>
</div>

<div id="myModal" class="modal" [style.display]="isPopupOpen ? 'block' : 'none'">
  <div class="modal-content">
    <p>Are you sure you wish to approve the user?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button><button type="button" (click)="isPopupOpen = false;confirmActivate()" class="btnn">
        Approve
      </button>
    </div>
  </div>
</div>

