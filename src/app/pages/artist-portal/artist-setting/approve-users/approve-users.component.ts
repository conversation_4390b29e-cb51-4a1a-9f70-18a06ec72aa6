
import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../services/artist.service';

@Component({
  selector: 'app-approve-users',
  templateUrl: './approve-users.component.html',
  styleUrls: ['./approve-users.component.scss']
})


export class ApproveUsersComponent implements OnInit {
    limit: any=10;
    offset:any=1
    userList: any=[];
    isPopupOpen:boolean= false
    selectedUserId: any;
    modulesArr: any=[];
    selectedRoleId: any;
    total: any=0;
    status :any='ACTIVE';
  
    constructor(
      private formBuilder: FormBuilder,private server: CollectorService
    ) { }
  
  
  
  
    ngOnInit(): void {
      this.getUsers();
    }

    // to get users
    getUsers() {
      let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
      let userDetails = JSON.parse(decode)
      this.userList = []
      let url = apiUrl.addUser + `?limit=${this.limit}&offset=${this.offset}&status=PENDING&role_id=${userDetails.role_id['_id']}`
      this.server.showSpinner()
      this.server.getApi(url).subscribe(res=>{
        this.server.hideSpinner()
        if(res.statusCode == 200) {
          this.userList = res.data;
          this.total = res.total;
        }
      })
    }

    // to manage pagination
    managePagination(page) {
      this.offset = page;
      this.total = 0;
      this.getUsers();
    }

     // to confirm activate user
     confirmActivate() {
      let url = apiUrl.approveUser + `/${this.selectedUserId}`
      // let data = {
      //   "user_id": this.selectedUserId,
      //   "status": "ACTIVE"
      // }
      this.server.showSpinner()
      this.server.getApi(url).subscribe(res=>{
        this.server.hideSpinner()
        if(res.statusCode == 200) {
          alert(res.message || res.Message)
          this.offset=1
          this.getUsers();
        }else {
          alert(res.Message || res.message)
        }
      },err=>{
        alert(err.error.Message || err.error.message)
      })
    }

}
