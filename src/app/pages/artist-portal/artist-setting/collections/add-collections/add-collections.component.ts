import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-add-collections',
  templateUrl: './add-collections.component.html',
  styleUrls: ['./add-collections.component.scss']
})
export class AddCollectionsComponent implements OnInit {
  isFilterDropdownOpen=false;
  isFilterDropdownOpen1=false;
  isArtworkPopupOpen;
  isDropDownOpen;
  isArtworkDropdownOpen1;
  isArtworkDropdownOpen2;
  isBannerDropdownOpen;
  componentName: string;
  bannerTypeIndexed=[];

  form: FormGroup;
  selectedFiles: File[][] = [[], [], [], [], [], [], [], [], [], [], [], [], [],];
  constructor(private formBuilder: FormBuilder) { }
  MediaTypeImage = {
    banner: [false, false],
    titleBanner: false
  }
  // MediaTypeImage = {
  //   banner: {id0:false, id1:false},
  //   titleBanner: false
  // }
  ComponentSelection = {
    titleBanner: false,
    description: false,
    introVideo: false,
    quote: false,
    artworkSelect: false,
    bannerDivide: false

  }

  Reusable = {
    repeat: ['artworkSelect', 'bannerDivide'],
    artworkSelect: 'selectArtwork',
    bannerDivide: 'selectBannerDivide'
  }
  deleteBanner(index) {
    // this.bannerTypeIndexed[index]='';
    this.selectBannerDivide.removeAt(index);
  }
  deleteArtwork(index) {
    // console.log(this.selectArtwork[index].)
    this.selectArtwork.removeAt(index);
  }


  ComponentSelect(componentName, dropdownName) {
    this.form.get("componentSelector").setValue(dropdownName);
    this.componentName = componentName;

  }
  addComponent(componentName) {
    if (this.ComponentSelection[componentName] && this.Reusable.repeat.includes(componentName)) {
      switch (componentName) {
        case 'artworkSelect':
          this.selectArtwork.push(
            this.formBuilder.group({ artistTitle: { value: 'Artist Title', disabled: true }, artistSelect: { value: 'Artist Select', disabled: false }})
          );
          break;
        case 'bannerDivide':
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.push(
              this.formBuilder.group({ bannerType: { value: 'Banner Type', disabled: true } })
            );
          }
          else { }
          break;

        default:
          break;
      }

    }
    else
      this.ComponentSelection[componentName] = true;

  }


  bannerTypeSelect(type, index) {
    this.selectBannerDivide.controls[index].get("bannerType").setValue(type);
    this.bannerTypeIndexed[index]= this.selectBannerDivide.controls[index].get("bannerType").value;
    if (type = "Video") {
      this.MediaTypeImage.banner[index] = false;
    }
    else {
      this.MediaTypeImage.banner[index] = true;
    }

  }
  artworkSelector(index,name){
    this.selectArtwork.controls[index].get("artistTitle").setValue(name);
  }
  artistSelector(index,name){
    this.selectArtwork.controls[index].get("artistSelect").setValue(name);
  }



  ngOnInit(): void {
    this.isArtworkPopupOpen=false;
    this.isDropDownOpen = Array(10).fill(false);
    this.isArtworkDropdownOpen1 = Array(10).fill(false);
    this.isArtworkDropdownOpen2 = Array(10).fill(false);
    this.isBannerDropdownOpen = Array(10).fill(false);
    this.form = this.formBuilder.group({
      filter: new FormControl({value:'ID',disabled:true}),
      sort: new FormControl({value: "Sort" ,disabled:true}),
      showDescription: new FormControl(true),
      showVideo: new FormControl(true),
      showArtworks: new FormControl(true),
      showQuote: new FormControl(true),
      componentSelector: new FormControl('Choose Component'),
      showBanner: new FormControl(true),
      selectArtwork: this.formBuilder.array([this.formBuilder.group({ artistTitle: { value: 'Artist Title', disabled: true }, artistSelect: { value: 'Artist Select', disabled: false }, })]),
      selectBannerDivide: this.formBuilder.array([this.formBuilder.group({ bannerType: { value: 'Banner Type', disabled: true } })]),
    })

  }
  get selectArtwork(): FormArray {
    return <FormArray>this.form.get('selectArtwork');
  }
  get selectBannerDivide(): FormArray {
    return <FormArray>this.form.get('selectBannerDivide');
  }


  async onFileSelect(index: number, files: FileList) {
    console.log(index);

    if (files[0].size < 2100000) {
      this.selectedFiles[index].push(files[0]);
    }
    console.log(this.selectedFiles);
  }

}
