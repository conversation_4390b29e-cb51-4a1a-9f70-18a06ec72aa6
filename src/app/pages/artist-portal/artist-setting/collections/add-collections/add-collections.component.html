<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form [formGroup]="form">
                    <div class="permanent">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Publish Profile :</div>
                                <label class="switch">
                  <input type="checkbox" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to publish the collection on the website.
                            </div>
                        </div>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="text" placeholder="Url" />
                                <div class="placeholder">Page URL</div>
                                <div class="input-info">
                                    Exact details to give in URL name for the collection (max two words).
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value">
                            <div class="input-container">
                                <input formControlName="componentSelector" type="text" class="selection" placeholder="form.get(componentSelector).value" disabled />
                                <button (click)="isDropDownOpen[4] = !isDropDownOpen[4]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[4],
                    'dropdown-visible': isDropDownOpen[4]
                  }">
                                    <ul>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('titleBanner', 'Title Banner')
                      ">
                                            <div class="country-name">Title Banner</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('description', 'Description')
                      ">
                                            <div class="country-name">Description</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('introVideo', 'Intro Video')
                      ">
                                            <div class="country-name">Intro Video</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('artworkSelect', 'Artwork Select')
                      ">
                                            <div class="country-name">Artwork Select</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('quote', 'Quote')
                      ">
                                            <div class="country-name">Quote</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen[4] = false;
                        ComponentSelect('bannerDivide', 'Banner Divide')
                      ">
                                            <div class="country-name">Banner Divide</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="input-info">
                                Choose which component to use in the collection page.
                            </div>
                        </div>
                        <div class="field-value">
                            <button class="save" (click)="addComponent(componentName)">
                Add +
              </button>
                        </div>
                    </div>

                    <div class="title_section" *ngIf="ComponentSelection.titleBanner">
                        <div class="splitter">
                            <div class="field-value">
                                <div class="input-container">
                                    <input type="text" class="selection" placeholder="Banner Type" disabled />
                                    <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]" type="button">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                                    <div [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[0],
                      'dropdown-visible': isDropDownOpen[0]
                    }">
                                        <ul>
                                            <li (click)="
                          isDropDownOpen[0] = false;
                          MediaTypeImage.titleBanner = true
                        ">
                                                <div class="country-name">Image</div>
                                            </li>
                                            <li (click)="
                          isDropDownOpen[0] = false;
                          MediaTypeImage.titleBanner = false
                        ">
                                                <div class="country-name">Video</div>
                                            </li>
                                            <li (click)="
                          isDropDownOpen[0] = false;
                          MediaTypeImage.titleBanner = true
                        ">
                                                <div class="country-name">GIF</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="input-info">Choose the type of Banner to use</div>
                            </div>
                        </div>
                        <!-- Banner Title-->
                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                            Banner Title
                        </div>
                        <div class="field-value">
                            <angular-editor id="editor8"></angular-editor>
                            <div class="input-info">
                                Provide the collection title to be used in the banner. Maximum 25 characters.
                            </div>
                        </div>

                        <div class="image-type" *ngIf="MediaTypeImage.titleBanner">
                            <!-- Banner-->
                            <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                Banner Image
                            </div>
                            <div class="field-value flex-block">
                                <div class="input-container file-upload">
                                    <input class="upload-input" type="file" (change)="onFileSelect(0, $event.target.files)" accept="image/*" />
                                    <div *ngIf="selectedFiles[0].length <= 0" class="icon">
                                        <img src="assets/icons/<EMAIL>" />
                                    </div>
                                    <div *ngIf="selectedFiles[0].length <= 0" class="text-content">
                                        <div class="title">
                                            You can upload or drop your file here.
                                        </div>
                                        <div class="sub-title">Maximum upload size: 2 MB</div>
                                    </div>
                                    <ng-container *ngIf="selectedFiles[0].length > 0">
                                        <ng-container *ngFor="let file of selectedFiles[0]; let i = index">
                                            <div class="text-content">
                                                <div class="title">
                                                    {{ file.name }}
                                                </div>
                                                <div class="sub-title">
                                                    File size: {{ (file.size / 1048576).toFixed(2) }} MB
                                                </div>
                                                <div (click)="removeItem(0, i)" class="close">
                                                    <img src="assets/icons/close.png" />
                                                </div>
                                            </div>
                                        </ng-container>
                                    </ng-container>

                                    <div class="button-container">
                                        <div class="button">
                                            {{ selectedFiles[0].length > 0 ? "Add file" : "Choose file" }}
                                        </div>
                                    </div>
                                </div>
                                <div class="input-info">
                                    Provide the media to be used in the banner.
                                </div>
                            </div>
                        </div>

                        <div class="video-type" *ngIf="!MediaTypeImage.titleBanner">
                            <div class="splitter">
                                <div class="field-value" style="padding-right: 0.5vw">
                                    <input type="text" placeholder="URL" />
                                    <div class="placeholder">Banner Video</div>
                                    <div class="input-info">
                                        Provide the Vimeo player link for the video to be featured as banner. e.g.: https://www.player.vimeo.com/12342143.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--show Description-->
                    <div class="description" *ngIf="ComponentSelection.description">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Description :</div>
                                <label class="switch">
                  <input type="checkbox" formControlName="showDescription" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show description text.
                            </div>
                        </div>
                        <div *ngIf="form.get('showDescription').value">
                            <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                Description Text
                            </div>
                            <div class="field-value">
                                <angular-editor id="editor9"></angular-editor>
                                <div class="input-info">
                                    Provide the collection description or note to be shown on the page.
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Show Video -->
                    <div *ngIf="ComponentSelection.introVideo">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Video :</div>
                                <label class="switch">
                  <input type="checkbox" formControlName="showVideo" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show a video on the Gallery profile page
                            </div>
                        </div>
                        <div class="videoSection" *ngIf="form.get('showVideo').value">
                            <div class="splitter">
                                <div class="field-value" style="padding-right: 0.5vw">
                                    <input type="text" placeholder="Link" />
                                    <div class="placeholder">Intro Video</div>
                                    <div class="input-info">
                                        Provide the Vimeo player link for the video to be featured on the collection page. e.g.: https://www.player.vimeo.com/12342143.
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 2.08vw; font-size: 1.11vw">
                                Video Text
                            </div>
                            <div class="field-value">
                                <angular-editor id="editor10"></angular-editor>
                                <div class="input-info">
                                    Provide the text to be displayed on the Video thumbnail. e.g.: Artist XYZ talking about their works.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Show Artworks-->
                    <div *ngIf="ComponentSelection.artworkSelect">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Artworks :</div>
                                <label class="switch">
                  <input type="checkbox" formControlName="showArtworks" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show list of Artworks for sale on the Gallery profile page
                            </div>
                        </div>
                        <div class="artworkSection" *ngIf="form.get('showArtworks').value">
                            <div class="field-value doted">
                                <div formArrayName="selectArtwork" *ngFor="let item of selectArtwork.controls; let i = index" style="
                  padding: 1rem;
                  border: 0.069vw solid var(--timeline-color);
                  margin: 1.2rem 0;
                  position: relative;
                ">
                                    <div class="splitter" [formGroupName]="i">
                                        <div class="field-value">
                                            <div class="input-container">
                                                <input type="text" class="selection" formControlName="artistTitle" placeholder="form.get(artistTitle).value" disabled />
                                                <button (click)="
                            isArtworkDropdownOpen1[i] =
                              !isArtworkDropdownOpen1[i]
                          " type="button">
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                                                <div [ngClass]="{
                            'dropdown-hidden': !isArtworkDropdownOpen1[i],
                            'dropdown-visible': isArtworkDropdownOpen1[i]
                          }">
                                                    <ul>
                                                        <li (click)="isArtworkDropdownOpen1[i] = false; artworkSelector(i,'Single');">
                                                            <div class="country-name">Single</div>
                                                        </li>
                                                        <li (click)="isArtworkDropdownOpen1[i] = false;artworkSelector(i,'2x1');">
                                                            <div class="country-name">2x1</div>
                                                        </li>
                                                        <li (click)="isArtworkDropdownOpen1[i] = false;artworkSelector(i,'3x1');">
                                                            <div class="country-name">3x1</div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="input-info">
                                                Choose the grid to display the artworks in.
                                            </div>
                                        </div>
                                        <!-- <div class="field-value">
                      <div class="input-container">
                        <input
                          type="text"
                          class="selection"
                          formControlName="artistSelect"
                          placeholder="form.get(artistSelect).value"
                        />
                        <button
                          (click)="
                            isArtworkDropdownOpen2[i] =
                              !isArtworkDropdownOpen2[i]
                          "
                          type="button"
                        >
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !isArtworkDropdownOpen2[i],
                            'dropdown-visible': isArtworkDropdownOpen2[i]
                          }"
                        >
                          <ul>
                            <li (click)="isArtworkDropdownOpen2[i] = false;artistSelector(i,'Artwork 1')">
                              <div class="country-name">Artwork 1</div>
                            </li>
                            <li (click)="isArtworkDropdownOpen2[i] = false;artistSelector(i,'Artwork 2')">
                              <div class="country-name">Artwork 2</div>
                            </li>
                            <li (click)="isArtworkDropdownOpen2[i] = false;artistSelector(i,'Artwork 3')">
                              <div class="country-name">Artwork 3</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div class="input-info">
                        Choose the artworks to be shown in the artwork grid.
                      </div>
                    </div>
                    -->



                                    </div>
                                    <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                        Artwork Select
                                    </div>
                                    <div class="contain_">
                                        <div class="selection_container">
                                            <div class="selection_headings">
                                                <div class="property_title box">ID</div>
                                                <div class="property_title box2">Primary Image</div>
                                                <div class="property_title box2">Artist Name</div>
                                                <div class="property_title box2">Artwork Title</div>
                                                <div class="property_title box2">Year</div>
                                                <div class="property_title box">Action</div>
                                            </div>
                                            <div class="selection_data">
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                                <div class="artwork_details">
                                                    <div class="properties box">56</div>
                                                    <div class="properties box2">
                                                        <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                                    </div>
                                                    <div class="properties box2">Sukanya Garg</div>
                                                    <div class="properties box2">Panchama(Earth Mother)</div>
                                                    <div class="properties box2">2019</div>
                                                    <div class="properties box">
                                                        <p class="close_icon">&times;</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="select" (click)="isArtworkPopupOpen = true">Select Existing</button>
                                    </div>
                                    <div class="input-info">
                                        Choose the artworks to be shown in the artwork grid
                                    </div>





                                    <img (click)="deleteArtwork(i)" src="assets/icons/grey-close.png" title="Delete this " style="
                    width: 1.9vw;
                    right: .2vw;
                    top: .2vw;
                    position: absolute;
                  " />
                                </div>
                                <div class="input-info">
                                    Choose 4 to 8 artworks to feature on the Collections page
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--show Quote-->
                    <div class="Quote" *ngIf="ComponentSelection.quote">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Quote :</div>
                                <label class="switch">
                  <input type="checkbox" formControlName="showQuote" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show the quote on page.
                            </div>
                        </div>
                        <div *ngIf="form.get('showQuote').value">
                            <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                Quote Text
                            </div>
                            <div class="field-value">
                                <angular-editor id="editor11"></angular-editor>
                                <div class="input-info">
                                    Provide the artist quote to be displayed on the page.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- show banner -->
                    <div class="Banner_section" *ngIf="ComponentSelection.bannerDivide">
                        <div class="field-value">
                            <div class="input-container">
                                <div class="text-before">Show Banner :</div>
                                <label class="switch">
                  <input type="checkbox" formControlName="showBanner" />
                  <span class="slider round"></span>
                </label>
                            </div>
                            <div class="input-info">
                                Choose whether to show the banner video/image.
                            </div>
                        </div>

                        <div *ngIf="form.get('showBanner').value" class="field-value doted">
                            <div formArrayName="selectBannerDivide" *ngFor="let item of selectBannerDivide.controls; let i = index" style="
                  padding: 1rem;
                  border: 0.069vw solid var(--timeline-color);
                  margin: 1.2rem 0;
                  position: relative;
                ">
                                <div [formGroupName]="i">
                                    <div class="splitter">
                                        <div class="field-value">
                                            <div class="input-container">
                                                <input formControlName="bannerType" placeholder="form.get(bannerType).value" type="text" class="selection" />
                                                <button (click)="
                            isBannerDropdownOpen[i] = !isBannerDropdownOpen[i]
                          " type="button">
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                                                <div [ngClass]="{
                            'dropdown-hidden': !isBannerDropdownOpen[i],
                            'dropdown-visible': isBannerDropdownOpen[i]
                          }">
                                                    <ul>
                                                        <li (click)="
                                isBannerDropdownOpen[i] = false;
                                bannerTypeSelect('Image',i)
                              ">
                                                            <div class="country-name">Image</div>
                                                        </li>
                                                        <li (click)="
                                isBannerDropdownOpen[i] = false;
                                bannerTypeSelect('Video', i)
                              ">
                                                            <div class="country-name">Video</div>
                                                        </li>
                                                        <li (click)="
                                isBannerDropdownOpen[i] = false;
                                bannerTypeSelect('GIF', i)
                              ">
                                                            <div class="country-name">GIF</div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="input-info">
                                                Choose the type of Banner to use.
                                            </div>
                                        </div>
                                    </div>
                                    <!-- *ngIf="MediaTypeImage.banner[i]" -->
                                    <div class="image-type" *ngIf="bannerTypeIndexed[i]=='Image' || bannerTypeIndexed[i]=='GIF'">
                                        <!-- Banner-->
                                        <div style="margin-top: 2.08vw; font-size: 1.25vw">
                                            Banner Image
                                        </div>
                                        <div class="field-value flex-block">
                                            <div class="input-container file-upload">
                                                <input class="upload-input" type="file" (change)="onFileSelect(0, $event.target.files)" accept="image/*" />
                                                <div *ngIf="selectedFiles[0].length <= 0" class="icon">
                                                    <img src="assets/icons/<EMAIL>" />
                                                </div>
                                                <div *ngIf="selectedFiles[0].length <= 0" class="text-content">
                                                    <div class="title">
                                                        You can upload or drop your file here.
                                                    </div>
                                                    <div class="sub-title">Maximum upload size: 2 MB</div>
                                                </div>
                                                <ng-container *ngIf="selectedFiles[0].length > 0">
                                                    <ng-container *ngFor="let file of selectedFiles[0]; let i = index">
                                                        <div class="text-content">
                                                            <div class="title">
                                                                {{ file.name }}
                                                            </div>
                                                            <div class="sub-title">
                                                                File size: {{ (file.size / 1048576).toFixed(2) }} MB
                                                            </div>
                                                            <div (click)="removeItem(0, i)" class="close">
                                                                <img src="assets/icons/close.png" />
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                </ng-container>

                                                <div class="button-container">
                                                    <div class="button">
                                                        {{ selectedFiles[0].length > 0 ? "Add file" : "Choose file" }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="input-info">
                                                Provide the media to be used in the banner.
                                            </div>
                                        </div>
                                    </div>

                                    <div class="video-type" *ngIf="bannerTypeIndexed[i]=='Video'">
                                        <div class="splitter">
                                            <div class="field-value" style="padding-right: 0.5vw">
                                                <input type="text" placeholder="URL" />
                                                <div class="placeholder">Banner Video</div>
                                                <div class="input-info">
                                                    Provide the Vimeo player link for the video to be featured as banner. e.g.: https://www.player.vimeo.com/12342143.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <img (click)="deleteBanner(i)" src="assets/icons/grey-close.png" title="Delete this " style="
                    width: 1.9vw;
                    right: 2vw;
                    top: 2.5vw;
                    position: absolute;
                  " />
                            </div>
                        </div>
                    </div>
                    <div class="artworkModal" [style.display]="isArtworkPopupOpen ? 'block' : 'none'">
                        <!-- Modal content -->
                        <div class="artwork-modal-content">
                            <div class="Addselection_container">
                                <div class="Addtitle_bar">
                                    Select Existing
                                    <p class="Addclose_icon" (click)="isArtworkPopupOpen = false">&times;</p>
                                </div>
                                <div class="Addsearch_bar">
                                    <div class="search_container">
                                        <div class="fieldd-value">
                                            <input type="text" placeholder="Search" />
                                        </div>
                                    </div>
                                    <div class="filter_container">
                                        <div class="fieldd-value">
                                            <div class="input-container" style="width: 12vw !important;">
                                                <input formControlName='filter' placeholder="form.get('filter').value" type="text" class="selection" disabled />
                                                <button (click)="
                              isFilterDropdownOpen = !isFilterDropdownOpen
                            " type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                                                <div [ngClass]="{
                              'dropdown-hidden': !isFilterDropdownOpen,
                              'dropdown-visible': isFilterDropdownOpen
                            }">
                                                    <ul>
                                                        <li (click)="
                                  isFilterDropdownOpen = false;
                                  form.get('filter').setValue('Artist Name');
                                ">
                                                            <div class="country-name">Artist Name</div>
                                                        </li>
                                                        <li (click)="
                                  isFilterDropdownOpen = false;
                                  form.get('filter').setValue('Artwork Title');
                                ">
                                                            <div class="country-name">Artwork Title</div>
                                                        </li>
                                                        <li (click)="
                                  isFilterDropdownOpen = false;
                                  form.get('filter').setValue('ID');
                                ">
                                                            <div class="country-name">ID</div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="fieldd-value">
                                            <div class="input-container" style="width: 7vw !important;">
                                                <input formControlName='sort' placeholder="form.get('sort').value" type="text" class="selection" />
                                                <button (click)="
                              isFilterDropdownOpen1 = !isFilterDropdownOpen1
                            " type="button">
                            <img
                              src="assets/icons/arrow-down.png"
                              class="flag-arrow"
                            />
                          </button>
                                                <div [ngClass]="{
                              'dropdown-hidden': !isFilterDropdownOpen1,
                              'dropdown-visible': isFilterDropdownOpen1
                            }">
                                                    <ul>
                                                        <li (click)="
                                  isFilterDropdownOpen1 = false;
                                  form.get('sort').setValue('&uarr;');
                                ">
                                                            <div class="country-name">&uarr;</div>
                                                        </li>
                                                        <li (click)="
                                  isFilterDropdownOpen1 = false;
                                  form.get('sort').setValue('&darr;');
                                ">
                                                            <div class="country-name">&darr;</div>
                                                        </li>

                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="Addselection_headings">
                                    <div class="Addproperty_title Addbox">
                                        <input type="checkbox" name="title" id="title" disabled />
                                    </div>
                                    <div class="Addproperty_title Addbox">ID</div>
                                    <div class="Addproperty_title Addbox2">
                                        Primary Image
                                    </div>
                                    <div class="Addproperty_title Addbox2">Artist Name</div>
                                    <div class="Addproperty_title Addbox2">Artwork Title</div>
                                </div>
                                <div class="Addselection_data">
                                    <div class="Addartwork_details">
                                        <div class="Addproperties Addbox">
                                            <input type="checkbox" name="asd" id="asd" />
                                        </div>
                                        <div class="Addproperties Addbox">56</div>
                                        <div class="Addproperties Addbox2">
                                            <img src="https://picsum.photos/200/300" alt="Artwork Thumbanail" />
                                        </div>
                                        <div class="Addproperties Addbox2">Sukanya Garg</div>
                                        <div class="Addproperties Addbox2">
                                            Panchama(Earth Mother)
                                        </div>
                                    </div>
                                </div>
                                <div class="Addfooter_bar">
                                    <button class="Addselect" (click)="isArtworkPopupOpen = false">Cancel</button>
                                    <button class="Addselect" style="float: right" (click)="isArtworkPopupOpen = false">Done</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>