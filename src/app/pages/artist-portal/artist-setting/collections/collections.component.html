<div class="w-100">
	<div class="heading">
		<div class="head">
			Collections
			<a routerLink="/artist-portal/settings/collections/add"
				><button class="save">Add +</button></a
			>
		</div>
	</div>

	<div class="collector-form">

		<div class="interests">
			<div class="contents">
				<div class="artist">
					<!-- <a [routerLink]="'/artist-portal/settings/artwork/edit/' + art.id"> -->
						<!-- <img
							[src]="
								'https://api.artist.terrain.art/' + art?.primaryImage?.path
							"
						/> -->
                        <img/>
					<!-- </a> -->
					<div class="name">Collections Title </div>
				</div>
				<!-- <div *ngFor="let item of emptyDiv" class="artist"></div> -->
			</div>
		</div>
	</div>
</div>
