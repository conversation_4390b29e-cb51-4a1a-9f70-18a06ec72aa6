import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../services/artist.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-invite-artist',
  templateUrl: './invite-artist.component.html',
  styleUrls: ['./invite-artist.component.scss'],
})
export class InviteArtistComponent implements OnInit {
  form: FormGroup;
  showdrop: boolean = false;
  limit: any = 200;
  offset: any = 0;
  userList: any[] = [];
  showPage: any = 'List';
  roleList: any = [];
  isPopupOpen: boolean = false;
  isDeactivatePopupOpen: boolean = false;
  selectedUserId: any;
  modulesArr: any = [];
  selectedRoleId: any;
  total: any = 0;
  status: any = 'ACTIVE';
  isChecked: boolean = true;

  isSearched = false;

  searchedArtist;
  userDetails;
  isAdmin = false;
  editBio = true;
  addArt = true;
  isPopupOpen2: boolean = false;
  changeEditBio = true;
  changeAddArt = true;
  p: number = 1;
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private router: Router
  ) {}

  ngOnInit(): void {
    localStorage.removeItem('artworkArtistID');
    localStorage.removeItem('artworkID');
    localStorage.removeItem('bioArtistID');
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetails = JSON.parse(decode);
    if (this.userDetails?.role === 'SUPERADMIN') {
      this.isAdmin = true;
    }
    this.initialiseForm();
    this.getRoles();
    this.getUsers();
    this.resetModules();
  }

  // to initialise form
  initialiseForm() {
    this.form = this.formBuilder.group({
      first_name: new FormControl(''),
      last_name: new FormControl(''),
      email: new FormControl(''),
    });
  }

  // to get roles
  getRoles() {
    let url =
      apiUrl.addRole +
      `?limit=${this.limit}&offset=${this.offset}&status=ACTIVE`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.roleList = res.data;
      }
    });
  }

  // to get users
  getUsers() {
    this.userList = [];
    let url = `partners-artist/all?limit=${this.limit}&offset=${this.offset}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.userList = res.data;
        this.total = res.total;
      }
    });
  }

  onUserEdit(item, index) {
    this.selectedUserId = item['_id'];
    this.showPage = 'Update';
    this.patchUser(item);
  }

  // to view role
  onUserView(item, index) {
    this.selectedUserId = item['_id'];
    this.showPage = 'View';
    this.patchUser(item);
  }

  searchUser() {
    let email = this.form.value.email;
    let url = `partners-artist/checkUser?email=${email}`;
    this.isSearched = true;
    this.form.reset();
    this.form.patchValue({ email });
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.searchedArtist = res.data;
        if (this.searchedArtist) {
          this.form.patchValue({
            first_name: this.searchedArtist.first_name,
            last_name: this.searchedArtist.last_name,
          });
        }
      }
    });
  }
  inviteArtist() {
    let email = this.form.value.email;
    let url = `partners-artist/add`;
    this.isSearched = true;

    this.server.postApi(url, this.form.value).subscribe((res) => {
      if (res.statusCode == 200) {
        this.form.reset();
        this.showPage = 'List';
        alert('Success');
        this.ngOnInit();
      }
    });
  }

  // to patch Role value on edit and view
  patchUser(item) {
    this.form.patchValue({
      first_name: item['first_name'],
      last_name: item['last_name'],
      role: item.role,
      email: item.email,
    });
    this.form.controls['email'].disable();
    let index = this.roleList.findIndex((x) => x.role == item.role);
    if (index != -1) {
      this.modulesArr = this.roleList[index].permissions;
      this.selectedRoleId = this.roleList[index]['_id'];
    }
  }

  // to confirm deactivate user
  confirmDeactivate() {
    let url = `partners-artist/reject?id=${this.selectedUserId}`;

    // this.isPopupOpen = false;
    this.server.showSpinner();
    this.server.getApi(url).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert('req rejected successfully!');
          this.ngOnInit();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }

  // to confirm activate user
  confirmActivate() {
    let url = `partners-artist/accept?id=${this.selectedUserId}&addArtwork=${this.addArt}&editBio=${this.editBio}`;
    // this.isDeactivatePopupOpen = false;
    this.server.showSpinner();
    this.server.getApi(url).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert('req accepted successfully!');
          this.offset = 0;
          this.ngOnInit();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }

  //on clicking back button
  onBack() {
    this.showPage = 'List';
    this.limit = 10;
    this.offset = 0;
    this.status = 'ACTIVE';
    this.getUsers();
  }

  // on clicking adding role buuton
  onAddingUser() {
    this.showPage = 'Add';
    this.form.reset();
    //this.resetModules();
  }

  // to reset
  resetModules() {
    this.modulesArr = apiUrl.modules;
  }

  // on selecting role
  selectRole(item) {
    console.log(item);
    this.form.patchValue({ role: item.role });
    this.modulesArr = item.permissions;
    this.selectedRoleId = item['_id'];
  }

  // add user api
  addUser() {
    if (this.form.invalid) {
      alert('Form invalid!');
      return;
    }
    let url = apiUrl.addUser;
    let data = {
      first_name: this.form.value.first_name,
      last_name: this.form.value.last_name,
      fullName: this.form.value.first_name + ' ' + this.form.value.last_name,
      email: this.form.value.email,
      role_id: this.selectedRoleId,
      cmsUrl: this.server.cmsUrl,
    };
    this.server.showSpinner();
    this.server.postApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Email verfication link sent !');
        this.showPage = 'List';
        this.offset = 0;
        this.limit = 10;
        this.getUsers();
      }
    });
  }

  // update user api
  updateUser() {
    if (this.form.invalid) {
      alert('Form invalid!');
      return;
    }
    let url = apiUrl.addUser;
    let data = {
      first_name: this.form.value.first_name,
      last_name: this.form.value.last_name,
      role_id: this.selectedRoleId,
      user_id: this.selectedUserId,
    };
    this.server.showSpinner();
    this.server.putApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.showPage = 'List';
        this.offset = 0;
        this.limit = 10;
        this.getUsers();
      }
    });
  }

  // to manage pagination
  managePagination(page) {
    this.offset = page;
    this.total = 0;
    this.getUsers();
  }

  // on toggle
  onToggle() {
    this.isChecked = !this.isChecked;
    if (this.isChecked) {
      this.status = 'ACTIVE';
    } else {
      this.status = 'BLOCK';
    }
    this.limit = 10;
    this.offset = 0;
    this.getUsers();
  }

  checkIsAddArtwork(item) {
    let partner = item?.artist?.partners?.find((a) => {
      return a?.partner == this.userDetails?._id;
    });
    if (partner?.addArtwork === 'true') {
      return true;
    } else {
      return false;
    }
  }
  checkIsEditBio(item) {
    let partner = item?.artist?.partners?.find((a) => {
      return a?.partner == this.userDetails?._id;
    });
    if (partner?.editBio === 'true') {
      return true;
    } else {
      return false;
    }
  }
  checkIsAddArtwork2(item) {
    let partner = item?.artist?.partners?.find((a) => {
      return a?.partner == item?.partner?._id;
    });
    if (partner?.addArtwork === 'true') {
      return true;
    } else {
      return false;
    }
  }
  checkIsEditBio2(item) {
    let partner = item?.artist?.partners?.find((a) => {
      return a?.partner == item?.partner?._id;
    });
    if (partner?.editBio === 'true') {
      return true;
    } else {
      return false;
    }
  }
  addArtwork(item) {
    localStorage.setItem(
      'artworkArtistID',
      JSON.stringify({
        _id: item?.artist?._id,
        display_name: item?.artist?.display_name,
      })
    );
    this.router.navigate(['/artist-portal/settings/artwork/add']);
  }
  editBios(item) {
    localStorage.setItem('bioArtistID', JSON.stringify(item?.artist));
    this.router.navigate(['/artist-portal/settings/artwork/profile-page/list']);
  }

  openChangeAcces(item) {
    this.selectedUserId = item['_id'];
    this.changeAddArt = this.checkIsAddArtwork2(item);
    this.changeEditBio = this.checkIsEditBio2(item);

    this.isPopupOpen2 = true;
  }
  changeAccess() {
    let index = this.userList.findIndex((a) => {
      return a._id == this.selectedUserId;
    });
    let url = `partners-artist/change?partnerId=${this.userList[index].partner?._id}&artistId=${this.userList[index].artist?._id}&addArtwork=${this.changeAddArt}&editBio=${this.changeEditBio}`;
    // this.isDeactivatePopupOpen = false;
    this.server.showSpinner();
    this.server.getApi(url).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert('req accepted successfully!');
          this.offset = 0;
          this.ngOnInit();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }
}
