<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <!--SEO section-->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            *ngIf="showPage != 'List'"
          >
            <button class="btn-edit" (click)="onBack()">Back</button>
          </div>
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            *ngIf="showPage == 'List' && !isAdmin"
          >
            <button class="btn-edit" (click)="onAddingUser()">
              Invite Artist
            </button>
            <!-- <button class="mySlider shiftRight">
              <label class="switch">
                <input id="status" type="checkbox">
              <span class="slider round"></span>
            </label>
          </button> -->
            <!-- <span class="side-btn">ACTIVE</span>
            <label class="switch">
              <input type="checkbox" checked (change)="onToggle()" />
              <span class="slider round"></span>
            </label>
            <span class="side-btn">INACTIVE</span> -->
          </div>
          <div class="field-value doted">
            <div class="splitter" *ngIf="showPage != 'List'">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="email"
                  placeholder="Email"
                  (keyup)="isSearched = false"
                />
                <div class="placeholder">Email</div>
                <div class="input-info">Provide the email of the artist.</div>
              </div>
              <div
                *ngIf="!isSearched"
                class="text-center"
                style="margin-top: 2.08vw"
              >
                <button class="btn-edit" (click)="searchUser()">
                  Search Artist
                </button>
              </div>
            </div>
            <div class="splitter" *ngIf="showPage != 'List' && isSearched">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="first_name"
                  placeholder="First Name"
                  [attr.disabled]="searchedArtist"
                />
                <div class="placeholder">First Name</div>
                <div class="input-info">Provide the first name to be used.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="last_name"
                  placeholder="Last Name"
                  [attr.disabled]="searchedArtist"
                />
                <div class="placeholder">Last Name</div>
                <div class="input-info">Provide the last name to be used.</div>
              </div>
            </div>
            <div class="splitter" *ngIf="showPage != 'List' && isSearched">
              <div class="text-center" style="margin-top: 2.08vw">
                <button class="btn-edit" (click)="inviteArtist()">
                  {{ searchedArtist ? "Request Access" : "Request to Invite" }}
                </button>
              </div>
            </div>

            <div *ngIf="showPage == 'List'">
              <div>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Full Name</th>
                      <th class="text-center">Email</th>
                      <th *ngIf="isAdmin" class="text-center">Gallery</th>
                      <th class="text-center">Status</th>
                      <th class="text-center">Action</th>
                    </tr>
                  </thead>

                  <tbody id="MyTable">
                    <tr
                      *ngFor="
                        let item of userList
                          | paginate
                            : {
                                itemsPerPage: 10,
                                currentPage: p
                              };
                        let i = index
                      "
                    >
                      <td class="text-center">
                        {{ item?.first_name }} {{ item?.last_name }}
                      </td>
                      <td>{{ item?.email }}</td>
                      <td *ngIf="isAdmin">
                        {{ item?.partner?.first_name }}
                        {{ item?.partner?.last_name }}
                      </td>
                      <td>{{ item?.status }}</td>

                      <td *ngIf="isAdmin" class="text-center">
                        <button
                          class="btn-edit"
                          *ngIf="item?.status == 'REQUESTED'"
                          (click)="
                            isDeactivatePopupOpen = true;
                            selectedUserId = item['_id']
                          "
                        >
                          Accept
                        </button>
                        <button
                          class="btn-cancel"
                          (click)="
                            isPopupOpen = true; selectedUserId = item['_id']
                          "
                          *ngIf="item?.status == 'REQUESTED'"
                        >
                          Reject
                        </button>
                        <button
                          class="btn-edit"
                          *ngIf="item?.status == 'ACCEPTED'"
                          (click)="openChangeAcces(item)"
                        >
                          Change Access
                        </button>
                      </td>
                      <td *ngIf="!isAdmin" class="text-center">
                        <button
                          class="btn-edit"
                          *ngIf="checkIsAddArtwork(item)"
                          (click)="addArtwork(item)"
                        >
                          Add Artwork
                        </button>
                        <button
                          class="btn-edit"
                          *ngIf="checkIsEditBio(item)"
                          (click)="editBios(item)"
                        >
                          Edit Bio
                        </button>
                        <span
                          *ngIf="
                            !checkIsAddArtwork(item) &&
                            !checkIsEditBio(item) &&
                            item?.status == 'ACCEPTED'
                          "
                          >Access Removed</span
                        >
                      </td>
                    </tr>
                    <tr *ngIf="userList.length == 0">
                      <td class="text-center" colspan="3">No record found!</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div *ngIf="showPage == 'List'" class="pagination">
            <pagination-controls
              (pageChange)="p = $event"
            ></pagination-controls>
          </div>
          <!-- <div
            class="text-center mt10"
            *ngIf="showPage == 'Update' || showPage == 'Add'"
          >
            <button
              class="btn-edit"
              (click)="showPage == 'Update' ? updateUser() : addUser()"
            >
              {{ showPage }} User
            </button>
          </div> -->
        </form>
      </div>
    </div>
  </div>
</div>

<div
  id="myModal"
  class="modal"
  [style.display]="isPopupOpen ? 'block' : 'none'"
  style="background: #0000004d"
>
  <div class="modal-content">
    <p>Are you sure you wish to reject the request?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button
      ><button
        type="button"
        (click)="isPopupOpen = false; confirmDeactivate()"
        class="btnn"
      >
        Confirm
      </button>
    </div>
  </div>
</div>
<div
  id="myModal"
  class="modal"
  [style.display]="isDeactivatePopupOpen ? 'block' : 'none'"
  style="background: #0000004d"
>
  <div class="modal-content">
    <p>Choose access to provide to the Gallery</p>
    <div
      class="d-flex align-items-center justify-content-start input-field"
      style="width: 100%; margin-top: 0.5vw"
    >
      <input
        [(ngModel)]="addArt"
        [ngModelOptions]="{ standalone: true }"
        type="checkbox"
      />
      <label class="form-control-label" style="margin: 0; margin-left: 0.69vw">
        Add Artwork
      </label>
    </div>
    <div
      class="d-flex align-items-center justify-content-start input-field"
      style="width: 100%; margin-top: 0.5vw"
    >
      <input
        [(ngModel)]="editBio"
        [ngModelOptions]="{ standalone: true }"
        type="checkbox"
      />
      <label class="form-control-label" style="margin: 0; margin-left: 0.69vw"
        >Edit Bio
      </label>
    </div>

    <div class="buttonKeeper">
      <button
        type="button"
        class="btnn"
        (click)="isDeactivatePopupOpen = false"
      >
        Cancel</button
      ><button
        type="button"
        (click)="isDeactivatePopupOpen = false; confirmActivate()"
        class="btnn"
      >
        Confirm
      </button>
    </div>
  </div>
</div>
<div
  id="myModal"
  class="modal"
  [style.display]="isPopupOpen2 ? 'block' : 'none'"
  style="background: #0000004d"
>
  <div class="modal-content">
    <p>Change Gallery Access</p>
    <div
      class="d-flex align-items-center justify-content-start input-field"
      style="width: 100%; margin-top: 0.5vw"
    >
      <input
        [(ngModel)]="changeAddArt"
        [ngModelOptions]="{ standalone: true }"
        type="checkbox"
      />
      <label class="form-control-label" style="margin: 0; margin-left: 0.69vw">
        Add Artwork
      </label>
    </div>
    <div
      class="d-flex align-items-center justify-content-start input-field"
      style="width: 100%; margin-top: 0.5vw"
    >
      <input
        [(ngModel)]="changeEditBio"
        [ngModelOptions]="{ standalone: true }"
        type="checkbox"
      />
      <label class="form-control-label" style="margin: 0; margin-left: 0.69vw"
        >Edit Bio
      </label>
    </div>

    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen2 = false">
        Cancel</button
      ><button
        type="button"
        (click)="isPopupOpen2 = false; changeAccess()"
        class="btnn"
      >
        Confirm
      </button>
    </div>
  </div>
</div>
