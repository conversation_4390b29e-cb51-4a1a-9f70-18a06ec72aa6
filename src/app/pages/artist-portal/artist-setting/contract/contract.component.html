<div class="container__main">
	<div class="content__section">
		<div class="profile-field">
			<div class="w-100 field-contents">
				<form [formGroup]="form">
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<div class="input-container">
								<input
									formControlName="ContractType"
									type="text"
									class="selection"
									placeholder="Contract Type"
								/>
								<button (click)="isDropDownOpen[0] = !isDropDownOpen[0]" type="button">
									<img src="assets/icons/arrow-down.png" class="flag-arrow" />
								</button>
								<div
									[ngClass]="{
										'dropdown-hidden': !isDropDownOpen[0],
										'dropdown-visible': isDropDownOpen[0]
									}"
								>
									<ul>
										<li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Represented</div>
										</li>
										<li
											(click)="
												form.get('createdAs').setValue('Collaboratively');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Exhibition only</div>
										</li>
                                        <li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Archive only</div>
										</li>
                                        <li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Open Marketplace</div>
										</li>
                                        <li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">LE Prints</div>
										</li>
									</ul>
								</div>
							</div>
							<div class="input-info">
								Select the contract type
							</div>
						</div>
					</div>
                    <div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<div class="input-container">
								<input
									formControlName="ContractStatus"
									type="text"
									class="selection"
									placeholder="Contract Status"
								/>
								<button (click)="isDropDownOpen[0] = !isDropDownOpen[0]" type="button">
									<img src="assets/icons/arrow-down.png" class="flag-arrow" />
								</button>
								<div
									[ngClass]="{
										'dropdown-hidden': !isDropDownOpen[0],
										'dropdown-visible': isDropDownOpen[0]
									}"
								>
									<ul>
										<li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Signed</div>
										</li>
										<li
											(click)="
												form.get('createdAs').setValue('Collaboratively');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Pending</div>
										</li>
                                        <li
											(click)="
												form.get('createdAs').setValue('Individually');
												isDropDownOpen[0] = false
											"
										>
											<div class="country-name">Terminated</div>
										</li>
                                        
									</ul>
								</div>
							</div>
							<div class="input-info">
								Select the contract status
							</div>
						</div>
					</div>
					<div class="splitter">
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								formControlName="title"
								type="date"
                                style="width: 100%;"
							/>
							<div class="placeholder">Contract Start Date</div>
							<div class="input-info">
								Provide the start date of the contract
							</div>
						</div>
						<div class="field-value" style="padding-right: 0.5vw">
							<input
								formControlName="title"
								type="date"
                                style="width: 100%;"
							/>
							<div class="placeholder">Contract End Date</div>
							<div class="input-info">
								Provide the end date of the contract
							</div>
						</div>
					</div>
				
				</form>
			</div>
		</div>
	</div>
</div>
