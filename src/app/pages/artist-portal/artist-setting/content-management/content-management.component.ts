import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-content-management',
  templateUrl: './content-management.component.html',
  styleUrls: ['./content-management.component.scss']
})
export class ContentManagementComponent implements OnInit {
  discoverForm: FormGroup;
  discoverItems: FormArray;
  isPopupOpen:boolean=false;
  showDiscover:boolean=false
  constructor(private formBuilder: FormBuilder) { }

  ngOnInit(): void {
    this.initialiseForm()
  }
  
  // to initialise form
  initialiseForm() {
    this.discoverForm = new FormGroup({
      items: new FormArray([])
    });
    this.addItem()
  }

  createItem(): FormGroup {
    return this.formBuilder.group({
      main_heading: new FormControl(''),
      title: new FormControl(''),
      sub_title: new FormControl(''),
      btn_label: new FormControl(''),
      btn_route: new FormControl('')
    });
  }

  addItem(): void {
    this.discoverItems = this.discoverForm.get('items') as FormArray;
    this.discoverItems.push(this.createItem());
  }

  deleteComponent(){

  }

  removeDynamicForm(index:number) {
    const items = this.discoverForm.get('items') as FormArray;
    items.removeAt(index)
  }

}
