<div class="container__main">
    <div class="content__section">
      <div class="profile-field">
        <div class="w-100 field-contents">
          <form [formGroup]="discoverForm">
          <div>
            <div class="cmp">
               
                <label>Discover Component</label>
               
                <button class="btn-edit" (click)="addItem()">Add</button>
                <button class="btn-edit" (click)="showDiscover = !showDiscover">Show</button>
            </div>
            <div *ngIf="showDiscover">
            <div formArrayName="items" *ngFor="let item of discoverForm.get('items')?.controls; let i = index;">
            <div class="field-value doted" [formGroupName]="i">
                <div class="text-right">
                <img class="cross-icon" src="assets/images/cross.png" (click)="removeDynamicForm(i)">
                </div>

                <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                    <input type="text" formControlName="main_heading" placeholder="Main heading" />  
                    <div class="placeholder">Main heading</div>
                    <div class="input-info">Provide the main heading to be used.</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                    <input type="text" formControlName="title"  placeholder="Title" />
                    <div class="placeholder">Title</div>
                    <div class="input-info">
                    Provide the title to be used.
                    </div>
                </div>
                
                </div>
                <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                    <input type="text" formControlName="sub_title" placeholder="Sub Title" />  
                    <div class="placeholder">Sub Title</div>
                    <div class="input-info">Provide the sub title to be used.</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                    <input type="text" formControlName="btn_label" placeholder="Button Label" />
                    <div class="placeholder">Button Label</div>
                    <div class="input-info">
                    Provide the button label to be used.
                    </div>
                </div>
                </div>
                <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                    <input type="text" formControlName="btn_route" placeholder="Button Route" />  
                    <div class="placeholder">Button Route</div>
                    <div class="input-info">Provide the button route to be used.</div>
                </div>
                
                
                </div>
    
                
                

                
            </div>
            </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div id="myModal" class="modal" [style.display]="isPopupOpen ? 'block' : 'none'">
  <div class="modal-content">
    <p>Are you sure you wish to delete the discover component?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button><button type="button" (click)="isPopupOpen = false;deleteComponent()" class="btnn">
        Confirm
      </button>
    </div>
  </div>
</div>
