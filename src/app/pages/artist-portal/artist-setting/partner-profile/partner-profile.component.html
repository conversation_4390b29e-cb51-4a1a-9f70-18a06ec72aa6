<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Publish Profile :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="publish" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to publish the information on the website
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                type="text"
                placeholder="Gallery Name"
                formControlName="galleryName"
              />
              <div class="placeholder">Gallery Name</div>
              <div class="input-info"></div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <input type="text" placeholder="Url" formControlName="url_name" />
              <div class="placeholder">Page URL</div>
              <div class="input-info">
                Exact details to give in URL name for the gallery (e.g.:
                nature-morte)
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="placeholder">Banner Image</div>
              <div class="flex-block">
                <form-image-upload
                  [control]="form.get('bannerImage')"
                  [fileSize]="8388608"
                  [hideAltText]="true"
                ></form-image-upload>
              </div>
            </div>
            <div class="field-value">
              <div class="placeholder">Gallery Thumbnail</div>
              <div class="flex-block">
                <form-image-upload
                  [control]="form.get('galleryThumbnail')"
                  [fileSize]="8388608"
                  [hideAltText]="true"
                ></form-image-upload>
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="placeholder">Gallery Logo</div>
              <div class="flex-block">
                <form-image-upload
                  [control]="form.get('galleryLogo')"
                  [fileSize]="8388608"
                  [hideAltText]="true"
                ></form-image-upload>
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value">
              <div class="placeholder">Gallery Bio</div>
              <div class="flex-block">
                <angular-editor
                  *ngIf="htmlcheckBox[0]"
                  [placeholder]="' Gallery Bio'"
                  id="editor6"
                  formControlName="galleryBio"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[0]"
                  [placeholder]="' Gallery Bio'"
                  formControlName="galleryBio"
                  rows="6"
                  style="padding: 1vw; width: 88%"
                ></textarea>
                <div class="input-info">
                  Provide a short bio for the Gallery (less than 700 characters
                  with spaces) to be used on the profile page.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[0]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                type="text"
                placeholder="Location"
                formControlName="location"
              />
              <div class="placeholder">Gallery Location</div>
              <div class="input-info">
                Provide the gallery's physical location (City, Country). For
                e.g.: New Delhi, India.
              </div>
            </div>
            <div class="field-value">
              <input
                type="text"
                placeholder="Website"
                formControlName="website"
              />
              <div class="placeholder">Gallery Website</div>
              <div class="input-info">
                Exact URL for the gallery website (e.g.:
                https://www.naturemorte.com)
              </div>
            </div>
          </div>

          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input type="text" placeholder="Email" formControlName="email" />
              <div class="placeholder">Gallery Email</div>
              <div class="input-info">
                Provide the gallery's contact email for sales or general
                enquiries.
              </div>
            </div>
            <div class="field-value">
              <input
                type="text"
                placeholder="Contact Number"
                formControlName="number"
              />
              <div class="placeholder">Gallery Number</div>
              <div class="input-info">
                Provide the gallery's contact number, with Area code.
              </div>
            </div>
          </div>

          <div class="field-value doted">
            <div class="placeholder">Gallery Address</div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Address 1"
                  formControlName="address1"
                />
                <div class="placeholder">Address Line 1</div>
              </div>
              <div class="field-value">
                <input
                  type="text"
                  placeholder="Address 2"
                  formControlName="address2"
                />
                <div class="placeholder">Address Line 2</div>
              </div>
            </div>
            <div class="input-info">
              Provide the gallery's physical location address.
            </div>
          </div>
          <div class="splitter"></div>

          <div
            style="margin-top: 3.47vw; font-size: 1.11vw; margin-bottom: 1vw"
          >
            Social Media
          </div>
          <forms-dynamic-table
            [columns]="tableColumns"
            [formArray]="form.get('socialMedia')"
            [enableDrag]="true"
          ></forms-dynamic-table>

          <!-- Show Video -->
          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Video :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showVideo" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show a video on the Gallery profile page
            </div>
          </div>
          <div class="videoSection" *ngIf="form.get('showVideo').value">
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Link"
                  formControlName="videoLink"
                />
                <div class="placeholder">Intro Video</div>
                <div class="input-info">
                  Provide the Vimeo player link for the video to be featured on
                  the gallery page. e.g.: https://www.player.vimeo.com/12342143
                </div>
              </div>
              <div class="field-value">
                <div class="placeholder">Video Text</div>
                <angular-editor
                  *ngIf="htmlcheckBox[1]"
                  [placeholder]="' Video Text'"
                  id="editor6"
                  formControlName="videoText"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[1]"
                  [placeholder]="' Video Text'"
                  formControlName="videoText"
                  rows="6"
                  style="padding: 1vw; width: 88%"
                ></textarea>
                <div class="input-info">
                  Provide the text to be displayed on the Video thumbnail. e.g.:
                  Gallery Director talking about the current exhibition.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[1]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Show Artist -->
          <div *ngIf="false" class="field-value">
            <div class="input-container">
              <div class="text-before">Show Artist :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showArtist" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show list of Artists on the Gallery profile page
            </div>
          </div>
          <!-- *ngIf="form.get('showArtist').value" -->
          <div *ngIf="false" class="artistSection">
            <div style="margin-top: 3.47vw; font-size: 1.25vw">
              Select Artist
              <img
                class="plus-icon"
                (click)="addRepeater1()"
                src="assets/images/load-more.png"
                style="width: 1.9vw; margin-left: 2vw"
              />
            </div>
            <div class="field-value doted">
              <div
                formArrayName="selectArtist"
                *ngFor="let item of selectArtist.controls; let i = index"
              >
                <div class="splitter">
                  <div class="field-value">
                    <div class="input-container">
                      <input
                        type="text"
                        class="selection"
                        placeholder="Artist"
                        disabled
                      />
                      <button
                        (click)="isDropDownOpen2[0] = !isDropDownOpen2[0]"
                        type="button"
                      >
                        <img
                          src="assets/icons/arrow-down.png"
                          class="flag-arrow"
                        />
                      </button>
                      <div
                        [ngClass]="{
                          'dropdown-hidden': !isDropDownOpen2[0],
                          'dropdown-visible': isDropDownOpen2[0]
                        }"
                      >
                        <ul>
                          <li (click)="isDropDownOpen2[0] = false">
                            <div class="country-name">Artist 1</div>
                          </li>
                          <li (click)="isDropDownOpen2[0] = false">
                            <div class="country-name">Artist 2</div>
                          </li>
                          <li (click)="isDropDownOpen2[0] = false">
                            <div class="country-name">Artist 3</div>
                          </li>
                          <li (click)="isDropDownOpen2[0] = false">
                            <div class="country-name">Artist 4</div>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div [formGroupName]="i">
                  <div class="field-value">
                    <div class="input-container">
                      <div class="text-before">Default thumbnail :</div>
                      <label class="switch">
                        <input type="checkbox" formControlName="defThumbnail" />
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <div
                    class="field-value flex-block"
                    *ngIf="!selectArtist.controls[i].get('defThumbnail').value"
                  >
                    <div class="input-container file-upload">
                      <input
                        class="upload-input"
                        type="file"
                        (change)="onFileSelect(1, $event.target.files)"
                        accept="image/*"
                      />
                      <div *ngIf="selectedFiles[1].length <= 0" class="icon">
                        <img src="assets/icons/<EMAIL>" />
                      </div>
                      <div
                        *ngIf="selectedFiles[1].length <= 0"
                        class="text-content"
                      >
                        <div class="title">
                          You can upload or drop your file here.
                        </div>
                        <div class="sub-title">Maximum upload size: 2 MB</div>
                      </div>
                      <ng-container *ngIf="selectedFiles[1].length > 0">
                        <ng-container
                          *ngFor="let file of selectedFiles[1]; let i = index"
                        >
                          <div class="text-content">
                            <div class="title">
                              {{ file.name }}
                            </div>
                            <div class="sub-title">
                              File size:
                              {{ (file.size / 1048576).toFixed(2) }} MB
                            </div>
                            <div (click)="removeItem(1, i)" class="close">
                              <img src="assets/icons/close.png" />
                            </div>
                          </div>
                        </ng-container>
                      </ng-container>

                      <div class="button-container">
                        <div class="button">
                          {{
                            selectedFiles[1].length > 0
                              ? "Add file"
                              : "Choose file"
                          }}
                        </div>
                      </div>
                    </div>
                    <div class="input-info">
                      Provide the cropped image to be used on the gallery
                      listing page (cropped as per aspect ratio 1:1).
                    </div>
                  </div>
                </div>
              </div>
              <div class="input-info">
                Choose 3 to 6 artists to feature on the Gallery page
              </div>
            </div>
          </div>
          <!-- Show Artworks-->

          <div *ngIf="false" class="field-value">
            <div class="input-container">
              <div class="text-before">Show Artworks :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showArtworks" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show list of Artworks for sale on the Gallery
              profile page
            </div>
          </div>
          <!-- *ngIf="form.get('showArtworks').value" -->
          <div *ngIf="false" class="artworkSection">
            <div class="field-value doted">
              <!-- <div
                formArrayName="selectArtwork"
                *ngFor="let item of selectArtwork.controls; let i = index" -->
              <!-- > -->
              <div style="margin-top: 2.08vw; font-size: 1.25vw">
                Artwork Select
              </div>
              <div class="contain_">
                <div class="selection_container">
                  <div class="selection_headings">
                    <div class="property_title box">ID</div>
                    <div class="property_title box2">Primary Image</div>
                    <div class="property_title box2">Artist Name</div>
                    <div class="property_title box2">Artwork Title</div>
                    <div class="property_title box2">Year</div>
                    <div class="property_title box">Action</div>
                  </div>
                  <div class="selection_data">
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                    <div class="artwork_details">
                      <div class="properties box">56</div>
                      <div class="properties box2">
                        <img
                          src="https://picsum.photos/200/300"
                          alt="Artwork Thumbanail"
                        />
                      </div>
                      <div class="properties box2">Sukanya Garg</div>
                      <div class="properties box2">Panchama(Earth Mother)</div>
                      <div class="properties box2">2019</div>
                      <div class="properties box">
                        <p class="close_icon">&times;</p>
                      </div>
                    </div>
                  </div>
                </div>
                <button class="select" (click)="isArtworkPopupOpen = true">
                  Select Existing
                </button>
              </div>
              <!-- <div class="input-info">
                Choose the artworks to be shown in the artwork grid
              </div> -->
              <!-- </div> -->
              <div class="input-info">
                Choose 4 to 8 artworks to feature on the Gallery page
              </div>
            </div>
          </div>
          <!-- Show Exhibitions-->

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Exhibitions :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showExhibition" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show 3 featured Exhibitions on the Gallery
              profile page
            </div>
          </div>
          <div
            class="exhibitionSection"
            *ngIf="form.get('showExhibition').value"
          >
            <div style="margin-bottom: 1vw"></div>
            <forms-dynamic-table
              [columns]="tableColumns2"
              [formArray]="form.get('exhibitions')"
              [enableDrag]="true"
              [expandedMode]="true"
              [expandedColumn]="tableColumns2Expanded"
            ></forms-dynamic-table>
          </div>
          <!-- Show Events-->

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Events :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showEvent" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show 2 featured Events on the Gallery profile
              page
            </div>
          </div>

          <div class="eventSection" *ngIf="form.get('showEvent').value">
            <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <input
                    type="text"
                    class="selection"
                    placeholder="Events Title"
                    disabled
                    formControlName="eventTitle"
                  />
                  <button
                    (click)="isDropDownOpen4[0] = !isDropDownOpen4[0]"
                    type="button"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen4[0],
                      'dropdown-visible': isDropDownOpen4[0]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          isDropDownOpen4[0] = false;
                          form.get('eventTitle').setValue('Upcoming')
                        "
                      >
                        <div class="country-name">Upcoming</div>
                      </li>
                      <li
                        (click)="
                          isDropDownOpen4[0] = false;
                          form.get('eventTitle').setValue('Ongoing')
                        "
                      >
                        <div class="country-name">Ongoing</div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div style="margin-bottom: 1vw"></div>
            <forms-dynamic-table
              [columns]="tableColumns3"
              [formArray]="form.get('events')"
              [enableDrag]="true"
              [expandedMode]="true"
              [expandedColumn]="tableColumns3Expanded"
              [maxRow]="2"
            ></forms-dynamic-table>
          </div>

          <!-- Show Map-->
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Show Map :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showMap" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to show a map image of the gallery's primary
                location
              </div>
            </div>
            <div
              *ngIf="form.get('showMap').value"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <div class="placeholder">Map Image</div>
              <form-image-upload
                [control]="form.get('mapImage')"
                [fileSize]="8388608"
                [hideAltText]="true"
              ></form-image-upload>
            </div>
            <div class="field-value"></div>
          </div>

          <div
            class="artworkModal"
            [style.display]="isArtworkPopupOpen ? 'block' : 'none'"
          >
            <!-- Modal content -->
            <div class="artwork-modal-content">
              <div class="Addselection_container">
                <div class="Addtitle_bar">
                  Select Existing
                  <p class="Addclose_icon" (click)="isArtworkPopupOpen = false">
                    &times;
                  </p>
                </div>
                <div class="Addsearch_bar">
                  <div class="search_container">
                    <div class="fieldd-value">
                      <input type="text" placeholder="Search" />
                    </div>
                  </div>
                  <div class="filter_container">
                    <div class="fieldd-value">
                      <div
                        class="input-container"
                        style="width: 12vw !important"
                      >
                        <input
                          formControlName="filter"
                          placeholder="form.get('filter').value"
                          type="text"
                          class="selection"
                          disabled
                        />
                        <button
                          (click)="isFilterDropdownOpen = !isFilterDropdownOpen"
                          type="button"
                        >
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !isFilterDropdownOpen,
                            'dropdown-visible': isFilterDropdownOpen
                          }"
                        >
                          <ul>
                            <li
                              (click)="
                                isFilterDropdownOpen = false;
                                form.get('filter').setValue('Artist Name')
                              "
                            >
                              <div class="country-name">Artist Name</div>
                            </li>
                            <li
                              (click)="
                                isFilterDropdownOpen = false;
                                form.get('filter').setValue('Artwork Title')
                              "
                            >
                              <div class="country-name">Artwork Title</div>
                            </li>
                            <li
                              (click)="
                                isFilterDropdownOpen = false;
                                form.get('filter').setValue('ID')
                              "
                            >
                              <div class="country-name">ID</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="fieldd-value">
                      <div
                        class="input-container"
                        style="width: 7vw !important"
                      >
                        <input
                          formControlName="sort"
                          placeholder="form.get('sort').value"
                          type="text"
                          class="selection"
                        />
                        <button
                          (click)="
                            isFilterDropdownOpen1 = !isFilterDropdownOpen1
                          "
                          type="button"
                        >
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="{
                            'dropdown-hidden': !isFilterDropdownOpen1,
                            'dropdown-visible': isFilterDropdownOpen1
                          }"
                        >
                          <ul>
                            <li
                              (click)="
                                isFilterDropdownOpen1 = false;
                                form.get('sort').setValue('&uarr;')
                              "
                            >
                              <div class="country-name">&uarr;</div>
                            </li>
                            <li
                              (click)="
                                isFilterDropdownOpen1 = false;
                                form.get('sort').setValue('&darr;')
                              "
                            >
                              <div class="country-name">&darr;</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="Addselection_headings">
                  <div class="Addproperty_title Addbox">
                    <input type="checkbox" name="title" id="title" disabled />
                  </div>
                  <div class="Addproperty_title Addbox">ID</div>
                  <div class="Addproperty_title Addbox2">Primary Image</div>
                  <div class="Addproperty_title Addbox2">Artist Name</div>
                  <div class="Addproperty_title Addbox2">Artwork Title</div>
                </div>
                <div class="Addselection_data">
                  <div class="Addartwork_details">
                    <div class="Addproperties Addbox">
                      <input type="checkbox" name="asd" id="asd" />
                    </div>
                    <div class="Addproperties Addbox">56</div>
                    <div class="Addproperties Addbox2">
                      <img
                        src="https://picsum.photos/200/300"
                        alt="Artwork Thumbanail"
                      />
                    </div>
                    <div class="Addproperties Addbox2">Sukanya Garg</div>
                    <div class="Addproperties Addbox2">
                      Panchama(Earth Mother)
                    </div>
                  </div>
                </div>
                <div class="Addfooter_bar">
                  <button
                    class="Addselect"
                    (click)="isArtworkPopupOpen = false"
                  >
                    Cancel
                  </button>
                  <button
                    class="Addselect"
                    style="float: right"
                    (click)="isArtworkPopupOpen = false"
                  >
                    Done
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="updateProfile()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
