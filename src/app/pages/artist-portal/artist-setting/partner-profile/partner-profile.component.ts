import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
@Component({
  selector: 'app-partner-profile',
  templateUrl: './partner-profile.component.html',
  styleUrls: ['./partner-profile.component.scss'],
})
export class PartnerProfileComponent implements OnInit {
  tableColumns = [
    {
      name: 'Website',
      type: 'select',
      control: 'website',
      data: [
        {
          label: 'Facebook',
          value: 'Facebook',
        },
        {
          label: 'Instagram',
          value: 'Instagram',
        },
        {
          label: 'Twitter',
          value: 'Twitter',
        },
        {
          label: 'LinkedIn',
          value: 'LinkedIn',
        },
      ],
    },
    { name: 'URL', type: 'text', control: 'url' },
  ];
  tableColumns2 = [
    { name: 'Exhibition Title', type: 'text', control: 'title' },
    {
      name: 'Thumbnail Image',
      type: 'media',
      control: 'thumbnail',
    },
  ];
  tableColumns2Expanded = [
    { name: 'Exhibition Text', type: 'text', control: 'text' },
    { name: 'Exhibition URL', type: 'text', control: 'url' },
  ];
  tableColumns3 = [
    { name: 'Exhibition Title', type: 'text', control: 'title' },
    {
      name: 'Thumbnail Image',
      type: 'media',
      control: 'thumbnail',
    },
  ];
  tableColumns3Expanded = [
    { name: 'Exhibition Text', type: 'text', control: 'text' },
    { name: 'Exhibition URL', type: 'text', control: 'url' },
    { name: 'Exhibition Date', type: 'text', control: 'date' },
  ];
  isArtworkPopupOpen;
  isFilterDropdownOpen = false;
  isFilterDropdownOpen1 = false;
  isDropDownOpen = Array(10).fill(false);
  isDropDownOpen2 = Array(10).fill(false);
  isDropDownOpen3 = Array(10).fill(false);
  isDropDownOpen4 = Array(10).fill(false);

  selectedFiles: File[][] = [
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
  ];

  form: FormGroup;
  htmlcheckBox = Array(10).fill(false);

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private server: CollectorService
  ) {}

  ngOnInit(): void {
    this.isArtworkPopupOpen = false;
    this.form = this.formBuilder.group({
      bannerImage: new FormControl(),
      galleryName: new FormControl(),
      galleryThumbnail: new FormControl(),
      galleryLogo: new FormControl(),
      galleryBio: new FormControl(),
      location: new FormControl(),
      website: new FormControl(),
      email: new FormControl(),
      number: new FormControl(),
      address1: new FormControl(),
      address2: new FormControl(),
      url_name: new FormControl(),
      publish: new FormControl(false),
      socialMedia: this.formBuilder.array([]),
      showVideo: new FormControl(false),
      videoLink: new FormControl(''),
      videoText: new FormControl(''),
      showExhibition: new FormControl(false),
      exhibitions: this.formBuilder.array([]),
      showEvent: new FormControl(false),
      eventTitle: new FormControl(''),
      events: this.formBuilder.array([]),
      showMap: new FormControl(false),
      mapImage: new FormControl(),

      // filter: new FormControl({ value: 'ID', disabled: true }),
      // sort: new FormControl({ value: 'Sort', disabled: true }),

      // selectArtist: this.formBuilder.array([
      //   this.formBuilder.group({ defThumbnail: new FormControl(true) }),
      // ]),
      // // selectArtwork: this.formBuilder.array([this.formBuilder.group({})]),
      // selectExhibition: this.formBuilder.array([this.formBuilder.group({})]),
      // upcomingEvent: this.formBuilder.array([this.formBuilder.group({})]),

      // showArtist: new FormControl(false),
      // showArtworks: new FormControl(false),
    });
    this.getProfile();
  }

  getProfile() {
    let url = `partner/mine`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200 && res.data?.contents) {
        this.form.patchValue(res.data?.contents);
        if (res.data?.contents?.socialMedia?.length > 0) {
          res.data?.contents?.socialMedia?.forEach((a) => {
            const row = Object.keys(a).reduce((acc, column) => {
              acc[column] = new FormControl(a[column]);
              return acc;
            }, {});
            (this.form.get('socialMedia') as FormArray).push(
              new FormGroup(row)
            );
          });
        }
        if (res.data?.contents?.exhibitions?.length > 0) {
          res.data?.contents?.exhibitions?.forEach((a) => {
            const row = Object.keys(a).reduce((acc, column) => {
              acc[column] = new FormControl(a[column]);
              return acc;
            }, {});
            (this.form.get('exhibitions') as FormArray).push(
              new FormGroup(row)
            );
          });
        }
        if (res.data?.contents?.events?.length > 0) {
          res.data?.contents?.events?.forEach((a) => {
            const row = Object.keys(a).reduce((acc, column) => {
              acc[column] = new FormControl(a[column]);
              return acc;
            }, {});
            (this.form.get('events') as FormArray).push(new FormGroup(row));
          });
        }
      }
    });
  }

  updateProfile() {
    let url = `partner/mine`;
    let contents = this.form.getRawValue();
    this.server.showSpinner();
    this.server
      .postApi(url, {
        contents,
        publish: contents?.publish,
        url_name: contents?.url_name,
        galleryName: contents?.galleryName,
      })
      .subscribe((res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200 && res.data) {
          alert('Success');
        }
      });
  }

  // defThumbnail: new FormControl(false)

  get selectArtist(): FormArray {
    return <FormArray>this.form.get('selectArtist');
  }
  get socialMedia(): FormArray {
    return <FormArray>this.form.get('socialMedia');
  }
  // get selectArtwork(): FormArray {
  //   return <FormArray>this.form.get('selectArtwork');
  // }
  get selectExhibition(): FormArray {
    return <FormArray>this.form.get('selectExhibition');
  }
  get upcomingEvent(): FormArray {
    return <FormArray>this.form.get('upcomingEvent');
  }

  async onFileSelect(index: number, files: FileList) {
    console.log(index);

    if (files[0].size < 2100000) {
      this.selectedFiles[index].push(files[0]);
    }
    console.log(this.selectedFiles);
  }
  removeItem(index: number, item) {
    this.selectedFiles[index].splice(item, 1);
  }
  addRepeater() {
    this.socialMedia.push(this.formBuilder.group({}));
  }
  addRepeater1() {
    this.selectArtist.push(
      this.formBuilder.group({ defThumbnail: new FormControl(true) })
    );
  }
  // addRepeater2() {
  //   this.selectArtwork.push(
  //     this.formBuilder.group({})
  //   );
  // }
  addRepeater3() {
    this.selectExhibition.push(this.formBuilder.group({}));
  }
  addRepeater4() {
    this.upcomingEvent.push(this.formBuilder.group({}));
  }
  onSubmit() {}
}
