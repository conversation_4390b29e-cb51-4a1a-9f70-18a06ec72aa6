import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  FormControl,
  FormArray,
  Validators,
} from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CollectorService } from 'src/app/services/collector.service';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-invite-users',
  templateUrl: './invite-users.component.html',
  styleUrls: ['./invite-users.component.scss'],
})
export class InviteUsersComponent implements OnInit, OnDestroy {
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  routerObserver: Subscription;
  activeMenu = 'main';
  searchValue: any = '';
  userDetails;
  ngOnDestroy(): void {
    // this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetails = JSON.parse(decode);
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/invite-user':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/invite-user/gallery':
        this.activeMenu = 'gallery';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }

  searchResult() {}

  // onMenuClick(index) {
  //   console.log(index);
  //   switch (index) {
  //     case 0:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/main']);
  //       break;
  //     case 1:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/add-on']);
  //       break;
  //     default:
  //       break;
  //   }
  // }
}
