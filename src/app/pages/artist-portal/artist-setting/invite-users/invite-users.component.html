<div>
  <div class="d-flex">
    <div class="section left-container" style="width: 100%">
      <div class="section-inner">
        <div class="d-flex justify-content-start slider-header">
          <a [routerLink]="'/artist-portal/settings/invite-user'">
            <h3
              [ngClass]="{
                active: activeMenu === 'main'
              }"
            >
              Main
            </h3>
          </a>
          <a
            *ngIf="userDetails?.role === 'SUPERADMIN'"
            [routerLink]="'/artist-portal/settings/invite-user/gallery'"
          >
            <h3
              [ngClass]="{
                active: activeMenu === 'gallery'
              }"
            >
              Gallery Invite
            </h3>
          </a>
          <!-- <a [routerLink]="
              '/artist-portal/settings/artwork/profile-page/:id/seo'
            ">
                        <h3 [ngClass]="{
                active: activeMenu === 'seo'
              }">
                            SEO
                        </h3>
                    </a> -->
        </div>

        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</div>
