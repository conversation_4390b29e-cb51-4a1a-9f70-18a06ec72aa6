<div class="section">
  <div class="section-inner">
    <div class="w-100">
      <div class="heading">
        <div class="head">
          <a class="link-head" routerLink=".."
            ><img src="assets/images/back-chevron.png" title="Back"
          /></a>
          Your favourite artists
          <a class="mob-link-head" routerLink=".."
            ><img src="assets/icons/grey-close.png"
          /></a>
        </div>
        <!-- (click)="choose(0)" -->
        <!-- <div class="sub">

        </div> -->
      </div>

      <div class="collector-form" *ngIf="!(isCancel || isSave || isMessage)">
        <div class="search-input">
          <input
            type="text"
            placeholder="Search"
            (keyup)="searchArtist($event.target.value)"
          />
          <img src="assets/icons/search/<EMAIL>" />
        </div>
        <span *ngFor="let art of artists">
          <span *ngFor="let item of selectedArtist">
            <span class="chip" *ngIf="art?.id == item"
              >{{ art?.display_name }}
              <fa-icon
                style="margin-left: 0.2vw"
                [icon]="faTimes"
                (click)="selectArtist(art?.id)"
              ></fa-icon> </span
          ></span>
        </span>
        <div class="interests">
          <div class="contents">
            <div
              *ngFor="let artist of artists; let i = index"
              class="artist"
              [ngClass]="{
                active: selectedArtist.indexOf(artist?.id) !== -1
              }"
            >
              <img
                (click)="selectArtist(artist?.id)"
                [src]="
                  'https://www.terrain.art/cdn-cgi/image/width=200,quality=52/https://register.terrain.art/artist-portal/assets/' +
                  artist?.artist_list_thumbnail?.private_hash
                "
              />
              <div class="name">{{ artist?.display_name }}</div>
            </div>
            <div *ngFor="let item of emptyDiv" class="artist"></div>
          </div>
        </div>
        <div class="flex-block buttonList">
          <button class="save" (click)="isSave = true">Save</button>
          <button
            class="cancel"
            [routerLink]="['..']"
            routerLinkActive="router-link-active"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <div class="popup" *ngIf="isCancel || isSave">
      <div class="tab">
        <div class="close">
          <a style="cursor: pointer" (click)="close()">
            <img src="assets/icons/grey-close.png" />
          </a>
        </div>
        <h1 *ngIf="isCancel">Are you sure you want to exit ?</h1>
        <p *ngIf="isCancel">Changes you made so far will not be saved</p>
        <h1 *ngIf="isSave">Are you sure you want to save ?</h1>
        <p *ngIf="isSave">Changes you made will be saved</p>
        <div class="buttonList">
          <button class="save" (click)="submit()">Save</button>
          <button class="cancel" (click)="isCancel = false; isSave = false">
            Cancel
          </button>
        </div>
      </div>
    </div>

    <div class="popup" *ngIf="isMessage">
      <div class="tab1">
        <div class="close">
          <a style="cursor: pointer" (click)="close()">
            <img src="assets/icons/grey-close.png" />
          </a>
        </div>
        <h1 *ngIf="isSuccess">Changes are successfully updated</h1>
        <h1 *ngIf="!isSuccess">Updation was unsuccessful</h1>
      </div>
    </div>
  </div>
</div>
