import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { ArtistService } from 'src/app/pages/collector/services/artist.service';
import { CollectorService } from 'src/app/pages/collector/services/collector.service';
import { PersonalInfo } from 'src/app/pages/explore/artists/models/personal-info.model';
// import { ArtistService } from '../../../services/artist.service';
// import { CollectorService } from '../../../services/collector.service';

@Component({
  selector: 'app-artists',
  templateUrl: './artists.component.html',
  styleUrls: ['./artists.component.scss'],
})
export class ArtistsComponent implements OnInit {
  isCancel = false;
  isMessage = false;
  isSuccess = false;
  isSave = false;
  faTimes=faTimes;

  content = {
    1: {
      title: 'Buy art',
      isChecked: false,
    },
    2: {
      title: 'Learn about art',
      isChecked: false,
    },
    3: {
      title: 'Learn about artists',
      isChecked: false,
    },
    4: {
      title: 'Discover curations',
      isChecked: false,
    },
    5: {
      title: 'Learn about art pricing',
      isChecked: false,
    },
  };

  artists: PersonalInfo[];
  selectedArtist = [];
  emptyDiv = [];

  constructor(
    private artistService: ArtistService,
    private router: Router,
    private collectorService: CollectorService
  ) {}

  ngOnInit(): void {
    this.getData();
    this.searchArtist();
  }

  getData = async () => {
    this.collectorService.getPreferenceByType('artist').subscribe((data) => {
      const current = [];
      data?.data?.ids.filter((id: number) => {
        current.push(id);
      });
      this.selectedArtist = current;
    });
  };

  searchArtist(value: string = '') {
    this.artistService.getAllArtists(value).then((data) => {
      this.artists = data;
      const reminder = this.artists?.length % 4;
      if (reminder > 0) {
        this.emptyDiv = Array(reminder).fill(0);
      }
    });
  }

  selectArtist(id) {
    const position = this.selectedArtist.indexOf(id);
    if (position !== -1) {
      this.selectedArtist.splice(position, 1);
    } else {
      this.selectedArtist.push(id);
    }
    this.selectedArtist.sort((a, b) => a - b);
  }

  choose(index) {
    if (index === 0) {
      for (let i = 1; i < 5; i++) this.content[i].isChecked = true;
    } else this.content[index].isChecked = !this.content[index].isChecked;
  }

  submit() {
    this.collectorService
      .addPreference('artist', {
        ids: this.selectedArtist,
      })
      .subscribe(
        (data) => {
          this.isSuccess = true;
          this.isCancel = false;
          this.isSave = false;
          this.isMessage = true;
        },
        (err) => {
          this.isSuccess = false;
          this.isCancel = false;
          this.isSave = false;
          this.isMessage = true;
        }
      );
  }

  close() {
    this.isCancel = false;
    this.isSave = false;
    this.isMessage = false;
    if (this.isSuccess)
      this.router.navigateByUrl('collector/profile/preferences');
  }
}
