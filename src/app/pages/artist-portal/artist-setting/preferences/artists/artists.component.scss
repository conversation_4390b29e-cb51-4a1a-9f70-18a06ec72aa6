.heading {
  // margin-bottom: 1.47vw;
  margin-bottom: 0;
  position: relative;
  .head {
    width: 82.5%;
    font-size: 1.66vw;
    // font-weight: 600;
    display: flex;
    justify-content: start;
    align-items: center;
    img {
      width: 2.29vw;
      height: 2.29vw;
      margin-right: 1.08vw;
    }
  }
  .sub {
    font-size: var(--default-font-size);
    font-family: var(--secondary-font);
    margin-top: 0.42vw;
    padding-left: 3.33vw;
  }
}
.flex-block {
  display: flex !important;
}

.collector-form {
  .search-input {
    font-size: var(--default-font-size);
    margin-top:1vw;
    position: relative;
    input[type="text"] {
      width: 100%;
      padding: 0.34vw 0;
      border: none;
      text-indent: 2.08vw;
      color: var(--quaternary-font-color);
      border-bottom: 0.138vw solid var(--timeline-color);
      &:focus {
        outline: none;
      }
    }
    img {
      position: absolute;
      left: 0;
      top: 50%;
      height: 0.97vw;
      width: 0.97vw;
      transform: translate(0, -50%);
    }
  }
  .interests {
    width: auto;
    // margin-left: 0;
    // margin-top: 1.74vw;
    // overflow-y: auto;
    // height: calc(100vh - 25.471vw);
    // height: calc(100vh - 44.75vw);
    // padding-bottom: 8vw;
    margin-left: 0;
    margin-top: 0.5vw;
    overflow-y: auto;
    height: calc(100vh - 22.5vw);
    .contents {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .artist {
        width: 15.06vw;
        padding: 1.74vw 0;
        img {
          width: 14.93vw;
          height: 10.69vw;
          min-height: 10.69vw;
          -o-object-fit: cover;
          object-fit: cover;
          border-radius: 0.41vw;
          cursor: pointer;
        }
        .name {
          font-size: var(--default-font-size);
          margin-top: 1.04vw;
        }
        &.active {
          color: var(--tertiary-font-color);
          img {
            border: solid 0.25vw var(--tertiary-font-color);
          }
        }
      }
    }
  }
  .buttonList {
    margin-top: 3.47vw;
    margin-top: 1.47vw;
    .save {
      outline: none;
      font-size: 1.25vw;
      min-width: 20.56vw;
      background-color: var(--primary-background-color);
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }
    .save:hover {
      font-weight: 600;
    }
    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: var(--primary-background-color);
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }
    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }
}

.popup {
  position: absolute;
  top: -24.15vw;
  left: -28.88vw;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  text-align: center;
  .tab {
    padding: 2.08vw 3.47vw;
    background-color: var(--primary-background-color);
    width: 35.63vw;
    height: 21.32vw;
    transform: translate(32.19vw, 34.34vh);
    position: relative;
    .close {
      position: absolute;
      right: 2.08vw;
      top: 2.08vw;
      img {
        width: 2.29vw;
        height: 2.29vw;
      }
    }
    h1 {
      margin: 0;
      margin-top: 3.125vw;
      font-size: 1.67vw;
    }
    p {
      margin: 0;
      margin-top: 1.39vw;
      color: var(--quaternary-font-color);
      font-size: 1.11vw;
    }
    .buttonList {
      margin-top: 3.47vw;
      display: inline-grid;
      .save {
        outline: none;
        font-size: 1.25vw;
        width: 20.56vw;
        background-color: var(--primary-background-color);
        color: var(--tertiary-font-color);
        padding: 0.83vw 1.04vw;
        border: 0.069vw solid var(--tertiary-font-color);
        border-radius: 1.46vw;
      }
      .save:hover {
        font-weight: 600;
      }
      .cancel {
        outline: none;
        font-size: 1.25vw;
        background-color: var(--primary-background-color);
        color: var(--quaternary-font-color);
        padding: 1.39vw 2.78vw;
        border: none;
      }
      .cancel:hover {
        font-weight: 600;
        // color: var(--tertiary-font-color);
      }
    }

  }
  .tab1 {
    padding: 2.08vw 3.47vw;
    background-color: var(--primary-background-color);
    width: 35.63vw;
    height: auto;
    transform: translate(32.19vw, 34.34vh);
    position: relative;
    color: var(--secondary-font);
    .close {
      position: absolute;
      right: 2.08vw;
      top: 2.08vw;
      img {
        width: 2.29vw;
        height: 2.29vw;
      }
    }
    h1 {
      margin: 3.125vw 0;
      font-size: 1.67vw;
    }
  }
}

.section {
  position: relative;
  width: 100%;
  left: 0;
  top: 0;
  height: auto;
  padding-top: 0;
  background-color: transparent;
  padding-left: 0;
  padding-right: 0;
}
.section-inner {
  position: relative;
}
.link-head {
  display: block !important;
}
.mob-link-head {
  display: none !important;
}

@media (max-width: 768px) {
  .section {
    position: absolute;
    width: 100%;
    left: 0;
    top: -14.97vw;
    height: 100vh;
    padding-top: 12.56vw;
    background-color: var(--primary-background-color);
    padding-left: 5.797vw;
    padding-right: 6.04vw;
  }
  .mob-link-head {
    display: block !important;
  }
  .link-head {
    display: none !important;
  }

  .heading {
    margin-bottom: 7.25vw;
    .head {
      font-size: 5.797vw;
      img {
        position: absolute;
        width: 7.97vw;
        height: 7.97vw;
        top: 0;
        right: 0;
        margin-right: 0;
      }
    }
    .sub {
      font-family: var(--secondary-font);
      font-size: 3.87vw;
      margin-top: 3.62vw;
      padding-left: 0;
    }
  }

  .collector-form {
    .search-input {
      font-size: 3.87vw;
      margin-top: 0;
      position: relative;
      input[type="text"] {
        width: 100%;
        padding: 4.84vw 0;
        text-indent: 7.25vw;
        border: none;
        color: var(--quaternary-font-color);
        border-bottom: 0.242vw solid var(--timeline-color);
      }
      img {
        position: absolute;
        left: 0;
        top: 50%;
        height: 3.382vw;
        width: 3.382vw;
        transform: translate(0, -50%);
      }
    }
    .interests {
      width: 100vw;
      margin-left: -5.797vw;
      margin-top: 7.25vw;
      overflow-y: auto;
      // height: calc(100vh - 25.471vw);
      height: calc(100vh - 96vw);
      padding-bottom: 8vw;

      .contents {
        justify-content: space-around;
        .artist {
          width: 46.62vw;
          padding: 4.84vw 0;
          img {
            width: 46.62vw;
            height: 28.99vw;
            min-height: 28.99vw;
            border-radius: 1.21vw;
          }
          .name {
            font-size: 3.87vw;
            margin-top: 1.04vw;
          }
          &.active {
            color: var(--tertiary-font-color);
            img {
              border: solid 0.013vw var(--tertiary-font-color);
            }
          }
        }
      }
    }
    .buttonList {
      margin-top: 12.08vw;
      .save {
        font-size: 4.35vw;
        min-width: 100%;
        padding: 3.62vw 3.62vw;
        border: 0.242vw solid var(--tertiary-font-color);
        border-radius: 5.19vw;
      }
      .save:hover {
        font-weight: 600;
      }
      .cancel {
        width: 100%;
        font-size: 3.86vw;
        padding: 4.84vw 4.84vw;
      }
      .cancel:hover {
        font-weight: 600;
        // color: var(--tertiary-font-color);
      }
    }
  }
  .flex-block {
    display: block !important;
  }

  .popup {
    top: -12.5vw;
    left: -5.797vw;
    .tab {
      padding: 20.05vw 9.66vw 2.42vw 9.66vw;
      width: 88.16vw;
      height: 89.13vw;
      transform: translate(6.19vw, 25.34vh);
      .close {
        position: absolute;
        right: 4.84vw;
        top: 4.84vw;
        img {
          width: 7.97vw;
          height: 7.97vw;
        }
      }
      h1 {
        margin: 0;
        margin-top: 0;
        font-size: 5.797vw;
      }
      p {
        margin: 0;
        margin-top: 4.84vw;
        color: var(--quaternary-font-color);
        font-size: 3.87vw;
      }
      .buttonList {
        margin-top: 12.08vw;
        display: inline-grid;
        width: 100%;
        .save {
          font-size: 4.35vw;
          width: 100%;
          padding: 3.62vw 3.62vw;
          border: 0.242vw solid var(--tertiary-font-color);
          border-radius: 5.19vw;
        }
        .cancel {
          font-size: 3.87vw;
          padding: 4.84vw 4.84vw;
        }
      }

    }
    .tab1 {
      padding: 20.05vw 9.66vw;
      width: 88.16vw;
      transform: translate(6.19vw, 25.34vh);
      .close {
        position: absolute;
        right: 4.84vw;
        top: 4.84vw;
        img {
          width: 7.97vw;
          height: 7.97vw;
        }
      }
      h1 {
        margin: 0;
        margin-top: 0;
        font-size: 5.797vw;
      }
    }
  }

}
.chip{
  padding: 0.2vw 0.5vw;
  background-color: #6695ef;
  color: white;
  font-size: 0.6944vw;
  border-radius: 10vw;
  margin-top: .3vw;
  vertical-align: middle;
}