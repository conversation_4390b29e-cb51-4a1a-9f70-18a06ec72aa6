import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { ArtistService } from 'src/app/pages/collector/services/artist.service';
import { CollectorService } from 'src/app/pages/collector/services/collector.service';
// import { ArtistService } from '../../../services/artist.service';
// import { CollectorService } from '../../services/collector.service';

@Component({
  selector: 'app-arts',
  templateUrl: './arts.component.html',
  styleUrls: ['./arts.component.scss'],
})
export class ArtsComponent implements OnInit {
  isCancel = false;
  isMessage = false;
  isSuccess = false;
  isSave = false;
  faTimes=faTimes;
  content = {
    1: {
      title: 'Buy art',
      isChecked: false,
    },
    2: {
      title: 'Learn about art',
      isChecked: false,
    },
    3: {
      title: 'Learn about artists',
      isChecked: false,
    },
    4: {
      title: 'Discover curations',
      isChecked: false,
    },
    5: {
      title: 'Learn about art pricing',
      isChecked: false,
    },
  };
  mediums = [];
  movements = [];
  keywords = [];
  selectedKeyword = [];
  emptyDiv2 = [];

  constructor(
    private artistService: ArtistService,
    private router: Router,
    private collectorService: CollectorService
  ) {}

  ngOnInit(): void {
    this.getData();
    this.searchMovmentandkeywords();
  }

  getData = async () => {
    this.collectorService.getPreferenceByType('art').subscribe((data) => {
      const current = [];
      data?.data?.ids.filter((id: number) => {
        current.push(id);
      });
      this.selectedKeyword = current;
    });
  };

  searchMovmentandkeywords(value: string = '') {
    this.artistService.getAllKeywords(value).then(async (data) => {
      this.mediums = data;
      this.movements = await this.artistService.getAllMovements(value);
      if (this.mediums.length > 0 && this.movements.length > 0) {
        this.keywords = [this.mediums, this.movements]
          .reduce(
            (r, a) => (a.forEach((a, i) => (r[i] = r[i] || []).push(a)), r),
            []
          )
          .reduce((a, b) => a.concat(b));
      } else {
        this.keywords = [...this.mediums, ...this.movements];
      }

      const reminder = this.keywords?.length % 4;
      if (reminder > 0) {
        this.emptyDiv2 = Array(reminder).fill(0);
      }
    });
  }

  selectKeyword(id) {
    const position = this.selectedKeyword.indexOf(id);
    if (position !== -1) {
      this.selectedKeyword.splice(position, 1);
    } else {
      this.selectedKeyword.push(id);
    }
    this.selectedKeyword.sort((a, b) => a - b);
  }

  choose(index) {
    if (index === 0) {
      for (let i = 1; i < 5; i++) this.content[i].isChecked = true;
    } else this.content[index].isChecked = !this.content[index].isChecked;
  }

  submit() {
    this.collectorService
      .addPreference('art', {
        ids: this.selectedKeyword,
      })
      .subscribe(
        (data) => {
          this.isSuccess = true;
          this.isCancel = false;
          this.isSave = false;
          this.isMessage = true;
        },
        (err) => {
          this.isSuccess = false;
          this.isCancel = false;
          this.isSave = false;
          this.isMessage = true;
        }
      );
  }

  close() {
    this.isCancel = false;
    this.isSave = false;
    this.isMessage = false;
    if (this.isSuccess)
      this.router.navigateByUrl('collector/profile/preferences');
  }
}
