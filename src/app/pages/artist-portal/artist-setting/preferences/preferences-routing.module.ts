import { ArtistsComponent } from './artists/artists.component';
import { MainComponent } from './main/main.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ArtsComponent } from './arts/arts.component';

const routes: Routes = [{
  path:'',
  component:MainComponent
},
{
  path:'artists',
  component:ArtistsComponent
},
{
  path:'arts',
  component:ArtsComponent
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PreferencesRoutingModule { }
