import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ArtistsComponent } from './artists/artists.component';
import { ArtsComponent } from './arts/arts.component';

import { PreferencesRoutingModule } from './preferences-routing.module';
import { MainComponent } from './main/main.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [MainComponent,ArtistsComponent,ArtsComponent],
  imports: [
    CommonModule,
    PreferencesRoutingModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
   
  ]
})
export class PreferencesModule { }
