import { Component, OnInit } from '@angular/core';
import { ArtistService } from 'src/app/pages/collector/services/artist.service';
import { CollectorService } from 'src/app/pages/collector/services/collector.service';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
  constructor(
    private collectorService: CollectorService,
    private artistService: ArtistService
  ) {}

  artist = [];
  art = [];
  looking = [];
  isLoadArt = false;
  isLoadArtist = false;
  isLoadLooking = false;
  content = {
    1: 'Buy art',
    2: 'Learn about art',
    3: 'Learn about artists',
    4: 'Discover curations',
    5: 'Learn about art pricing',
  };
  mediums = [];
  movements = [];
  keywordsList = [];
  artistsList = [];
  // getArtist
  ngOnInit(): void {
    // this.getData();
  }

  getData = async () => {
    this.collectorService.getPreferenceByType('art').subscribe((data) => {
      const current = [];
      data?.data?.ids.filter((id: number) => {
        current.push(id);
      });
      this.art = current;
    });
    this.collectorService.getPreferenceByType('artist').subscribe((data) => {
      const current = [];
      data?.data?.ids.filter((id: number) => {
        current.push(id);
      });
      this.artist = current;
      
    });
    this.collectorService.getPreferenceByType('artist').subscribe((data) => {
      const current = [];
      data?.data?.ids.filter((id: number) => {
        current.push(id);
      });
      this.looking = current;
    });

    this.getKeywordsData();
    this.getArtistsData();
  };

  getKeywordsData() {
    this.artistService.getAllKeywords('').then(async (data) => {
      this.mediums = data;
      this.movements = await this.artistService.getAllMovements('');
      if (this.mediums.length > 0 && this.movements.length > 0) {
        this.keywordsList = [this.mediums, this.movements]
          .reduce(
            (r, a) => (a.forEach((a, i) => (r[i] = r[i] || []).push(a)), r),
            []
          )
          .reduce((a, b) => a.concat(b));
      } else {
        this.keywordsList = [...this.mediums, ...this.movements];
      }
    });
  }

  getArtistsData() {
    this.artistService.getAllArtists('').then((data) => {
      this.artistsList = data;
    });
  }

  public get getLooking(): any[] {
    const lookingArray = this.looking.map((element) => {
      return this.content[element];
    });
    return this.isLoadLooking ? lookingArray : lookingArray.slice(0, 3);
  }

  public get getArtist(): any[] {
    let current = [];
    this.artistsList=[{id:121,display_name:'Artist Name 1'},]
    this.artist=[121,121,121,121,121,121,121,121,121,]
    this.artist.forEach((id) => {
      this.artistsList.filter((element) => {
        if (element.id === id) {
          current.push(element.display_name);
        }
      });
    });
    return this.isLoadArtist ? current : current.slice(0, 3);
  }

  public get getArt(): any[] {
    let current = [];
    this.keywordsList=[{id:121,name:'Artwork 1'},]
    this.art=[121,121,121,121,121,121,121,121,121,121,121,121,121,121]
    this.art.forEach((id) => {
      this.keywordsList.filter((element) => {
        if (element.id === id) {
          current.push(element.name);
        }
      });
    });
    return this.isLoadArt ? current : current.slice(0, 3);
  }
}
