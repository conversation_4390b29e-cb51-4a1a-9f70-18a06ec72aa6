<div class="w-100">
    <div class="heading">Preferences</div>
    <!-- <div class="profile-field">
      <div
        class="w-100 field-contents"
      >
        <div class="field-name">Looking to</div>
        <div class="field-value">
          <span *ngFor="let item of getLooking; let i=index"><span *ngIf="i!==0">,</span> {{item}}</span>
          <a style="color: var(--quaternary-font-color);" (click)="isLoadLooking=!isLoadLooking" *ngIf="looking?.length>3">{{
            isLoadLooking
              ? ' hide'
              : ' +' + (looking?.length - 3) + ' more'
          }}</a>
        </div>
        <div class="edit-icon">
          <a routerLink="select"><img src="assets/icons/edit.png" /></a>
        </div>
      </div>
    </div> -->
    <div class="profile-field">
      <!-- <div class="hr-line"></div> -->
      <div
        class="w-100 field-contents"
      >
        <div class="field-name">Artists I Follow</div>
        <div class="field-value">
          <span *ngFor="let item of getArtist; let i=index"  style="font-family:var(--secondary-font);"><span *ngIf="i!==0">,</span> {{item}}</span>
          <a style="color: var(--quaternary-font-color);" (click)="isLoadArtist=!isLoadArtist" *ngIf="artist?.length>3">{{
            isLoadArtist
              ? ' hide'
              : ' +' + (artist?.length - 3) + ' more'
          }}</a>
        </div>
        <div class="edit-icon">
          <a routerLink="artists"><img src="assets/icons/edit.png" /></a>
        </div>
      </div>
    </div>
    <div class="profile-field">
      <div class="hr-line"></div>
      <div
        class="w-100 field-contents"
      >
        <div class="field-name">Artworks I like</div>
        <div class="field-value">
          <span *ngFor="let item of getArt; let i=index" style="font-family:var(--secondary-font);"><span *ngIf="i!==0">,</span> {{item}}</span>
          <a style="color: var(--quaternary-font-color);" (click)="isLoadArt=!isLoadArt" *ngIf="art?.length>3">{{
            isLoadArt
              ? ' hide'
              : ' +' + (art?.length - 3) + ' more'
          }}</a>
        </div>
        <div class="edit-icon">
          <a routerLink="arts"><img src="assets/icons/edit.png" /></a>
        </div>
      </div>
    </div>
  </div>
  