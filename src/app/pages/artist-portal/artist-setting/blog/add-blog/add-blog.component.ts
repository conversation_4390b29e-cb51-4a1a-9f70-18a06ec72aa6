import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
import { Subscription } from 'rxjs';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-add-blog',
  templateUrl: './add-blog.component.html',
  styleUrls: ['./add-blog.component.scss'],
})
export class AddBlogComponent implements OnInit, OnDestroy {
  constructor(private router: Router, private footerService: FooterService) { }
  /** selected tab Index stored variable */
  // selectedMenu;
  routerObserver: Subscription;
  activeMenu = 'main';
  faArrowAltCircleLeft = faArrowAltCircleLeft;
  ngOnDestroy(): void {
    //this.footerService.changeFooterType(FooterType.DEFAULT);
    // Unsubscribe from router events
    if (this.routerObserver) {
      this.routerObserver.unsubscribe();
    }
  }
  ngOnInit(): void {
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/artwork/blog/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/artwork/blog/add/add-on':
        this.activeMenu = 'add-on';
        break;
      case '/artist-portal/settings/artwork/blog/add/seo':
        this.activeMenu = 'seo';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }

  // Clear blogID when navigating back to blog list
  navigateBack() {
    sessionStorage.removeItem('blogID');
    this.router.navigate(['artist-portal/settings/artwork/blog']);
  }
}
