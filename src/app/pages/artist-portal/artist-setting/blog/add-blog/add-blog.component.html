<div>
  <div class="d-flex">
    <div class="section left-container" style="width: 100%">
      <div class="section-inner">
        <div class="d-flex justify-content-start slider-header">
          <a (click)="navigateBack()" style="cursor: pointer;">
            <h3 style="color: var(--tertiary-font-color)">
              <fa-icon [icon]="faArrowAltCircleLeft"></fa-icon>
            </h3>
          </a>
          <a [routerLink]="'/artist-portal/settings/artwork/blog/add'">
            <h3
              [ngClass]="{
                active: activeMenu === 'main'
              }"
            >
              Main
            </h3>
          </a>
          <a [routerLink]="'/artist-portal/settings/artwork/blog/add/add-on'"
            ><h3
              [ngClass]="{
                active: activeMenu === 'add-on'
              }"
            >
              Add-Ons
            </h3></a
          >
          <!-- <a [routerLink]="'/artist-portal/settings/artwork/blog/add/seo'"
            ><h3
              [ngClass]="{
                active: activeMenu === 'seo'
              }"
            >
              SEO
            </h3></a
          > -->
        </div>
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</div>
