<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="dynamicForm" novalidate autocomplete="off">
          <div class="splitter">
            <div class="field-value">
              <div class="input-container" (click)="showdrop = !showdrop">
                <input
                  [(ngModel)]="componentSelector"
                  [ngModelOptions]="{ standalone: true }"
                  type="text"
                  class="selection"
                  [placeholder]="componentSelector"
                  disabled
                />
                <button type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="showdrop ? 'dropdown-visible' : 'dropdown-hidden'"
                >
                  <ul>
                    <li
                      (click)="onComponentSelect('textSection', 'Text Section')"
                    >
                      <div class="country-name">Text Section</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('1x1Imagegrid', '1x1 Image Grid')
                      "
                    >
                      <div class="country-name">1x1 Image Grid</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('2x1Imagegrid', '2x1 Image grid')
                      "
                    >
                      <div class="country-name">2x1 Image grid</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('3x1ImageGrid', '3x1 Image Grid')
                      "
                    >
                      <div class="country-name">3x1 Image Grid</div>
                    </li>
                    <li (click)="onComponentSelect('quote', 'Quote')">
                      <div class="country-name">Quote</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect(
                          'quoteMediacomponent',
                          'Quote-Media component'
                        )
                      "
                    >
                      <div class="country-name">Quote-Media component</div>
                    </li>
                    <li
                      (click)="
                        onComponentSelect('videoComponent', 'Video Component')
                      "
                    >
                      <div class="country-name">Video Component</div>
                    </li>
                  </ul>
                </div>
              </div>
              <!-- <div class="dropdown-collapse" *ngIf="showdrop">
                  <p id="option" >Description</p>
                  <p id="option" >Intro Video</p>
                  <p id="option" >Artwork Select</p>
                  <p id="option" >Quote</p>
                  <p id="option" >Quote Media</p>
                  <p id="option" >Audio</p>
                  <p id="option" >Artist Profile-1</p>
                  <p id="option" >Artist Profile-2</p>
                  <p id="option" >Banner Divide</p>
                </div> -->

              <div class="input-info" *ngIf="!showdrop">
                Choose which component to use in the collection page.
              </div>
            </div>
            <div class="field-value">
              <button class="save" (click)="addNewComponent()">Add +</button>
            </div>
          </div>
          <div
            formArrayName="mainArr"
            [dragula]="'task-group'"
            [(dragulaModel)]="mainArr.controls"
          >
            <div
              formGroupName="{{ i }}"
              class="outer-border"
              *ngFor="let item of mainArr.controls; let i = index"
            >
              <div
                class="description"
                *ngIf="item.controls.compType.value == 'quote'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Quote</label>
                  </div>
                  <div class="col-md-6 col-lg-6 col-sm-6 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div [hidden]="!item.controls.collapse.value">
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">Quote</div>
                  <div class="field-value">
                    <angular-editor
                      id="editor5"
                      formControlName="quoteData"
                    ></angular-editor>
                    <div class="input-info">
                      Provide the quote to be used on the page.
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="TextSection"
                *ngIf="item.controls.compType.value == 'textSection'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Text Section</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <label
                      >{{
                        item.controls.textSectionData.value.slice(0, 25)
                      }}...</label
                    >
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div [hidden]="!item.controls.collapse.value">
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Article Text
                  </div>
                  <div class="field-value">
                    <angular-editor
                      *ngIf="htmlcheckBox[0]"
                      [placeholder]="' Article Text'"
                      id="editor6"
                      formControlName="textSectionData"
                    ></angular-editor>
                    <textarea
                      *ngIf="!htmlcheckBox[0]"
                      [placeholder]="' Article Text'"
                      formControlName="textSectionData"
                      rows="6"
                      style="padding: 1vw; width: 100%"
                    ></textarea>
                    <div class="input-info">
                      Provide the content for Text Section 1 for the article.
                      <div
                        style="
                          display: inline-block;
                          text-align: right;
                          width: 100%;
                        "
                      >
                        <input
                          type="checkbox"
                          [(ngModel)]="htmlcheckBox[0]"
                          [ngModelOptions]="{ standalone: true }"
                        />
                        HTML Editor
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="quoteMediacomponent"
                *ngIf="item.controls.compType.value == 'quoteMediacomponent'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Quote-Media component</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <label
                      >{{
                        item.controls.quoteText.value.slice(0, 25)
                      }}...</label
                    >
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div [hidden]="!item.controls.collapse.value">
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Quote Text
                  </div>
                  <div class="field-value">
                    <angular-editor
                      id="editor7"
                      formControlName="quoteText"
                    ></angular-editor>
                    <div class="input-info">
                      Provide the quote to be used on the page next to the
                      image.
                    </div>
                  </div>
                  <div class="sub-head">
                    <p>Quote Image</p>
                    <div class="">
                      <div class="field-value" style="padding-right: 0.5vw">
                        <image-upload
                          [accept]="'image/*'"
                          [selectedData]="selectedFilesObj?.quoteImageArr"
                          (onFileChange)="onFileSelect($event, 'quoteImageArr')"
                          [fileSize]="8388608"
                          [placeholder]="placeholder"
                        ></image-upload>
                      </div>
                    </div>
                    <div class="input-info">
                      Upload the image to be used next to the quote.
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="videoComponent"
                *ngIf="item.controls.compType.value == 'videoComponent'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">Video Component</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <label
                      >{{
                        item.controls.video_title.value.slice(0, 25)
                      }}...</label
                    >
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>
                <div [hidden]="!item.controls.collapse.value">
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        formControlName="video_url"
                        placeholder="URL"
                      />
                      <div class="placeholder">Video URL</div>
                      <div class="input-info">
                        Provide the Vimeo player link for the video
                      </div>
                    </div>
                    <div class="field-value">
                      <input
                        type="text"
                        formControlName="video_title"
                        placeholder="Title"
                      />
                      <div class="placeholder">Video Title</div>
                      <div class="input-info">
                        Provide a title for the video
                      </div>
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input
                        type="text"
                        formControlName="video_description"
                        placeholder="Description"
                      />
                      <div class="placeholder">Video Description</div>
                      <div class="input-info">
                        Provide a one-line description for the video
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="isValidUrl(item.controls.video_url.value)"
                    style="width: 48%"
                  >
                    <div style="padding: 56.25% 0 0 0; position: relative">
                      <iframe
                        [src]="
                          item.controls.video_url.value | safe : 'resourceUrl'
                        "
                        style="
                          position: absolute;
                          top: 0;
                          left: 0;
                          width: 100%;
                          height: 100%;
                        "
                        frameborder="0"
                        allow="autoplay; fullscreen; picture-in-picture"
                        allowfullscreen
                      ></iframe>
                    </div>
                  </div>
                  <div
                    *ngIf="
                      item.controls.video_url.value &&
                      !isValidUrl(item.controls.video_url.value)
                    "
                    style="width: 48%; color: red"
                  >
                    Invalid URL
                  </div>
                </div>
              </div>

              <div
                class="1x1Imagegrid"
                *ngIf="item.controls.compType.value == '1x1Imagegrid'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">1x1 Image Grid</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <img
                      *ngIf="
                        selectedFilesObj?.oneCrossOneImageArr[
                          item.controls.uuid.value
                        ]?.length > 0
                      "
                      [src]="
                        'https://www.terrain.art/cdn-cgi/image/width=100,quality=72/' +
                        selectedFilesObj?.oneCrossOneImageArr[
                          item.controls.uuid.value
                        ][0]?.url
                      "
                      style="height: 30px; object-fit: contain"
                    />
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>

                <div [hidden]="!item.controls.collapse.value">
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.oneCrossOneImageArr[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'oneCrossOneImageArr',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="5242880"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">
                          Upload a single image to use in the blog.
                        </div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for the image
                              used
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="2x1Imagegrid"
                *ngIf="item.controls.compType.value == '2x1Imagegrid'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">2x1 Image Grid</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <img
                      *ngIf="
                        selectedFilesObj?.twoCrossOneImageArrOne[
                          item.controls.uuid.value
                        ]?.length > 0
                      "
                      [src]="
                        'https://www.terrain.art/cdn-cgi/image/width=100,quality=72/' +
                        selectedFilesObj?.twoCrossOneImageArrOne[
                          item.controls.uuid.value
                        ][0]?.url
                      "
                      style="height: 30px; object-fit: contain"
                    />
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>

                <div [hidden]="!item.controls.collapse.value">
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 1 Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.twoCrossOneImageArrOne[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'twoCrossOneImageArrOne',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="8388608"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">Upload Image 1.</div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 1 Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText_2_1"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for image 1
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 2 Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.twoCrossOneImageArrTwo[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'twoCrossOneImageArrTwo',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="8388608"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">Upload Image 2.</div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 2 Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText_2_2"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for image 2.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="3x1ImageGrid"
                *ngIf="item.controls.compType.value == '3x1ImageGrid'"
              >
                <div class="row">
                  <div class="col-md-2 col-lg-2 col-sm-2 drag-icon">
                    <span class="cross-icon" style="cursor: grab !important">
                      <fa-icon [icon]="faEllipsisV" class="mr2"></fa-icon>
                      <span style="margin-left: 1vw">{{ i + 1 }}</span>
                    </span>
                  </div>
                  <div class="col-md-4 col-lg-4 col-sm-4">
                    <label class="main-title">3x1 Image Grid</label>
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3">
                    <img
                      *ngIf="
                        selectedFilesObj?.threeCrossOneImageArrOne[
                          item.controls.uuid.value
                        ]?.length > 0
                      "
                      [src]="
                        'https://www.terrain.art/cdn-cgi/image/width=100,quality=72/' +
                        selectedFilesObj?.threeCrossOneImageArrOne[
                          item.controls.uuid.value
                        ][0]?.url
                      "
                      style="height: 30px; object-fit: contain"
                    />
                  </div>
                  <div class="col-md-3 col-lg-3 col-sm-3 FlexEnd">
                    <div class="SwitchInBar">
                      {{ item.controls.publish.value ? "Publish" : "Draft" }}
                      <label class="switch">
                        <input type="checkbox" formControlName="publish" />
                        <span class="slider round"></span>
                      </label>
                    </div>

                    <button type="button" class="eye-icon">
                      <span
                        *ngIf="item.controls.collapse.value == true"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEye"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="collapseForm(item.controls.collapse.value, i)"
                        ><fa-icon [icon]="faEyeSlash"></fa-icon
                      ></span>
                      <span
                        *ngIf="item.controls.collapse.value == false"
                        (click)="deleteRow(i)"
                        ><fa-icon [icon]="faTrash" class="ml20"></fa-icon>
                      </span>
                    </button>
                  </div>
                </div>

                <div [hidden]="!item.controls.collapse.value">
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 1 Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.threeCrossOneImageArrOne[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'threeCrossOneImageArrOne',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="8388608"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">Upload Image 1.</div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image_1_Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText_3_1"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for image 1
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 2 Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.threeCrossOneImageArrTwo[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'threeCrossOneImageArrTwo',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="8388608"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">Upload Image 2.</div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image_2_Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText_3_2"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for image 2.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="splitter">
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image 3 Upload</p>
                        <div class="">
                          <div class="" style="padding-right: 0.5vw">
                            <image-upload
                              [accept]="'image/*'"
                              [selectedData]="
                                selectedFilesObj?.threeCrossOneImageArrThree[
                                  item.controls.uuid.value
                                ]
                              "
                              (onFileChange)="
                                onFileSelect(
                                  $event,
                                  'threeCrossOneImageArrThree',
                                  item.controls.uuid.value
                                )
                              "
                              [fileSize]="8388608"
                              [placeholder]="placeholder"
                              [hideAltText]="true"
                            ></image-upload>
                          </div>
                        </div>
                        <div class="input-info">Upload Image 3.</div>
                      </div>
                    </div>
                    <div class="field-value">
                      <div class="sub-head">
                        <p>Image_3_Text</p>
                        <div class="">
                          <div class="field-value" style="padding-right: 0.5vw">
                            <textarea
                              [placeholder]="'Image Text'"
                              formControlName="imageText_3_3"
                              rows="6"
                              cols="45"
                              style="padding: 1vw"
                            ></textarea>
                            <div class="input-info">
                              Provide the description/reference for image 3.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav" *ngIf="mainArr.controls.length > 0">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  id="myModal"
  class="modal"
  [style.display]="isPopupOpen ? 'block' : 'none'"
>
  <div class="modal-content">
    <p>Are you sure you wish to remove the component?</p>
    <div class="buttonKeeper">
      <button type="button" class="btnn" (click)="isPopupOpen = false">
        Cancel</button
      ><button type="button" (click)="confirmDelete()" class="btnn">
        Confirm
      </button>
    </div>
  </div>
</div>
