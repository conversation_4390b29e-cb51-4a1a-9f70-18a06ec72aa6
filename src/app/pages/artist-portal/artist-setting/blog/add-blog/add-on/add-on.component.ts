import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';

import {
  faEye,
  faEyeSlash,
  faTrash,
  faEllipsisV,
  faGripVertical,
} from '@fortawesome/free-solid-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
@Component({
  selector: 'app-add-on',
  templateUrl: './add-on.component.html',
  styleUrls: ['./add-on.component.scss'],
})
export class AddOnComponent implements OnInit {
  faEllipsisV = faGripVertical;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faTrash = faTrash;
  dynamicForm: FormGroup;
  selectedIndex: any;
  htmlcheckBox = Array(10).fill(false);
  dataObj: any = {};
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private router: Router,
    private sanitizer: DomSanitizer
  ) { }
  isFilterDropdownOpen = false;
  isFilterDropdownOpen1 = false;
  isDropDownOpen;
  isPopupOpen: boolean = false;
  isBannerDropdownOpen;
  componentName: string;
  bannerTypeIndexed = [];
  form: FormGroup;
  ForDeleteComponentName;
  ForDeleteIndex;
  MediaTypeImage = {
    banner: [false, false],
    titleBanner: false,
    quoteMedia: null,
    artistMedia: null,
  };
  selectedFilesObj: any = {
    quoteImageArr: [],
    oneCrossOneImageArr: {},
    twoCrossOneImageArrOne: {},
    twoCrossOneImageArrTwo: {},
    threeCrossOneImageArrOne: {},
    threeCrossOneImageArrTwo: {},
    threeCrossOneImageArrThree: {},
  };

  // new
  componentSelector: any = 'Choose Component';
  showdrop: boolean = false;

  ngOnInit(): void {
    this.initialiseForm();
    if (sessionStorage.getItem('blogID')) {
      this.getBlogById();
    }

    // this.isArtworkPopupOpen=false;
    // this.isDropDownOpen = Array(10).fill(false);
    // this.isArtworkDropdownOpen1 = Array(10).fill(false);
    // this.isArtworkDropdownOpen2 = Array(10).fill(false);
    // this.isArtworkDropdownOpen3 = Array(10).fill(false);
    // this.isQuoteMediaDropdownOpen1 = Array(10).fill(false);
    // this.isQuoteMediaDropdownOpen2 = Array(10).fill(false);
    // this.isQuoteMediaDropdownOpen3 = Array(10).fill(false);
    // this.isArtistProfile2DropdownOpen = Array(10).fill(false);
    // this.isBannerDropdownOpen = Array(10).fill(false);
    // this.form = this.formBuilder.group({
    //   showDescription: new FormControl(true),
    //   EnableReadMore: new FormControl(true),
    //   showVideo: new FormControl(true),
    //   // showArtworks: new FormControl(true),
    //   // showQuote: new FormControl(true),
    //   // showQuoteMedia: new FormControl(true),
    //   playAudio: new FormControl(true),
    //   filter: new FormControl({value:'ID',disabled:true}),
    //   sort: new FormControl({value: "Sort" ,disabled:true}),
    //   audioLoop: new FormControl(true),
    //   artistMedia: new FormControl({ value: 'Artist Media Type', disabled: true }),
    //   showArtistProfile1: new FormControl(true),
    //   showArtistProfile2: new FormControl(true),
    //   showProfileLink: new FormControl(true),
    //   selectArtistMedia2: this.formBuilder.array([this.formBuilder.group({ showUrl: true, position: { value: 'Position', disabled: true } })]),
    //   componentSelector: new FormControl('Choose Component'),
    //   // showBanner: new FormControl(true),
    //   selectArtwork: this.formBuilder.array([this.formBuilder.group({ showArtworks: true, artworkGrid: { value: '3x1', disabled: true }, artworkInterior: { value: 'Artwork Interior', disabled: true }, artistSelect: { value: 'Artwork Select', disabled: false }, showPrice: true })]),
    //   selectBannerDivide: this.formBuilder.array([this.formBuilder.group({ showBanner: true, bannerType: { value: 'Banner Type', disabled: true }, showTitle: true, showButtons: true, showButton2: true, showButton3: true })]),
    //   selectQuote: this.formBuilder.array([this.formBuilder.group({ showQuote: true })]),
    //   selectQuoteMedia: this.formBuilder.array([this.formBuilder.group({ showQuoteMedia: true, quoteMediaPosition: { value: 'Quote Media Position', disabled: true }, quoteMediaBackground: { value: 'Quote Media Background', disabled: true }, quoteMedia: { value: 'Quote Media', disabled: true } })]),
    // })
    // showUrl:true
  }
  ComponentSelection = {
    titleBanner: false,
    description: false,
    introVideo: false,
    quote: false,
    audio: false,
    artistProfile1: false,
    artistProfile2: false,
    quoteMedia: false,
    artworkSelect: false,
    bannerDivide: false,
  };
  repeat = ['artworkSelect', 'bannerDivide', 'quote', 'quoteMedia'];
  setDeleteSelection(componentname, index) {
    switch (componentname) {
      case 'artworkSelect':
        this.ForDeleteComponentName = 'artworkSelect';
        this.ForDeleteIndex = index;
        break;
      case 'quote':
        this.ForDeleteComponentName = 'quote';
        this.ForDeleteIndex = index;
        break;
      case 'quoteMedia':
        this.ForDeleteComponentName = 'quoteMedia';
        this.ForDeleteIndex = index;
        break;
      case 'artistProfileMedia':
        this.ForDeleteComponentName = 'artistProfileMedia';
        this.ForDeleteIndex = index;
        break;
      case 'bannerDivide':
        if (this.selectBannerDivide.length < 2) {
          this.ForDeleteComponentName = 'bannerDivide';
          this.ForDeleteIndex = index;
        }
        break;

      default:
        break;
    }
    this.isPopupOpen = true;
  }
  ComponentSelect(componentName, dropdownName) {
    this.form.get('componentSelector').setValue(dropdownName);
    this.componentName = componentName;
  }
  deleteComponent() {
    if (
      this.repeat.includes(this.ForDeleteComponentName) ||
      this.ForDeleteComponentName == 'artistProfileMedia'
    ) {
      switch (this.ForDeleteComponentName) {
        case 'artworkSelect':
          this.selectArtwork.removeAt(this.ForDeleteIndex);
          break;
        case 'quote':
          this.selectQuote.removeAt(this.ForDeleteIndex);
          break;
        case 'quoteMedia':
          this.selectQuoteMedia.removeAt(this.ForDeleteIndex);
          break;
        case 'artistProfileMedia':
          this.selectArtistMedia2.removeAt(this.ForDeleteIndex);
          break;
        case 'bannerDivide':
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.removeAt(this.ForDeleteIndex);
          }
          break;

        default:
          break;
      }

      this.isPopupOpen = false;
    }
  }

  // addComponent(componentName) {
  //   if (this.ComponentSelection[componentName] && this.repeat.includes(componentName)) {
  //     switch (componentName) {
  //       case 'artworkSelect':
  //         this.selectArtwork.push(
  //           this.formBuilder.group({ showArtworks: true, artworkGrid: { value: '3x1', disabled: true }, artworkInterior: { value: 'Artwork Interior', disabled: true }, artistSelect: { value: 'Artwork Select', disabled: false }, showPrice: true })
  //         );
  //         break;
  //       case 'quote':
  //         this.selectQuote.push(
  //           this.formBuilder.group({ showQuote: true })
  //         );;
  //         break;
  //       case 'quoteMedia':
  //         this.selectQuoteMedia.push(
  //           this.formBuilder.group({ showQuoteMedia: true, quoteMediaPosition: { value: 'Quote Media Position', disabled: true }, quoteMediaBackground: { value: 'Quote Media Background', disabled: true }, quoteMedia: { value: 'Quote Media', disabled: true } })
  //         );;
  //         break;
  //       case 'bannerDivide':
  //         if (this.selectBannerDivide.length < 2) {
  //           this.selectBannerDivide.push(
  //             this.formBuilder.group({ showBanner: true, bannerType: { value: 'Banner Type', disabled: true }, showTitle: true, showButtons: true, showButton2: true, showButton3: true })
  //           );
  //         }
  //         break;

  //       default:
  //         break;
  //     }

  //   }
  //   else
  //     this.ComponentSelection[componentName] = true;

  // }

  bannerTypeSelect(type, index) {
    this.selectBannerDivide.controls[index].get('bannerType').setValue(type);
    this.bannerTypeIndexed[index] =
      this.selectBannerDivide.controls[index].get('bannerType').value;
    if ((type = 'Video')) {
      this.MediaTypeImage.banner[index] = false;
    } else {
      this.MediaTypeImage.banner[index] = true;
    }
  }
  artworkSelector(index, name) {
    this.selectArtwork.controls[index].get('artworkGrid').setValue(name);
  }
  artistSelector(index, name) {
    this.selectArtwork.controls[index].get('artistSelect').setValue(name);
  }
  addRepeater1() {
    this.selectArtistMedia2.push(
      this.formBuilder.group({
        showUrl: true,
        position: { value: 'Position', disabled: true },
      })
    );
  }
  get selectArtwork(): FormArray {
    return <FormArray>this.form.get('selectArtwork');
  }
  get selectQuoteMedia(): FormArray {
    return <FormArray>this.form.get('selectQuoteMedia');
  }
  get selectQuote(): FormArray {
    return <FormArray>this.form.get('selectQuote');
  }
  get selectArtistMedia2(): FormArray {
    return <FormArray>this.dynamicForm.get('selectArtistMedia2');
  }
  get selectBannerDivide(): FormArray {
    return <FormArray>this.form.get('selectBannerDivide');
  }

  onFileSelect(files, key, i = null) {
    if (i || i == 0) {
      this.selectedFilesObj[key][i] = files;
    } else {
      this.selectedFilesObj[key] = files;
    }
    if (files.length == 0) {
      if (i || i == 0) {
        this.selectedFilesObj[key][i] = [];
      } else {
        this.selectedFilesObj[key] = [];
      }
    } else {
      this.uploadFile(key, files, i);
    }
  }

  // to upload file
  uploadFile(key, files: any, i = null) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          if (i || i == 0) {
            this.selectedFilesObj[key][i][0]['url'] = res.data;
          } else {
            this.selectedFilesObj[key][0]['url'] = res.data;
          }

          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // new code starts from here
  onComponentSelect(componentName, dropdownName) {
    // this.form.get("componentSelector").setValue(dropdownName);
    this.componentName = componentName;
    this.componentSelector = dropdownName;
    this.showdrop = true;
  }

  initialiseForm() {
    this.dynamicForm = this.formBuilder.group({
      mainArr: this.formBuilder.array([
        // this.formBuilder.group({
        // compType : new FormControl(),
        // }),
      ]),
      // description
      // showDescription: new FormControl(true),
      // EnableReadMore: new FormControl(true),
      // showVideo: new FormControl(true),
      // playAudio: new FormControl(true),
      // filter: new FormControl({value:'ID',disabled:true}),
      // sort: new FormControl({value: "Sort" ,disabled:true}),
      // audioLoop: new FormControl(true),
      // artistMedia: new FormControl({ value: 'Artist Media Type', disabled: true }),
      // showArtistProfile1: new FormControl(true),
      // showArtistProfile2: new FormControl(true),
      // showProfileLink: new FormControl(true),
      // selectArtistMedia2: this.formBuilder.array([this.formBuilder.group({ showUrl: true, position: { value: 'Position', disabled: true } })]),
      // componentSelector: new FormControl('Choose Component'),
      // selectArtwork: this.formBuilder.array([this.formBuilder.group({ showArtworks: true, artworkGrid: { value: '3x1', disabled: true }, artworkInterior: { value: 'Artwork Interior', disabled: true }, artistSelect: { value: 'Artwork Select', disabled: false }, showPrice: true })]),
      // selectBannerDivide: this.formBuilder.array([this.formBuilder.group({ showBanner: true, bannerType: { value: 'Banner Type', disabled: true }, showTitle: true, showButtons: true, showButton2: true, showButton3: true })]),
      // selectQuote: this.formBuilder.array([this.formBuilder.group({ showQuote: true })]),
      // selectQuoteMedia: this.formBuilder.array([this.formBuilder.group({ showQuoteMedia: true, quoteMediaPosition: { value: 'Quote Media Position', disabled: true }, quoteMediaBackground: { value: 'Quote Media Background', disabled: true }, quoteMedia: { value: 'Quote Media', disabled: true } })]),
    });
  }

  addNewComponent() {
    console.log(this.componentName);
    if (this.componentName == 'Choose Component' || !this.componentName) {
      return;
    }
    this.mainArr.push(
      this.formBuilder.group({
        collapse: new FormControl(false),
        uuid: new FormControl(this.generateUUID()),
        publish: new FormControl(false),
        compType: new FormControl(this.componentName),
        // description
        EnableReadMore: new FormControl(true),

        // text section
        textSectionData: new FormControl(''),

        // 2x1 Image Grid
        imageText: new FormControl(''),
        imageText_2_1: new FormControl(''),
        imageText_2_2: new FormControl(''),
        imageText_3_1: new FormControl(''),
        imageText_3_2: new FormControl(''),
        imageText_3_3: new FormControl(''),
        // 3x1 Image Grid
        // video
        showVideoComponent: new FormControl(false),
        // showVideoComponent
        video_url: new FormControl(''),
        video_description: new FormControl(''),
        video_title: new FormControl(''),
        //artwork select
        showArtworks: new FormControl(true),
        artworkGrid: new FormControl({ value: '', disabled: true }),
        artworkInterior: new FormControl({ value: '', disabled: true }),
        artistSelect: new FormControl({ value: '', disabled: false }),
        showPrice: new FormControl(true),

        // quote
        quoteData: new FormControl(''),

        // quote media
        quoteText: new FormControl(''),
      })
    );
    this.componentName = '';
    this.componentSelector = 'Choose Component';
  }

  get mainArr(): FormArray {
    return <FormArray>this.dynamicForm.get('mainArr');
  }

  collapseForm(value, index) {
    (this.mainArr.at(index) as FormGroup).get('collapse').patchValue(!value);
  }

  // to delete row
  deleteRow(index) {
    this.selectedIndex = index;
    this.isPopupOpen = true;
  }

  confirmDelete() {
    this.mainArr.removeAt(this.selectedIndex);
    this.isPopupOpen = false;
  }

  value(item) {
    // console.log(item.controls.selectArtistMedia2.controls[0].controls.showUrl.value)
    console.log(item);
  }

  getBlogById() {
    let url = apiUrl.blogs.get + `/${sessionStorage.getItem('blogID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.dataObj = res.data;
        res.data.contents
          .filter((a) => a?.compType)
          .forEach((ele, index) => {
            if (ele?.compType == 'textSection') {
              this.componentName = 'textSection';
            } else if (ele?.compType == '1x1Imagegrid') {
              this.componentName = '1x1Imagegrid';
            } else if (ele?.compType == '2x1Imagegrid') {
              this.componentName = '2x1Imagegrid';
            } else if (ele?.compType == '3x1ImageGrid') {
              this.componentName = '3x1ImageGrid';
            } else if (ele?.compType == 'quoteMediacomponent') {
              this.componentName = 'quoteMediacomponent';
            } else if (ele?.compType == 'videoComponent') {
              this.componentName = 'videoComponent';
            } else if (ele?.compType == 'quote') {
              this.componentName = 'quote';
            } else {
              return;
            }
            this.addNewComponent();
            setTimeout(() => {
              this.patchComponent(ele, index);
            }, 2000);
          });
      }
    });
  }

  patchComponent(ele, index) {
    if (ele.compType) {
      this.dynamicForm['controls'].mainArr?.['controls']?.[index]
        ?.get('publish')
        ?.setValue(ele.data?.publish);
    }

    if (ele.compType == 'textSection') {
      this.dynamicForm['controls'].mainArr?.['controls']?.[index]
        ?.get('textSectionData')
        ?.setValue(ele.data.text);
    } else if (ele.compType == '1x1Imagegrid') {
      console.log(ele.data.images[0]['imageText']);
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText')
        ?.setValue(ele.data.images[0]['imageText']);
      if (ele.data.images[0].src) {
        this.selectedFilesObj.oneCrossOneImageArr[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[0]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
    } else if (ele.compType == '2x1Imagegrid') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText_2_1')
        ?.setValue(ele.data.images[0]['imageText']);
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText_2_2')
        ?.setValue(ele.data.images[1]['imageText']);

      // image code here
      if (ele.data.images[0].src) {
        this.selectedFilesObj.twoCrossOneImageArrOne[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[0]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
      if (ele.data.images[1].src) {
        this.selectedFilesObj.twoCrossOneImageArrTwo[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[1]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
    } else if (ele.compType == '3x1ImageGrid') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText_3_1')
        ?.setValue(ele.data.images[0]['imageText']);
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText_3_2')
        ?.setValue(ele.data.images[1]['imageText']);
      this.dynamicForm['controls'].mainArr['controls'][index]
        ?.get('imageText_3_3')
        ?.setValue(ele.data.images[2]['imageText']);


      // image code here
      if (ele.data.images[0].src) {
        this.selectedFilesObj.threeCrossOneImageArrOne[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[0]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
      if (ele.data.images[1].src) {
        this.selectedFilesObj.threeCrossOneImageArrTwo[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[1]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
      if (ele.data.images[2].src) {
        this.selectedFilesObj.threeCrossOneImageArrThree[
          this.dynamicForm['controls'].mainArr?.['controls']?.[index]?.get(
            'uuid'
          ).value
        ] = [
            {
              url: ele.data.images[2]['src'],
              type: 'image',
              name: 'File',
              size: '',
            },
          ];
      }
    } else if (ele.compType == 'quoteMediacomponent') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteText')
        .setValue(ele.data.quote);

      // image code here
      if (ele.data.image) {
        this.selectedFilesObj.quoteImageArr.push({
          url: ele.data.image,
          type: 'image',
          name: 'File',
          size: '',
        });
      }
    } else if (ele.compType == 'videoComponent') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_url')
        .setValue(ele.data.src);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_description')
        .setValue(ele.data.desc);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_title')
        .setValue(ele.data.title);
    } else if (ele.compType == 'quote') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteData')
        .setValue(ele.data.text);
    }
  }

  onSubmit() {
    console.log(this.mainArr.controls);
    // console.log(this.mainArr.value)
    let arr = [];
    this.mainArr.controls.forEach((ele, index) => {
      console.log(ele);
      let obj = {};
      if (ele['value']['compType'] == 'textSection') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'text-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          text: ele['value']['textSectionData'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == '1x1Imagegrid') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'image-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          publish: ele['value']['publish'],
          images: [
            {
              src: this.selectedFilesObj.oneCrossOneImageArr[
                ele['value']['uuid']
              ][0]?.url,
              imageText: ele['value']['imageText'],
            },
          ],
        };
      } else if (ele['value']['compType'] == '2x1Imagegrid') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'image-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          publish: ele['value']['publish'],
          images: [
            {
              src: this.selectedFilesObj.twoCrossOneImageArrOne[
                ele['value']['uuid']
              ][0]?.url,
              imageText: ele['value']['imageText_2_1'],
            },
            {
              src: this.selectedFilesObj.twoCrossOneImageArrTwo[
                ele['value']['uuid']
              ][0]?.url,
              imageText: ele['value']['imageText_2_2'],
            },
          ],
        };
      } else if (ele['value']['compType'] == '3x1ImageGrid') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'image-container';
        obj['model'] = 'model-1';
        console.log('sdf', this.selectedFilesObj.threeCrossOneImageArrOne[
          ele['value']['uuid']
        ]);

        obj['data'] = {
          publish: ele['value']['publish'],
          images: [
            {
              src: this.selectedFilesObj.threeCrossOneImageArrOne[
                ele['value']['uuid']
              ]?.[0]?.url,
              imageText: ele['value']['imageText_3_1'],
            },
            {
              src: this.selectedFilesObj.threeCrossOneImageArrTwo[
                ele['value']['uuid']
              ]?.[0]?.url,
              imageText: ele['value']['imageText_3_2'],
            },
            {
              src: this.selectedFilesObj.threeCrossOneImageArrThree[
                ele['value']['uuid']
              ]?.[0]?.url,
              imageText: ele['value']['imageText_3_3'],
            },
          ],
        };
      } else if (ele['value']['compType'] == 'quoteMediacomponent') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'quote-cards';
        obj['model'] = 'model-1';
        obj['data'] = {
          publish: ele['value']['publish'],
          quote: ele['value']['quoteText'],
          quoteBy: '',
          image: this.selectedFilesObj.quoteImageArr[0].url,
        };
      } else if (ele['value']['compType'] == 'videoComponent') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'video-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          publish: ele['value']['publish'],
          src: ele['value']['video_url'],
          title: ele['value']['video_title'],
          desc: ele['value']['video_description'],
        };
      } else if (ele['value']['compType'] == 'quote') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'quote-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          publish: ele['value']['publish'],
          text: ele['value']['quoteData'],
        };
      }
      arr.push(obj);
    });
    let req = {
      bannerImage: this.dataObj.bannerImage || '',
      blogThumbnail: this.dataObj.blogThumbnail || '',
      title: this.dataObj.title || '',
      blogType: this.dataObj.blogType || '',
      author_name: this.dataObj.author_name || [],
      published_date: this.dataObj.published_date || '',
      article_duration: this.dataObj.article_duration || '',
      contents: [...arr],
      blogStatus: this.dataObj.blogStatus || false,
      showSummary: this.dataObj.showSummary || false,
      summaryText: this.dataObj.summaryText,
      showRelatedArtists: this.dataObj.showRelatedArtists || false,
      relatedArtists: this.dataObj.relatedArtists || [],
    };
    let url = sessionStorage.getItem('blogID')
      ? apiUrl.blogs.get + `/${sessionStorage.getItem('blogID')}`
      : apiUrl.blogs.get;
    if (sessionStorage.getItem('blogID')) {
      this.updateBlog(req, url);
      // return;
    }
    // this.server.postApi(url, req).subscribe((res) => {
    //   if (res.statusCode == 200) {
    //     sessionStorage.setItem('blogID', res.data['_id']);
    //     alert(res.message);
    //   }
    // });
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        sessionStorage.setItem('blogID', res.data['_id']);
        alert(res.message);
        // Uncomment if you want to navigate back to blog list after update
        // this.router.navigate(['artist-portal/settings/artwork/blog'])
      }
    });
  }

  generateUUID() {
    // Public Domain/MIT
    var d = new Date().getTime(); //Timestamp
    var d2 =
      (typeof performance !== 'undefined' &&
        performance.now &&
        performance.now() * 1000) ||
      0; //Time in microseconds since page-load or 0 if unsupported
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        var r = Math.random() * 16; //random number between 0 and 16
        if (d > 0) {
          //Use timestamp until depleted
          r = (d + r) % 16 | 0;
          d = Math.floor(d / 16);
        } else {
          //Use microseconds since page-load if supported
          r = (d2 + r) % 16 | 0;
          d2 = Math.floor(d2 / 16);
        }
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
      }
    );
  }
  isValidUrl(url) {
    if (!url || !url.match(/^https?:\/\/.*/)) {
      return false;
    }
    if (
      url.indexOf('javascript:') !== -1 ||
      url.indexOf('vbscript:') !== -1 ||
      url.indexOf('data:') !== -1
    ) {
      return false;
    }

    return true;
  }
}
