import { Component, OnInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  FormControl,
  FormArray,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl, environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit {
  isDropDownOpen = Array(10).fill(false);
  htmlcheckBox = Array(10).fill(false);

  selectedFiles: File[][] = [
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
  ];
  form: FormGroup;
  selectedFilesObj: any = {
    bannerImageArr: [],
    blogThumbnailArr: [],
    relatedArtistsArr: [],
    bannerImage: '',
    blogThumbnail: '',
  };
  placeholder: any = '';
  permissionsObj: any = {};
  showObj: any = {};
  disableObj: any = {};
  userDetailsObj;

  artist_search = '';
  artist_limit = 12;
  artist_select = [];
  selected_artist = [];

  constructor(
    private formBuilder: FormBuilder,
    public server: CollectorService,
    private router: Router
  ) { }

  getArtists() {
    let url = `api/artist?limit=${this.artist_limit}&search=${this.artist_search}&offset=1`;
    this.server.getApi(url).subscribe((res) => {
      this.artist_select = res.data.map((a) => {
        return {
          id: a._id,
          name: a.display_name,
        };
      });
    });
  }
  getSelectedItem(a, i) {
    this.selected_artist = a;
  }

  ngOnInit(): void {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetailsObj = JSON.parse(decode);
    this.permissionsObj = JSON.parse(decode)
    ['role_id']['permissions'].find((x) => x.name == 'Blog')
      .tabsArr.find((x) => x.name == 'Main');
    console.log(this.permissionsObj);
    this.form = this.formBuilder.group({
      publishBlog: new FormControl(),
      blogType: new FormControl(),
      articleAuthor: new FormControl(),
      publishedDate: new FormControl(),
      articleDuration: new FormControl(),
      articleTitle: new FormControl(),
      showSummary: new FormControl(false),
      summaryText: new FormControl(),
      artist_ids: new FormControl(''),
      // showRelatedArtists: new FormControl(false),
    });
    this.getArtists();
    this.permissionsObj.fields.forEach((ele) => {
      let obj = {};
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;
      if (ele.parent) {
        ele.child.forEach((ele_child) => {
          obj[ele_child.name] = ele_child.defaultValue;
          this.showObj[ele_child.name] = ele_child.show;
        });
      }
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name]?.setValidators([Validators.required]);
        }
        ele['disabled'] =
          ele.disabled == 'true' || ele.disabled == true ? true : false;
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
        obj[ele.name] = ele.defaultValue;
        // console.log(obj)
        // to patch default value
        if (!sessionStorage.getItem('blogID')) {
          if (this.userDetailsObj.role === 'THOUGHT-LEADER') {
            obj['articleAuthor'] = this.userDetailsObj.display_name;
            obj['blogType'] = 'Articles';
          }
          obj['showSummary'] = false;
          obj['publishBlog'] = false;

          this.form.patchValue(obj);
        }
      }
    });
    if (sessionStorage.getItem('blogID')) {
      this.getBlogById();
    }
  }

  getBlogById() {
    let url = apiUrl.blogs.get + `/${sessionStorage.getItem('blogID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.form.patchValue({
          publishBlog: res.data.blogStatus,
          blogType: res.data.blogType,
          articleAuthor: res.data.author_name,
          publishedDate: res.data.published_date,
          articleDuration: res.data.article_duration,
          articleTitle: res.data.title,
          showSummary: res.data.showSummary,
          summaryText: res.data.summaryText,
          artist_ids: res.data?.artist_ids?.[0]?.display_name,
          // showRelatedArtists: res.data.showRelatedArtists,
        });
        res.data?.artist_ids?.forEach((a) => {
          this.selected_artist.push({ id: a._id, name: a.display_name });
        });
        if (res.data.bannerImage) {
          this.selectedFilesObj.bannerImageArr.push({
            url: res.data.bannerImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.blogThumbnail) {
          this.selectedFilesObj.blogThumbnailArr.push({
            url: res.data.blogThumbnail,
            type: 'image',
            name: 'File',
            size: '',
          });
        }

        if (res.data.relatedArtists.length > 0) {
          this.selectedFilesObj.relatedArtistsArr = res.data.relatedArtists;
        }
      }
    });
  }

  get articleImages(): FormArray {
    return <FormArray>this.form.get('articleImages');
  }

  addRepeater1() {
    this.articleImages.push(this.formBuilder.group({}));
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onMultiAdd(files, key) {
    this.selectedFilesObj[key] = files;
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    }
  }

  onSubmit() {
    // if (this.form.invalid) {
    //   alert('Form not valid.Please fill required fields correctly !');
    //   return;
    // }
    if (this.userDetailsObj.role === 'THOUGHT-LEADER') {
      if (this.form.value.publishBlog) {
        if (this.form.value.publishBlog) {
          if (!this.selectedFilesObj.bannerImageArr?.[0]?.url) {
            alert('Please upload banner image !');
            return;
          }
          if (!this.form.value.publishedDate) {
            alert('Please provide publish date!');
            return;
          }
          if (!this.form.value.articleTitle) {
            alert('Please provide blog title!');
            return;
          }
        }
        if (this.form.value.showSummary && !this.form.value.summaryText) {
          alert('Please provide summary text!');
          return;
        }
      }
    }
    if (
      this.selectedFilesObj.bannerImageArr.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'bannerImageArr')
        .mandatory
    ) {
      alert('Please upload banner image !');
      return;
    }
    if (
      this.selectedFilesObj.blogThumbnailArr.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'blogThumbnailArr')
        .mandatory
    ) {
      alert('Please upload thumbnail image !');
      return;
    }
    let req = {
      bannerImage:
        this.selectedFilesObj.bannerImageArr.length > 0
          ? this.selectedFilesObj.bannerImageArr[0].url
          : '',
      blogThumbnail:
        this.selectedFilesObj.blogThumbnailArr.length > 0
          ? this.selectedFilesObj.blogThumbnailArr[0].url
          : '',
      title: this.form.value.articleTitle,
      blogType: this.form.value.blogType,
      author_name: this.form.value.articleAuthor,
      published_date: this.form.value.publishedDate,
      article_duration: this.form.value.articleDuration,
      type: this.userDetailsObj.role === 'THOUGHT-LEADER' ? 1 : 0,
      // "contents": [
      //   {
      //     "component": "quote-cards",
      //     "model": "model-1",
      //     "data": {
      //       "quote": "\"I always the process.\"",
      //       "quoteBy": "Dhanashri Sujit Deshmukh",
      //       "image": "https://register.terrain.art/artist-portal/assets/hin1fj813o8cw8og"
      //     }
      //   }
      // ],
      blogStatus: this.form.value.publishBlog,
      showSummary: this.form.value.showSummary,
      summaryText: this.form.value.summaryText,
      // showRelatedArtists: this.form.value.showRelatedArtists,
      relatedArtists: this.selectedFilesObj.relatedArtistsArr,
      artist_ids: this.selected_artist.map((a) => a.id),
    };
    let url = sessionStorage.getItem('blogID')
      ? apiUrl.blogs.get + `/${sessionStorage.getItem('blogID')}`
      : apiUrl.blogs.get;
    if (sessionStorage.getItem('blogID')) {
      this.updateBlog(req, url);
      return;
    }
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        sessionStorage.setItem('blogID', res.data['_id']);
        alert(res.message);
      }
    });
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        sessionStorage.setItem('blogID', res.data['_id']);
        alert(res.message);
        // Uncomment if you want to navigate back to blog list after update
        // this.router.navigate(['artist-portal/settings/artwork/blog'])
      }
    });
  }

  changeFocus(index) {
    console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }
}
