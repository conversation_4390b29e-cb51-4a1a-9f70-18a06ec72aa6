<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <!-- Pubish Blog-->
          <div class="field-value" [hidden]="!showObj?.publishBlog">
            <div class="input-container">
              <div class="text-before">Publish Blog :</div>
              <label class="switch">
                <input type="checkbox" formControlName="publishBlog" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to publish the blog on the website
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <multi-input
                placeholder="Choose artists from the list"
                [itemsArray]="artist_select"
                [previosItems]="selected_artist"
                [newEntry]="false"
                [disableSearch]="true"
                (searchEvent)="artist_search = $event; getArtists()"
                (selectedItem)="getSelectedItem($event, 0)"
              ></multi-input>
              <div class="placeholder">Artists</div>
              <div class="input-info"></div>
            </div>
          </div>
          <!-- Banner-->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.bannerImageArr"
          >
            Banner Image
          </div>
          <div
            class="field-value flex-block"
            [hidden]="!showObj?.bannerImageArr"
          >
            <image-upload
              [accept]="'image/*'"
              [selectedData]="selectedFilesObj?.bannerImageArr"
              (onFileChange)="onFileSelect($event, 'bannerImageArr')"
              [fileSize]="5242880"
              [placeholder]="placeholder"
            ></image-upload>
            <div class="input-info">
              Provide the banner image to be used on the blog page (cropped as
              per aspect ratio).
            </div>
          </div>

          <!--Thumbnail-->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.blogThumbnailArr"
          >
            Blog Thumbnail
          </div>
          <div
            class="field-value flex-block"
            [hidden]="!showObj?.blogThumbnailArr"
          >
            <image-upload
              [accept]="'image/*'"
              [selectedData]="selectedFilesObj?.blogThumbnailArr"
              (onFileChange)="onFileSelect($event, 'blogThumbnailArr')"
              [fileSize]="5242880"
              [placeholder]="placeholder"
            ></image-upload>
            <div class="input-info">
              Provide the cropped image to be used on the blog listing page
              (cropped as per aspect ratio 1:1).
            </div>
          </div>

          <!-- Blog Type-->
          <div class="splitter">
            <div
              *ngIf="userDetailsObj.role !== 'THOUGHT-LEADER'"
              class="field-value"
              [hidden]="!showObj?.blogType"
            >
              <div
                class="input-container"
                (focusout)="
                  userDetailsObj.role !== 'THOUGHT-LEADER' && changeFocus(0)
                "
              >
                <input
                  type="text"
                  class="selection"
                  placeholder="Blog Type"
                  formControlName="blogType"
                  (focus)="
                    userDetailsObj.role !== 'THOUGHT-LEADER' &&
                    disableObj.blogType
                      ? null
                      : (isDropDownOpen[0] = true)
                  "
                  readonly
                />
                <button
                  *ngIf="userDetailsObj.role !== 'THOUGHT-LEADER'"
                  (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                  type="button"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  *ngIf="userDetailsObj.role !== 'THOUGHT-LEADER'"
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[0],
                    'dropdown-visible': isDropDownOpen[0]
                  }"
                >
                  <ul>
                    <li
                      (click)="
                        isDropDownOpen[0] = false;
                        form.get('blogType').setValue('Articles')
                      "
                    >
                      <div class="country-name">Articles</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[0] = false;
                        form.get('blogType').setValue('Intro_Videos')
                      "
                    >
                      <div class="country-name">Intro Videos</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[0] = false;
                        form.get('blogType').setValue('How_Tos')
                      "
                    >
                      <div class="country-name">How Tos</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[0] = false;
                        form.get('blogType').setValue('Linked_In')
                      "
                    >
                      <div class="country-name">Linked In</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">Choose the blog type</div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.publishedDate"
            >
              <input
                type="date"
                placeholder="Date"
                formControlName="publishedDate"
              />
              <div class="placeholder">Published Date</div>
              <div class="input-info">Choose the date of publishing</div>
            </div>
          </div>

          <div class="splitter">
            <div
              *ngIf="userDetailsObj.role !== 'THOUGHT-LEADER'"
              class="field-value"
              [hidden]="!showObj?.articleAuthor"
            >
              <input
                type="text"
                placeholder="Article Author"
                formControlName="articleAuthor"
                [readOnly]="userDetailsObj.role === 'THOUGHT-LEADER'"
              />
              <!-- <div class="input-info">Choose the author of the article</div> -->
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.articleDuration"
            >
              <input
                type="text"
                placeholder="Duration"
                formControlName="articleDuration"
              />
              <div class="placeholder">Article Duration</div>
              <div class="input-info">
                Provide the estimate duration of the article (e.g.: 2 min. read)
              </div>
            </div>
          </div>

          <!-- Article Title-->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.articleTitle"
          >
            Article Title
          </div>
          <div class="field-value" [hidden]="!showObj?.articleTitle">
            <angular-editor
              *ngIf="htmlcheckBox[0]"
              id="editor1"
              [placeholder]="'Article Title'"
              formControlName="articleTitle"
            ></angular-editor>
            <textarea
              *ngIf="!htmlcheckBox[0]"
              [placeholder]="'Article Title'"
              formControlName="articleTitle"
              rows="6"
              style="padding: 1vw; width: 100%"
            ></textarea>
            <div class="input-info">
              Provide the title of the article (max. 15 words).
              <div
                style="display: inline-block; text-align: right; width: 100%"
              >
                <input
                  type="checkbox"
                  [(ngModel)]="htmlcheckBox[0]"
                  [ngModelOptions]="{ standalone: true }"
                />
                HTML Editor
              </div>
            </div>
          </div>

          <!--show Summary-->
          <div class="field-value" [hidden]="!showObj?.showSummary">
            <div class="input-container">
              <div class="text-before">Show Summary :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showSummary" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">Choose whether to show summary text.</div>
          </div>
          <div *ngIf="form.get('showSummary').value">
            <div style="margin-top: 2.08vw; font-size: 1.25vw">
              Summary Text
            </div>
            <div class="field-value">
              <angular-editor
                *ngIf="htmlcheckBox[1]"
                [placeholder]="' Summary Text'"
                id="editor2"
                formControlName="summaryText"
              ></angular-editor>
              <textarea
                *ngIf="!htmlcheckBox[1]"
                [placeholder]="' Summary Text'"
                formControlName="summaryText"
                rows="6"
                style="padding: 1vw; width: 100%"
              ></textarea>
              <div class="input-info">
                Provide the summary text for the article (max. 80 words).
                <div
                  style="display: inline-block; text-align: right; width: 100%"
                >
                  <input
                    type="checkbox"
                    [(ngModel)]="htmlcheckBox[1]"
                    [ngModelOptions]="{ standalone: true }"
                  />
                  HTML Editor
                </div>
              </div>
            </div>
          </div>

          <!--show Related Artists -->
          <!-- <div class="field-value" [hidden]="!showObj?.showSummary">
                        <div class="input-container">
                            <div class="text-before">Show Related Artists :</div>
                            <label class="switch">
                                <input type="checkbox" formControlName="showRelatedArtists" />
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show/hide related artist names in the article page.
                        </div>
                    </div>
                    <div *ngIf="form.get('showRelatedArtists').value">
                        <div class="field-value">
                            <multi-input placeholder="Related Artists" [selectedData]="selectedFilesObj?.relatedArtistsArr" (onFileChange)="onMultiAdd($event, 'relatedArtistsArr')"></multi-input>
                            <div class="input-info">
                                Choose the artists to be tagged in the article.
                            </div>
                        </div>
                    </div> -->

          <!--show Quote -->
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
