import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-seo',
  templateUrl: './seo.component.html',
  styleUrls: ['./seo.component.scss']
})
export class SeoComponent implements OnInit {
  form: FormGroup;
  dataforSEO: any = [];
  selectedObj: any = {
    'keywordsArr': []
  }
  selectedKeywords;
  previousData = [{ value: 1, display: 'asdass' }, { value: 2, display: 'dfjusa' }];
  selectedFilesObj: any = {
    seoImageArr: []
  }
  permissionsObj: any = {};
  limit: any = 999;
  offset: any = 1;
  constructor(
    private formBuilder: FormBuilder, private server: CollectorService, private router: Router
  ) {
    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    this.permissionsObj = JSON.parse(decode)[
      'role_id'
    ]['permissions'].find((x) => x.name == 'Blog').tabsArr.find(x => x.name == 'Seo');

  }
  getSelectedItem(items: Array<string>) {
    // this.selectedKeywords = items;
    this.selectedObj.keywordsArr = items
    console.log(this.selectedKeywords);
  }



  ngOnInit(): void {
    this.getSeoKeys()
    this.form = this.formBuilder.group({
      seoTitle: new FormControl(''),
      metaDescription: new FormControl('')
    });
  }

  // to get seo keys
  getSeoKeys() {
    let url = apiUrl.seo.getSEO + `?limit=${this.limit}&offset=${this.offset}`
    this.server.getApi(url).subscribe(res => {
      if (res.statusCode == 200) {
        this.dataforSEO = res.data
        this.dataforSEO.forEach((ele, index) => {
          ele['value'] = index
          ele['display'] = ele['name']
        });
        console.log(this.dataforSEO)
      }
    })
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner()
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner()
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }
    if (
      this.selectedFilesObj.bannerImageArr.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'bannerImageArr').mandatory
    ) {
      alert('Please upload banner image !');
      return;
    }
    if (
      this.selectedFilesObj.blogThumbnailArr.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'blogThumbnailArr').mandatory
    ) {
      alert('Please upload thumbnail image !');
      return;
    }
    let req = {
      "bannerImage": this.selectedFilesObj.bannerImageArr.length > 0 ? this.selectedFilesObj.bannerImageArr[0].url : '',
      "blogThumbnail": this.selectedFilesObj.blogThumbnailArr.length > 0 ? this.selectedFilesObj.blogThumbnailArr[0].url : '',
      "title": this.form.value.articleTitle,
      "blogType": this.form.value.blogType,
      "author_name": this.form.value.articleAuthor,
      "published_date": this.form.value.publishedDate,
      "article_duration": this.form.value.articleDuration,
      // "contents": [
      //   {
      //     "component": "quote-cards",
      //     "model": "model-1",
      //     "data": {
      //       "quote": "\"I always the process.\"",
      //       "quoteBy": "Dhanashri Sujit Deshmukh",
      //       "image": "https://register.terrain.art/artist-portal/assets/hin1fj813o8cw8og"
      //     }
      //   }
      // ],
      "blogStatus": this.form.value.publishBlog,
      "showSummary": this.form.value.showSummary,
      "summaryText": this.form.value.summaryText,
      "showRelatedArtists": this.form.value.showRelatedArtists,
      "relatedArtists": this.selectedFilesObj.relatedArtistsArr,
    }
    let url = sessionStorage.getItem('blogID') ? apiUrl.blogs.get + `/${sessionStorage.getItem('blogID')}` : apiUrl.blogs.get
    if (sessionStorage.getItem('blogID')) {
      this.updateBlog(req, url)
      return;
    }
    this.server.postApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        sessionStorage.setItem('blogID', res.data['_id']);
        alert(res.message)
      }
    })
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        sessionStorage.setItem('blogID', res.data['_id']);
        alert(res.message)
        // Clear blogID before navigating back to blog list
        sessionStorage.removeItem('blogID');
        this.router.navigate(['artist-portal/settings/artwork/blog'])
      }
    })
  }
}
