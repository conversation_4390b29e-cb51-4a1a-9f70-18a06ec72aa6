<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form [formGroup]="form">
                    <!--SEO section-->
                    <div style="margin-top: 3.47vw; font-size: 1.25vw">SEO Details</div>
                    <div class="field-value doted">
                        <!-- <div class=""> -->
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Title" formControlName="seoTitle" />
                            <div class="placeholder">SEO Title</div>
                            <div class="input-info">Provide the SEO title to be used.</div>
                        </div>
                        <div class="field-value">
                            <multi-input placeholder="SEO Keywords" [itemsArray]="dataforSEO" [previosItems]="previousData" (selectedItem)="getSelectedItem($event)"></multi-input>
                            <div class="placeholder">SEO Keywords</div>
                            <div class="input-info">
                                Choose the SEO keywords
                            </div>
                        </div>
                        <!-- </div> -->
                        <!-- <div class=""> -->
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Decription" formControlName="metaDescription" />
                            <div class="placeholder">Meta Description</div>
                            <div class="input-info">
                                Provide the Meta Description
                            </div>
                        </div>
                        <!-- <div class="field-value">
                <input type="text" placeholder="Tag" />
                <div class="placeholder">ALT Tag</div>
                <div class="input-info">
                  Provide the ALT Tag for the article.
                </div>
              </div> -->
                        <!-- </div> -->
                        <div class="upload-head">SEO Image</div>

                        <div class="field-value flex-block">
                            <image-upload [accept]="'image/*'" [selectedData]="selectedFilesObj?.seoImageArr" (onFileChange)="onFileSelect($event, 'seoImageArr')" [fileSize]="2147483648" [placeholder]="''"></image-upload>

                            <div class="input-info">
                                Use images with a 1.91:1 ratio and minimum recommended dimensions of 1200x630 for optimal clarity across all devices.
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>