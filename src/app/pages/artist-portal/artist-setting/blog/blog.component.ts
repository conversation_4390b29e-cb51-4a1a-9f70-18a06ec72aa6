import { Component, HostListener, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import {
  faEllipsisV,
  faPencilAlt,
  faCircle,
  faAngleLeft,
  faAngleRight,
} from '@fortawesome/free-solid-svg-icons';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-blog',
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.scss'],
})
export class BlogComponent implements OnInit {
  list_selection: number;
  faEllipsisV = faEllipsisV;
  faPencilAlt = faPencilAlt;
  faCircle = faCircle;
  searchValue = '';
  showMenuList = Array(1000).fill(false);
  noOfArtPerPage = 20;
  index = 0;
  lastIndex = 0;
  currentWorks: any[] = [];
  current: any[] = [];
  isSidemenuOpen = false;
  openStyle = {
    width: '250px',
  };
  form: FormGroup;
  artworks;
  emptyDiv = [];
  temporary = {
    // 'status': 'ForSale',
    // 'status': 'Not For Sale',
    status: 'Sold',
    artwork_type: 'Edition',
    published: false,
    // 'status': 'Reserved',
    minted: false,
    // 'network': 'BSC',
    network: 'Etherum',
    // 'network': 'Solana',
    // 'network': 'Polygon',
    createdBy: 'Ashuthosh NagarWala',
    NFT_id: 'Terrain.Art_289',
  };
  offset: any = 1;
  limit: any = 12;
  total: number = 0;
  isLoading = false;
  temporary2 = [{}, {}, {}, {}, {}, {}, {}, {}];
  userDetailsObj: any = {};
  showMenu = Array(1000).fill(false);
  permissionsObj: any = {};

  faAngleLeft = faAngleLeft;
  totalPage = 0;
  faAngleRight = faAngleRight;

  searchResult() {
    this.current = [];
    this.getBlogs();
    // console.log(this.searchValue + ' here');
    // this.currentWorks = [];
    // this.currentWorks = this.current;
    // // this.current = null;
    // // this.currentWorks = [];
    // // this.currentWorks = this.artworks;
    // if (this.searchValue !== '') {
    //   console.log(this.searchValue);
    //   const newArray = this.currentWorks.filter((art) =>
    //     art?.artwork_title
    //       ? art.artwork_title
    //           .toLowerCase()
    //           .includes(this.searchValue.toLowerCase())
    //       : false
    //   );
    //   this.currentWorks = newArray;
    //   console.log(newArray);
    // } else {
    //   this.currentWorks = this.artworks;
    // }
    // this.updateCurrent();
  }

  updateCurrent(): void {
    this.current = [];

    for (
      let i = this.index * this.noOfArtPerPage;
      i < (this.index + 1) * this.noOfArtPerPage;
      i++
    ) {
      const element = this.currentWorks[i];
      if (element === undefined) {
        break;
      }
      this.current.push(element);
    }
  }

  MintlockColor(status) {
    if (status == true) {
      return {
        background: '-webkit-linear-gradient(#004ddd, #004ddd)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    } else {
      return {
        background: '-webkit-linear-gradient(#9e9e9e, #9e9e9e)',
        '-webkit-background-clip': 'text',
        '-webkit-text-fill-color': 'transparent',
      };
    }
  }

  blockchainType(type) {
    switch (type) {
      case 'Ethereum':
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
      case 'BSC':
        return '../../../../../assets/icons/blockchain-type/binance-icon.png';
        break;
      case 'Polygon':
        return '../../../../../assets/icons/blockchain-type/polygon-icon.png';
        break;
      case 'Solana':
        return '../../../../../assets/icons/blockchain-type/solana-icon.png';
        break;

      default:
        return '../../../../../assets/icons/blockchain-type/ethereum-icon.png';
        break;
    }
  }

  lockColor(status) {
    switch (status) {
      case 'Ropsten':
        return {
          background: '-webkit-linear-gradient(#eecda3, #ef629f)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Private':
        return {
          background: '-webkit-linear-gradient(#f87bff, #ffa9cb)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Main Net':
        return {
          background: '-webkit-linear-gradient(#86fde8,#acb6e5)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Non-Terrain':
        return {
          background: '-webkit-linear-gradient(#92eab0, #6eff7a)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      default:
        break;
    }
  }

  dotColor(status) {
    switch (status) {
      case 'ForSale':
        return {
          background: '-webkit-linear-gradient(#03ff26, #0ae729)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Reserved':
        return {
          background: '-webkit-linear-gradient(#f5f54f, #ffff00)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Sold':
        return {
          background: '-webkit-linear-gradient(#fc5c5c, #ee2828)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      case 'Not For Sale':
        return {
          background: '-webkit-linear-gradient(#eebdfd, #df06f9)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
        };
        break;
      default:
        break;
    }
  }

  openNav(i) {
    console.log(i);
    if (this.list_selection == i) {
      this.isSidemenuOpen = !this.isSidemenuOpen;
    } else {
      this.list_selection = i;
      this.isSidemenuOpen = true;
    }
  }

  closeNav() {
    this.isSidemenuOpen = false;
  }

  constructor(
    private server: CollectorService,
    private router: Router,
    public formBuilder: FormBuilder
  ) {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'];
    console.log(this.permissionsObj);
  }
  ngOnInit(): void {
    this.form = this.formBuilder.group({
      showListView: new FormControl(false),
    });
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetailsObj = JSON.parse(decode);
    this.getBlogs();
  }

  // to get artworks
  getBlogs() {
    // this.current = [];
    let url =
      apiUrl.blogs.get +
      `?offset=${this.index + 1}&limit=${this.noOfArtPerPage
      }&cms=true&adminAccess=${this.permissionsObj['admin']}`;
    if (this.searchValue) {
      url = url + `&keyword=${this.searchValue}`;
    }
    if (this.userDetailsObj.role === 'THOUGHT-LEADER') {
      url = url + `&createdBy=${this.userDetailsObj._id}&type=1`;
    }
    this.isLoading = true;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.current = [];
      this.server.hideSpinner();
      this.isLoading = false;
      if (res.statusCode == 200) {
        this.totalPage = Math.ceil(res.total / this.noOfArtPerPage);

        const reminder = res.data?.length % 4;

        this.current.push(...res.data);
        if (reminder > 0) {
          this.emptyDiv = Array(reminder).fill(0);
        }
      }
    });
  }

  navigateTo() {
    sessionStorage.removeItem('blogID');
    this.router.navigate(['artist-portal/settings/artwork/blog/add']);
  }

  // to edit artwork
  editArtWork(item) {
    sessionStorage.setItem('blogID', item['_id']);
    this.router.navigate(['artist-portal/settings/artwork/blog/add']);
  }

  // to manage pagination
  managePagination(page) {
    switch (page) {
      case 'next':
        this.index = this.index + 1;
        break;

      default:
        if (this.index > 0) {
          this.index = this.index - 1;
        }
        break;
    }
    this.getBlogs();
  }

  // @HostListener('window:scroll', [])
  // onScroll(): void {
  //   if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
  //     if (!this.isLoading) {
  //       // this.offset = this.offset + this.limit;
  //       this.index = this.index + 1;
  //       this.getBlogs();
  //     }

  //     //this.temporary.push(...[{}, {}, {}, {}]);
  //   }
  // }
  // onElementScroll(event) {
  //   if (
  //     event.target.offsetHeight + event.target.scrollTop >=
  //     event.target.scrollHeight
  //   ) {
  //     if (!this.isLoading) {
  //       // this.offset = this.offset + this.limit;
  //       this.index = this.index + 1;
  //       this.getBlogs();
  //     }
  //   }
  // }
  publishArtWork(item) {
    let url = apiUrl.blogs.get + `/${item['_id']}`;

    this.server.patchApi(url, { blogStatus: true }).subscribe((res) => {
      if (res.statusCode == 200) {
        alert('Blog published!');
      }
    });
  }
}
