import { AddOnComponent } from './add-blog/add-on/add-on.component';
import { MainComponent } from './add-blog/main/main.component';
import { AddBlogComponent } from './add-blog/add-blog.component';
import { BlogComponent } from './blog.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { SeoComponent } from './add-blog/seo/seo.component';

const routes: Routes = [{
  path:'',
  component:BlogComponent
},
{
  path:'add',
  component:AddBlogComponent,
  data: {
    hidebread: true,
  },
  children: [
    {
      path: '',
      component: MainComponent,
      data: {
        hidebread: true,
      },
    },
    {
      path: 'add-on',
      component: AddOnComponent,
      data: {
        hidebread: true,
      },
    },
    {
      path: 'seo',
      component: SeoComponent,
      data: {
        hidebread: true,
      },
    },
  ],
},];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BlogRoutingModule { }
