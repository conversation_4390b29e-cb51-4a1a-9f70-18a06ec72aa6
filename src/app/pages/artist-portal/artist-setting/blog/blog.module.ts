import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BlogRoutingModule } from './blog-routing.module';
import { AddBlogComponent } from './add-blog/add-blog.component';
import { MainComponent } from './add-blog/main/main.component';
import { AddOnComponent } from './add-blog/add-on/add-on.component';
import { SeoComponent } from './add-blog/seo/seo.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { DragulaModule } from 'ng2-dragula';
import { NgxPaginationModule } from 'ngx-pagination';
import { ImageUploadModule } from 'src/app/core/image-upload/image-upload.module';
import { MultiInputModule } from 'src/app/core/multi-input/multi-input.module';
import { PlaceholderModule } from 'src/app/core/placeholder/placeholder.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [AddBlogComponent, MainComponent, AddOnComponent, SeoComponent],
  imports: [
    CommonModule,
    BlogRoutingModule,
    DragulaModule.forRoot(),
    NgxPaginationModule,

    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    AngularEditorModule,
    NgxQRCodeModule,
    MultiInputModule,
    ImageUploadModule,
    PlaceholderModule,
  ]
})
export class BlogModule { }
