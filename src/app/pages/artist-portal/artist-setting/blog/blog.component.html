<div id="mainContent" [formGroup]="form">
  <div class="heading">
    <div class="head">
      <div>
        Blogs
        <a (click)="navigateTo()"><button class="saveBtn">Add +</button></a>
      </div>
      <div class="filter">
        <input
          type="text"
          placeholder="Search by Blog"
          name="search"
          [(ngModel)]="searchValue"
          [ngModelOptions]="{ standalone: true }"
          (keyup)="searchResult()"
        />
        <div
          class="input-container"
          style="display: flex; float: right; margin-top: 1.5vw"
        >
          <div
            class="text-before"
            [innerHtml]="
              form.get('showListView').value ? 'List view' : 'Grid view'
            "
          ></div>
          <label class="switch">
            <input type="checkbox" formControlName="showListView" />
            <span class="slider round"></span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <div
    class="d-flex justify-content-end view-actions"
    style="flex-wrap: nowrap; margin-top: 1vw"
  >
    <div class="d-flex" style="gap: 2vw">
      <span class="d-flex" style="gap: 0.7vw">
        <fa-icon
          [icon]="faAngleLeft"
          (click)="managePagination('prev')"
        ></fa-icon>
        Page
        {{ index + 1 }}
        <!-- <input
        style="height: 1vw; width: 1vw"
        type="number"
        [max]="totalPage"
        [value]="offset"
        min="1"
        name="pager"
        id="pager"
      /> -->
        Of {{ totalPage }}
        <fa-icon
          [icon]="faAngleRight"
          (click)="managePagination('next')"
        ></fa-icon>
      </span>
    </div>
  </div>

  <div class="collector-form">
    <div class="interests">
      <div class="contents" *ngIf="!form.get('showListView').value">
        <div
          *ngFor="let art of current; let i = index"
          class="artist"
          style="position: relative"
          (mouseleave)="showMenu[i] = false"
        >
          <img
            (click)="editArtWork(art)"
            class="artworkImggg"
            [src]="art?.bannerImage"
          />
          <a
            (focusout)="showMenu[i] = false"
            (click)="showMenu[i] = !showMenu[i]"
          >
            <div (mouseover)="showMenu[i] = true" class="edit_grid"></div>
            <div
              *ngIf="showMenu[i]"
              (mouseleave)="showMenu[i] = false"
              class="dropdown-container"
            >
              <div class="dropdown-content">
                <a (click)="editArtWork(art)">
                  <div
                    class="list-hover"
                    style="padding: 0.6vw; cursor: pointer; color: black"
                  >
                    Edit
                  </div>
                </a>
                <a (click)="publishArtWork(art)">
                  <div
                    class="list-hover"
                    style="padding: 0.6vw; cursor: pointer; color: black"
                  >
                    Publish
                  </div>
                </a>
              </div>
            </div>
          </a>

          <div
            class="wrappp"
            style="
              display: flex;
              justify-content: space-between;
              flex-direction: row;
              align-items: center;
            "
          >
            <div class="name" style="width: 90%">
              <i>{{ art?.title }}</i>
            </div>
            <div style="width: 10%; text-align: center; margin-top: 0.5vw">
              <fa-icon
                [icon]="faCircle"
                style="font-size: 0.9027vw"
                [style.color]="art?.blogStatus ? '#00FF00' : '#FF0000'"
              ></fa-icon>
            </div>
          </div>
        </div>
        <ng-container *ngIf="isLoading">
          <div *ngFor="let item of temporary2" class="artist">
            <app-placeholder height="10.69vw" width="14.93vw">
            </app-placeholder>
          </div>
        </ng-container>
      </div>

      <div class="contentList" hidden *ngIf="form.get('showListView').value">
        <div class="Table">
          <div
            class="Table-row"
            *ngFor="let art of current; let i = index"
            (click)="openNav(i)"
            [ngStyle]="
              list_selection == i
                ? { 'background-color': '#b0c7dda3', color: '#fff' }
                : {}
            "
          >
            <div class="Table-row-item img-item" data-header="Header1">
              <img
                class="img-artwork-thumbnail"
                [src]="art?.bannerImage"
                alt="thumbnail"
              />
            </div>
            <!-- <div class="Table-row-item" data-header="Header2">
              <div [ngClass]="art?.published ? 'published-dot' : 'unpublished-dot'"></div>
              <div *ngIf="temporary.artwork_type == 'Edition'" class="editionList">

              </div>
              <div class="status-dot" [ngStyle]="dotColor(temporary.status)"></div>
              <div [ngClass]="temporary.minted ? 'minted' : 'not-minted'" [ngStyle]="MintlockColor(temporary.minted)">
              </div>
              <img class="blockchainIconList" *ngIf="temporary.minted" [src]="art?.bannerImage"
                alt="Blockchain Type" />
            </div> -->

            <div
              class="Table-row-item title-art"
              [ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
              data-header="Header3"
            >
              <i>{{ art?.title }}</i>
            </div>

            <div
              class="Table-row-item"
              *ngIf="!isSidemenuOpen"
              data-header="Header5"
            ></div>

            <div class="Table-row-item">
              <a (click)="editArtWork(art)">
                <div class="aserd"></div>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- ============================================= -->

      <div class="contentList" *ngIf="form.get('showListView').value">
        <div class="Table">
          <div
            class="Table-row"
            *ngFor="let art of current; let i = index"
            (click)="openNav(i); $event.stopPropagation()"
            [ngStyle]="
              list_selection == i
                ? { 'background-color': '#b0c7dda3', color: '#fff' }
                : {}
            "
          >
            <div
              class="Table-row-item img-item"
              style="width: 20%"
              data-header="Header1"
            >
              <img
                class="img-artwork-thumbnail"
                [src]="art?.bannerImage"
                alt="thumbnail"
              />
            </div>
            <div
              class="Table-row-item"
              [style.width]="'2%'"
              data-header="Header5"
            >
              <fa-icon
                [icon]="faCircle"
                style="font-size: 0.9027vw"
                [style.color]="art?.blogStatus ? '#00FF00' : '#FF0000'"
              ></fa-icon>
            </div>
            <div
              class="Table-row-item"
              [style.width]="isSidemenuOpen ? '25%' : '58%'"
              data-header="Header5"
            >
              {{ art?.title }}
            </div>
            <div
              class="Table-row-item actions-fa-icon"
              [style.width]="isSidemenuOpen ? '30%' : '20%'"
              style="position: relative; justify-content: end"
            >
              <fa-icon
                (click)="editArtWork(art)"
                [icon]="faPencilAlt"
              ></fa-icon>

              <fa-icon
                [style.color]="showMenuList[i] ? '#004ddd' : 'inherit'"
                [icon]="faEllipsisV"
                (click)="showMenuList[i] = !showMenuList[i]"
              ></fa-icon>
              <div [hidden]="!showMenuList[i]" class="dropdown-container-list">
                <!-- <div class="dropdown-content"> -->
                <a (click)="editArtWork(art)">
                  <div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Edit
                  </div>
                </a>
                <a (click)="publishArtWork(art)">
                  <div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Publish
                  </div>
                </a>
                <a>
                  <div
                    class="list-hover"
                    style="padding: 0.5vw; cursor: pointer; color: black"
                  >
                    Duplicate
                  </div>
                </a>
                <!-- </div> -->
              </div>
              <!-- </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  id="mySidenav"
  class="sidenav"
  [style.width]="isSidemenuOpen ? '400px' : 0"
>
  <div class="sidenav-content">
    <div class="closebtn" (click)="closeNav()">&times;</div>
    <div class="head-wrapper">
      <div class="img-side">
        <img [src]="current[list_selection]?.bannerImage" />
      </div>
      <div class="text-side">
        <div class="text-container">{{ current[list_selection]?.title }},</div>
      </div>
    </div>
  </div>
</div>
