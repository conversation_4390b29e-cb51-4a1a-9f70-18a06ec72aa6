.heading {
    font-size: 1.66vw;
    font-weight: 600;
    margin-bottom: 3.47vw;
  }
  .profile-field {
    margin-top: 2.08vw;
    .hr-line {
      margin-bottom: 2.08vw;
      height: 0.07vw;
      width: 100%;
      background-color: var(--hr-primary-color);
    }
    .field-contents {
      position: relative;
      .field-name {
        font-size: 1.25vw;
        font-weight: 600;
      }
      .del-btn {
        margin-top: 1.04vw;
        margin-bottom: 1.04vw;
        color: var(--quaternary-font-color);
        font-size: var(--default-font-size);
      }
      .field-value {
        margin-top: 1.04vw;
        .ph-flag {
          height: 1.25vw;
          padding-right: 0.62vw;
          border-right: solid 0.09vw var(--quaternary-font-color);
        }
        .password {
          margin-top: 1.52vw;
        }
        .last-changed {
          margin-top: 1.04vw;
          font-size: 0.97vw;
          color: var(--quaternary-font-color);
        }
      }
      .edit-icon {
        position: absolute;
        right: 0;
        top: 0;
        img {
          cursor: pointer;
          width: 2.85vw;
          height: 2.85vw;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .heading {
      font-size: 5.8vw;
      font-weight: 600;
      margin-bottom: 3.47vw;
    }
    .profile-field {
      margin-top: 7.25vw;
      .hr-line {
        margin-bottom: 7.25vw;
        height: 0.242vw;
      }
      .field-contents {
        .field-name {
          font-size: 4.84vw;
        }
        .del-btn {
          margin-top: 7.25vw;
          margin-bottom: 7.25vw;
          font-size: 3.62vw;
        }
        .field-value {
          margin-top: 3.62vw;
          font-size: 3.85vw;
          .last-changed {
            margin-top: 3.62vw;
            font-size: 3.38vw;
          }
        }
        .edit-icon {
          img {
            width: 9.9vw;
            height: 9.9vw;
          }
        }
      }
    }
  }
  