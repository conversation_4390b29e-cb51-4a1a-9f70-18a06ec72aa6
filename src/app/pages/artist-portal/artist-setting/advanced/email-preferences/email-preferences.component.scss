.container__main {
  ::placeholder {
    color: var(--primary-font-color);
  }
  // padding-right: calc(41.66vw - var(--page-default-margin));
  padding-bottom: 6.3vw;
  .heading {
    display: inline-flex;
    margin-bottom: 1.392vw;
    img {
      width: 2.291vw;
      height: 2.291vw;
      margin-right: 1.042vw;
    }

    .main__heading {
      flex-grow: 1;
      font-size: 1.666vw;
      margin-bottom: 1.042vw;
      font-weight: 600;
    }
  }
  .content__section {
    color: var(--quaternary-font-color);
    .main-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.180vw;
      &-text {
        flex-grow: 1;
        font-size: 1.25vw;
        font-family: var(--secondary-font);
      }
      img {
        width: 2.291vw;
        height: 2.291vw;
        cursor: pointer;
      }
    }
    .sub-section {
        padding-left: 2.083vw;
      .options {
        display: flex;
        align-items: center;
        padding: 1.805vw 0;
        border-bottom: solid 0.069vw var(--timeline-color);
        &-text {
          flex-grow: 1;
          font-size: 1.111vw;
          font-family: var(--secondary-font);
          margin-bottom: unset;
        }
        img {
          width: 2.291vw;
          height: 2.291vw;
          cursor: pointer;
        }
      }
    }
    .active {
      color: var(--primary-font-color);
    }
  }
}
