.container__main {
    ::placeholder {
      color: var(--primary-font-color);
    }
    padding-right: calc(41.66vw - var(--page-default-margin));
    padding-bottom: 6.3vw;
    // .division {
    //   height: 0.069vw;
    //   width: 100%;
    //   background-color: var(--timeline-color);
    // }
    .heading {
      display: inline-flex;
      margin-bottom: 1.392vw;
      img {
        width: 2.291vw;
        height: 2.291vw;
        margin-right: 1.042vw;
      }
  
      .main__heading {
        flex-grow: 1;
        font-size: 1.666vw;
        margin-bottom: 1.042vw;
        font-weight: 600;
      }
    }
  
    .profile-field {
      .field-contents {
        .field-heading {
          margin-top: 2.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 1.25vw;
        }
        .double {
          width: 35vw;
          .input-double {
            width: 17.01vw !important;
          }
        }
        .field-value {
          margin-top: 2.08vw;
          position: relative;
          display: flex;
          justify-content: start;
          align-items: center;
          input[type="text"] {
            background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 35vw;
            height: 3.47vw;
          }
          .input-container {
            // width: 100%;
            width: 20.48vw;
            position: relative;
            display: inline-flex;
            justify-content: start;
            align-items: center;
            .placeholder-institution {
              position: absolute;
              left: 1.041vw;
              top: 50%;
              width: 1.25vw;
              height: 1.25vw;
              transform: translate(0, -50%);
            }
            .flag-icon {
              position: absolute;
              left: 1.04vw;
            }
            button {
              outline: none;
              background: none;
              border: none;
            }
            .flag-arrow {
              position: absolute;
              max-width: 0.6944vw;
              height: auto;
              left: 3.125vw;
            }
            .division {
              position: absolute;
              width: 0.069vw;
              height: 1.04vw;
              background-color: var(--timeline-color);
              left: 4.86vw;
            }
            input[type="text"] {
              background: transparent;
              font-family: var(--secondary-font);
              border: 0.069vw solid var(--timeline-color);
              padding: 1.04vw 1.11vw 1.04vw 5.34vw;
              border-radius: 0.14vw;
              height: 3.47vw;
              width: 20.48vw;
              z-index: 0;
              &.selection {
                padding: 1.04vw 1.11vw 1.04vw 1.04vw;
              }
            }
            .dropdown-visible {
              background-color: var(--primary-background-color);
              visibility: visible;
              position: absolute;
              top: 3.47vw;
              z-index: 1;
              box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
              ul {
                list-style: none;
                padding: 0.69vw 0;
                max-height: 27.97vw;
                margin: 0;
                overflow: hidden;
                overflow-y: scroll;
                li {
                  padding: 1.042vw 1.04vw;
                  width: 20.48vw;
                  // width: 100%;
                  display: flex;
                  .country-name {
                    margin-left: 0.69vw;
                  }
                }
                li:hover {
                  background-color: var(--timeline-color);
                }
              }
              ul::-webkit-scrollbar {
                display: none;
              }
            }
            .dropdown-hidden {
              display: none;
            }
          }
  
          .data-item {
            display: inline-flex;
            // height: 2.638vw;
            align-items: center;
            p {
              margin-bottom: unset;
            }
            img {
              width: 1.25vw;
              height: 1.25vw;
              margin-right: 0.6944vw;
            }
          }
          .ph-flag {
            height: 1.25vw;
            // padding-right: 0.62vw;
            // border-right: solid 0.09vw var(--quaternary-font-color);
          }
          .placeholder {
            position: absolute;
            top: -0.4vw;
            left: 1.04vw;
            font-size: 0.8333vw;
            color: var(--quaternary-font-color);
            padding: 0 0.3vw;
            background-color: var(--primary-background-color);
            // background-color: #ededf1;
          }
          .send {
            margin-left: 1.111vw;
            color: var(--tertiary-font-color);
            font-weight: 600;
          }
        }
        .verify {
          margin-top: 2.78vw;
          font-family: var(--secondary-font);
          font-size: 1.25vw;
        }
        .partitioned {
          // margin-top: 1.33vw;
          margin-top: 2.083vw;
          outline: none;
          padding-left: 0.8vw;
          letter-spacing: 0;
          border: 0;
          background-image: linear-gradient(
            to left,
            var(--timeline-color) 70%,
            rgba(255, 255, 255, 0) 0%
          );
          background-position: bottom;
          background-size: 3.2vw 0.069vw;
          width: 3.2vw;
          background-repeat: repeat-x;
          background-position-x: 2.2vw;
          height: 2vw;
          padding-bottom: 0.35vw;
          font-family: var(--secondary-font);
        }
        .last-changed {
          margin-top: 1.04vw;
          font-size: 0.97vw;
          color: var(--quaternary-font-color);
        }
      }
      .buttonList {
        // margin-top: 3.47vw;
        display: inline-flex;
        margin-bottom: 1.042vw;
  
        .save {
          // display: block;
          width: 100%;
          outline: none;
          font-size: 1.25vw;
          width: 20.56vw;
          background-color: transparent;
          color: var(--tertiary-font-color);
          padding: 0.833333vw 8.263vw;
          border: 0.069vw solid var(--tertiary-font-color);
          border-radius: 1.46vw;
        }
        .cancel {
          width: 100%;
          outline: none;
          font-size: 1.25vw;
          width: 10.56vw;
          background-color: transparent;
          padding: 0.833333vw 2.777vw;
          border: 0.069vw solid transparent;
          border-radius: 1.46vw;
          color: var(--quaternary-font-color);
        }
        .save:hover {
          font-weight: 500;
        }
      }
    }
  }
  