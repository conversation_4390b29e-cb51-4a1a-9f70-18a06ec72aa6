<div class="container__main">
  <div class="heading">
    <img src="../../../../../../assets/icons/<EMAIL>" alt="" />
    <p class="main__heading">Two-Factor Login</p>
  </div>

  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form autocomplete="off">
          <div class="field-value">
            <div class="input-container">
              <input
                type="text"
                [(ngModel)]="mobile"
                placeholder="(*************"
                id="myInput"
                name="mobile_number"
              />
              <img
                src="assets/icons/flag-circle.png"
                alt="image"
                class="placeholder-institution"
              />

              <div class="division"></div>

              <button (click)="isDropDown = !isDropDown" type="button">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDown,
                  'dropdown-visible': isDropDown
                }"
              >
                <ul>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                </ul>
              </div>
            </div>
            <div class="placeholder">Mobile</div>
            <a href="#" class="send">Resend Code</a>
          </div>

          <p class="last-changed">
            We’ll send you a security code to this number <br />whenever you log
            into Terrain.art
          </p>

          <div class="verify">Enter code</div>

          <input
            #input1
            name="otp1"
            [(ngModel)]="otp1"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(1)"
          />
          <input
            #input2
            name="otp2"
            [(ngModel)]="otp2"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(2)"
          />
          <input
            #input3
            name="otp3"
            [(ngModel)]="otp3"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(3)"
          />
          <input
            #input4
            name="otp4"
            [(ngModel)]="otp4"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(4)"
          />
          <input
            #input5
            name="otp5"
            [(ngModel)]="otp5"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(5)"
          />
          <input
            #input6
            name="otp6"
            [(ngModel)]="otp6"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(6)"
          />
          <div class="last-changed" style="margin-bottom: 3.4722vw">
            A six digit code has been sent to your phone
          </div>

          <div class="buttonList">
            <button class="save">Turn on</button>
            <button class="cancel">Cancel</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
