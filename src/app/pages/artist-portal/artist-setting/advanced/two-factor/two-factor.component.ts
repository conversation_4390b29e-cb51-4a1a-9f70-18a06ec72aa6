import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';

@Component({
  selector: 'app-two-factor',
  templateUrl: './two-factor.component.html',
  styleUrls: ['./two-factor.component.scss']
})
export class TwoFactorComponent implements OnInit {
  @ViewChild('input1') otp1El: ElementRef;
  @ViewChild('input2') otp2El: ElementRef;
  @ViewChild('input3') otp3El: ElementRef;
  @ViewChild('input4') otp4El: ElementRef;
  @ViewChild('input5') otp5El: ElementRef;
  @ViewChild('input6') otp6El: ElementRef;

  mobile = '(*************';
  isDropDown = false;
  otp1 = ''
  otp2 = ''
  otp3 = ''
  otp4 = ''
  otp5 = ''
  otp6 = ''

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

   /** Auto Focus to next otp input Field */
  keyUpEvent(index) {
    switch (index) {
      case 1:
        if (this.otp1)
          this.otp2El.nativeElement.focus();
        else
          this.otp1El.nativeElement.focus();
        break;
      case 2:
        if (this.otp2)
          this.otp3El.nativeElement.focus();
        else
          this.otp2El.nativeElement.focus();
        break;
      case 3:
        if (this.otp3)
          this.otp4El.nativeElement.focus();
        else
          this.otp3El.nativeElement.focus();
        break;
      case 4:
        if (this.otp4)
          this.otp5El.nativeElement.focus();
        else
          this.otp4El.nativeElement.focus();
        break;
      case 5:
        if (this.otp5)
          this.otp6El.nativeElement.focus();
        else
          this.otp5El.nativeElement.focus();
        break;
      default:
        break;
    }
  }
}
