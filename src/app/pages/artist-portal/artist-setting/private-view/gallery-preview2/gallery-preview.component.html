<button (click)="generateDocx()" *ngIf="show_download" class="download-span">
  Download Docx
</button>
<button (click)="generateCSV()" *ngIf="show_download" class="download-span">
  Download CSV
</button>

Total artworks: {{ totalArtwork }} , # of artists: {{ totalArtist }}, Published
works: {{ noPublished }}, For Sale: {{ noForSale }}, Reserved: {{ noReserved }},
Not For Sale: {{ noNotforsale }}, Sold: {{ noSold }}.
<div id="pdfContainer">
  <div class="preview" [ngClass]="{ dark: dark_theme }">
    <div
      class="Banner"
      [ngStyle]="bannerCss"
      [style.height]="isMobile ? '90vw' : '34.722vw'"
      *ngIf="show_banner"
    >
      <span class="Collection_Name">{{ title }}</span>
      <!-- <span class="Gallery_Name">GALLEry</span> -->
    </div>
    <div class="Decription">
      <div *ngIf="!show_banner">
        <div *ngIf="!hide_logo" class="logo-container">
          <a id="logo">
            <img [src]="'assets/images/logo.png'" alt="Logo" />
          </a>
        </div>
        <div *ngIf="!hide_logo" class="logo-line"></div>
        <h1>{{ title }}</h1>
      </div>
      <!-- <p *ngIf="show_download" class="download-span">Download PDF</p> -->
      <p class="Desc">
        {{ above_desc }}
      </p>
    </div>

    <div class="Image-Wrap">
      <div
        class="Image-Item"
        [ngStyle]="
          isMobile
            ? { width: '100%' }
            : {
                width: 100 / grid - 10 / grid + '%'
              }
        "
        *ngFor="let item of artworks; let i = index"
      >
        <div
          class="Image-cell"
          [ngStyle]="
            isMobile
              ? { 'min-height': '40vw', width: '50%' }
              : {
                  paddingBottom: (100 * grid) / grid - 10 / grid + '%'
                }
          "
        >
          <img
            (click)="(openArtwork = true) && (selectedArtwork = i)"
            [src]="
              primary_images[i] ||
              'https://www.terrain.art/cdn-cgi/image/width=250,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1692331552330_Image%20Not%20Available%20error.png'
            "
            alt=""
          />
        </div>
        <div class="Image-Details">
          <p>
            <i>{{ item?.artwork_title }}</i
            >, {{ item?.year }}
          </p>
          <p>{{ item?.artist_id?.display_name }}</p>
          <p *ngIf="show_medium">
            {{ item?.medium }}
          </p>
          <p *ngIf="show_dimensions">
            {{ item?.dimensions }}
          </p>
          <p *ngIf="show_signature" class="signed_and_dated">
            {{ item?.signature_details }}
          </p>

          <p *ngIf="show_catalog" class="stock_number">
            {{ item?.catalog_number }}
          </p>
          <fa-icon
            *ngIf="
              show_available &&
              item?.sale_options != 'OnLoan' &&
              item?.sale_options != 'ReturnedToArtist'
            "
            [style.color]="
              item?.sale_options == 'ForSale'
                ? '#00FF00'
                : item?.sale_options == 'Reserved'
                ? '#FFFF00'
                : item?.sale_options == 'OnReserve'
                ? '#FFFF00'
                : item?.sale_options == 'Sold'
                ? '#FF0000'
                : '#101010'
            "
            [icon]="faCircle"
          ></fa-icon>
          <span
            *ngIf="show_available && item?.sale_options == 'ReturnedToArtist'"
            class="circle"
            style="background-color: rgb(236, 112, 10)"
            >R</span
          >
          <span
            *ngIf="show_available && item?.sale_options == 'OnLoan'"
            class="circle"
            style="background-color: rgb(236, 228, 10)"
            >L</span
          >
          <p *ngIf="show_publish">
            {{ item?.publish_artworks ? "Published" : "Not published" }}
          </p>
          <p *ngIf="show_price">
            ₹
            {{ item?.sale_price | number : "1.0" }}
            | $
            {{ item?.sale_price | forex : "USD" | async | number : "1.0" }}
          </p>
          <p *ngIf="show_crypto">
            BTC
            {{ item?.sale_price | forex : "BTC" | async | number : "1.0" }}
            | ETH
            {{ item?.sale_price | forex : "ETH" | async | number : "1.0" }}
            | USDC
            {{ item?.sale_price | forex : "USDC" | async | number : "1.0" }}
          </p>
          <p *ngIf="show_location" class="location">
            {{ item?.location }}
          </p>
          <p *ngIf="show_rights" class="copyright">
            {{ item?.credit_line }}
          </p>
          <p *ngIf="show_edition" class="copyright">
            {{ item?.edition_details }}
          </p>
          <p *ngIf="show_sale_info" class="copyright">
            {{ item?.sale_information }}
          </p>
          <p *ngIf="show_conditions" class="copyright">
            {{ item?.size }}
          </p>
          <p *ngIf="show_conditions" class="copyright">
            Condition: {{ item?.current_state }}
          </p>
          <p *ngIf="show_conditions" class="copyright">
            Install ready: {{ item?.install_ready }}
          </p>
        </div>
      </div>
    </div>
    <div class="Decription">
      <p class="Desc">
        {{ below_desc }}
      </p>
    </div>
  </div>
  <div [ngClass]="{ dark: dark_theme }">
    <div class="Footer-T">
      <hr />
      <div class="text-section text-style">
        <span class="left"
          >All images Courtesy and Copyright of the Artist.
        </span>
        <span class="right">
          Gallery Preview by Repra Art Private Limited © 2023
        </span>
      </div>
    </div>
  </div>
</div>
