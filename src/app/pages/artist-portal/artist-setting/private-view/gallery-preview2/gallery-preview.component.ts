import { <PERSON>mpo<PERSON>, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { faCircle } from "@fortawesome/free-solid-svg-icons";
import { CollectorService } from "src/app/services/collector.service";
import {
	FooterService,
	FooterType,
} from "src/app/shared/services/footer.service";
import {
	NavBarService,
	NavBarType,
} from "src/app/shared/services/navbar.service";
import { apiUrl } from "src/environments/environment.prod";
faCircle;
import { jsPDF } from "jspdf";

import html2canvas from "html2canvas";
import { DomSanitizer } from "@angular/platform-browser";
// import domtoimage from 'dom-to-image';

declare let docx;
declare let saveAs;
@Component({
	selector: "app-gallery-preview",
	templateUrl: "./gallery-preview.component.html",
	styleUrls: ["./gallery-preview.component.scss"],
})
export class GalleryPreview2Component implements OnInit, OnDestroy {
	isMobile = false;
	faCircle = faCircle;

	openArtwork;
	selectedArtwork = 0;

	status = "Sold";
	previewLinkObj: any = {};

	artworks = [];

	dark_theme = false;

	show_banner = false;
	show_download = true;
	show_medium = true;
	show_dimensions = true;
	show_signature = true;
	show_catalog = true;
	show_available = true;
	show_price = true;
	show_crypto = true;
	show_location = true;
	show_publish = true;
	show_rights = true;
	hide_logo = false;
	publish_artwork = true;
	show_sale_info = false;
	show_edition = false;
	show_conditions = false;
	artwork_group = "";
	multiple_type_of_work = [];
	multiple_artwork_group = [];
	multiple_sale_options = [];
	multiple_sort_by = [];

	sort_artist = false;

	above_desc = "";
	below_desc = "";
	title = "";
	grid = 4;
	offset = 1;
	limit = 12;
	min_price = 0;
	max_price = 0;
	sale_options = "";
	search = "";
	location = "";
	artist = [];

	primary_images = [];
	primary_imagesBlobs = [];
	imageSizes = [];
	totalArtwork = 0;
	totalArtist = 0;
	noPublished = 0;
	noForSale = 0;
	noReserved = 0;
	noNotforsale = 0;
	noSold = 0;
	constructor(
		private Activatedroute: ActivatedRoute,
		private server: CollectorService,
		private footerService: FooterService,
		private navBarService: NavBarService,
		private domSanitizer: DomSanitizer,
	) {}
	bannerCss = {
		color: "white",
		"background-color": "black",
	};
	// 'height':'34.722vw'
	ItemWidth = 100 / 4 - 2 + "%";

	ngOnInit(): void {
		this.addScripts();
		this.Activatedroute.queryParamMap.subscribe(async (params) => {
			if (params.get("dark_theme")) {
				this.dark_theme = Boolean(JSON.parse(params.get("dark_theme")));
			} else {
				this.dark_theme = false;
			}
			if (params.get("show_banner")) {
				this.show_banner = Boolean(JSON.parse(params.get("show_banner")));
			} else {
				this.show_banner = false;
			}
			if (params.get("show_download")) {
				this.show_download = Boolean(JSON.parse(params.get("show_download")));
			} else {
				this.show_download = false;
			}
			if (params.get("show_medium")) {
				this.show_medium = Boolean(JSON.parse(params.get("show_medium")));
			} else {
				this.show_medium = true;
			}
			if (params.get("show_dimensions")) {
				this.show_dimensions = Boolean(
					JSON.parse(params.get("show_dimensions")),
				);
			} else {
				this.show_dimensions = true;
			}
			if (params.get("show_signature")) {
				this.show_signature = Boolean(JSON.parse(params.get("show_signature")));
			} else {
				this.show_signature = true;
			}
			if (params.get("show_catalog")) {
				this.show_catalog = Boolean(JSON.parse(params.get("show_catalog")));
			} else {
				this.show_catalog = true;
			}
			if (params.get("show_publish")) {
				this.show_publish = Boolean(JSON.parse(params.get("show_publish")));
			} else {
				this.show_publish = true;
			}
			if (params.get("show_available")) {
				this.show_available = Boolean(JSON.parse(params.get("show_available")));
			} else {
				this.show_available = true;
			}
			if (params.get("show_price")) {
				this.show_price = Boolean(JSON.parse(params.get("show_price")));
			} else {
				this.show_price = true;
			}
			if (params.get("show_crypto")) {
				this.show_crypto = Boolean(JSON.parse(params.get("show_crypto")));
			} else {
				this.show_crypto = true;
			}
			if (params.get("show_location")) {
				this.show_location = Boolean(JSON.parse(params.get("show_location")));
			} else {
				this.show_location = true;
			}
			if (params.get("publish_artwork")) {
				this.publish_artwork = Boolean(
					JSON.parse(params.get("publish_artwork")),
				);
			} else {
				this.publish_artwork = true;
			}
			if (params.get("show_rights")) {
				this.show_rights = Boolean(JSON.parse(params.get("show_rights")));
			} else {
				this.show_rights = true;
			}
			if (params.get("show_edition")) {
				this.show_edition = Boolean(JSON.parse(params.get("show_edition")));
			} else {
				this.show_edition = false;
			}
			if (params.get("show_sale_info")) {
				this.show_sale_info = Boolean(JSON.parse(params.get("show_sale_info")));
			} else {
				this.show_sale_info = false;
			}
			if (params.get("show_conditions")) {
				this.show_conditions = Boolean(
					JSON.parse(params.get("show_conditions")),
				);
			} else {
				this.show_conditions = false;
			}
			if (params.get("title")) {
				this.title = params.get("title");
			} else {
				this.title = "";
			}
			if (params.get("above_desc")) {
				this.above_desc = params.get("above_desc");
			} else {
				this.above_desc = "";
			}
			if (params.get("below_desc")) {
				this.below_desc = params.get("below_desc");
			} else {
				this.below_desc = "";
			}
			if (params.get("grid")) {
				this.grid = Number(params.get("grid"));
			} else {
				this.grid = 4;
			}
			if (params.get("offset")) {
				this.offset = Number(params.get("offset"));
			} else {
				this.offset = 1;
			}
			if (params.get("limit")) {
				if (params.get("limit") == "All") {
					this.limit = 40000;
				} else {
					this.limit = Number(params.get("limit"));
				}
			} else {
				this.limit = 12;
			}
			if (params.get("min_price")) {
				this.min_price = Number(params.get("min_price"));
			} else {
				this.min_price = 0;
			}
			if (params.get("max_price")) {
				this.max_price = Number(params.get("max_price"));
			} else {
				this.max_price = 0;
			}
			if (params.get("sale_options")) {
				this.sale_options = params.get("sale_options");
			} else {
				this.sale_options = "";
			}
			if (params.get("search")) {
				this.search = params.get("search");
			} else {
				this.search = "";
			}
			if (params.get("location")) {
				this.location = params.get("location");
			} else {
				this.location = "";
			}

			if (params.get("sort_artist")) {
				this.sort_artist = Boolean(JSON.parse(params.get("sort_artist")));
			} else {
				this.sort_artist = false;
			}
			if (params.get("hide_logo")) {
				this.hide_logo = Boolean(JSON.parse(params.get("hide_logo")));
			} else {
				this.hide_logo = false;
			}
			if (params.get("artist")) {
				this.artist = params.get("artist").split(",");
			} else {
				this.artist = [];
			}
			if (params.get("multiple_type_of_work")) {
				this.multiple_type_of_work = params
					.get("multiple_type_of_work")
					.split(",");
			} else {
				this.multiple_type_of_work = [];
			}
			if (params.get("sale_options")) {
				this.multiple_sale_options = params.get("sale_options").split(",");
			} else {
				this.multiple_sale_options = [];
			}
			if (params.get("artwork_group")) {
				this.multiple_artwork_group = params.get("artwork_group").split(",");
			} else {
				this.multiple_artwork_group = [];
			}
			if (params.get("multiple_sort_by")) {
				this.multiple_sort_by = params.get("multiple_sort_by").split(",");
			} else {
				this.multiple_sort_by = [];
			}
			this.getData();
		});
		this.footerService.changeFooterType(FooterType.HIDE);
		this.navBarService.changeNavbarType(NavBarType.HIDE);
		this.getIsMobile(document.documentElement.clientWidth);
	}
	addScripts() {
		(function (d, s, id, obj) {
			var js,
				fjs = d.getElementsByTagName(s)[0];
			if (d.getElementById(id)) {
				window["docx"]();
				return;
			}
			js = d.createElement(s);
			js.id = id;
			js.async = true;
			js.src = "https://unpkg.com/docx@5.0.2/build/index.js";
			fjs.parentNode.insertBefore(js, fjs);
		})(document, "script", "doxc-sdk", this);
		(function (d, s, id, obj) {
			var js,
				fjs = d.getElementsByTagName(s)[0];
			if (d.getElementById(id)) {
				window["saveAs"]();
				return;
			}
			js = d.createElement(s);
			js.id = id;
			js.async = true;
			js.src =
				"https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.js";
			fjs.parentNode.insertBefore(js, fjs);
		})(document, "script", "file-saver", this);
	}
	ngOnDestroy(): void {
		this.footerService.changeFooterType(FooterType.DEFAULT);
		this.navBarService.changeNavbarType(NavBarType.DEFAULT);
	}
	@HostListener("window:resize", ["$event"])
	onResize(event) {
		// event.target.innerWidth;
		const w = event.target.innerWidth;
		this.isMobile = this.getIsMobile(w);
	}

	public getIsMobile(w): boolean {
		const breakpoint = 768;
		if (w <= breakpoint) {
			this.isMobile = true;
			return true;
		} else {
			this.isMobile = false;
			return false;
		}
	}

	getData = async () => {
		let url = apiUrl.getArtwork;
		let data = {
			offset: this.offset,
			limit: this.limit,
			sort: 0,
			publish_artworks: this.publish_artwork,
			priceMin: this.min_price,
			priceMax: this.max_price,
			multiple_sale_options: this.multiple_sale_options,
			location: this.location,
			search: this.search,
			multiple_artwork_group: this.multiple_artwork_group,
			multiple_sort_by: this.multiple_sort_by,
			artist: this.artist,
			medium: [],
			subject: [],
			movement: [],
			tags: [],
			multiple_type_of_work: this.multiple_type_of_work,
		};
		this.server.postApi(url, data).subscribe((res) => {
			if (res.statusCode == 200) {
				this.artworks = res.data;
				if (this.sort_artist) {
					this.artworks.sort((a, b) => {
						const first = a?.artist_id?._id;
						const second = b?.artist_id?._id;
						if (first < second) {
							return -1;
						}
						if (first > second) {
							return 1;
						}
						return 0;
					});
				}
				this.primary_images = Array(this.artworks?.length).fill(null);
				this.primary_imagesBlobs = Array(this.artworks?.length).fill(null);
				this.imageSizes = Array(this.artworks?.length).fill(null);
				this.totalArtwork = this.artworks?.length;
				const uniArtist = [];
				this.artworks.forEach((ele, i) => {
					if (!uniArtist.includes(ele?.artist_id?._id)) {
						uniArtist.push(ele?.artist_id?._id);
						this.totalArtist++;
					}
					if (ele?.publish_artworks) {
						this.noPublished++;
					}
					if (ele?.sale_options === "ForSale") {
						this.noForSale++;
					}
					if (ele?.sale_options === "NotForSale") {
						this.noNotforsale++;
					}
					if (ele?.sale_options === "Sold") {
						this.noSold++;
					}
					if (ele?.sale_options === "Reserved") {
						this.noReserved++;
					}
					if (this.get_url_extension(ele?.primary_image[0]?.url) === "gif") {
						this.server
							.getImage(ele?.primary_image[0]?.url)
							.subscribe((image) => {
								this.primary_imagesBlobs[i] = image;
								this.createImageFromBlob(image, i);
							});
					} else {
						this.server
							.getImage(
								"https://www.terrain.art/cdn-cgi/image/width=" +
									(1600 / Number(this.grid)).toString() +
									",quality=52/" +
									ele?.primary_image[0]?.url,
							)
							.subscribe((image) => {
								this.primary_imagesBlobs[i] = image;
								this.createImageFromBlob(image, i);
							});
					}
				});
			}
		});
	};
	get_url_extension(url) {
		return url?.split(/[#?]/)[0].split(".").pop().trim();
	}
	htmltoPDF() {
		// parentdiv is the html element which has to be converted to PDF
		let HTML_Width = document.querySelector("#pdfContainer").scrollWidth;
		let HTML_Height = document.querySelector("#pdfContainer").scrollHeight;
		let top_left_margin = 15;
		let PDF_Width = HTML_Width + top_left_margin * 2;
		let PDF_Height = PDF_Width * 1.5 + top_left_margin * 2;

		let canvas_image_width = HTML_Width;
		let canvas_image_height = HTML_Height;

		let totalPDFPages = Math.ceil(HTML_Height / PDF_Height) - 1;

		html2canvas(document.querySelector("#pdfContainer"), {
			allowTaint: true,
			useCORS: true,
			logging: true,
			proxy: "https://cors-anywhere.herokuapp.com/",
		}).then((canvas) => {
			canvas.getContext("2d");

			//console.log(canvas.height+"  "+canvas.width);

			let imgData = canvas.toDataURL("image/jpeg", 1.0);
			let pdf = new jsPDF("p", "mm", [PDF_Width, PDF_Height]);
			pdf.addImage(
				imgData,
				"JPG",
				top_left_margin,
				top_left_margin,
				canvas_image_width,
				canvas_image_height,
			);

			for (let i = 1; i <= totalPDFPages; i++) {
				pdf.addPage([PDF_Width, PDF_Height]);

				pdf.addImage(
					imgData,
					"JPG",
					top_left_margin,
					-(PDF_Height * i) + top_left_margin * 4 + top_left_margin,
					canvas_image_width,
					canvas_image_height,
				);
			}

			// var pdf = new jsPDF('p', 'pt', [canvas.width, canvas.height]);

			// var imgData = canvas.toDataURL('image/jpeg', 1.0);
			// pdf.addImage(imgData, 0, 0, canvas.width, canvas.height);
			// // pdf.addPage();
			// // pdf.addImage(imgData, 0, 0, canvas.width, canvas.width);
			pdf.save("artworks.pdf");
		});
		// var node = document.getElementById('pdfContainer');

		// var img;
		// var filename;
		// var newImage;

		// domtoimage
		//   .toPng(node, { bgcolor: '#fff', cacheBust: true })

		//   .then(function (dataUrl) {
		//     img = new Image();
		//     img.src = dataUrl;
		//     newImage = img.src;

		//     img.onload = function () {
		//       var pdfWidth = img.width;
		//       var pdfHeight = img.height;

		//       // FileSaver.saveAs(dataUrl, 'my-pdfimage.png'); // Save as Image

		//       var doc;

		//       if (pdfWidth > pdfHeight) {
		//         doc = new jsPDF('l', 'px', [pdfWidth, pdfHeight]);
		//       } else {
		//         doc = new jsPDF('p', 'px', [pdfWidth, pdfHeight]);
		//       }

		//       var width = doc.internal.pageSize.getWidth();
		//       var height = doc.internal.pageSize.getHeight();

		//       doc.addImage(newImage, 'PNG', 10, 10, width, height);
		//       filename = 'mypdf_' + '.pdf';
		//       doc.save(filename);
		//     };
		//   })
		//   .catch(function (error) {
		//     // Error Handling
		//   });
	}
	async generateDocx() {
		const doc = new docx.Document();
		const cellBoarderOptions = {
			style: docx.BorderStyle.NONE,
			color: "FFFFFF",
			size: 0,
		};
		const cellBoarder = {
			top: cellBoarderOptions,
			bottom: cellBoarderOptions,
			left: cellBoarderOptions,
			right: cellBoarderOptions,
		};
		const headerMargins = {
			top: 200,
			bottom: 200,
		};

		const tableRows = [];

		const grid = Number(this.grid);

		for (let i = 0; i < this.artworks.length; i += grid) {
			const tableCell = [];
			const tableCellimage = [];
			for (let j = 0; j < grid; j++) {
				const element = this.artworks[i + j];
				if (!element) {
					break;
				}
				let image_width, image_height;
				if (this.imageSizes[i + j].height < this.imageSizes[i + j].width) {
					image_width = Math.floor(700 / grid);
					image_height = Math.floor(
						(this.imageSizes[i + j].height * Math.floor(700 / grid)) /
							this.imageSizes[i + j].width,
					);
				} else {
					image_width = Math.floor(
						(this.imageSizes[i + j].width * Math.floor(700 / grid)) /
							this.imageSizes[i + j].height,
					);
					image_height = Math.floor(700 / grid);
				}
				const image = docx.Media.addImage(
					doc,
					this.primary_imagesBlobs[i + j],
					image_width,
					image_height,
				);
				const cellContents = [];
				tableCellimage.push(
					new docx.TableCell({
						children: [
							new docx.Paragraph({
								children: [image],
								alignment: docx.AlignmentType.CENTER,
							}),
						],
						width: {
							size: Math.floor(95 / grid),
							type: docx.WidthType.PERCENTAGE,
						},
						margins: { top: 200, bottom: 0 },
						borders: cellBoarder,
						verticalAlign: docx.VerticalAlign.CENTER,
					}),
				);
				// cellContents.push(new docx.Paragraph({
				//   children: [image],
				//   alignment: docx.AlignmentType.CENTER,
				// }));
				cellContents.push(
					new docx.Paragraph({
						children: [
							new docx.TextRun({
								text: "",
								bold: true,
								color: "000000",
								size: 30,
							}),
						],
						alignment: docx.AlignmentType.LEFT,
						spacing: { after: 100 },
					}),
				);
				cellContents.push(
					new docx.Paragraph({
						children: [
							new docx.TextRun({
								text: element.artwork_title,
								italics: true,
								color: "000000",
								size: 24,
							}),
							new docx.TextRun({
								text: ", " + element.year,
								color: "000000",
								size: 24,
							}),
						],
						alignment: docx.AlignmentType.LEFT,
						spacing: { after: 100 },
					}),
				);
				cellContents.push(
					new docx.Paragraph({
						children: [
							new docx.TextRun({
								text: element.artist_id?.display_name,
								bold: true,
								color: "000000",
								size: 26,
							}),
						],
						alignment: docx.AlignmentType.LEFT,
						spacing: { after: 100 },
					}),
				);
				if (this.show_medium && element.medium) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element.medium,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_dimensions && element.dimensions) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element.dimensions,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_price && element.sale_price) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: "₹" + this.numberWithCommas(element?.sale_price),
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_signature && element.signature_details) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element.signature_details,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_catalog && element.catalog_number) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element.catalog_number,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_available && element.sale_options) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.sale_options,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_publish && element.publish_artworks) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.publish_artworks
										? "Published"
										: "Not published",
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}

				if (this.show_location && element.location) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.location,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_rights && element.credit_line) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.credit_line,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_edition && element.edition_details) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.edition_details,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_sale_info && element.sale_information) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.sale_information,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_conditions && element.size) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: element?.size,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_conditions && element.current_state) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: " Condition: " + element?.current_state,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}
				if (this.show_conditions && element.install_ready) {
					cellContents.push(
						new docx.Paragraph({
							children: [
								new docx.TextRun({
									text: "Install ready: " + element?.install_ready,
									color: "000000",
									size: 24,
								}),
							],
							alignment: docx.AlignmentType.LEFT,
							spacing: { after: 50 },
						}),
					);
				}

				tableCell.push(
					new docx.TableCell({
						children: cellContents,
						width: {
							size: Math.floor(95 / grid),
							type: docx.WidthType.PERCENTAGE,
						},
						margins: { top: 0, bottom: 200 },
						borders: cellBoarder,
					}),
				);
			}

			tableRows.push(
				new docx.TableRow({
					children: tableCellimage,
					cantSplit: true,
				}),
			);
			tableRows.push(
				new docx.TableRow({
					children: tableCell,
					cantSplit: true,
				}),
			);
		}
		console.log(tableRows);
		const table = new docx.Table({
			rows: tableRows,
			width: {
				size: 100,
				type: docx.WidthType.PERCENTAGE,
			},
		});
		doc.addSection({
			children: [table],
			margins: { left: 500, right: 500 },
		});

		docx.Packer.toBlob(doc).then((blob) => {
			saveAs(blob, "artworks.docx");
			console.log("Document created successfully");
		});
	}
	generateCSV() {
		const csvRows = [];
		const csvHeader = [
			"Artwork ID",
			"Title",
			"Year",
			"Artwork Group",
			"Artist ID",
			"Artist Name",
			"Artist Type",
			"Thumbnail Image URL",
		];
		if (this.show_medium) {
			csvHeader.push("Medium");
		}
		if (this.show_dimensions) {
			csvHeader.push("Dimensions");
		}
		if (this.show_signature) {
			csvHeader.push("Signature");
		}
		if (this.show_catalog) {
			csvHeader.push("Catalog Number");
		}
		if (this.show_available) {
			csvHeader.push("Availablity");
		}
		if (this.show_publish) {
			csvHeader.push("Publish");
		}
		if (this.show_price) {
			csvHeader.push("Sale Price");
		}
		if (this.show_location) {
			csvHeader.push("Location");
		}
		if (this.show_rights) {
			csvHeader.push("Credit Line");
		}
		if (this.show_edition) {
			csvHeader.push("Edition Details");
		}
		if (this.show_sale_info) {
			csvHeader.push("Sale Information");
		}
		if (this.show_sale_info) {
			csvHeader.push("Sold Date");
		}
		if (this.show_conditions) {
			csvHeader.push("Size");
			csvHeader.push("Condition");
			csvHeader.push("Install ready");
		}
		csvRows.push(csvHeader.join(","));

		for (let i = 0; i < this.artworks.length; i++) {
			const item = this.artworks[i];
			const csvRow = [
				item?._id,
				item?.artwork_title,
				item?.year,
				item?.artwork_group,
				item?.artist_id?._id,
				item?.artist_id?.display_name,
				item?.artist_id?.artist_type,
				item?.thumbnail_of_primary,
			];
			if (this.show_medium) {
				csvRow.push(item?.medium);
			}
			if (this.show_dimensions) {
				csvRow.push(item?.dimensions);
			}
			if (this.show_signature) {
				csvRow.push(item?.signature_details);
			}
			if (this.show_catalog) {
				csvRow.push(item?.catalog_number);
			}
			if (this.show_available) {
				csvRow.push(item?.sale_options);
			}
			if (this.show_publish) {
				csvRow.push(item?.publish_artworks);
			}
			if (this.show_price) {
				csvRow.push(item?.sale_price);
			}
			if (this.show_location) {
				csvRow.push(item?.location);
			}
			if (this.show_rights) {
				csvRow.push(item?.credit_line);
			}
			if (this.show_edition) {
				csvRow.push(item?.edition_details);
			}
			if (this.show_sale_info) {
				csvRow.push(item?.sale_information);
			}
			if (this.show_sale_info) {
				csvRow.push(item?.sold_date);
			}
			if (this.show_conditions) {
				csvRow.push(item?.size);
				csvRow.push(item?.current_state);
				csvRow.push(item?.install_ready);
			}
			csvRows.push(
				csvRow
					.map((value) => {
						if (value?.toString()?.includes(",")) {
							return `"${value}"`;
						} else {
							return value;
						}
					})
					.join(","),
			);
		}
		let csvData = csvRows.join("\n");
		const mimeType = "text/csv;charset=utf-8";
		const csvBlob = new Blob([csvData], { type: mimeType });
		saveAs(csvBlob, "artworks.csv");
		console.log("CSV created successfully");
	}
	createImageFromBlob(image: Blob, i: number) {
		let reader = new FileReader();
		reader.addEventListener(
			"load",
			() => {
				this.primary_images[i] =
					this.domSanitizer.bypassSecurityTrustResourceUrl(
						reader.result as any,
					);
				var img = new Image();

				img.onload = () => {
					this.imageSizes[i] = { width: img.width, height: img.height };
				};

				img.src = reader.result as string;
			},
			false,
		);

		if (image) {
			reader.readAsDataURL(image);
		}
	}
	numberWithCommas(x) {
		return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
	}
}
