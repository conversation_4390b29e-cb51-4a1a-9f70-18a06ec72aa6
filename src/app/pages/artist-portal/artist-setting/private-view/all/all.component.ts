import { ToastService } from "../../../../../core/toast/toast.service";
import {
	FooterService,
	FooterType,
} from "./../../../../../shared/services/footer.service";
import {
	Component,
	ElementRef,
	OnDestroy,
	OnInit,
	QueryList,
	ViewChild,
	ViewChildren,
} from "@angular/core";
import {
	faAngleDown,
	faCircle,
	faAngleLeft,
	faAngleRight,
	faCheckCircle,
	faTimesCircle,
	faSearchPlus,
} from "@fortawesome/free-solid-svg-icons";
import { apiUrl } from "src/environments/environment.prod";
import { CollectorService } from "src/app/services/collector.service";
import { Router } from "@angular/router";
import {
	NavBarService,
	NavBarType,
} from "src/app/shared/services/navbar.service";
import { Subject } from "rxjs";
import { debounceTime } from "rxjs/operators";

interface Command {
	execute(): void;
	undo(): void;
}

interface ArtworkState {
	_id: string;
	selected: boolean;
	artist_id: {
		display_name: string;
	};
	// ... other artwork properties
}

class RemoveSelectedCommand implements Command {
	private removedItems: ArtworkState[] = [];
	private removedIndices: number[] = [];

	constructor(private artworks: ArtworkState[]) {
		// Store the items and their positions before removal
		this.artworks.forEach((artwork, index) => {
			if (artwork.selected) {
				this.removedItems.push({ ...artwork });
				this.removedIndices.push(index);
			}
		});
	}

	execute(): void {
		// Remove the selected items
		const selectedIds = new Set(this.removedItems.map((item) => item._id));
		const newArtworks = this.artworks.filter(
			(artwork) => !selectedIds.has(artwork._id),
		);
		this.artworks.length = 0; // Clear the array while maintaining reference
		this.artworks.push(...newArtworks);
	}

	undo(): void {
		// Reinsert items at their original positions
		for (let i = this.removedIndices.length - 1; i >= 0; i--) {
			const index = this.removedIndices[i];
			const item = { ...this.removedItems[i] };
			this.artworks.splice(index, 0, item);
		}
	}
}
class MoveItemsCommand implements Command {
	private previousState: ArtworkState[];

	constructor(
		private artworks: ArtworkState[],
		private operation: "up" | "down" | "top" | "bottom",
		private indices?: number[],
	) {
		// Store deep copy of original state
		this.previousState = JSON.parse(JSON.stringify(artworks));
	}

	execute(): void {
		const items = [...this.artworks];

		switch (this.operation) {
			case "up":
				if (this.indices) {
					// Move specific indices up
					this.indices.sort((a, b) => a - b); // Sort ascending to avoid conflicts
					// biome-ignore lint/complexity/noForEach: <explanation>
					this.indices.forEach((index) => {
						if (index > 0) {
							const temp = items[index];
							items[index] = items[index - 1];
							items[index - 1] = temp;
						}
					});
				}
				break;

			case "down":
				if (this.indices) {
					// Move specific indices down
					this.indices.sort((a, b) => b - a); // Sort descending to avoid conflicts
					// biome-ignore lint/complexity/noForEach: <explanation>
					this.indices.forEach((index) => {
						if (index < items.length - 1) {
							const temp = items[index];
							items[index] = items[index + 1];
							items[index + 1] = temp;
						}
					});
				}
				break;

			case "top":
				if (this.indices) {
					// Move selected items to top
					const selectedItems = this.indices.map((index) => items[index]);
					const unselectedItems = items.filter(
						(_, index) => !this.indices?.includes(index),
					);
					Object.assign(this.artworks, [...selectedItems, ...unselectedItems]);
					return;
				}
				break;

			case "bottom":
				if (this.indices) {
					// Move selected items to bottom
					const selectedItems = this.indices.map((index) => items[index]);
					const unselectedItems = items.filter(
						(_, index) => !this.indices?.includes(index),
					);
					Object.assign(this.artworks, [...unselectedItems, ...selectedItems]);
					return;
				}
				break;
		}

		Object.assign(this.artworks, items);
	}

	undo(): void {
		Object.assign(
			this.artworks,
			JSON.parse(JSON.stringify(this.previousState)),
		);
	}
}

class SelectCommand implements Command {
	private previousState: boolean[];

	constructor(
		private artworks: ArtworkState[],
		private indices: number[],
		private selected: boolean,
	) {
		this.previousState = artworks.map((artwork) => artwork.selected);
	}

	execute(): void {
		// biome-ignore lint/complexity/noForEach: <explanation>
		this.indices.forEach((index) => {
			if (index >= 0 && index < this.artworks.length) {
				this.artworks[index].selected = this.selected;
			}
		});
	}

	undo(): void {
		this.artworks.forEach((artwork, index) => {
			artwork.selected = this.previousState[index];
		});
	}
}

// Command manager to handle undo/redo operations
class CommandManager {
	private undoStack: Command[] = [];
	private redoStack: Command[] = [];

	execute(command: Command): void {
		command.execute();
		this.undoStack.push(command);
		this.redoStack = []; // Clear redo stack when new command is executed
	}

	undo(): void {
		const command = this.undoStack.pop();
		if (command) {
			command.undo();
			this.redoStack.push(command);
		}
	}

	redo(): void {
		const command = this.redoStack.pop();
		if (command) {
			command.execute();
			this.undoStack.push(command);
		}
	}

	canUndo(): boolean {
		return this.undoStack.length > 0;
	}

	canRedo(): boolean {
		return this.redoStack.length > 0;
	}
}

@Component({
	selector: "app-all",
	templateUrl: "./all.component.html",
	styleUrls: ["./all.component.scss"],
	host: {
		"(document:click)": "onClick($event)",
	},
})
export class AllComponent implements OnInit, OnDestroy {
	private commandManager = new CommandManager();
	canUndo = false;
	canRedo = false;

	headerfieldSize: number = 20;
	faAngleLeft = faAngleLeft;
	faAngleRight = faAngleRight;
	faCircle = faCircle;
	faCheckCircle = faCheckCircle;
	faTimesCircle = faTimesCircle;
	faSearchPlus = faSearchPlus;
	isArtworkPopupOpen = false;
	isArtworkPopupOpen2 = false;
	isArtworkPopupOpen3 = false;
	artImg;
	status = "Sold";
	faAngleDown = faAngleDown;
	items = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}];
	SideDrawer = Array(10).fill(false);
	CheckboxDropdown = Array(20).fill(false);
	dropDownFirstClick = Array(20).fill(false);
	previewLinkObj: any = {};
	contentObj: any = {
		showAvailability: true,
		showCatalogId: false,
		showDescription: false,
		showProvenance: false,
		showLiterature: false,
		showExhibitions: false,
		showPublications: false,
		showCopyrightLine: false,
		showPhotoCredit: false,
		showSalePrice: false,
		showCryptoPrice: false,
		showBanner: false,
		darkTheme: false,
		displayImageFiles: false,
		excludeSecondaryImages: false,
		showArtworks: false,
		installationViews: false,
		imageAlignHorizontal: "Left",
		imageAlignVertical: "Middle",
		synchronize: false,
		showMedium: false,
		showDimensions: false,
		showSignature: false,
		showLocation: false,
		showDownload: false,
		showSaleInfo: false,
		showEditionDetails: false,
		showCondition: false,
		grid: 4,
	};
	offset;
	sort;
	searchKey = "";
	artworksArr = [];
	totalRecords;
	totalPage;
	search: Subject<any> = new Subject();
	search2: Subject<any> = new Subject();
	filter: any = "Artwork Title";
	isFilterDropdownOpen = false;
	isFilterDropdownOpen1 = false;
	isDuplicatePopupOpen = false;
	isFilterDropdownOpen2 = false;
	isFilterDropdownOpen3 = false;
	isFilterDropdownOpen4 = false;
	isFilterDropdownOpen5 = false;
	duplicateTitle = "";
	multiple_sale_options = [];
	work_type = [];

	priceFrom;
	priceTo;
	minPrice;
	maxPrice;

	heightFrom;
	heightTo;
	minHeight;
	maxHeight;
	widthFrom;
	widthTo;
	minWidth;
	maxWidth;

	gotoPage;

	inputCheckBox = Array(20).fill(false);

	filteredArtworksArr: any = [];
	filteredArtworksArr2: any = [];
	offset2 = 0;
	totalRecords2 = 0;
	totalPage2 = 0;
	searchKey2 = "";
	sort2 = "Title↑";

	gotoPage2 = 1;

	@ViewChildren("dropDowns") dropDowns: QueryList<ElementRef>;
	@ViewChild("dropDowns1") dropDowns1: ElementRef;
	@ViewChild("dropDowns2") dropDowns2: ElementRef;
	@ViewChild("dropDowns3") dropDowns3: ElementRef;
	@ViewChild("dropDowns4") dropDowns4: ElementRef;
	@ViewChild("dropDowns5") dropDowns5: ElementRef;
	constructor(
		private footerService: FooterService,
		private navBarService: NavBarService,
		private server: CollectorService,
		private router: Router,
		private toastService: ToastService,
	) {
		this.getPreviewLinks();
	}

	ngOnInit(): void {
		this.searchByKey();
		this.searchByKey2();
		this.footerService.changeFooterType(FooterType.HIDE);
		// this.navBarService.changeNavbarType(NavBarType.HIDE);
	}
	ngOnDestroy(): void {
		this.footerService.changeFooterType(FooterType.DEFAULT);
		// this.navBarService.changeNavbarType(NavBarType.DEFAULT);
	}

	// to get preview links
	getPreviewLinks() {
		let preview_id = window.location.href.split("edit/")[1];
		this.getPrivateViewByID(preview_id);
	}

	// to get private view by ID
	getPrivateViewByID(preview_id) {
		let url = apiUrl.artworks.createPrivate + `/${preview_id}`;
		this.server.postApi(url, {}).subscribe((res) => {
			if (res.statusCode == 200) {
				this.previewLinkObj = res.data;
				Object.assign(this.contentObj, res.data.contents[0].data);
				//this.contentObj = res.data.contents[0].data
				this.previewLinkObj.artworkIds.forEach((ele) => {
					ele["selected"] = false;
				});
				this.filteredArtworksArr2 = this.previewLinkObj.artworkIds;
				this.totalPage2 = Math.ceil(this.filteredArtworksArr2.length / 8);
				this.totalRecords2 = this.filteredArtworksArr2.length;
				this.filteredArtworksArr = this.filteredArtworksArr2.slice(0, 8);
				this.gotoPage2 = 1;
			}
		});
	}

	onRemoveSelected() {
		// Check if there are any selected items
		const hasSelectedItems = this.previewLinkObj.artworkIds.some(
			(ele) => ele.selected,
		);

		if (!hasSelectedItems) {
			this.toastService.createToast("No items selected for removal");
			return;
		}

		// Create and execute the remove command
		const removeCommand = new RemoveSelectedCommand(
			this.previewLinkObj.artworkIds,
		);
		this.commandManager.execute(removeCommand);

		// Update UI state
		this.updateUndoRedoState();

		// Show confirmation toast
		this.toastService.createToast("Removed Selected Items");
	}

	// to update private link
	updatePrivateLink() {
		let id = [];
		this.previewLinkObj.artworkIds.forEach((ele) => {
			id.push(ele["_id"]);
		});
		let req = {
			title: this.previewLinkObj.title,
			aboveDescription: this.previewLinkObj.aboveDescription,
			BelowDescription: this.previewLinkObj.BelowDescription,
			privateViewLink: this.previewLinkObj.privateViewLink,
			contents: [
				{
					data: this.contentObj,
					model: "model-1",
					component: "artwork settings",
				},
			],
			artworkIds: [...id],
		};
		let url = apiUrl.artworks.createPrivate + `/${this.previewLinkObj["_id"]}`;
		this.server.patchApi(url, req).subscribe((res) => {
			if (res.statusCode == 200) {
				this.toastService.createToast("Preview Link Saved!");
			}
		});
	}
	duplicatePrivateLink() {
		let id = [];
		this.previewLinkObj.artworkIds.forEach((ele) => {
			id.push(ele["_id"]);
		});
		const previewLink = this.previewLinkObj.privateViewLink.split("=");
		previewLink.splice(-1);
		let previewLinkurl = previewLink.join("=") + "=";
		let req = {
			title: this.duplicateTitle,
			aboveDescription: this.previewLinkObj.aboveDescription,
			BelowDescription: this.previewLinkObj.BelowDescription,
			privateViewLink: previewLinkurl,
			contents: [
				{
					data: this.contentObj,
					model: "model-1",
					component: "artwork settings",
				},
			],
			artworkIds: [...id],
		};
		let url = apiUrl.artworks.createPrivate;
		this.server.postApi(url, req).subscribe((res) => {
			if (res.statusCode == 200) {
				alert(res.message);
				this.router.navigate([
					"/artist-portal/settings/artwork/private_view/list",
				]);
			}
		});
	}

	OpenDrawer(id) {
		if (this.SideDrawer[id]) {
			this.SideDrawer[id] = false;
		} else {
			this.SideDrawer = Array(10).fill(false);
			this.SideDrawer[id] = true;
		}
	}

	onEdit(item) {
		localStorage.setItem("artworkID", item["_id"]);
		this.router.navigate(["artist-portal/settings/artwork/add"]);
	}

	back() {
		this.router.navigate(["artist-portal/settings/artwork/private_view/list"]);
	}

	copy(text) {
		navigator.clipboard
			.writeText(text)
			.then()
			.catch((e) => console.error(e));
		this.toastService.createToast("Url copied !");
		console.log("Test MF");
	}

	openArtworks() {
		this.isArtworkPopupOpen = true;
		this.offset = 1;
		this.sort = "Title↑";
		this.artworksArr = [];
		this.getArtworks();
	}
	getArtworks() {
		let url = apiUrl.getArtwork;
		this.gotoPage = this.offset;
		let req = {
			search: this.searchKey,
			external: false,
			artworkIds: [],
			platform: "",
			userId: "",
			offset: this.offset,
			limit: 8,
			sort: this.sort == "Title↑" ? 4 : 3,
			publish_artworks: false,
			sale_options: "",
			artwork_group: "",
			approved_status: "",
			artist: [],
			medium: [],
			subject: [],
			movement: [],
			tags: [],
			flag: null,
			multiple_sale_options: this.multiple_sale_options,
			work_type: this.work_type,
			priceMin: this.minPrice ? this.minPrice : 0,
			priceMax: this.maxPrice ? this.maxPrice : 0,
			heightMin: this.minHeight ? this.minHeight - 1 : 0,
			heightMax: this.maxHeight ? this.maxHeight + 1 : 0,
			widthMin: this.minWidth ? this.minWidth - 1 : 0,
			widthMax: this.maxWidth ? this.maxWidth + 1 : 0,
		};

		this.server.postApi(url, req).subscribe((res) => {
			if (res.statusCode == 200) {
				this.artworksArr = res.data;
				this.artworksArr.forEach((ele) => {
					ele["selected"] = false;
					let a_id = this?.previewLinkObj?.artworkIds.find(
						(a) => a._id == ele?._id,
					);
					if (a_id) {
						ele["selected"] = true;
					}
				});
				this.totalRecords = res.fiter_totalCount;
				this.totalPage = Math.ceil(res.fiter_totalCount / 8);
			}
		});
	}

	searchResult(evt) {
		var charCode = evt.which ? evt.which : evt.keyCode;
		let string = evt.target.value;
		if (charCode == 13 || charCode == 8 || charCode == 16) {
			return;
		} else {
			this.search.next(string.trim());
		}
	}
	manageSelectedArtworks() {
		this.isArtworkPopupOpen = false;
		this.artworksArr.forEach((ele) => {
			if (ele.selected) {
				delete ele.selected;
				this.previewLinkObj.artworkIds.push(ele);
			}
		});
	}
	// for artwork select
	searchByKey() {
		if (navigator.onLine) {
			this.search.pipe(debounceTime(1500)).subscribe((val) => {
				this.searchKey = val;
				this.offset = 1;
				this.totalPage = 0;
				this.getArtworks();
			});
		}
	}
	// to manage pagination
	managePagination(action) {
		if (action == "prev") {
			if (this.offset > 1) {
				this.offset = this.offset - 1;
			} else {
				return;
			}
		} else if (action == "next") {
			if (this.offset < this.totalPage) {
				this.offset = this.offset + 1;
			} else {
				return;
			}
		}
		this.getArtworks();
	}
	sortByartist() {
		this.previewLinkObj.artworkIds = this.previewLinkObj?.artworkIds.sort(
			(a, b) => a?.year.localeCompare(b?.year),
		);
		this.previewLinkObj.artworkIds = this.previewLinkObj?.artworkIds.sort(
			(a, b) => a?.artwork_title.localeCompare(b?.artwork_title),
		);
		this.previewLinkObj.artworkIds = this.previewLinkObj?.artworkIds.sort(
			(a, b) =>
				a?.artist_id?.display_name.localeCompare(b?.artist_id?.display_name),
		);
	}

	SetAdvSaleFilter(status, selected) {
		this.isFilterDropdownOpen2 = false;
		if (selected == true) this.multiple_sale_options.push(status);
		else {
			let index = this.multiple_sale_options.indexOf(status);
			this.multiple_sale_options.splice(index, 1);
		}

		this.multiple_sale_options = this.multiple_sale_options.filter(
			(x, i, a) => a.indexOf(x) == i,
		);

		this.getArtworks();
	}
	SetArtworkGroupFilter2(status, selected) {
		this.isFilterDropdownOpen3 = false;
		if (selected == true) this.work_type.push(status);
		else {
			let index = this.work_type.indexOf(status);
			this.work_type.splice(index, 1);
		}

		this.getArtworks();
	}

	SetArtworkPrice() {
		this.isFilterDropdownOpen4 = false;
		this.minPrice = Number(this.priceFrom) || undefined;
		this.maxPrice = Number(this.priceTo) || undefined;
		this.getArtworks();
	}
	SetArtworkDimension() {
		this.isFilterDropdownOpen5 = false;
		this.minHeight = Number(this.heightFrom) * 2.54 || undefined;
		this.maxHeight = Number(this.heightTo) * 2.54 || undefined;
		this.minWidth = Number(this.widthFrom) * 2.54 || undefined;
		this.maxWidth = Number(this.widthTo) * 2.54 || undefined;
		this.getArtworks();
	}
	onGotoPage() {
		this.offset = Number(this.gotoPage);
		if (!this.offset) {
			this.offset = 1;
		}
		this.getArtworks();
	}

	clearFilters() {
		this.priceFrom = null;
		this.priceTo = null;
		this.minPrice = null;
		this.maxPrice = null;

		this.heightFrom = null;
		this.heightTo = null;
		this.minHeight = null;
		this.maxHeight = null;
		this.widthFrom = null;
		this.widthTo = null;
		this.minWidth = null;
		this.maxWidth = null;

		this.work_type = [];
		this.multiple_sale_options = [];
		this.inputCheckBox = Array(20).fill(false);

		this.getArtworks();
	}
	// headerWidth(){
	//   this.headerfieldSize=this.previewLinkObj?.title.length + 1;
	// }
	onClick(event) {
		this.dropDowns.forEach((a, i) => {
			if (this.CheckboxDropdown[i] && !a.nativeElement.contains(event.target)) {
				if (this.dropDownFirstClick[6]) {
					this.CheckboxDropdown[i] = false;
					this.dropDownFirstClick[6] = false;
				} else {
					this.dropDownFirstClick[6] = true;
				}
			}
		});
		if (
			this.isFilterDropdownOpen2 &&
			!this.dropDowns2.nativeElement.contains(event.target)
		) {
			if (this.dropDownFirstClick[1]) {
				this.isFilterDropdownOpen2 = false;
				this.dropDownFirstClick[1] = false;
			} else {
				this.dropDownFirstClick[1] = true;
			}
		}
		if (
			this.isFilterDropdownOpen3 &&
			!this.dropDowns3.nativeElement.contains(event.target)
		) {
			if (this.dropDownFirstClick[2]) {
				this.isFilterDropdownOpen3 = false;
				this.dropDownFirstClick[2] = false;
			} else {
				this.dropDownFirstClick[2] = true;
			}
		}
		if (
			this.isFilterDropdownOpen4 &&
			!this.dropDowns3.nativeElement.contains(event.target)
		) {
			if (this.dropDownFirstClick[3]) {
				this.isFilterDropdownOpen4 = false;
				this.dropDownFirstClick[3] = false;
			} else {
				this.dropDownFirstClick[3] = true;
			}
		}
		console.log(this.dropDowns4.nativeElement.contains(event.target));

		if (
			this.isFilterDropdownOpen5 &&
			!this.dropDowns4.nativeElement.contains(event.target)
		) {
			if (this.dropDownFirstClick[4]) {
				this.isFilterDropdownOpen5 = false;
				this.dropDownFirstClick[4] = false;
			} else {
				this.dropDownFirstClick[4] = true;
			}
		}
		if (
			this.isFilterDropdownOpen1 &&
			!this.dropDowns5.nativeElement.contains(event.target)
		) {
			if (this.dropDownFirstClick[5]) {
				this.isFilterDropdownOpen1 = false;
				this.dropDownFirstClick[5] = false;
			} else {
				this.dropDownFirstClick[5] = true;
			}
		}
	}
	copyshortUrl(item) {
		if (item?.shortUrl) {
			this.copy("https://previews.terrain.art/" + item?.shortUrl);
		} else {
			this.copy(item?.privateViewLink);
		}
	}
	getSaleInformation(art): string {
		switch (art.sale_options) {
			case "NotForSale":
				return `${art?.extras?.notForSaleReason} - ${art?.extras?.notForSaleDate}`;
			case "Sold":
				return `${art?.extras?.soldTo} - ${art?.extras?.soldDate} - ${art?.extras?.soldBy}`;
			case "Reserved":
				return `${art?.extras?.reservedFor} - ${art?.extras?.reservedDate} - ${art?.extras?.reservedBy}`;
			case "ReturnedToArtist":
				return `${art?.extras?.returnedToArtistReason} - ${art?.extras?.returnedDate}`;
			case "OnLoan":
				return `${art?.extras?.loanDetails} - From:${art?.extras?.startDate} - To:${art?.extras?.endDate}`;
			default:
				break;
		}
		return "";
	}

	getArtworks2() {
		this.filteredArtworksArr2 = JSON.parse(
			JSON.stringify(this.previewLinkObj.artworkIds),
		);
		if (this.sort2 === "Title↑") {
			this.filteredArtworksArr2 = this.filteredArtworksArr2.sort((a, b) =>
				a.artwork_title.localeCompare(b.artwork_title),
			);
		} else if (this.sort2 === "Title↓") {
			this.filteredArtworksArr2 = this.filteredArtworksArr2.sort((a, b) =>
				b.artwork_title.localeCompare(a.artwork_title),
			);
		} else if (this.sort2 === "Artist↑") {
			this.filteredArtworksArr2 = this.filteredArtworksArr2.sort((a, b) =>
				a.artist_id.display_name.localeCompare(b.artist_id.display_name),
			);
		} else if (this.sort2 === "Artist↓") {
			this.filteredArtworksArr2 = this.filteredArtworksArr2.sort((a, b) =>
				b.artist_id.display_name.localeCompare(a.artist_id.display_name),
			);
		}
		if (this.searchKey2) {
			this.filteredArtworksArr2 = this.filteredArtworksArr2.filter((art) => {
				return (
					art.artwork_title
						.toLowerCase()
						.includes(this.searchKey2.toLowerCase()) ||
					art.artist_id.display_name
						.toLowerCase()
						.includes(this.searchKey2.toLowerCase())
				);
			});
		}
		// if (this.multiple_sale_options.length > 0) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return this.multiple_sale_options.includes(art.sale_options);
		// 	});
		// }

		// if (this.work_type.length > 0) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return this.work_type.includes(art.work_type);
		// 	});
		// }

		// if (this.minPrice) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.price >= this.minPrice;
		// 	});
		// }

		// if (this.maxPrice) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.price <= this.maxPrice;
		// 	});
		// }

		// if (this.minHeight) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.height >= this.minHeight;
		// 	});
		// }

		// if (this.maxHeight) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.height <= this.maxHeight;
		// 	});
		// }

		// if (this.minWidth) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.width >= this.minWidth;
		// 	});
		// }

		// if (this.maxWidth) {
		// 	this.filteredArtworksArr = this.filteredArtworksArr.filter((art) => {
		// 		return art.width <= this.maxWidth;
		// 	});
		// }

		this.totalRecords2 = this.filteredArtworksArr2.length;
		this.totalPage2 = Math.ceil(this.filteredArtworksArr2.length / 8);
		console.log(this.filteredArtworksArr2);

		this.filteredArtworksArr = this.filteredArtworksArr2.slice(
			this.offset2 * 8,
			(this.offset2 + 1) * 8,
		);
		console.log(this.filteredArtworksArr);
		this.gotoPage2 = this.offset2 + 1;
	}

	searchResult2(evt) {
		var charCode = evt.which ? evt.which : evt.keyCode;
		let string = evt.target.value;
		if (charCode == 13 || charCode == 8 || charCode == 16) {
			return;
		} else {
			console.log("searchResult2", string.trim());

			this.search2.next(string.trim());
		}
	}

	// for artwork select
	searchByKey2() {
		if (navigator.onLine) {
			this.search2.pipe(debounceTime(1500)).subscribe((val) => {
				this.searchKey2 = val;
				this.offset2 = 0;
				this.totalPage2 = 0;
				console.log("searchResult2", this.searchKey2);
				this.getArtworks2();
			});
		}
	}

	onGotoPage2() {
		this.offset2 = Number(this.gotoPage2) - 1;
		if (!this.offset2) {
			this.offset2 = 0;
		}
		this.getArtworks2();
	}
	managePagination2(action) {
		if (action === "prev") {
			if (this.offset2 > 0) {
				this.offset2 = this.offset2 - 1;
			} else {
				return;
			}
		} else if (action === "next") {
			if (this.offset2 < this.totalPage2 - 1) {
				this.offset2 = this.offset2 + 1;
			} else {
				return;
			}
		}
		this.getArtworks2();
	}

	getSelectedArtCount() {
		return this.previewLinkObj.artworkIds.filter((a) => a.selected).length;
	}

	selectAllArtworks() {
		const indices = this.filteredArtworksArr2.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			true,
		);

		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}
	selectAllArtOnPage() {
		const indices = this.filteredArtworksArr.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			true,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}

	unSelectAllArtworks() {
		const indices = this.filteredArtworksArr2.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			false,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}
	unSelectAllArtOnPage() {
		const indices = this.filteredArtworksArr.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			false,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}

	// Modified move method with undo/redo support
	move(dir: number, index: number) {
		this.CheckboxDropdown[index] = false;
		const selectedItems = this.previewLinkObj?.artworkIds.filter(
			(a) => a.selected,
		);

		let indices: number[] = [];
		let operation: "up" | "down" | "top" | "bottom";

		if (selectedItems.length >= 2) {
			// Get indices of all selected items
			indices = selectedItems.map((item) =>
				this.previewLinkObj.artworkIds.findIndex((a) => a._id === item._id),
			);

			switch (dir) {
				case 1: // Move up
					operation = "up";
					break;
				case 2: // Move down
					operation = "down";
					break;
				case 3: // Move to top
					operation = "top";
					break;
				case 4: // Move to bottom
					operation = "bottom";
					break;
			}
		} else {
			// Single item move
			indices = [index];
			switch (dir) {
				case 1:
					operation = "up";
					break;
				case 2:
					operation = "down";
					break;
				case 3:
					operation = "top";
					break;
				case 4:
					operation = "bottom";
					break;
			}
		}

		const moveCommand = new MoveItemsCommand(
			this.previewLinkObj.artworkIds,
			operation,
			indices,
		);
		this.commandManager.execute(moveCommand);
		this.updateUndoRedoState();
	}

	// Modified select/unselect methods
	SelectAll() {
		const indices = this.previewLinkObj.artworkIds.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			true,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}

	unselectAll() {
		const indices = this.previewLinkObj.artworkIds.map((_, index) => index);
		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			false,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}

	// New undo/redo methods
	undo() {
		this.commandManager.undo();
		this.updateUndoRedoState();
	}

	redo() {
		this.commandManager.redo();
		this.updateUndoRedoState();
	}

	private updateUndoRedoState() {
		this.canUndo = this.commandManager.canUndo();
		this.canRedo = this.commandManager.canRedo();
	}

	selectAllArtistWorks(index: number) {
		const artist = this.previewLinkObj.artworkIds[index].artist_id.display_name;
		const indices = this.previewLinkObj.artworkIds
			.map((ele, i) => (ele.artist_id.display_name === artist ? i : -1))
			.filter((i) => i !== -1);

		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			true,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}
	selectAllOnLine(index: number) {
		const startIndex = Math.floor(index / 3) * 3;
		const indices = [startIndex, startIndex + 1, startIndex + 2].filter(
			(i) => i < this.previewLinkObj.artworkIds.length,
		);

		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			true,
		);
		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}
	unSelectAllArtistWorks(index: number) {
		const artist = this.previewLinkObj.artworkIds[index].artist_id.display_name;
		const indices = this.previewLinkObj.artworkIds
			.map((ele, i) => (ele.artist_id.display_name === artist ? i : -1))
			.filter((i) => i !== -1);

		const unselectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			false, // false for unselect
		);
		this.commandManager.execute(unselectCommand);
		this.updateUndoRedoState();
	}
	unSelectAllOnLine(index: number) {
		const startIndex = Math.floor(index / 3) * 3;
		const indices = [startIndex, startIndex + 1, startIndex + 2].filter(
			(i) => i < this.previewLinkObj.artworkIds.length,
		);

		const unselectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			indices,
			false, // false for unselect
		);
		this.commandManager.execute(unselectCommand);
		this.updateUndoRedoState();
	}
	onCheckboxChange(item: ArtworkState, event: any) {
		const index = this.previewLinkObj.artworkIds.findIndex(
			(a) => a._id === item._id,
		);

		const selectCommand = new SelectCommand(
			this.previewLinkObj.artworkIds,
			[index],
			event,
		);

		this.commandManager.execute(selectCommand);
		this.updateUndoRedoState();
	}
}
