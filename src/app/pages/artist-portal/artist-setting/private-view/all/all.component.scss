.Main-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  .Content-Wraped {
    padding: 0 3.541vw 0 3.541vw;
    padding-top: 5.333vw;
    width: 70%;
    max-height: 85vh;
    overflow-y: scroll;
    .Head-Section {
      .Main-Head {
        display: flex;
        justify-content: space-between;
        input[type="text"] {
          font-size: 1.805vw;
          letter-spacing: 2.5px;
          //text-transform: uppercase;
          letter-spacing: 2px;
          font-weight: 300;
          margin-bottom: 1.3888vw;
          padding-bottom: 0.833vw;
        }
      }
    }
    .Artwork-Section {
      .Artwork-Item {
        margin-left: 1.5%;
        // margin-right: 1.5%;
        padding-top: 1%;
        padding-bottom: 1%;
        margin-top: 0;
        margin-bottom: 0;
        position: relative;
        display: inline-block;
        // *display: inline;
        zoom: 1;
        // width: 28.3333333333%;
        // margin: 10px 0;
        vertical-align: top;
        word-spacing: normal;
        // font-size: 14px;
        line-height: 1.4;
        .Image-Space {
          display: inline-block;
          height: 100%;
          vertical-align: middle;
          img {
            margin: 0 auto;
            vertical-align: bottom;
            display: inline-block;
            max-width: 100%;
            max-height: 100%;
          }
        }
        .Artwork-Info {
          margin-top: 1.11vw;
          padding-right: 1.388vw;
          line-height: 1.4;
          p {
            margin-bottom: unset;
          }

          .Checkbox-div {
            position: relative;

            .Checkbox-Dropdown {
              position: absolute;
              max-width: 12vw;
              max-height: 10vw;
              top: 2vw;
              right: 3.888vw;
              overflow-x: hidden;
              overflow-y: scroll;
              scroll-behavior: smooth;
              z-index: 1001;
              box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px,
                rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
              ul {
                background-color: white;
                list-style: none;
                padding: 0;
                li {
                  width: 100%;
                  font-size: 1.042vw;
                  padding: 0.5vw;
                  border-bottom: 0.06944vw solid var(--timeline-color);
                }
                li:hover {
                  background-color: rgba(128, 128, 128, 0.493);
                }
              }
            }
          }
        }
      }
    }
  }
  .Side-Options {
    width: 30%;
    .Side-Content-Wrap {
      max-height: 82vh;
      overflow-y: scroll;
      padding-bottom: 4.583vw;
      padding: 0px 1.388vw 1.388vw;
      .Main-heading {
        display: block;
        font-size: 1.388vw;
        font-weight: 300;
        text-transform: none;
        letter-spacing: 0.0208vw;
        line-height: 1.488vw;
        text-align: left;
        margin-top: 1vw;
      }
      .PV-Details {
        .PV-name {
          margin-top: 2vw;
          font-size: 1.25vw;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-bottom: 0.2vw;
        }
        .PV-link {
          color: #848484;
          margin-bottom: 0.2vw;
        }
      }
      .Drawer-Wrap {
        .Drawer-Section-Name {
          border-top: 0.06944vw solid #ddd;
          padding: 0.7vw 0 0.7vw 0;
          display: flex;
          justify-content: space-between;
          span {
            font-weight: 200;
            letter-spacing: 0.1041vw;
            letter-spacing: 0.1388vw;
            font-size: 0.868vw;
          }
        }
        .Drawer-Section-Content {
          padding-top: 1vw;
          padding-bottom: 2.083vw;
        }
      }
    }
  }
}
.NoBorder {
  border: none;
  background-color: unset;
}

.rte-plain {
  padding: 0 1px;
  outline: none;
  cursor: text;
  background: transparent;

  resize: vertical;
  display: inline-block;
  /* display: inline-block; */
  font-size: 1.042vw;
}
[contenteditable] {
  -webkit-user-select: text;
  user-select: text;
}
.f-w-full {
  width: 100%;
  width: 100%;
  width: 10.833vw;
  outline: none;
  padding: 0px 0.027555px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 1.388vw !important;
  -webkit-overflow-scrolling: touch;
  -webkit-font-smoothing: subpixel-antialiased;
}
[contenteditable="true"]:empty:not(:focus):before {
  content: attr(data-placeholder);
  /* color: #aaa; */
  font-weight: normal;
  cursor: text;
}
[data-placeholder]:empty:not(:focus):before {
  content: attr(data-placeholder);
  display: block;
  opacity: 0.5;
}
dropdown-menu-arrow {
  top: -25px;
  left: 50%;
  width: 0;
  height: 0;
  position: relative;
}
.dropdown-menu-arrow:before,
.dropdown-menu-arrow:after {
  content: "";
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-width: 7px 8px;
  border-style: solid;
  border-color: transparent;
  z-index: 1001;
}
.dropdown-menu-arrow:after {
  bottom: -18px;
  right: -8px;
  border-bottom-color: #fff;
}
.dropdown-menu-arrow:before {
  bottom: -17px;
  right: -8px;
  border-bottom-color: rgba(0, 0, 0, 0.15);
}
.field-value {
  margin-top: 2.08vw;
  margin-top: 0.5vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;
  &.doted {
    border: 0.069vw dotted var(--timeline-color);
    padding-left: 1vw;
    padding-right: 1vw;
    padding-bottom: 0.5vw;
  }
  input[type="text"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type="date"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type="password"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  .input-container {
    width: 100%;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }
    button {
      outline: none;
      background: none;
      border: none;
    }
    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 2.08vw;
      top: 0;
      bottom: 0;
      margin: auto;
      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }
    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }
    input[type="text"] {
      // background: transparent;
      font-family: var(--secondary-font);
      border: 0.069vw solid var(--timeline-color);
      padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      border-radius: 0.14vw;
      height: 3.47vw;
      width: 100%;
      z-index: 0;
      @media (max-width: 768px) {
        height: 11.35vw;
        font-size: 3.86vw;
        padding: 1.04vw 2.11vw;
      }
      &.selection {
        padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }
    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;
      @media (max-width: 768px) {
        top: 10.47vw;
      }
      ul {
        list-style: none;
        padding: 0.69vw 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;
        li {
          padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;
          @media (max-width: 768px) {
            padding: 1.25vw 1.04vw;
          }
          .country-name {
            margin-left: 0.69vw;
            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }
        li:hover {
          background-color: var(--timeline-color);
        }
      }
      ul::-webkit-scrollbar {
        display: none;
      }
    }
    .dropdown-hidden {
      display: none;
    }
  }
  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }
  .placeholder {
    position: absolute;
    top: -0.4vw;
    left: 1.04vw;
    font-size: 0.8333vw;
    color: var(--quaternary-font-color);
    padding: 0 0.3vw;
    background-color: var(--primary-background-color);
    // background-color: #ededf1;
    @media (max-width: 768px) {
      top: -1.8vw;
      left: 2.04vw;
      font-size: 3.38vw;
    }
  }
  .send {
    margin-left: 2.08vw;
    color: var(--tertiary-font-color);
  }
}
.button-type {
  display: inline-block;
  text-align: center;
  min-width: 4.444vw;
  background: transparent;
  font-size: 0.833vw;
  /* font-weight: 600; */
  text-transform: uppercase;
  letter-spacing: 0.0694vw;
  color: #555;
  padding: 0.486vw 0.833vw;
  border: 0.0694vw solid #c8c8c8;
  border-radius: 1.527vw;
  margin-top: 0.2083vw;
  margin-bottom: 0.2083vw;
  &:disabled{
    background-color: #c8c8c8;
    color: #fff;
    cursor: not-allowed;
  }
}
.simpe-type {
  display: inline-block;
  text-align: center;
  min-width: 4.444vw;
  background: transparent;
  font-size: 0.833vw;
  /* font-weight: 600; */
  text-transform: uppercase;
  letter-spacing: 0.0694vw;
  letter-spacing: 1px;
  color: #888;
  border: none;
  padding: 0.486vw 0.833vw;
  margin-top: 0.2083vw;
  margin-bottom: 0.2083vw;
}

.switch {
  position: relative;
  display: inline-block;
  width: 2.6vw;
  height: 1.11vw;
  margin-bottom: 0 !important;
  @media (max-width: 768px) {
    width: 10.6vw;
    height: 4.11vw;
  }
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 1vw;
  width: 1vw;
  left: 0.06vw;
  bottom: 0.06vw;
  background-color: white;
  transition: 0.4s;
  @media (max-width: 768px) {
    height: 3.58vw;
    width: 3.58vw;
    left: 0.4vw;
    bottom: 0.2vw;
  }
}

input:checked + .slider {
  background-color: var(--tertiary-font-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked + .slider:before {
  transform: translateX(1.4vw);
  @media (max-width: 768px) {
    transform: translateX(6.2vw);
  }
}

/* Rounded sliders */
.slider.round {
  border-radius: 3vw;
}

.slider.round:before {
  border-radius: 50%;
}
.text-before {
  margin-right: 0.5vw;
  @media (max-width: 768px) {
    font-size: 4.34vw;
    margin-right: 2.5vw;
  }
}
.input-with-text {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
}
.input-info {
  font-size: 0.95vw;
  margin-top: 0.5vw;
  @media (max-width: 768px) {
    font-size: 3.38vw;
    margin-top: 0.76vw;
    margin-bottom: 4.62vw;
  }
}
.SideHeads {
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.06944vw;
  font-size: 0.833vw;
  color: #555;
  // margin-bottom: 14px;
  // margin-top: 33px;
  margin-bottom: 1.527vw;
  margin-top: 2.777vw;
}
.UnderSideHead {
  display: inline-block;
  font-weight: normal;
  font-style: italic;
  color: #888;
  font-size: 90%;
  margin-top: 0.4em;
}
.checkboxCls {
  margin-right: 0.5vw;
}
.nameVal {
  min-width: 6vw;
}
.d-flex {
  display: flex;
}
.Footer-Buttons {
  position: absolute;
  bottom: 0;
  z-index: 100;
}
.footer-text {
  color: #dadada;
  text-transform: uppercase;
  letter-spacing: 0.173vw;
  font-size: 0.763vw;
}
.Content-Footer {
  display: flex;
  justify-content: space-between;
  .fft {
    display: flex;
    gap: 3vw;
  }
}

textarea {
  width: 100%;
  border: none;
  margin-left: 1vw;
}
.dark {
  color: #fff;
  background-color: #000;
}
.Artwork-Item {
  .Image-Space {
    position: relative;
    height: 0;

    width: 100%;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
.artworkModal {
  // display: block; /* Hidden by default */
  position: fixed;
  /* Stay in place */
  z-index: 1;
  /* Sit on top */
  padding-top: 8vw;
  /* Location of the box */
  left: 0;
  top: 0;
  width: 100%;
  /* Full width */
  height: 100%;
  /* Full height */
  //   overflow:; /* Enable scroll if needed */
  background-color: rgb(0, 0, 0);
  /* Fallback color */
  background-color: rgba(0, 0, 0, 0.4);
  /* Black w/ opacity */
}

.artwork-modal-content {
  background-color: #fefefe;
  margin: auto;
  height: 70vh;
  width: 60vw;
  border-radius: 0.14vw;
  .Addselection_container {
    height: 100%;
    display: flex;
    margin: 1vw 0.7vw;
    flex-direction: column;
    .Addtitle_bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 1vw 0;
      .Addclose_icon {
        font-size: 2vw;
        margin: 0;
        cursor: pointer;
        float: right;
      }
    }
    .Addselection_headings {
      background-color: var(--secondary-background-color);
      border-top-left-radius: 0.14vw;
      border-top-right-radius: 0.14vw;
      display: flex;
      flex-direction: row;
      .Addproperty_title {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1 2 auto;
        font-family: var(--secondary-font);
        font-weight: 500;
        font-size: 1.042vw;
        padding: 1vw 0;
        input[type="checkbox"] {
          width: 1.2vw;
          height: 1.2vw;
        }
      }
    }
    .Addfooter_bar {
      .Addselect {
        margin: 1vw 0;
        outline: none;
        font-size: 1.042vw;
        // width: 20.56vw;
        background-color: transparent;
        color: var(--tertiary-font-color);
        padding: 0.5vw 1vw;
        border: 0.05vw solid var(--tertiary-font-color);
        border-radius: 0.46vw;
      }
    }
    .Addsearch_bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;
      margin-bottom: 1vw;
      .filter_container {
        display: flex;
        // justify-content: flex-end;
      }
    }
    .Addselection_data {
      // background-color: #bbba;
      flex-grow: 3;
      border-bottom-left-radius: 0.14vw;
      border-bottom-right-radius: 0.14vw;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      // background-color: #888;
      .Addartwork_details {
        height: 3.5vw;
        border-bottom: 1px solid var(--primary-border-color);
        display: flex;
        flex-direction: row;
        font-size: 1.042vw;
        .Addproperties {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          input[type="checkbox"] {
            width: 1.2vw;
            height: 1.2vw;
          }
          img {
            width: 2vw;
            height: 2vw;
            object-fit: cover;
            border-radius: 0.14vw;
            cursor: pointer;
          }
        }
      }
    }
    .Addbox {
      width: 12.5%;
    }
    .Addbox2 {
      width: 25%;
    }
  }
  .fieldd-value {
    position: relative;
    display: flex;
    justify-content: start;
    align-items: center;
    input[type="text"] {
      background: transparent;
      font-family: var(--secondary-font);
      border: none;
      border: 0.069vw solid var(--timeline-color);
      border-radius: 0.14vw;
      padding: 1.04vw 1.11vw;
      width: 70%;
      height: 2.47vw;
    }
    .input-container {
      // width: 70%;
      position: relative;
      display: inline-flex;
      justify-content: start;
      align-items: center;
      .flag-icon {
        position: absolute;
        left: 1.04vw;
      }
      button {
        outline: none;
        background: none;
        border: none;
      }
      .flag-arrow {
        position: absolute;
        max-width: 0.69vw;
        height: auto;
        right: 2.08vw;
      }
      .division {
        position: absolute;
        width: 0.069vw;
        height: 1.04vw;
        background-color: var(--timeline-color);
        left: 3.33vw;
      }
      input[type="text"] {
        // background: transparent;
        font-family: var(--secondary-font);
        border: 0.069vw solid var(--timeline-color);
        padding: 1.04vw 1.11vw 1.04vw 4.2vw;
        border-radius: 0.14vw;
        // height: 3.47vw;
        width: 100%;
        // padding: 1.04vw 1.11vw;
        // width: 70%;
        height: 2.47vw;
        z-index: 0;
        &.selection {
          padding: 1.04vw 1.11vw 1.04vw 1.04vw;
        }
      }
      .dropdown-visible {
        background-color: var(--primary-background-color);
        visibility: visible;
        position: absolute;
        top: 3.47vw;
        z-index: 1;
        box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
        width: 92%;
        @media (max-width: 768px) {
          top: 10.47vw;
        }
        ul {
          list-style: none;
          padding: 0.69vw 0;
          max-height: 27.97vw;
          margin: 0;
          max-width: 100%;
          overflow: hidden;
          overflow-y: scroll;
          @media (max-width: 768px) {
            padding: 1.69vw 0;
          }
          li {
            padding: 0.69vw 1.04vw;
            width: 100%;
            display: flex;
            @media (max-width: 768px) {
              padding: 1.69vw 1.04vw;
            }
            .country-name {
              margin-left: 0.69vw;
              @media (max-width: 768px) {
                font-size: 3.38vw;
              }
            }
          }
          li:hover {
            background-color: var(--timeline-color);
          }
        }
        ul::-webkit-scrollbar {
          display: none;
        }
      }
      .dropdown-hidden {
        display: none;
      }
    }
    .ph-flag {
      height: 1.25vw;
      // padding-right: 0.62vw;
      // border-right: solid 0.09vw var(--quaternary-font-color);
    }
    .placeholder {
      position: absolute;
      top: -0.4vw;
      left: 1.04vw;
      font-size: 0.8333vw;
      color: var(--quaternary-font-color);
      padding: 0 0.3vw;
      background-color: var(--primary-background-color);
      // background-color: #ededf1;
    }
    .send {
      margin-left: 2.08vw;
      color: var(--tertiary-font-color);
    }
  }
}
.banner {
  background-color: #000;
  height: 30vw;
  width: 100%;
  color: #fff;
  display: flex;
  justify-content: center;
  justify-items: center;
  align-items: center;
  font-size: 2vw;
  .NoBorder {
    color: #fff !important;
  }
}
.circle {
  display: inline-block;
  // border: 1px solid; /* Adjust as needed */
  border-radius: 50%; /* This makes the border circular */
  width: 1.2vw;
  height: 1.2vw;
  text-align: center;
  font-family: Arial, sans-serif;
  font-size: 0.85vw;
  color: white;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
}
.filterInput {
  background: transparent;
  font-family: var(--secondary-font);
  border: none;
  border: 0.069vw solid var(--timeline-color) !important;
  border-radius: 0.5vw !important;
  padding: 0.4vw 0.4vw !important;
  font-size: 1vw;
  width: 100% !important;
  height: 2.47vw !important;

  @media (max-width: 768px) {
    height: 11.35vw;
    font-size: 3.86vw;
    padding: 1.04vw 2.11vw;
  }
}
.action_Btn {
  background-color: black;
  color: white;
  border: 0.06944vw solid var(--timeline-color);
  padding: 0.5vw 1vw;
  border-radius: 2vw;
  display: inline-flex;
  // font-weight: 600;
  // gap: .5vw;
  width: max-content;
  justify-content: center;
  align-items: center;
}
.saveBtn {
  // display: block;
  //width: 4vw;
  outline: none;
  font-size: 1.25vw;
  // width: 20.56vw;
  margin-left: 2vw;
  background-color: transparent;
  color: var(--tertiary-font-color);
  padding: 0.333333vw 0.89vw;
  border: 0.069vw solid var(--tertiary-font-color);
  border-radius: 1.46vw;
  @media (max-width: 768px) {
    font-size: 3.86vw;
    margin-bottom: 4.83vw;
    border-radius: 10.1vw;
    padding: 3.45vw 6.89vw;
  }
}
