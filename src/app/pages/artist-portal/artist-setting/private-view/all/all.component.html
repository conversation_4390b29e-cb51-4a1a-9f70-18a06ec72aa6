<div class="Main-container" [ngClass]="{ dark: contentObj.darkTheme }">
  <div class="Content-Wraped">
    <div *ngIf="contentObj.showBanner" class="banner">
      <input
        class="NoBorder"
        type="text"
        [value]="previewLinkObj?.title"
        name="Celia ABBOT"
        id="head"
        style="margin-left: 1vw"
      />
    </div>
    <div class="Head-Section">
      <div class="Main-Head">
        <input
          *ngIf="!contentObj.showBanner"
          class="NoBorder"
          [style.width.ch]="previewLinkObj?.title.length + 1"
          type="text"
          [(ngModel)]="previewLinkObj.title"
          onkeypress="this.style.width = (this.value.length + 3) + 'ch';"
          name="Celia ABBOT"
          id="head"
          style="margin-left: 1vw"
        />
        <span
          *ngIf="contentObj.showDownload"
          style="
            cursor: pointer;
            font-weight: 300;
            color: #555;
            font-size: 0.833vw;
          "
          >DOWNLOAD PDF</span
        >
      </div>
      <!-- <div
      style="margin-left: 1vw;"
        id="app-content-above"
        [(ngModel)]="previewLinkObj.aboveDescription"
        class="f ce-editable rte-plain f-rte f-w-full ce-editable-inited body-font mb20"
        data-placeholder="Enter text..."

        data-rte_settings_preset="allow_links"
        contenteditable="true"
        spellcheck="true"
      ></div> -->
      <textarea
        rows="4"
        [(ngModel)]="previewLinkObj.aboveDescription"
        placeholder="Enter text..."
      ></textarea>
    </div>
    <div class="Artwork-Section">
      <div
        class="Artwork-Item"
        [ngStyle]="{
          width: 100 / contentObj.grid - 10 / contentObj.grid + '%'
        }"
        *ngFor="let item of previewLinkObj?.artworkIds; let i = index"
      >
        <div
          class="Image-Space"
          [ngStyle]="{
            paddingBottom:
              (100 * contentObj.grid) / contentObj.grid -
              10 / contentObj.grid +
              '%'
          }"
        >
          <img
            [src]="
              item?.primary_image[0]?.url ||
              'https://www.terrain.art/cdn-cgi/image/width=250,quality=52/https://ta-python.s3.us-east-2.amazonaws.com/1692331552330_Image%20Not%20Available%20error.png'
            "
          />
        </div>
        <div class="Artwork-Info">
          <p class="Artist">
            <i>{{ item?.artwork_title }}</i
            >, {{ item?.year }}
          </p>
          <p>{{ item?.artist_id?.display_name }}</p>
          <p *ngIf="contentObj.showMedium" class="medium">{{ item?.medium }}</p>
          <p *ngIf="contentObj.showDimensions" class="dimensions">
            {{ item?.dimensions }}
          </p>
          <p *ngIf="contentObj.showSignature" class="signed_and_dated">
            {{ item?.signature_details }}
          </p>

          <p *ngIf="contentObj.showCatalogId" class="stock_number">
            {{ item?.catalog_number }}
          </p>
          <ng-container *ngIf="contentObj.showAvailability">
            <fa-icon
              *ngIf="
                item?.sale_options != 'ReturnedToArtist' &&
                item?.sale_options != 'OnLoan'
              "
              [style.color]="
                item?.sale_options == 'ForSale'
                  ? '#00FF00'
                  : item?.sale_options == 'Reserved'
                  ? '#FFFF00'
                  : item?.sale_options == 'OnReserve'
                  ? '#FFFF00'
                  : item?.sale_options == 'Sold'
                  ? '#FF0000'
                  : '#101010'
              "
              [icon]="faCircle"
            ></fa-icon>
            <span
              *ngIf="item?.sale_options == 'ReturnedToArtist'"
              class="circle"
              style="background-color: rgb(236, 112, 10)"
              >R</span
            >
            <span
              *ngIf="item?.sale_options == 'OnLoan'"
              class="circle"
              style="background-color: rgb(236, 228, 10)"
              >L</span
            >
          </ng-container>

          <p *ngIf="contentObj.showSalePrice">
            ₹
            {{ item?.sale_price | number : "1.0" }}
            | $
            {{ item?.sale_price | forex : "USD" | async | number : "1.0" }}
          </p>
          <p
            *ngIf="
              contentObj.showLocation && item?.extras?.isGallerWearhouse != 2
            "
            class="location"
          >
            {{ item?.location }}
          </p>

          <p
            *ngIf="
              contentObj.showLocation &&
              item?.extras?.storageAddress &&
              item?.extras?.isGallerWearhouse == 1
            "
            class="location"
          >
            {{ item?.extras?.storageAddress }}
          </p>
          <p
            *ngIf="
              contentObj.showLocation &&
              item?.extras?.artistAddress?.title &&
              item?.extras?.isGallerWearhouse == 2
            "
            class="location"
          >
            {{ item?.extras?.artistAddress?.title }} -
            {{ item?.extras?.artistAddress?.city }}
          </p>
          <p *ngIf="contentObj.showCopyrightLine" class="copyright">
            {{ item?.credit_line }}
          </p>
          <p *ngIf="contentObj.showSaleInfo" class="copyright">
            {{ item?.sale_information }}
          </p>
          <p *ngIf="contentObj.showSaleInfo" class="copyright">
            {{ item?.sale_information }}
          </p>
          <p
            [innerHTML]="getSaleInformation(item)"
            *ngIf="contentObj.showSaleInfo"
            class="copyright"
          ></p>
          <p *ngIf="contentObj.showEditionDetails" class="copyright">
            {{ item?.edition_details }}
          </p>
          <p *ngIf="contentObj?.showCondition" class="copyright">
            Condition: {{ item?.current_state }}
          </p>
          <p
            *ngIf="contentObj?.showCondition && item?.extras?.packaging"
            class="copyright"
          >
            Packaging: {{ item?.extras?.packaging }}
          </p>
          <p
            *ngIf="contentObj?.showCondition && item?.extras?.installState"
            class="copyright"
          >
            Install State: {{ item?.extras?.installState }}
          </p>
          <div class="Checkbox-div">
            <input
              type="checkbox"
              style="margin-right: 0.5vw"
              name="check"
              [(ngModel)]="item.selected"
               (ngModelChange)="onCheckboxChange(item, $event)"
              [ngModelOptions]="{ standalone: true }"
            />
            <fa-icon
              style="color: #747474"
              [icon]="faAngleDown"
              (click)="CheckboxDropdown[i] = !CheckboxDropdown[i]"
            >
            </fa-icon>

            <div
              class="Checkbox-Dropdown"
              [hidden]="!CheckboxDropdown[i]"
              #dropDowns
            >
              <span class="dropdown-menu-arrow"></span>
              <ul>
                <li (click)="move(1, i)">Move item up</li>
                <li (click)="move(2, i)">Move item down</li>
                <li (click)="move(3, i)">Move item first</li>
                <li (click)="move(4, i)">Move item last</li>
                <li (click)="onRemoveSelected()">Remove selected items</li>
                <li (click)="SelectAll()">Select All</li>
                <li (click)="unselectAll()">Unselect All</li>
                <li (click)="selectAllOnLine(i)">Select Line</li>
                <li (click)="unSelectAllOnLine(i)">Unselect Line</li>
                <li (click)="selectAllArtistWorks(i)">Select Artist</li>
                <li (click)="unSelectAllArtistWorks(i)">Unselect Artist</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div style="margin:3vw 0 5vw 1vw"
    id="app-content-above"
    class="f ce-editable rte-plain f-rte f-w-full ce-editable-inited body-font mb20"
    data-placeholder="Enter text..."
    [(ngModel)]="previewLinkObj.BelowDescription"
    data-rte_settings_preset="allow_links"
    contenteditable="true"
    spellcheck="true"
  ></div> -->
    <textarea
      rows="4"
      [(ngModel)]="previewLinkObj.BelowDescription"
      placeholder="Enter text..."
    ></textarea>

    <hr />
    <div class="Content-Footer">
      <span class="fft">
        <span class="footer-text"
          >All images Courtesy and Copyright of the Artist.</span
        >
      </span>
      <span class="footer-text">
        Gallery Preview by Repra Art Private Limited © 2023</span
      >
    </div>
  </div>
  <div class="Side-Options">
    <div class="Side-Content-Wrap">
      <span class="Main-heading"> Gallery Preview settings </span>
      <div class="PV-Details">
        <p class="PV-name">{{ previewLinkObj?.title }}</p>
        <p class="PV-link">{{ previewLinkObj?.privateViewLink }}</p>
        <a
          style="margin-right: 2vw; font-size: 0.8944vw"
          href="javascript:;"
          (click)="back()"
          >Visit Gallery Previews</a
        ><span
          style="
            color: var(--tertiary-font-color);
            font-size: 0.8944vw;
            cursor: pointer;
          "
          (click)="copyshortUrl(previewLinkObj)"
          >Copy Short Url</span
        >
        <div class="field-value" style="margin-top: 1vw; padding-right: 3vw">
          <input
            type="text"
            placeholder="Provide label"
            style="height: 2.47vw"
          />
          <div class="placeholder">Label</div>
          <div class="input-info">(internal only)</div>
        </div>
        <button
          (click)="openArtworks()"
          class="button-type"
          style="margin: 1vw 0 1vw 0"
        >
          ADD ITEMS
        </button>
        <button
          (click)="sortByartist()"
          class="button-type"
          style="margin: 1vw 1vw 1vw 1vw"
        >
          Sort By Artist
        </button>
         <button
          (click)="isArtworkPopupOpen3 = true; getArtworks2()"
          class="button-type"
          style="margin: 1vw 0 1vw 0"
        >
          Select Items
        </button>
        <div></div>
        <button
          [disabled]="!canUndo" (click)="undo()"
          class="button-type"
          style="margin: 1vw 0 1vw 0"
        >
          Undo
        </button>
        <button
          [disabled]="!canRedo" (click)="redo()"
          class="button-type"
          style="margin: 1vw 1vw 1vw 1vw"
        >
          Redo
        </button>
      </div>
      <div class="Drawer-Wrap">
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(0)">
            <div class="span">Artwork settings</div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="SideDrawer[0]">
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Download:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showDownload"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Availabilities:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showAvailability"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>

            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Catalog ID:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showCatalogId"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <!-- <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Sale Information:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showSaleInfo"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div> -->
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Edition Details:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showEditionDetails"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>

            <p class="SideHeads">Items to show</p>
            <p class="UnderSideHead">
              The following fields are only visible within the Gallery Previews
              link and app
            </p>
            <div class="Checkbox-Collection">
              <label
                for="Medium"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="Medium"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showMedium"
                />Medium
              </label>
              <label
                for="showDimensions"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="showDimensions"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showDimensions"
                />Dimensions
              </label>
              <label
                for="showSignature"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="showSignature"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showSignature"
                />Signature
              </label>
              <label
                for="Location"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="Location"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showCondition"
                />Condition
              </label>
              <label
                for="Location"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="Location"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showSaleInfo"
                />Sale Info
              </label>
              <label
                for="Location"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="Location"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showLocation"
                />Location
              </label>
              <!-- <label
                for="CourtesyCreditline"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="CourtesyCreditline"
                  class="checkboxCls"
                  type="checkbox"
                  value="Courtesy/Credit line"
                />Courtesy/Credit line
              </label> -->
              <label
                for="Copyrightline"
                style="word-wrap: break-word; margin-right: 0.5vw"
              >
                <input
                  id="Copyrightline"
                  class="checkboxCls"
                  type="checkbox"
                  [(ngModel)]="contentObj.showCopyrightLine"
                />Copyright Line
              </label>
            </div>
            <p class="SideHeads">Prices</p>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Sale Price:</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showSalePrice"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div *ngIf="contentObj.showSalePrice" class="field-value">
              <div class="input-with-text">
                <div class="text-before">
                  <del>Show Crypto Price:</del>
                </div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showCryptoPrice"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(1)">
            <div class="span">Header Image / Background</div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="!SideDrawer[1]">
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show Banner:</div>
                <label class="switch">
                  <input type="checkbox" [(ngModel)]="contentObj.showBanner" />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(2)">
            <div class="span">Appearance</div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="!SideDrawer[2]">
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Dark Theme:</div>
                <label class="switch">
                  <input type="checkbox" [(ngModel)]="contentObj.darkTheme" />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <p class="SideHeads">Layout</p>
            <label for="Grid" style="word-wrap: break-word">
              Grid<input
                id="Grid"
                class="checkboxCls"
                style="margin-left: 2vw; width: 3vw"
                min="1"
                max="6"
                type="number"
                [(ngModel)]="contentObj.grid"
              />
            </label>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">
                  <del>Display images as tiles:</del>
                </div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.displayImageFiles"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <p class="SideHeads"><del>Secondary image layout</del></p>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Exclude secondary images :</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.excludeSecondaryImages"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="UnderSideHead">
              Show secondary images as additional items on the grid view:
            </div>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Artworks :</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.showArtworks"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Installation views :</div>
                <label class="switch">
                  <input
                    type="checkbox"
                    [(ngModel)]="contentObj.installationViews"
                  />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <p class="SideHeads"><del>Image Align</del></p>
            <div class="d-flex">
              <div class="nameVal">Horizontal</div>
              <div class="nameContent">
                <label for="Left" style="word-wrap: break-word">
                  <input
                    id="Left"
                    type="radio"
                    value="left"
                    style="margin-right: 0.2vw"
                    name="imageAlignHorizontal"
                    [(ngModel)]="contentObj.imageAlignHorizontal"
                  />Left
                </label>
                <label
                  for="Center"
                  style="word-wrap: break-word; margin-left: 1vw"
                >
                  <input
                    id="Center"
                    type="radio"
                    value="Center"
                    style="margin-right: 0.2vw"
                    name="imageAlignHorizontal"
                    [(ngModel)]="contentObj.imageAlignHorizontal"
                  />Center
                </label>
              </div>
            </div>
            <div class="d-flex">
              <div class="nameVal">Vertical</div>
              <div class="nameContent">
                <label for="Middle" style="word-wrap: break-word">
                  <input
                    id="Middle"
                    type="radio"
                    value="Middle"
                    style="margin-right: 0.2vw"
                    name="imageAlignVertical"
                    [(ngModel)]="contentObj.imageAlignVertical"
                  />Middle
                </label>
                <label
                  for="Bottom"
                  style="word-wrap: break-word; margin-left: 1vw"
                >
                  <input
                    id="Bottom"
                    type="radio"
                    value="Bottom"
                    style="margin-right: 0.2vw"
                    name="imageAlignVertical"
                    [(ngModel)]="contentObj.imageAlignVertical"
                  />Bottom
                </label>
              </div>
            </div>
            <p class="SideHeads"><del>Font Settings</del></p>
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Body text :</div>
                <select name="nothing" id="nothing">
                  <option value="Proxima Nova">Proxima Nova</option>
                  <option value="Proxima Nova">Arial</option>
                  <option value="Proxima Nova">Arial</option>
                  <option value="Proxima Nova">Arial</option>
                  <option value="Proxima Nova">Arial</option>
                  <option value="Proxima Nova">Arial</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(3)">
            <div class="span"><del>App synchronisation</del></div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="!SideDrawer[3]">
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Synchronise to iPads/iPhones :</div>
                <label class="switch">
                  <input type="checkbox" [(ngModel)]="contentObj.synchronize" />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <div class="UnderSideHead" style="margin-top: 1vw">
              Please note that when this Gallery Preview is displayed on your
              Gallery Previews app it will respect the settings on the
              iPad/iPhone Synchronisation screen. This includes what artwork
              specific information is displayed, the price display settings etc.
              Please check the 'Share' tab in individual Artwork records for
              further settings.
            </div>
          </div>
        </div>
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(4)">
            <div class="span"><del>Documents</del></div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="!SideDrawer[4]">
            <label for="Doc" style="word-wrap: break-word; margin-right: 0.5vw">
              <input
                id="Doc"
                class="checkboxCls"
                type="checkbox"
                value="Doc"
              />Allow recipient to download a PDF of the Gallery Preview
            </label>
            <div class="UnderSideHead">(max 50 artworks)</div>
            <div class="d-flex" style="justify-content: space-around">
              <button class="button-type" style="margin: 1vw 0 1vw 0">
                Upload documents
              </button>
              <button class="simpe-type" style="margin: 1vw 0 1vw 0">
                Manage documents
              </button>
            </div>
          </div>
        </div>
        <div class="Drawer-Section">
          <div class="Drawer-Section-Name" (click)="OpenDrawer(5)">
            <div class="span"><del>Social media sharing</del></div>
            <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
          </div>
          <div class="Drawer-Section-Content" [hidden]="!SideDrawer[5]">
            <div class="field-value">
              <div class="input-with-text">
                <div class="text-before">Show social media:</div>
                <label class="switch">
                  <input type="checkbox" />
                  <span class="slider round"></span>
                </label>
              </div>
            </div>
            <span style="margin-top: 1vw; display: block">Share Buttons</span>
            <div class="d-flex" style="align-items: flex-start">
              <div class="nameVal" style="min-width: 10vw; padding-top: 1vw">
                Title override
              </div>
              <div class="nameContent">
                <div class="field-value">
                  <input type="text" placeholder="" style="height: 2.47vw" />
                </div>
              </div>
            </div>
            <div
              class="d-flex"
              style="align-items: flex-start; margin-top: 1vw"
            >
              <div class="nameVal" style="min-width: 10vw; padding-top: 0.5vw">
                Description override
              </div>
              <div class="nameContent">
                <textarea name="asd" id="asd" cols="29" rows="2"></textarea>
              </div>
            </div>
            <div
              class="d-flex"
              style="align-items: flex-start; margin-top: 1vw"
            >
              <div class="nameVal" style="min-width: 10vw; padding-top: 1vw">
                Preview image
              </div>
              <div class="nameContent">
                <div style="margin-top: 0.5vw">
                  <label for="files" class="button-type">Select Image</label>
                  <input id="files" style="visibility: hidden" type="file" />
                </div>
              </div>
            </div>
            <div class="UnderSideHead">
              The Title, Description and Preview Image will show when this
              Gallery Previews link is shared on social media (e.g. Facebook,
              Twitter, etc.). For best results, we recommend using an image size
              of 1200 x 628 pixels.
            </div>
          </div>
          <div class="Drawer-Section">
            <div class="Drawer-Section-Name" (click)="OpenDrawer(6)">
              <div class="span"><del>Login / Expiry</del></div>
              <fa-icon style="color: #747474" [icon]="faAngleDown"> </fa-icon>
            </div>
            <div class="Drawer-Section-Content" [hidden]="!SideDrawer[6]">
              <div
                class="d-flex"
                style="align-items: flex-start; margin-top: 1vw"
              >
                <div class="nameVal" style="min-width: 8vw; padding-top: 0.5vw">
                  Username
                </div>
                <div class="nameContent">
                  <div class="field-value">
                    <input type="text" placeholder="" style="height: 2.47vw" />
                  </div>
                </div>
              </div>
              <div
                class="d-flex"
                style="align-items: flex-start; margin-top: 0.2vw"
              >
                <div class="nameVal" style="min-width: 8vw; padding-top: 0.5vw">
                  Password
                </div>
                <div class="nameContent">
                  <div class="field-value">
                    <input
                      type="password"
                      placeholder=""
                      style="height: 2.47vw"
                    />
                  </div>
                </div>
              </div>
              <div
                class="d-flex"
                style="align-items: flex-start; margin-top: 0.2vw"
              >
                <div class="nameVal" style="min-width: 8vw; padding-top: 0.5vw">
                  Expires <br />
                  (optional)
                </div>
                <div class="nameContent">
                  <div class="field-value">
                    <input type="date" placeholder="" style="height: 2.47vw" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="Footer-Buttons">
        <button
          class="button-type"
          style="margin: 1vw 0 1vw 0"
          (click)="updatePrivateLink()"
        >
          SAVE
        </button>
        <button
          class="button-type"
          style="margin: 1vw 0 1vw 0"
          (click)="updatePrivateLink()"
        >
          SAVE & CLOSE
        </button>
        <button
          (click)="isDuplicatePopupOpen = true"
          class="simpe-type"
          style="margin: 1vw 0 1vw 0"
        >
          DUPLICATE
        </button>
        <button class="simpe-type" style="margin: 1vw 0 1vw 0">DELETE</button>
      </div>
    </div>
  </div>
</div>

<div
  class="artworkModal"
  [style.display]="isArtworkPopupOpen ? 'block' : 'none'"
>
  <!-- Modal content -->
  <div class="artwork-modal-content">
    <div class="Addselection_container">
      <div class="Addtitle_bar">
        Select Existing
        <div
          *ngIf="
            work_type.length > 0 ||
            this.minPrice ||
            this.maxPrice ||
            this.minHeight ||
            this.maxHeight ||
            this.minWidth ||
            this.maxWidth ||
            this.multiple_sale_options.length > 0
          "
          style="padding-left: 0.1vw; cursor: pointer"
        >
          <a (click)="clearFilters()"
            ><button
              class="saveBtn"
              style="font-size: 0.8vw; margin-left: 0.2vw"
            >
              Clear filters
            </button></a
          >
        </div>
        <p class="Addclose_icon" (click)="isArtworkPopupOpen = false">
          &times;
        </p>
      </div>
      <div class="Addsearch_bar">
        <div class="search_container">
          <div class="fieldd-value">
            <input
              type="text"
              placeholder="Search"
              [(ngModel)]="searchKey"
              [ngModelOptions]="{ standalone: true }"
              (keyup)="searchResult($event)"
              style="width: 17vw"
            />
          </div>
        </div>
        <div class="filter_container">
          <div class="fieldd-value">
            <div class="input-container" style="width: 7.2vw !important">
              <input
                value="Status"
                placeholder="Status"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen2 = true"
              />
              <button
                (click)="isFilterDropdownOpen2 = !isFilterDropdownOpen2"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen2,
                  'dropdown-visible': isFilterDropdownOpen2
                }"
                style="width: 150%"
                #dropDowns1
              >
                <ul>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[0]"
                        (click)="
                          SetAdvSaleFilter(
                            'Published',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Published</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[1]"
                        (click)="
                          SetAdvSaleFilter(
                            'NotPublished',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Not Published</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[2]"
                        (click)="
                          SetAdvSaleFilter('Sold', $event.currentTarget.checked)
                        "
                      />
                      Sold</label
                    >
                  </li>
                  <li>
                    <label
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[3]"
                        (click)="
                          SetAdvSaleFilter(
                            'ForSale',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      For Sale</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[4]"
                        (click)="
                          SetAdvSaleFilter(
                            'NotForSale',
                            $event.currentTarget.checked
                          )
                        "
                      />Not For Sale</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[5]"
                        (click)="
                          SetAdvSaleFilter(
                            'Reserved',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Reserved</label
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="fieldd-value">
            <div class="input-container" style="width: 10vw !important">
              <input
                value="Work Type"
                placeholder="Work Type"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen3 = true"
              />
              <button
                (click)="isFilterDropdownOpen3 = !isFilterDropdownOpen3"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen3,
                  'dropdown-visible': isFilterDropdownOpen3
                }"
                style="width: 150%"
                #dropDowns2
              >
                <ul>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[6]"
                        (click)="
                          SetArtworkGroupFilter2(
                            'unique',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Unique</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[7]"
                        (click)="
                          SetArtworkGroupFilter2(
                            'Unique - Diptych',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Unique - Diptych</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[8]"
                        (click)="
                          SetArtworkGroupFilter2(
                            'Unique - Triptych',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Unique - Triptych</label
                    >
                  </li>
                  <li>
                    <label
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[9]"
                        (click)="
                          SetArtworkGroupFilter2(
                            'Unique - Set of works',
                            $event.currentTarget.checked
                          )
                        "
                      />
                      Unique - Set of works</label
                    >
                  </li>
                  <li>
                    <label style="display: flex; gap: 0.5vw"
                      ><input
                        type="checkbox"
                        class="checkbox_style"
                        [(ngModel)]="inputCheckBox[10]"
                        (click)="
                          SetArtworkGroupFilter2(
                            'edition',
                            $event.currentTarget.checked
                          )
                        "
                      />Edition</label
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="fieldd-value">
            <div class="input-container" style="width: 7vw !important">
              <input
                value="Price"
                placeholder="Price"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen4 = true"
              />
              <button
                (click)="isFilterDropdownOpen4 = !isFilterDropdownOpen4"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen4,
                  'dropdown-visible': isFilterDropdownOpen4
                }"
                style="width: 150%"
                #dropDowns3
              >
                <ul>
                  <div style="justify-content: space-between">
                    <div
                      style="
                        margin-top: 0.5vw;
                        font-size: 0.8vw;
                        margin-left: 1vw;
                      "
                    >
                      INR
                    </div>

                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="priceFrom"
                        type="text"
                        class="filterInput"
                        placeholder="Min"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        flex-direction: row;
                        width: 100%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="priceTo"
                        type="text"
                        class="filterInput"
                        placeholder="Max"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                  </div>
                  <li>
                    <button
                      class="action_Btn"
                      style="
                        border-radius: 1px;
                        border-style: solid !important;
                        background-color: black;
                        color: white;
                        border: 0.06944vw solid var(--timeline-color);
                        padding: 0.5vw 1vw;
                        border-radius: 2vw;
                        display: inline-flex;
                        width: max-content;
                        justify-content: center;
                        align-items: center;
                      "
                      (click)="SetArtworkPrice()"
                    >
                      Apply
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="fieldd-value">
            <div class="input-container" style="width: 7vw !important">
              <input
                value="Size"
                placeholder="Size"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen5 = true"
              />
              <button
                (click)="isFilterDropdownOpen5 = !isFilterDropdownOpen5"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen5,
                  'dropdown-visible': isFilterDropdownOpen5
                }"
                style="width: 150%"
                #dropDowns4
              >
                <ul>
                  <div
                    style="
                      margin-top: 0.5vw;
                      font-size: 0.8vw;
                      margin-left: 1vw;
                    "
                  >
                    Height (Inch)
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        width: 50%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="heightFrom"
                        type="text"
                        class="filterInput"
                        placeholder="Min"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        flex-direction: row;
                        width: 50%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="heightTo"
                        type="text"
                        class="filterInput"
                        placeholder="Max"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                  </div>
                  <div
                    style="
                      margin-top: 0.5vw;
                      font-size: 0.8vw;
                      margin-left: 1vw;
                    "
                  >
                    Width (Inch)
                  </div>

                  <div style="display: flex; justify-content: space-between">
                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        width: 50%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="widthFrom"
                        type="text"
                        class="filterInput"
                        placeholder="Min"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                    <li
                      style="
                        cursor: pointer;
                        display: flex;
                        justify-content: space-between;
                        flex-direction: row;
                        width: 50%;
                        align-items: center;
                        padding: 0.69vw 0.4vw;
                      "
                    >
                      <input
                        [(ngModel)]="widthTo"
                        type="text"
                        class="filterInput"
                        placeholder="Max"
                        style="
                          margin-left: 0.5vw;
                          border: 0.069vw solid var(--timeline-color) !important;
                        "
                      />
                    </li>
                  </div>
                  <li>
                    <button
                      class="action_Btn"
                      style="
                        border-radius: 1px;
                        border-style: solid !important;
                        background-color: black;
                        color: white;
                        border: 0.06944vw solid var(--timeline-color);
                        padding: 0.5vw 1vw;
                        border-radius: 2vw;
                        display: inline-flex;
                        width: max-content;
                        justify-content: center;
                        align-items: center;
                      "
                      (click)="SetArtworkDimension()"
                    >
                      Apply
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="fieldd-value">
            <div class="input-container" style="width: 7vw !important">
              <input
                [(ngModel)]="sort"
                [ngModelOptions]="{ standalone: true }"
                placeholder="sort"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen1 = true"
              />
              <button
                (click)="isFilterDropdownOpen1 = !isFilterDropdownOpen1"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen1,
                  'dropdown-visible': isFilterDropdownOpen1
                }"
                #dropDowns5
              >
                <ul>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort = 'Title↑';
                      getArtworks()
                    "
                  >
                    <div class="country-name">Title&uarr;</div>
                  </li>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort = 'Title↓';
                      getArtworks()
                    "
                  >
                    <div class="country-name">Title&darr;</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="Addselection_headings">
        <div class="Addproperty_title Addbox">
          <input type="checkbox" name="title" id="title" disabled />
        </div>

        <div class="Addproperty_title Addbox2">Primary Image</div>
        <div class="Addproperty_title Addbox2">Artist Name</div>
        <div class="Addproperty_title Addbox2">Artwork Title</div>
        <div class="Addproperty_title Addbox">Year</div>
        <div class="Addproperty_title Addbox2">Medium</div>
        <div class="Addproperty_title Addbox2">Dimension</div>
        <div class="Addproperty_title Addbox2">Price</div>
      </div>
      <div class="Addselection_data" *ngFor="let item of artworksArr">
        <div class="Addartwork_details">
          <div class="Addproperties Addbox">
            <input
              type="checkbox"
              [(ngModel)]="item.selected"
              [ngModelOptions]="{ standalone: true }"
            />
          </div>

          <div class="Addproperties Addbox2">
            <span style="margin-right: 0.5vw">
              <fa-icon
                [style.color]="item?.publish_artworks ? '#004ddd' : 'red'"
                [icon]="item?.publish_artworks ? faCheckCircle : faTimesCircle"
                style="margin-right: 0.2vw"
              ></fa-icon>
              <fa-icon
                *ngIf="
                  item?.sale_options != 'ReturnedToArtist' &&
                  item?.sale_options != 'OnLoan'
                "
                [style.color]="
                  item?.sale_options == 'ForSale'
                    ? '#00FF00'
                    : item?.sale_options == 'Reserved'
                    ? '#FFFF00'
                    : item?.sale_options == 'OnReserve'
                    ? '#FFFF00'
                    : item?.sale_options == 'Sold'
                    ? '#FF0000'
                    : '#101010'
                "
                [icon]="faCircle"
              ></fa-icon>
              <span
                *ngIf="item?.sale_options == 'ReturnedToArtist'"
                class="circle"
                style="background-color: rgb(236, 112, 10)"
                >R</span
              >
              <span
                *ngIf="item?.sale_options == 'OnLoan'"
                class="circle"
                style="background-color: rgb(236, 228, 10)"
                >L</span
              >
            </span>
            <a
              (click)="
                artImg =
                  'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                  item?.primary_image[0].url;
                isArtworkPopupOpen2 = true
              "
            >
              <img
                [src]="
                  item?.primary_image.length > 0
                    ? 'https://www.terrain.art/cdn-cgi/image/width=100,quality=72/' +
                      item?.primary_image[0].url
                    : 'https://picsum.photos/200/300'
                "
                alt="Artwork Thumbanail"
                style="width: 2.3vw; height: 2.3vw"
              />
              <fa-icon
                [icon]="faSearchPlus"
                style="margin-left: 0.2vw"
              ></fa-icon>
            </a>
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artist_id?.display_name }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artwork_title.slice(0, 30)
            }}{{ item.artwork_title.length > 30 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.year }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.medium.slice(0, 15)
            }}{{ item.medium.length > 15 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.dimensions.split("|")[1]?.slice(0, 15)
            }}{{ item.dimensions.split("|")[1]?.length > 15 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            ₹ {{ item?.sale_price | number : "1.0" }}
          </div>
        </div>
      </div>
      <div class="Addfooter_bar" *ngIf="artworksArr.length != 0">
        <button
          class="Addselect"
          (click)="isArtworkPopupOpen = false"
          style="margin-top: 2.5vw"
        >
          Cancel
        </button>
        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="manageSelectedArtworks()"
        >
          Done
        </button>

        <span
          class="d-flex"
          style="
            gap: 2vw;
            float: left;
            align-items: center;
            margin-top: 0.5vw;
            margin-bottom: 0.5vw;
          "
        >
          <span class="records">found {{ totalRecords }} records</span>
          <span class="d-flex" style="gap: 0.7vw; align-items: center">
            <fa-icon
              [icon]="faAngleLeft"
              (click)="managePagination('prev')"
            ></fa-icon>
            Page
            <input
              [(ngModel)]="gotoPage"
              [ngModelOptions]="{ standalone: true }"
              type="text"
              min="1"
              style="width: 2vw; text-align: center"
              (focusout)="onGotoPage()"
              pattern="\d*"
              (keydown.enter)="onGotoPage()"
            />
            <!-- <input
              style="height: 1vw; width: 1vw"
              type="number"
              [max]="totalPage"
              [(ngModel)]="offset"
              [ngModelOptions]="{ standalone: true }"
              min="1"
              name="pager"
            /> -->
            Of {{ totalPage }}
            <fa-icon
              [icon]="faAngleRight"
              (click)="managePagination('next')"
            ></fa-icon>
          </span>
          <!-- <span style="cursor: pointer" (click)="findAll()"> Find ALL</span> -->
        </span>
      </div>
    </div>
  </div>
</div>
<div
  class="artworkModal"
  [style.display]="isArtworkPopupOpen2 ? 'block' : 'none'"
>
  <div class="artwork-modal-content">
    <div class="Addselection_container">
      <div class="Addtitle_bar">
        <div></div>
        <p class="Addclose_icon" (click)="isArtworkPopupOpen2 = false">
          &times;
        </p>
      </div>
      <div
        class="w-100"
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          height: 90%;
        "
      >
        <img [src]="artImg" style="max-width: 95%; max-height: 90%" />
      </div>
    </div>
  </div>
</div>
<div
  class="artworkModal"
  [style.display]="isDuplicatePopupOpen ? 'block' : 'none'"
>
  <!-- Modal content -->
  <div class="artwork-modal-content" style="height: unset; margin: 30vh auto">
    <div class="Addselection_container" style="margin: 1vw 1.7vw">
      <div class="Addtitle_bar">Create Duplicate Gallery Preview Link</div>
      <div class="Addselection_data" style="overflow-y: unset">
        <div class="field-value">
          <input
            type="text"
            [(ngModel)]="duplicateTitle"
            placeholder=""
            style="height: 2.47vw"
          />
          <div class="placeholder">Gallery Preview Title</div>
          <div class="input-info">
            Provide title for duplicated gallery preview.
          </div>
        </div>
      </div>
      <div class="Addfooter_bar" style="text-align: right">
        <button class="Addselect" (click)="isDuplicatePopupOpen = false">
          Cancel
        </button>
        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="duplicatePrivateLink()"
        >
          Done
        </button>
      </div>
    </div>
  </div>
</div>
<!-- <toast></toast> -->
<div
  class="artworkModal"
  [style.display]="isArtworkPopupOpen3 ? 'block' : 'none'"
>
  <!-- Modal content -->
  <div class="artwork-modal-content">
    <div class="Addselection_container">
      <div class="Addtitle_bar">
        Select Items ( {{getSelectedArtCount()}} )

        <p class="Addclose_icon" (click)="isArtworkPopupOpen3 = false">
          &times;
        </p>
      </div>
      <div class="Addsearch_bar">
        <div class="search_container">
          <div class="fieldd-value">
            <input
              type="text"
              placeholder="Search"
              [(ngModel)]="searchKey2"
              [ngModelOptions]="{ standalone: true }"
              (keyup)="searchResult2($event)"
              style="width: 17vw"
            />
          </div>
        </div>
         <div class="input-container">
        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="selectAllArtOnPage()"
        >
          Select All On Page
        </button>

        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="selectAllArtworks()"
        >
          Select All
        </button>
        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="unSelectAllArtOnPage()"
        >
          Unselect All On Page
        </button>

        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="unSelectAllArtworks()"
        >
          Unselect All
        </button>
            </div>
        <div class="filter_container">




          <div class="fieldd-value">


            <div class="input-container" style="width: 7vw !important">
              <input
                [(ngModel)]="sort2"
                [ngModelOptions]="{ standalone: true }"
                placeholder="sort"
                type="text"
                class="selection"
                readonly
                (focus)="isFilterDropdownOpen1 = true"
              />
              <button
                (click)="isFilterDropdownOpen1 = !isFilterDropdownOpen1"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen1,
                  'dropdown-visible': isFilterDropdownOpen1
                }"
                #dropDowns5
              >
                <ul>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort2 = 'Title↑';
                      getArtworks2()
                    "
                  >
                    <div class="country-name">Title&uarr;</div>
                  </li>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort2 = 'Title↓';
                      getArtworks2()
                    "
                  >
                    <div class="country-name">Title&darr;</div>
                  </li>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort2 = 'Artist↑';
                      getArtworks2()
                    "
                  >
                    <div class="country-name">Artist&uarr;</div>
                  </li>
                  <li
                    (click)="
                      isFilterDropdownOpen1 = false;
                      sort2 = 'Artist↓';
                      getArtworks2()
                    "
                  >
                    <div class="country-name">Artist&darr;</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="Addselection_headings">
        <div class="Addproperty_title Addbox">
          <input type="checkbox" name="title" id="title" disabled />
        </div>

        <div class="Addproperty_title Addbox2">Primary Image</div>
        <div class="Addproperty_title Addbox2">Artist Name</div>
        <div class="Addproperty_title Addbox2">Artwork Title</div>
        <div class="Addproperty_title Addbox">Year</div>
        <div class="Addproperty_title Addbox2">Medium</div>
        <div class="Addproperty_title Addbox2">Dimension</div>
        <div class="Addproperty_title Addbox2">Price</div>
      </div>
      <div class="Addselection_data" *ngFor="let item of filteredArtworksArr">
        <div class="Addartwork_details">
          <div class="Addproperties Addbox">
            <input
              type="checkbox"
              [(ngModel)]="item.selected"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChange)="onCheckboxChange(item, $event)"
            />
          </div>

          <div class="Addproperties Addbox2">
            <span style="margin-right: 0.5vw">
              <fa-icon
                [style.color]="item?.publish_artworks ? '#004ddd' : 'red'"
                [icon]="item?.publish_artworks ? faCheckCircle : faTimesCircle"
                style="margin-right: 0.2vw"
              ></fa-icon>
              <fa-icon
                *ngIf="
                  item?.sale_options != 'ReturnedToArtist' &&
                  item?.sale_options != 'OnLoan'
                "
                [style.color]="
                  item?.sale_options == 'ForSale'
                    ? '#00FF00'
                    : item?.sale_options == 'Reserved'
                    ? '#FFFF00'
                    : item?.sale_options == 'OnReserve'
                    ? '#FFFF00'
                    : item?.sale_options == 'Sold'
                    ? '#FF0000'
                    : '#101010'
                "
                [icon]="faCircle"
              ></fa-icon>
              <span
                *ngIf="item?.sale_options == 'ReturnedToArtist'"
                class="circle"
                style="background-color: rgb(236, 112, 10)"
                >R</span
              >
              <span
                *ngIf="item?.sale_options == 'OnLoan'"
                class="circle"
                style="background-color: rgb(236, 228, 10)"
                >L</span
              >
            </span>
            <a
              (click)="
                artImg =
                  'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                  item?.primary_image[0].url;
                isArtworkPopupOpen2 = true
              "
            >
              <img
                [src]="
                  item?.primary_image.length > 0
                    ? 'https://www.terrain.art/cdn-cgi/image/width=100,quality=72/' +
                      item?.primary_image[0].url
                    : 'https://picsum.photos/200/300'
                "
                alt="Artwork Thumbanail"
                style="width: 2.3vw; height: 2.3vw"
              />
              <fa-icon
                [icon]="faSearchPlus"
                style="margin-left: 0.2vw"
              ></fa-icon>
            </a>
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artist_id?.display_name }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artwork_title.slice(0, 30)
            }}{{ item.artwork_title.length > 30 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.year }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.medium.slice(0, 15)
            }}{{ item.medium.length > 15 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.dimensions.split("|")[1]?.slice(0, 15)
            }}{{ item.dimensions.split("|")[1]?.length > 15 ? "..." : "" }}
          </div>
          <div class="Addproperties Addbox2">
            ₹ {{ item?.sale_price | number : "1.0" }}
          </div>
        </div>
      </div>
      <div class="Addfooter_bar" *ngIf="filteredArtworksArr.length != 0">

        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="isArtworkPopupOpen3 = false"
        >
          Done
        </button>

        <span
          class="d-flex"
          style="
            gap: 2vw;
            float: left;
            align-items: center;
            margin-top: 0.5vw;
            margin-bottom: 0.5vw;
          "
        >
          <span class="records">found {{ totalRecords2 }} records</span>
          <span class="d-flex" style="gap: 0.7vw; align-items: center">
            <fa-icon
              [icon]="faAngleLeft"
              (click)="managePagination2('prev')"
            ></fa-icon>
            Page
            <input
              [(ngModel)]="gotoPage2"
              [ngModelOptions]="{ standalone: true }"
              type="text"
              min="1"
              style="width: 2vw; text-align: center"
              (focusout)="onGotoPage2()"
              pattern="\d*"
              (keydown.enter)="onGotoPage2()"
            />
            <!-- <input
              style="height: 1vw; width: 1vw"
              type="number"
              [max]="totalPage"
              [(ngModel)]="offset"
              [ngModelOptions]="{ standalone: true }"
              min="1"
              name="pager"
            /> -->
            Of {{ totalPage2 }}
            <fa-icon
              [icon]="faAngleRight"
              (click)="managePagination2('next')"
            ></fa-icon>
          </span>
          <!-- <span style="cursor: pointer" (click)="findAll()"> Find ALL</span> -->
        </span>
      </div>
    </div>
  </div>
</div>
