import {
  FooterService,
  FooterType,
} from './../../../../../shared/services/footer.service';
import { Component, OnInit } from '@angular/core';
import {
  faTimes,
  faAngleLeft,
  faAngleRight,
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-art-show',
  templateUrl: './art-show.component.html',
  styleUrls: ['./art-show.component.scss'],
})
export class ArtShowComponent implements OnInit {
  close = faTimes;
  faAngleRight = faAngleRight;
  faAngleLeft = faAngleLeft;
  showInfo = true;
  id = 0;
  artworkArray = [
    { artist: '<PERSON><PERSON><PERSON>' },
    { artist: '<PERSON>' },
    { artist: 'Unnitahn' },
    { artist: 'IPK <PERSON>e' },
    { artist: 'Bdi HHey' },
    { artist: 'CARt OOh' },
    { artist: '<PERSON>' },
    { artist: '<PERSON><PERSON><PERSON>' },
    { artist: '<PERSON>' },
    { artist: 'Best Daday' },
    { artist: '<PERSON>' },
  ];
  constructor(private footer: FooterService) {}
  info() {
    if (!this.showInfo) {
      this.showInfo = true;
    } else {
      this.showInfo = false;
    }
  }
  Descrease() {
    if (this.id + 1 != 1) {
      this.id = this.id - 1;
    }
  }
  Increase() {
    if (this.id + 1 != this.artworkArray.length) {
      this.id = this.id + 1;
    }
  }
  ngOnInit(): void {
    //this.footer.changeFooterType(FooterType.HIDE);
  }
  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.
    // this.footer.changeFooterType(FooterType.DEFAULT);
  }
}
