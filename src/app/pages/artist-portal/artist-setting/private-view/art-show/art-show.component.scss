.contain {
  padding: 2vw 0;
}
.content {
    width: 100%;
    padding: 3vw 2vw;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 2vw;
  .img {
    img {
        width: 100%;
        height: 35vw;
        object-fit: contain;
    }
  }
  .info {
    width: 45%;
    padding-right: 3vw;
    span{
        width: 100%;
display: block;
    }
  }
}
.bottom-line{
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2vw;

    .actoins{justify-self:flex-end;
        display: flex;
        gap: 2vw;
    }
}