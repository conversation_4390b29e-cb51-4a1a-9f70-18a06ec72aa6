.Banner {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  //text-transform: uppercase;
  letter-spacing: 0.06944vw;
  -webkit-font-smoothing: antialiased;
}
.Collection_Name {
  font-size: 5vw;
  font-weight: 200;
  // font-weight: 500;
  padding-bottom: 8px;
}

.Gallery_Name {
  font-size: 2.638vw;
  //   font-weight: 500;
  font-weight: 200;
}
.Decription {
  padding: 2.777vw 0;
  margin: 0 8.333vw;
  display: flex;
  flex-direction: column;
  .download-span {
    text-align: end;
    margin-bottom: 1.686vw;
    animation: item-appear-fade 2.5s ease;
    font-size: 0.833vw;
    /* font-weight: 600; */
    text-transform: uppercase;
    letter-spacing: 0.06944vw;
    color: #888;
    cursor: pointer;
    // padding: 0.6944vw 0.833vw;
  }
  .Desc {
    //word-break: break-all;
    font-size: 1.042vw;
    animation: item-appear-fade 2.5s ease;
  }
}
.Image-Wrap {
  padding: 2.777vw 0;
  margin: 0 8.333vw;
  display: flex;
  gap: 1vw;
  flex-wrap: wrap;
  .Image-Item {
    // margin-left: 2%;
    // margin-right: 2%;

    padding-top: 1.5%;
    padding-bottom: 1.5%;
    font-size: 0.9027vw;
    .view-image {
      font-size: 0.63vw;
    }
    animation: item-appear-delay-1 1.7s ease;
    position: relative;
    display: inline-block;
    // *display: inline;
    zoom: 1;
    // width: 28.3333333333%;
    // margin: 10px 0;
    vertical-align: top;
    word-spacing: normal;
    // font-size: 14px;
    line-height: 1.4;
    .Image-cell {
      width: auto;
      img {
        margin: 0 auto;
        display: inline-block;
        max-width: 100%;
        max-height: 100%;
        vertical-align: middle;
        -o-object-fit: contain;
        object-fit: contain;
        padding: 1vw;
      }
    }
  }
  .Image-Details {
    margin-top: 2vw;
    p {
      line-height: 1.4;
      margin-bottom: 0;
      overflow-wrap: break-word;
    }
  }
}
.Footer-T {
  padding-top: 5vw;
  padding-bottom: 2vw;
  margin: 0 8.333vw;
  .text-style {
    text-transform: uppercase;
    font-size: 0.763vw;
    letter-spacing: 0.06944vw;
    color: #777;
  }
  .text-section {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
}
@media (max-width: 768px) {
  .Collection_Name {
    font-size: 8.138vw;
    //   font-weight: 200;
    font-weight: 500;
    padding-bottom: 1.666vw;
    letter-spacing: 0.13888vw;
  }

  .Gallery_Name {
    font-size: 3.777vw;
    //   font-weight: 500;
    font-weight: 200;
  }

  .Decription {
    padding: 5.5555vw 0;
    margin: 0 2.883vw;
    display: flex;
    flex-direction: column;
    .download-span {
      text-align: start;
      margin-bottom: 3.3vw;
      animation: item-appear-fade 2.5s ease;
      font-size: 1.666vw;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.13888vw;
      color: #888;
      cursor: pointer;
      // padding: 0.6944vw 0.833vw;
    }
    .Desc {
      // word-break: break-word;
      font-size: 3.0833vw;
      animation: item-appear-fade 2.5s ease;
    }
    h1 {
      font-size: 5vw;
    }
  }

  .Image-Wrap {
    padding: 5.555vw 0;
    margin: 0 2.878vw;
    display: flex;
    gap: 2vw;
    flex-direction: column;
    flex-wrap: wrap;

    .Image-Item {
      // margin-left: 2%;
      // margin-right: 2%;
      gap: 6vw;
      padding-top: 1.5%;
      padding-bottom: 2.5%;

      font-size: 2.5vw;
      .view-image {
        font-size: 1.75vw;
      }
      animation: item-appear-delay-1 1.7s ease;
      position: relative;
      display: flex;
      // *display: inline;
      flex-direction: row;
      zoom: 1;
      // width: 28.3333333333%;
      // margin: 10px 0;
      vertical-align: top;
      word-spacing: normal;
      // font-size: 14px;
      line-height: 1.4;
      .Image-cell {
        width: 45%;
        img {
          margin: 0 auto;
          display: inline-block;
          max-width: 100%;
          max-height: 100%;
          vertical-align: middle;
        }
      }

      .Image-Details {
        width: 55%;
        margin-top: unset;
        p {
          line-height: 1.4;
          margin-bottom: 0;
          overflow-wrap: break-word;
        }
      }
    }
  }

  .Footer-T {
    padding-top: 7vw;
    padding-bottom: 4vw;
    margin: 0 2.883vw;
    span {
      display: flex;
      flex-direction: column;
      gap: 1vw;
    }
    .text-style {
      text-transform: uppercase;
      font-size: 2vw;
      letter-spacing: 0.06944vw;
      color: #777;
    }
    .text-section {
      display: flex;
      flex-direction: column;
      gap: 3vw;
      justify-content: unset;
    }
  }
}
.preview {
  min-height: calc(100vh - 10vw);
}
.dark {
  color: #fff;
  background-color: #000;
}
.Image-cell {
  position: relative;
  height: 0;

  width: 100%;
  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

#logo img {
  height: 2vw;
  margin-bottom: 1.67vw;
  margin-right: 1vw;
  @media (max-width: 768px) {
    height: 4vw;
    margin-bottom: 3vw;
  }
}
.logo-line {
  border-top: 1px solid black;
  height: 1px;
  margin-bottom: 1.67vw;
  @media (max-width: 768px) {
    margin-bottom: 3vw;
  }
}
@media (max-width: 768px) {
  .Image-Item {
    display: flex;
    justify-content: space-between;
  }
  .Image-cell {
    display: flex;
    flex-direction: column;
    justify-content: start;
    img {
      height: auto;
    }
  }
}
