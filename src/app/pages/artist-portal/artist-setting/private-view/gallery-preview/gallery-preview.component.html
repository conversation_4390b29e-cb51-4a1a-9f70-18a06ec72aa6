<div *ngIf="previewLinkObj?.contents">
  <div
    class="preview"
    [ngClass]="{ dark: previewLinkObj?.contents[0]?.data?.darkTheme }"
  >
    <div
      class="Banner"
      [ngStyle]="bannerCss"
      [style.height]="isMobile ? '90vw' : '34.722vw'"
      *ngIf="previewLinkObj?.contents[0]?.data?.showBanner"
    >
      <span class="Collection_Name">{{ previewLinkObj?.title }}</span>
      <!-- <span class="Gallery_Name">GALLEry</span> -->
    </div>
    <div class="Decription">
      <div *ngIf="!previewLinkObj?.contents[0]?.data?.showBanner">
        <div class="logo-container">
          <a id="logo">
            <img [src]="'assets/images/logo.png'" alt="Logo" />
          </a>
        </div>
        <div class="logo-line"></div>
        <h1>{{ previewLinkObj?.title }}</h1>
      </div>
      <p
        *ngIf="previewLinkObj?.contents[0]?.data?.showDownload"
        class="download-span"
        (click)="generateDocx()"
      >
        Download PDF
      </p>
      <p class="Desc">
        {{ previewLinkObj?.aboveDescription }}
      </p>
    </div>

    <div class="Image-Wrap">
      <div
        class="Image-Item"
        [ngStyle]="
          isMobile
            ? { width: '100%' }
            : {
                width:
                  100 / previewLinkObj?.contents[0]?.data?.grid -
                  10 / previewLinkObj?.contents[0]?.data?.grid +
                  '%'
              }
        "
        *ngFor="let item of previewLinkObj?.artworkIds; let i = index"
      >
        <div
          class="Image-cell"
          [ngStyle]="
            isMobile
              ? { 'min-height': '40vw', width: '50%' }
              : {
                  paddingBottom:
                    (100 * previewLinkObj?.contents[0]?.data?.grid) /
                      previewLinkObj?.contents[0]?.data?.grid -
                    10 / previewLinkObj?.contents[0]?.data?.grid +
                    '%'
                }
          "
        >
          <img
            (click)="(openArtwork = true) && (selectedArtwork = i)"
            *ngIf="primary_images[i]"
            [src]="primary_images[i]"
            alt=""
          />
          <div *ngIf="!primary_images[i]" style="width: 100%; height: 100%">
            <app-placeholder width="100%" paddingTop="100%"> </app-placeholder>
          </div>
        </div>
        <div class="Image-Details">
          <p>
            <i>{{ item?.artwork_title }}</i
            >, {{ item?.year }}
          </p>
          <p>{{ item?.artist_id?.display_name }}</p>
          <p *ngIf="previewLinkObj?.contents[0]?.data?.showMedium">
            {{ item?.medium }}
          </p>
          <p *ngIf="previewLinkObj?.contents[0]?.data?.showDimensions">
            {{ item?.dimensions }}
          </p>
          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showSignature"
            class="signed_and_dated"
          >
            {{ item?.signature_details }}
          </p>

          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showCatalogId"
            class="stock_number"
          >
            {{ item?.catalog_number }}
          </p>
          <fa-icon
            *ngIf="previewLinkObj?.contents[0]?.data?.showAvailability"
            [style.color]="
              item?.sale_options == 'ForSale'
                ? '#00FF00'
                : item?.sale_options == 'Reserved'
                ? '#FFFF00'
                : item?.sale_options == 'OnReserve'
                ? '#FFFF00'
                : item?.sale_options == 'Sold'
                ? '#FF0000'
                : '#101010'
            "
            [icon]="faCircle"
          ></fa-icon>
          <p *ngIf="previewLinkObj?.contents[0]?.data?.showSalePrice">
            ₹
            {{ item?.sale_price | number : "1.0" }}
            <!-- | $
            {{ item?.sale_price | forex : "USD" | async | number : "1.0" }} -->
          </p>
          <p *ngIf="previewLinkObj?.contents[0]?.data?.showCryptoPrice">
            BTC
            {{ item?.sale_price | forex : "BTC" | async | number : "1.0" }}
            | ETH
            {{ item?.sale_price | forex : "ETH" | async | number : "1.0" }}
            | USDC
            {{ item?.sale_price | forex : "USDC" | async | number : "1.0" }}
          </p>
          <p
            *ngIf="
              previewLinkObj?.contents[0]?.data?.showLocation &&
              item?.extras?.isGallerWearhouse != 2
            "
            class="location"
          >
            {{ item?.location }}
          </p>
          <p
            *ngIf="
              previewLinkObj?.contents[0]?.data?.showLocation &&
              item?.extras?.storageAddress &&
              item?.extras?.isGallerWearhouse == 1
            "
            class="location"
          >
            {{ item?.extras?.storageAddress }}
          </p>
          <p
            *ngIf="
              previewLinkObj?.contents[0]?.data?.showLocation &&
              item?.extras?.artistAddress?.title &&
              item?.extras?.isGallerWearhouse == 2
            "
            class="location"
          >
            {{ item?.extras?.artistAddress?.title }} -
            {{ item?.extras?.artistAddress?.city }}
          </p>
          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showCopyrightLine"
            class="copyright"
          >
            {{ item?.credit_line }}
          </p>
          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showSaleInfo"
            class="copyright"
          >
            {{ item?.sale_information }}
          </p>
          <p
            [innerHTML]="getSaleInformation(item)"
            *ngIf="previewLinkObj?.contents[0]?.data?.showSaleInfo"
            class="copyright"
          ></p>
          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showEditionDetails"
            class="copyright"
          >
            {{ item?.edition_details }}
          </p>
          <p
            *ngIf="previewLinkObj?.contents[0]?.data?.showCondition"
            class="copyright"
          >
            Condition: {{ item?.current_state }}
          </p>
          <p
            *ngIf="
              previewLinkObj?.contents[0]?.data?.showCondition &&
              item?.extras?.packaging
            "
            class="copyright"
          >
            Packaging: {{ item?.extras?.packaging }}
          </p>
          <p
            *ngIf="
              previewLinkObj?.contents[0]?.data?.showCondition &&
              item?.extras?.installState
            "
            class="copyright"
          >
            Install State: {{ item?.extras?.installState }}
          </p>
          <p
            *ngIf="
              item?.show_media &&
              checkPublishAdditionalMedia(item?.additional_images)
            "
            style="color: var(--tertiary-font-color); cursor: pointer"
            (click)="(openArtwork = true) && (selectedArtwork = i)"
            class="view-image"
          >
            View additional images/videos
          </p>
        </div>
      </div>
    </div>
    <div class="Decription">
      <p class="Desc">
        {{ previewLinkObj?.BelowDescription }}
      </p>
    </div>
  </div>
  <div [ngClass]="{ dark: previewLinkObj?.contents[0]?.data?.darkTheme }">
    <div class="Footer-T">
      <hr />
      <div class="text-section text-style">
        <span class="left"
          >All images Courtesy and Copyright of the Artist.
        </span>
        <span class="right">
          Gallery Preview by Repra Art Private Limited © 2023
        </span>
      </div>
    </div>
  </div>
</div>
<app-artwork-comp
  (closePopup)="openArtwork = false"
  *ngIf="openArtwork"
  [privateViewData]="previewLinkObj"
  [selectedArtwork]="selectedArtwork"
></app-artwork-comp>
