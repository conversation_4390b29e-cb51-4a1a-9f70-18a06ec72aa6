import { isPlatformBrowser } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit, PLATFORM_ID, Inject } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { faCircle } from '@fortawesome/free-solid-svg-icons';
import { ToastService } from 'src/app/core/toast/toast.service';
import { CollectorService } from 'src/app/services/collector.service';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';
import {
  NavBarService,
  NavBarType,
} from 'src/app/shared/services/navbar.service';
import { apiUrl } from 'src/environments/environment.prod';
faCircle;
declare let docx;
declare let saveAs;
@Component({
  selector: 'app-gallery-preview',
  templateUrl: './gallery-preview.component.html',
  styleUrls: ['./gallery-preview.component.scss'],
})
export class GalleryPreviewComponent implements OnInit, OnDestroy {
  isMobile = false;
  faCircle = faCircle;

  openArtwork;
  selectedArtwork = 0;

  status = 'Sold';
  previewLinkObj: any = {};
  previewLinkObj2: any = {};

  primary_images = [];
  primary_imagesBlobs = [];
  imageSizes = [];

  logoImage;

  constructor(
    private server: CollectorService,
    private footerService: FooterService,
    private navBarService: NavBarService,
    private titleService: Title,
    private metaService: Meta,
    private Activatedroute: ActivatedRoute,
    @Inject(PLATFORM_ID) private platformId: any,
    private toastService: ToastService
  ) {
    //  document.addEventListener('contextmenu', event => event.preventDefault());
  }
  bannerCss = {
    color: 'white',
    'background-color': 'black',
  };
  // 'height':'34.722vw'
  ItemWidth = 100 / 4 - 2 + '%';

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.addScripts();
      this.footerService.changeFooterType(FooterType.HIDE);
      this.navBarService.changeNavbarType(NavBarType.HIDE);
      this.getIsMobile(document.documentElement.clientWidth);
      // this.server
      //   .getImage(
      //     `http://localhost:4200/assets/images/logo.png`
      //   )
      //   .subscribe((image) => {
      //     this.logoImage = image;

      //   });
    }


    this.getPreviewLinks();


  }

  ngOnDestroy(): void {
    this.footerService.changeFooterType(FooterType.DEFAULT);
    this.navBarService.changeNavbarType(NavBarType.DEFAULT);
  }
  addScripts() {
    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['docx']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src = 'https://unpkg.com/docx@5.5.0/build/index.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'doxc-sdk', this);
    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['saveAs']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src =
        'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'file-saver', this);
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    // event.target.innerWidth;
    const w = event.target.innerWidth;
    this.isMobile = this.getIsMobile(w);
  }

  public getIsMobile(w): boolean {
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  // to get preview links
  getPreviewLinks() {
    this.Activatedroute.queryParamMap.subscribe(async (params) => {
      if (params.get('private_id')) {

        this.getPrivateViewByID(params.get('private_id'));
      }

    });

  }

  // to get private view by ID
  getPrivateViewByID(preview_id) {
    let url = apiUrl.artworks.createPrivate + `/${preview_id}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.previewLinkObj2 = res.data;
        this.setSEOTags();
        if (isPlatformBrowser(this.platformId)) {
          this.previewLinkObj = res.data;
          this.primary_images = Array(this.previewLinkObj?.artworkIds).fill(
            null
          );
          this.primary_imagesBlobs = Array(this.previewLinkObj?.artworkIds).fill(
            null
          );
          this.imageSizes = Array(this.previewLinkObj?.artworkIds).fill(null);
          let loadedImages = 0;
          this.toastService.createToast(`Loading Artwork Images 1/${this.previewLinkObj?.artworkIds?.length}..`, 3000, 2, true);
          this.previewLinkObj?.artworkIds.forEach((ele, i) => {
            this.server
              .getImage(
                'https://www.terrain.art/cdn-cgi/image/width=' +
                Math.floor(
                  1600 / Number(this.previewLinkObj?.contents[0]?.data?.grid)
                ).toString() +
                ',quality=72/' +
                ele?.primary_image[0]?.url
              )
              .subscribe((image) => {
                loadedImages++;
                this.toastService.createToast(`Loading Artwork Images ${loadedImages}/${this.previewLinkObj?.artworkIds?.length}..`, 3000, 2, true);
                if (loadedImages >= this.previewLinkObj?.artworkIds?.length) {
                  this.toastService.createToast(`Loading Artwork Images ${loadedImages}/${this.previewLinkObj?.artworkIds?.length}..`, 3000, 2, false);
                }
                this.primary_imagesBlobs[i] = image;
                this.createImageFromBlob(image, i);
              });
          });
        }


      }
    });
  }
  createImageFromBlob(image: Blob, i: number) {
    let reader = new FileReader();
    reader.addEventListener(
      'load',
      () => {
        this.primary_images[i] = reader.result;
        var img = new Image();

        img.onload = () => {
          this.imageSizes[i] = { width: img.width, height: img.height };
        };

        img.src = reader.result as string;
      },
      false
    );

    if (image) {
      reader.readAsDataURL(image);
    }
  }

  async generateDocx() {
    const doc = new docx.Document();


    // Create header and footer
    // const header = new docx.Header();
    // header.add('Header on First Page');

    // const footer = new docx.Footer();
    // footer.add('Footer on Last Page');

    // // Add header to the first page
    // doc.addSection({
    //   headers: {
    //     default: header,
    //     first: header,
    //   },
    //   footers: {
    //     default: footer,
    //     first: footer,
    //     even: footer,
    //     odd: footer,
    //   },
    // });

    const cellBoarderOptions = {
      style: docx.BorderStyle.NONE,
      color: 'FFFFFF',
      size: 0,
    };
    const cellBoarder = {
      top: cellBoarderOptions,
      bottom: cellBoarderOptions,
      left: cellBoarderOptions,
      right: cellBoarderOptions,
    };
    const headerMargins = {
      top: 200,
      bottom: 200,
    };

    const tableRows = [];

    const grid = Number(this.previewLinkObj?.contents[0]?.data?.grid);

    for (let i = 0; i < this.previewLinkObj?.artworkIds.length; i += grid) {
      const tableCell = [];
      const tableCellimage = [];
      for (let j = 0; j < grid; j++) {
        const element = this.previewLinkObj?.artworkIds[i + j];
        if (!element) {
          break;
        }
        let image_width, image_height;
        if (this.imageSizes[i + j].height < this.imageSizes[i + j].width) {
          image_width = Math.floor(700 / grid);
          image_height = Math.floor(
            (this.imageSizes[i + j].height * Math.floor(700 / grid)) /
            this.imageSizes[i + j].width
          );
        } else {
          image_width = Math.floor(
            (this.imageSizes[i + j].width * Math.floor(700 / grid)) /
            this.imageSizes[i + j].height
          );
          image_height = Math.floor(700 / grid);
        }
        const image = docx.Media.addImage(
          doc,
          this.primary_imagesBlobs[i + j],
          image_width,
          image_height
        );
        const cellContents = [];
        tableCellimage.push(
          new docx.TableCell({
            children: [
              new docx.Paragraph({
                children: [image],
                alignment: docx.AlignmentType.CENTER,
              }),
            ],
            width: {
              size: Math.floor(95 / grid),
              type: docx.WidthType.PERCENTAGE,
            },
            margins: { top: 200, bottom: 0 },
            borders: cellBoarder,
            verticalAlign: docx.VerticalAlign.CENTER,
          })
        );
        // cellContents.push(new docx.Paragraph({
        //   children: [image],
        //   alignment: docx.AlignmentType.CENTER,
        // }));
        cellContents.push(
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: '',
                bold: true,
                color: '000000',
                size: 30,
              }),
            ],
            alignment: docx.AlignmentType.LEFT,
            spacing: { after: 100 },
          })
        );
        cellContents.push(
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: element.artwork_title,
                italics: true,
                color: '000000',
                size: 24,
              }),
              new docx.TextRun({
                text: ', ' + element.year,
                color: '000000',
                size: 24,
              }),
            ],
            alignment: docx.AlignmentType.LEFT,
            spacing: { after: 100 },
          })
        );
        cellContents.push(
          new docx.Paragraph({
            children: [
              new docx.TextRun({
                text: element.artist_id?.display_name,
                bold: true,
                color: '000000',
                size: 26,
              }),
            ],
            alignment: docx.AlignmentType.LEFT,
            spacing: { after: 100 },
          })
        );
        if (
          this.previewLinkObj?.contents[0]?.data?.showMedium &&
          element.medium
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element.medium,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showDimensions &&
          element.dimensions
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element.dimensions,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showSalePrice &&
          element.sale_price
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: '₹' + this.numberWithCommas(element?.sale_price),
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showSignature &&
          element.signature_details
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element.signature_details,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showCatalogId &&
          element.catalog_number
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element.catalog_number,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showAvailability &&
          element.sale_options
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element?.sale_options,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }

        if (
          this.previewLinkObj?.contents[0]?.data?.showLocation && element?.extras?.isGallerWearhouse != 2 &&
          element.location
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element?.location,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }


        if (
          this.previewLinkObj?.contents[0]?.data?.showLocation && element?.extras?.isGallerWearhouse == 1 &&
          element?.extras?.storageAddress
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element?.extras?.storageAddress,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showLocation && element?.extras?.isGallerWearhouse == 2 &&
          element?.extras?.artistAddress?.title
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: `${element?.extras?.artistAddress?.title} - ${element?.extras?.artistAddress?.city}`,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showCopyrightLine &&
          element.credit_line
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: element?.credit_line,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showCondition &&
          element?.extras?.packaging
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: ` Packaging: ${element?.extras?.packaging}`,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showCondition &&
          element?.extras?.installState
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: ` Install State: ${element?.extras?.installState}`,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }
        if (
          this.previewLinkObj?.contents[0]?.data?.showCondition &&
          element?.current_state
        ) {
          cellContents.push(
            new docx.Paragraph({
              children: [
                new docx.TextRun({
                  text: ` Condition: ${element?.current_state}`,
                  color: '000000',
                  size: 24,
                }),
              ],
              alignment: docx.AlignmentType.LEFT,
              spacing: { after: 50 },
            })
          );
        }

        tableCell.push(
          new docx.TableCell({
            children: cellContents,
            width: {
              size: Math.floor(95 / grid),
              type: docx.WidthType.PERCENTAGE,
            },
            margins: { top: 0, bottom: 200 },
            borders: cellBoarder,
          })
        );
      }

      tableRows.push(
        new docx.TableRow({
          children: tableCellimage,
          cantSplit: true,
        })
      );
      tableRows.push(
        new docx.TableRow({
          children: tableCell,
          cantSplit: true,
        })
      );
    }
    console.log(tableRows);
    const table = new docx.Table({
      rows: tableRows,
      width: {
        size: 100,
        type: docx.WidthType.PERCENTAGE,
      },
    });
    const table2 = new docx.Table({
      rows: [
        new docx.TableRow({
          children: [new docx.TableCell({
            children: [
              new docx.Paragraph({
                children: [docx.Media.addImage(
                  doc,
                  this.logoImage,
                  210,
                  36
                )],
                alignment: docx.AlignmentType.CENTER,
              }),
            ],
            width: {
              size: 100,
              type: docx.WidthType.PERCENTAGE,
            },
            margins: { top: 200, bottom: 0 },
            borders: cellBoarder,
            verticalAlign: docx.VerticalAlign.CENTER,
          })],
          cantSplit: true,
        })
      ],
      width: {
        size: 100,
        type: docx.WidthType.PERCENTAGE,
      },
    });
    doc.addSection({
      children: [new docx.Paragraph({
        children: [docx.Media.addImage(
          doc,
          this.logoImage,
          280,
          48
        )],
        alignment: docx.AlignmentType.LEFT,
        spacing: { after: 200 },
      }), table],
      margins: { left: 500, right: 500 },
    });



    docx.Packer.toBlob(doc).then((blob) => {
      saveAs(blob, 'artworks.docx');
      console.log('Document created successfully');
    });
  }

  numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  checkPublishAdditionalMedia(item) {

    let index = item.find((a) => {
      return (typeof a?.publish === 'undefined') ? true : a?.publish
    })


    if (index) {
      return true
    } else {
      return false
    }
  }
  getSaleInformation(art): string {
    switch (art.sale_options) {
      case 'NotForSale':
        return `${art?.extras?.notForSaleReason} - ${art?.extras?.notForSaleDate}`;
      case 'Sold':
        return `${art?.extras?.soldTo} - ${art?.extras?.soldDate} - ${art?.extras?.soldBy}`;
      case 'Reserved':
        return `${art?.extras?.reservedFor} - ${art?.extras?.reservedDate} - ${art?.extras?.reservedBy}`;
      case 'ReturnedToArtist':
        return `${art?.extras?.returnedToArtistReason} - ${art?.extras?.returnedDate}`;
      case 'OnLoan':
        return `${art?.extras?.loanDetails} - From:${art?.extras?.startDate} - To:${art?.extras?.endDate}`;
      default:
        break;
    }
    return '';
  }

  setSEOTags() {
    const websiteName = 'Terrain.Art';
    const maxLength = 50;
    const separator = ' | ';

    let pageTitle = `${this.previewLinkObj2?.title}${separator}${websiteName}`;

    if (pageTitle.length > maxLength) {
      const availableLength =
        maxLength - (separator.length + websiteName.length + 3);
      pageTitle = `${this.previewLinkObj2?.title.substring(
        0,
        availableLength
      )}...${separator}${websiteName}`;
    }

    let pageDesc = `A selection of artworks from www.terrain.art`;

    if (pageDesc.length > 150) {
      pageDesc = pageDesc.substring(0, 147) + '...';
    }

    this.titleService.setTitle(pageTitle);


    this.metaService.updateTag(
      {
        content: pageDesc,
      },
      'name="description"'
    );

    this.metaService.updateTag(
      {
        content: 'product',
      },
      'property="og:type"'
    );
    this.metaService.updateTag(
      {
        content:
          'https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
          this.previewLinkObj2?.artworkIds[0]?.primary_image[0]?.url,
      },
      'property="og:image"'
    );
    this.metaService.updateTag(
      {
        content: pageTitle,
      },
      'property="og:title"'
    );
    this.metaService.updateTag(
      {
        content: pageDesc,
      },
      'property="og:description"'
    );

  }

  //   getSelectedArtwork() {
  // return this.selectedArtwork;
  //   }
}
