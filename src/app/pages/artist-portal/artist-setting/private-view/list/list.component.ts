import { ToastService } from '../../../../../core/toast/toast.service';
import { FooterService, FooterType } from './../../../../../shared/services/footer.service';
import { faFlag as faFlagasOutlineFlag } from '@fortawesome/free-regular-svg-icons';
import { Component, OnInit } from '@angular/core';
import { faAngleDown, faAngleLeft, faAngleRight, faFlag, faPencilAlt, faPlusCircle } from '@fortawesome/free-solid-svg-icons';
import { faCopy } from '@fortawesome/free-regular-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { Router } from '@angular/router';
faPlusCircle
@Component({
  selector: 'app-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss']
})

export class ListComponent implements OnInit {
  faPlusCircle = faPlusCircle
  faCopy = faCopy;
  collectionsArray = [{}, {}, {}, {}, {}]
  inputItem = [{}, {}, {}]
  createCollection = false;
  isCreateCollectionModalOpen = false;
  radio = false;
  ExistingArtworkSelect: any = '';
  isDropDownOpen = Array(10).fill(false);
  isModalOpen = false;
  PageType = "Gallery Previews";
  PageTypeDrop = false;
  isFlagFilter = false;
  totalPage: number = 0;
  Category = "All";
  categoryDrop = false;
  faFlag = faFlag;
  faFlagasOutlineFlag = faFlagasOutlineFlag;
  temporary2 = [{ 'id': 1 }, { 'id': 2 }, { 'id': 3 }, { 'id': 4 }, { 'id': 5 }, { 'id': 6 }, { 'id': 7 }, { 'id': 8 }];
  faPencilAlt = faPencilAlt;
  faAngleLeft = faAngleLeft;
  faAngleRight = faAngleRight;
  faCartArrowDown = faAngleDown;
  flaggedArtworkArray = [];
  list_view_drop = Array(20).fill(false);
  offset: any = 1
  privateListArr: any = [];
  collectionLinksArr: any = []
  totalRecords: any = 0;
  limit: any = 10;
  flaggedLinksArr: any = [];
  collectionObj: any = {
    'live': false,
    'title': '',
    'description': '',
  }
  flag: any = 'all';
  ExistingArtworkSelectObj: any = {};
  constructor(private footerservice: FooterService, private server: CollectorService, private router: Router, private toastService: ToastService) { }
  searchValue;
  ngOnInit(): void {
    this.footerservice.changeFooterType(FooterType.HIDE);
    this.getPrivateList()
    this.getCollections()
  }
  copyLink(link) {
    navigator.clipboard.writeText(link);
    this.toastService.createToast('Url Copied !');
  }
  // to get private list
  getPrivateList() {
    let req = {
      "privateView_ids": [],
      "collectionId": "",
      "offset": this.offset,
      "limit": 10,
      "search": "",
      "userId": "",
      "flag": this.flag
    }
    let url = apiUrl.artworks.getPrivateList
    this.server.showSpinner();
    this.server.postApi(url, req).subscribe(res => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.privateListArr = res.data
        this.totalRecords = res.total;
        this.totalPage = Math.ceil(this.totalRecords / this.limit)
        this.offset = res.page;
        this.limit = res.perPage
        this.addToFlagArr()
      }
    })
  }

  // to add into flag array
  addToFlagArr() {
    this.privateListArr.forEach(item => {
      if (item.flag) {
        let index = this.flaggedLinksArr.findIndex(x => x['_id'] == item['_id'])
        if (index == -1) {
          this.flaggedLinksArr.push(item)
        }
      }
    });
  }

  applyFilter(action) {
    if (action == 'findFlag') {
      this.flag = 'flag';
    } else if (action == 'unFlagAll') {              // for unflag
      this.manageFlaggedApi(action, 'removeAll', [])
      this.flag = 'all'
      return;
    } else if (action == 'addToFlagged') {           // add to flag
      let id = []
      this.flaggedLinksArr.forEach(ele => {
        id.push(ele['_id'])
      });
      this.manageFlaggedApi(action, 'add', id)
      this.flag = null
      return;
    } else if (action == 'substract') {
      let id = []
      this.flaggedLinksArr.forEach(ele => {
        id.push(ele['_id'])
      });
      this.manageFlaggedApi(action, 'remove', id)
      this.flag = null
      return;
    } else if (action == 'findUnFlagged') {
      this.flag = 'unflag'
    }
    this.totalPage = 0;
    this.totalRecords = 0;
    this.offset = 1;
    this.getPrivateList()
  }

  // to find all
  findAll() {
    this.flag = null;
    this.totalPage = 0;
    this.totalRecords = 0;
    this.offset = 1;
    this.getPrivateList()
  }

  // to add to flagged
  manageFlaggedApi(filter, action, id) {
    let url = apiUrl.collection.markFlag + `?action=${action}`
    let req = {
      "gallery_previewIds": [...id]
    }
    this.server.postApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        alert(res.message)
        if (filter == ('unFlagAll' || 'substract')) {
          this.flaggedLinksArr = []
        }
        this.totalPage = 0;
        this.totalRecords = 0;
        this.offset = 1;
        this.getPrivateList()
      }
    })

  }

  // to manage pagination
  managePagination(action) {
    if (action == 'prev') {
      if (this.offset > 1) {
        this.offset = this.offset - 1
      } else {
        return;
      }
    } else if (action == 'next') {
      if (this.offset < this.totalPage) {
        this.offset = this.offset + 1
      } else {
        return;
      }
    }
    this.getPrivateList()
  }

  ngOnDestroy(): void {
    //Called once, before the instance is destroyed.
    //Add 'implements OnDestroy' to the class.
    this.footerservice.changeFooterType(FooterType.DEFAULT);
  }
  searchResult() {

  }
  // isFlagged(id) {
  //   return this.flaggedArtworkArray.includes(id);
  // }
  Flagged(id) {
    if (this.flaggedArtworkArray.includes(id)) {
      let index = this.flaggedArtworkArray.indexOf(id);
      if (index !== -1) {
        this.flaggedArtworkArray.splice(index, 1);
      }
      return 'false'
    }
    else {
      this.flaggedArtworkArray.push(id);
      return true;
    }

  }
  flagfilter() {
    setTimeout(() => {
      this.isFlagFilter = false;
    }, 200);
  }

  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }

  modalAction(condition) {
    console.log(condition)
    if (condition) {
      this.addToExistingCollection()
    }
    else {
      this.isModalOpen = false;
      this.isCreateCollectionModalOpen = true;
    }
  }

  // add to existing collection
  addToExistingCollection() {
    let url = apiUrl.collection.update + `/${this.ExistingArtworkSelectObj['_id']}`
    this.flaggedLinksArr.forEach(ele => {
      this.ExistingArtworkSelectObj['private_linkIds'].push(ele['_id'])
    });
    let req = {
      "collectionStatus": "draft",
      "visibility": "public",
      "title": this.ExistingArtworkSelectObj.title,
      "description": this.ExistingArtworkSelectObj.description,
      "private_linkIds": this.ExistingArtworkSelectObj['private_linkIds']
    }
    this.server.patchApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        this.isModalOpen = false;
        alert(res.message)
      }
    })
  }
  // to manage flagging
  manageFlagging(item, index) {
    this.privateListArr[index].flag = !item.flag
    this.addToFlagArr()
  }

  // to create collection
  onCreateCollection() {
    let id_arr = []
    this.flaggedLinksArr.forEach(ele => {
      id_arr.push(ele['_id'])
    });
    let req = {
      "collectionStatus": "draft",
      "visibility": "public",
      "title": this.collectionObj.title,
      "description": this.collectionObj.description,
      "thumbnailImage": "https://ta-python.s3.us-east-2.amazonaws.com/1646730494924_thumbnail.jpg",
      "private_linkIds": id_arr
    }
    let url = apiUrl.collection.create
    this.server.postApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        alert(res.message);
        this.isCreateCollectionModalOpen = false
      }
    })
  }

  // to get collecton list
  getCollections() {
    let url = apiUrl.collection.getList
    let req = {
      "offset": this.offset,
      "limit": this.limit,
      "search": "",
      "userId": "",
      "updateTab": false,
      // "last_sync": "2022-03-25T03:34:58.970Z",
      "collection_ids": []
    }

    this.server.postApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        this.collectionLinksArr = res.data
        this.totalRecords = res.total;
        this.totalPage = Math.ceil(this.totalRecords / this.limit)
        this.offset = res.page;
        this.limit = res.perPage
      }
    })
  }

  // to manage page type
  managePageType(tab) {
    this.PageType = tab
    this.totalRecords = 0;
    this.totalPage = 0;
    this.offset = 1;
    if (tab == 'Gallery Previews') {
      this.getPrivateList()
    } else if (tab == 'Collections') {
      this.getCollections()
    }
  }

  // to navigate to url
  navigateTo(item) {
    console.log(item);

    // this.router.navigate(['/artist-portal/settings/artwork/private_view/edit', item['_id']]);
    // this.router.navigate([]).then(result => {  window.open('/artist-portal/settings/artwork/private_view/edit', item['_id'], '_blank'); });
    this.router.navigate([]).then(result => { window.open(`/artist-portal/settings/artwork/private_view/edit/${item['_id']}`, '_blank'); });

  }
  copyshortUrl(item) {
    if (item?.shortUrl) {
      this.copyLink('https://previews.terrain.art/' + item?.shortUrl)
    } else {
      this.copyLink(item?.privateViewLink)
    }
  }

}
