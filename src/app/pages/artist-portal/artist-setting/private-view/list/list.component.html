<div class="Content-Wrap">
  <div class="head-section">
    <div class="d-flex j-content-space-bt">
      <div class="part1">
        <div class="page-title">
          <div class="d-flex itlt">
            <div
              [innerHtml]="PageType"
              style="padding-right: 1vw; cursor: default"
            ></div>
            <div style="position: relative; cursor: pointer">
              <fa-icon
                [icon]="faCartArrowDown"
                (click)="PageTypeDrop = !PageTypeDrop"
              >
              </fa-icon>
              <div
                class="drop-for-category"
                [hidden]="!PageTypeDrop"
                (mouseleave)="PageTypeDrop = false"
              >
                <div
                  class="drop-for-category-item"
                  (click)="managePageType('Gallery Previews')"
                >
                  Preview Links
                </div>
                <div
                  class="drop-for-category-item"
                  (click)="managePageType('Collections')"
                >
                  Preview Group
                </div>
              </div>
            </div>
          </div>

          <span class="with-carret d-flex">
            <div
              [innerHtml]="Category"
              style="margin-right: 0.5vw; cursor: default"
            >
              All
            </div>
            <div style="position: relative">
              <fa-icon
                [icon]="faCartArrowDown"
                style="cursor: pointer"
                (click)="categoryDrop = !categoryDrop"
              >
              </fa-icon>
              <div
                class="drop-for-category"
                [hidden]="!categoryDrop"
                (mouseleave)="categoryDrop = false"
              >
                <div class="drop-for-category-item" (click)="Category = 'All'">
                  All
                </div>
                <div
                  class="drop-for-category-item"
                  (click)="Category = 'Active'"
                >
                  Active
                </div>
                <div
                  class="drop-for-category-item"
                  (click)="Category = 'Expired'"
                >
                  Expired
                </div>
                <div
                  class="drop-for-category-item"
                  (click)="Category = 'Published to iPad/ iPhone'"
                >
                  Published to iPad/ iPhone
                </div>
              </div>
            </div>
          </span>
        </div>
      </div>
      <div class="part2">
        <div class="search-filter">
          <input
            type="text"
            placeholder="Search by Title"
            name="search"
            [(ngModel)]="searchValue"
            [ngModelOptions]="{ standalone: true }"
            (keyup)="searchResult()"
          />
        </div>
      </div>
    </div>
    <div
      class="d-flex j-content-space-bt view-actions"
      style="flex-wrap: nowrap"
    >
      <div class="d-flex" style="gap: 2vw">
        <span class="records">found 4 records</span>
        <span class="d-flex" style="gap: 0.7vw">
          <fa-icon
            [icon]="faAngleLeft"
            (click)="managePagination('prev')"
          ></fa-icon>
          Page
          <input
            style="height: 1vw; width: 1vw"
            type="number"
            [max]="totalPage"
            [value]="offset"
            min="1"
            name="pager"
            id="pager"
          />
          Of {{ totalPage }}
          <fa-icon
            [icon]="faAngleRight"
            (click)="managePagination('next')"
          ></fa-icon>
        </span>
        <span style="cursor: pointer" (click)="findAll()"> Find ALL</span>
      </div>
      <div class="d-flex">
        <div style="position: relative">
          <div
            class="flgSpan"
            tabindex="1"
            (mouseover)="isFlagFilter = !isFlagFilter"
          >
            Flag Options
            <fa-icon
              [ngStyle]="{
                color: isFlagFilter ? '#004ddd' : '#bfbfbf'
              }"
              [icon]="isFlagFilter ? faFlag : faFlagasOutlineFlag"
            >
            </fa-icon>
          </div>

          <div
            class="filter-div-for-flag"
            [hidden]="!isFlagFilter"
            (mouseleave)="flagfilter()"
          >
            <ul>
              <li (click)="applyFilter('findFlag')" class="pointer">
                Find Flagged
              </li>
              <li (click)="applyFilter('unFlagAll')" class="pointer">
                Unflag all
              </li>
              <li (click)="applyFilter('addToFlagged')" class="pointer">
                Add to flagged
              </li>
              <li (click)="isModalOpen = true" class="pointer">
                Add to Collection
              </li>
              <li (click)="applyFilter('findUnFlagged')" class="pointer">
                Find unflagged
              </li>
              <li class="pointer">Replace flagged</li>
              <li (click)="applyFilter('substract')" class="pointer">
                Subtract from flagged
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="content-table" *ngIf="PageType == 'Gallery Previews'">
    <table style="width: 100%">
      <thead>
        <tr>
          <th style="width: 17%; text-overflow: ellipsis">Title</th>
          <th style="width: 13%; text-overflow: ellipsis">Created By</th>
          <th style="width: 15%; text-overflow: ellipsis; text-align: center">
            Preview Group Names
          </th>
          <th style="width: 20%; text-overflow: ellipsis; text-align: center">
            Date Created
          </th>
          <th style="width: 25%; text-overflow: ellipsis; text-align: center">
            Visit
          </th>
          <th style="width: 5%; text-overflow: ellipsis">Action</th>
          <th style="width: 10%; text-overflow: ellipsis"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of privateListArr; let i = index">
          <td class="table-data">{{ item?.title }}</td>
          <td class="table-data">{{ item?.createdBy?.display_name }}</td>
          <td class="table-data">{{ item?.collectionName }}</td>
          <td class="table-data" style="text-align: center">
            {{ item?.createdAt | date : "medium" }}
          </td>
          <td class="table-data" style="text-align: center">
            <a [href]="item?.privateViewLink" target="_blank">{{
              item?.privateViewLink.length > 10
                ? item?.privateViewLink.slice(0, 30) + "..."
                : item?.privateViewLink
            }}</a>
            <fa-icon
              class="copy-hover"
              style="margin-left: 0.5vw; cursor: pointer"
              [icon]="faCopy"
              (click)="copyshortUrl(item)"
            ></fa-icon>
          </td>
          <td class="table-data" style="position: relative">
            <span (click)="list_view_drop[i] = !list_view_drop[i]"
              ><a target="_blank" [href]="item?.privateViewLink">View</a>
            </span>
            <div
              class="drop-for-view"
              [hidden]="!list_view_drop[i]"
              (mouseleave)="list_view_drop[i] = false"
            >
              <div class="drop-for-view-item">Find in artworks</div>
              <div class="drop-for-view-item">View in logs</div>
            </div>
          </td>
          <td class="d-flex table-data">
            <fa-icon
              style="margin-right: 1vw"
              [icon]="faPencilAlt"
              (click)="navigateTo(item)"
            ></fa-icon>
            <fa-icon
              class="pointer"
              [ngStyle]="{
                color: item?.flag ? '#004ddd' : '#bfbfbf'
              }"
              [icon]="item?.flag ? faFlag : faFlagasOutlineFlag"
              (click)="manageFlagging(item, i)"
            ></fa-icon>
            <!-- <fa-icon [icon]="faFlagasOutlineFlag"></fa-icon> -->
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="content-table photo-book" *ngIf="PageType !== 'Gallery Previews'">
    <div class="containerr">
      <div class="thumb album-thumb" *ngFor="let item of collectionLinksArr">
        <div class="thumb-container">
          <div class="images-container">
            <img class="thumb-image" [src]="item?.thumbnailImage" />
            <img class="thumb-image" [src]="item?.thumbnailImage" />
            <img class="thumb-image" [src]="item?.thumbnailImage" />
          </div>
          <!-- <div class='photo-count'>
            <div class='content'>
              <div class='number'>120</div>
              <div class='label'>PHOTOS</div>
            </div>
          </div> -->
        </div>
        <div class="title">
          {{ item?.title }}
        </div>
      </div>
      <div class="thumb album-thumb">
        <div class="thumb-container">
          <div
            class="images-container d-flex"
            style="
              justify-content: center;
              align-items: center;
              border: 0.5vw dashed var(--timeline-color);
              border-radius: 2vw;
            "
            (click)="isCreateCollectionModalOpen = true"
          >
            <span
              ><fa-icon [icon]="faPlusCircle"></fa-icon> Create a new
              collection</span
            >
            <!-- <img class='thumb-image' src='https://images.unsplash.com/photo-1505388763672-ee96ba65bac8?ixlib=rb-0.3.5&amp;ixid=eyJhcHBfaWQiOjEyMDd9&amp;s=aa2b17198c95694b3f90d9e8681d66bc&amp;auto=format&amp;fit=crop&amp;w=1950&amp;q=80'>
            <img class='thumb-image' src='https://images.unsplash.com/photo-1505388763672-ee96ba65bac8?ixlib=rb-0.3.5&amp;ixid=eyJhcHBfaWQiOjEyMDd9&amp;s=aa2b17198c95694b3f90d9e8681d66bc&amp;auto=format&amp;fit=crop&amp;w=1950&amp;q=80'>
            <img class='thumb-image' src='https://images.unsplash.com/photo-1505388763672-ee96ba65bac8?ixlib=rb-0.3.5&amp;ixid=eyJhcHBfaWQiOjEyMDd9&amp;s=aa2b17198c95694b3f90d9e8681d66bc&amp;auto=format&amp;fit=crop&amp;w=1950&amp;q=80'> -->
          </div>
          <!-- <div class='photo-count'>
            <div class='content'>
              <div class='number'>120</div>
              <div class='label'>PHOTOS</div>
            </div>
          </div> -->
        </div>
        <!-- <div class='title'>
          Senorita Arts
        </div> -->
      </div>
    </div>
  </div>

  <div class="footer-part">
    <hr />
    <button
      class="button-type"
      style="margin-right: 2vw"
      (click)="isCreateCollectionModalOpen = true"
    >
      New Collection
    </button>
    <button class="button-type" (click)="managePageType('Gallery Previews')">
      New Preview Links
    </button>
  </div>

  <div class="popup-for-confirmation" [hidden]="!isModalOpen">
    <div class="popup-card">
      <p style="font-size: 1.25vw; margin-bottom: 1.5vw; display: block">
        Choose Preview Group type
      </p>
      <span style="margin-right: 1vw"
        ><label for="AddTo"
          ><input
            type="radio"
            [(ngModel)]="radio"
            [value]="true"
            name="radd"
            id="AddTo"
          />
          Add to existing Preview Group</label
        ></span
      >
      <span
        ><label for="ColNew"
          ><input
            type="radio"
            [(ngModel)]="radio"
            [value]="false"
            name="radd"
            id="ColNew"
          />
          Create new Preview Group</label
        ></span
      >
      <div class="splitter">
        <div
          class="field-value"
          style="padding-right: 0.5vw; width: 75%"
          [hidden]="!radio"
        >
          <div class="input-container" (focusout)="changeFocus(0)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option"
              (focus)="isDropDownOpen[0] = true"
              [(ngModel)]="ExistingArtworkSelect"
              readonly
            />
            <div class="placeholder">Existing Preview Group</div>
            <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>
            <div class="dropdown-visible" [hidden]="!isDropDownOpen[0]">
              <ul>
                <li
                  style="cursor: pointer"
                  (click)="
                    ExistingArtworkSelectObj = option;
                    ExistingArtworkSelect = option.title;
                    isDropDownOpen[0] = false
                  "
                  *ngFor="let option of collectionLinksArr"
                >
                  <div class="country-name">{{ option?.title }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Choose from existing collection to add artworks
          </div>
        </div>
      </div>
      <div
        class="buttonList"
        style="
          margin-top: 2.042vw;
          margin-bottom: 0;
          display: flex;
          justify-content: space-around;
        "
      >
        <button
          class="save"
          style="width: 35%; padding: 0.833333vw 3.08333vw"
          (click)="isModalOpen = false"
        >
          Cancel
        </button>
        <button
          class="save"
          style="width: 35%; padding: 0.833333vw 3.08333vw"
          (click)="isModalOpen = false"
          [innerHtml]="radio ? 'Add ' : 'Next'"
          (click)="modalAction(radio)"
        ></button>
      </div>
    </div>
  </div>
  <div class="popup-for-confirmation" [hidden]="!isCreateCollectionModalOpen">
    <div class="popup-card-collection">
      <p style="font-size: 1.25vw; margin-bottom: 1.5vw; display: block">
        Create a new collection
      </p>
      <div class="tabbex">
        <div class="tabPart1"></div>
        <div class="tabPart2">
          <label for="checkkk">
            <input
              type="checkbox"
              name="checkkk"
              id="checkkk"
              [(ngModel)]="collectionObj.live"
            />This Preview Group is live</label
          >
          <p>
            Preview Group which are not live will not synchronize to your iPad
            or be visible in 'locked' mode
          </p>
        </div>
      </div>
      <div class="tabbex">
        <div class="tabPart1">Title</div>
        <div class="tabPart2">
          <input
            type="text"
            name="Title"
            id="Title"
            [(ngModel)]="collectionObj.title"
          />
        </div>
      </div>
      <div class="tabbex">
        <div class="tabPart1">Description</div>
        <div class="tabPart2">
          <angular-editor
            id="editor35"
            [(ngModel)]="collectionObj.description"
          ></angular-editor>
        </div>
      </div>
      <div class="tabbex">
        <div class="tabPart1">Linked Gallery Previews</div>
        <div class="tabPart2">
          <!--   <div class="part12">
          <p class="name-ff">Title</p>
          <input type="text" name="fie" class="input-ff" id="fie" />
        </div>
        <div class="part12" style="margin: 0 .1vw;" > -->
          <p class="name-ff">Gallery Preview URL/Link</p>
          <input
            type="text"
            name="fie"
            [value]="item?.privateViewLink"
            class="input-ff"
            [id]="i"
            *ngFor="let item of flaggedLinksArr; let i = index"
            style="margin-bottom: 0.2vw"
          />
          <span
            style="
              color: var(--tertiary-font-color);
              margin-top: 1vw;
              cursor: pointer;
            "
          >
            <fa-icon [icon]="faPlusCircle"></fa-icon> <del>Add URL</del></span
          >
          <!-- </div>
        <div class="part12">
          <p class="name-ff">'Email To' address</p>
          <input type="text" name="fie" class="input-ff" id="fie" />
        </div> -->
        </div>
      </div>

      <div
        class="buttonList"
        style="
          margin-top: 2.042vw;
          margin-bottom: 0;
          display: flex;
          justify-content: space-around;
        "
      >
        <button
          class="save"
          style="width: 35%; padding: 0.833333vw 3.08333vw"
          (click)="isCreateCollectionModalOpen = false"
        >
          Cancel
        </button>
        <button
          class="save"
          style="width: 35%; padding: 0.833333vw 3.08333vw"
          (click)="isModalOpen = false"
          (click)="onCreateCollection()"
        >
          Create
        </button>
      </div>
    </div>
  </div>
</div>

<!-- <toast></toast> -->
