.Content-Wrap{
// margin: 0 8.333vw;
}
.head-section {
    // padding-top: 1vw;
    padding-bottom: 1vw;
}
.content-table{
  display: block;
    height: 55vh;
    overflow-y: scroll;
}






.photo-book{
  // @import url(https://fonts.googleapis.com/css?family=Roboto);

  margin-top: 2vw;
// * {
//   font-family: Roboto;
  font-size: 0.9027vw;
// }

.containerr {
  // padding: 50px;
  // margin: 0 auto;
  // text-align: center;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 3vw;

}

.album-thumb {
  display: inline-block;
  // border: 1px solid #eaeaea;
  position: relative;

  .thumb-container {
    position: relative;
    padding: 1.388vw;
    cursor: pointer;

    &:hover {
      .photo-count {
        background: rgba(43, 43, 43, 0.9);
      }
      .thumb-image:nth-child(1) {
        transform: rotateZ(0deg);
      }

      .thumb-image:nth-child(2) {
        transform: rotateZ(0deg);
      }
    }
  }

  .photo-count {
    width: 5.555vw;
    height: 5.555vw;
    background: rgba(43, 43, 43, 0.4);
    border-radius: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

    .content {
      display: inline-block;
      color: #ffffff;
      text-align: center;
      margin-top: 50%;
      width: 100%;
      transform: translateY(-50%);

      .number {
        font-size: 2.083vw;
      }
    }
  }

  .title {
    color: #4f4f4f;
    margin: 0.6944vw;
    text-align: center;
  }
}

.images-container {
  width: 13.888vw;
  height: 13.888vw;
  position: relative;

  .thumb-image {
    max-width: 13.888vw;
    max-height: 13.888vw;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border: 0.06944vw solid #ffffff;
    box-shadow: 0 0.2777vw 0.2777vw rgba(0, 0, 0, 0.2);
    transition: transform 0.5s;

    &:nth-child(1) {
      transform: rotateZ(10deg);
    }

    &:nth-child(2) {
      transform: rotateZ(4deg);
    }
  }
}

}











.d-flex {
  display: flex;
}
.search-filter {
  color: var(--quaternary-font-color);
  font-size: var(--default-font-size);
  input[type="text"] {
    padding: 0.7vw 1.32vw 0.7vw 1vw;
    -o-object-fit: contain;
    object-fit: contain;
    outline: none;
    width: 100%;
    font-family: Arial, FontAwesome;
    border-radius: 0.5vw;
    font-weight: 500;
    // margin-bottom: 2.08vw;
    color: var(--quaternary-font-color);
    height: 2.8vw;
    border: solid 0.07vw var(--secondary-font-color);
    text-indent: 1.73vw;
    background-position: 0 50%;
    background-repeat: no-repeat;
    background-position-x: 1.04vw;
    background-size: 1.04vw;
    background-image: url("../../../../../../assets/icons/search/<EMAIL>");
  }
  input[type="text"]:focus {
    background-image: none;
    text-indent: 0px;
  }
}
.page-title {
  text-transform: uppercase;
  letter-spacing: 0.06944vw;
  padding-left: 0.06944vw;
  text-align: left;
  font-size: 1.66vw;
  font-weight: 300;
  padding-bottom: 0.833vw;
  margin-bottom: 1.04vw;
  // margin-top: 0.4em;
  // line-height: 2.777vw;
  // line-height: 2.361vw;
  -webkit-font-smoothing: antialiased;
  display: flex;
  gap: 1vw;
}
.itlt{
    padding-right: 1vw;
    border-right: 0.1388vw solid var(--timeline-color);
}
.with-carret {
  color: #999;
}
.j-content-space-bt {
  justify-content: space-between;
}
.drop-for-category {
  position: absolute;
  width: 15vw;
  top: 2vw;
  right: -14.2vw;
  text-align: left;
  z-index: 1000;
  animation: stick 0.15s ease;
  font-size: 0.902vw;
  box-shadow: 0px 0.4166vw 0.833vw 0px rgb(50 50 50 / 7%);
  
  background: #ffffff;
  border: 0.06944vw solid #dadada;
  padding: 0;
  margin-bottom: 0;
  max-height: 12vw;
  overflow: auto;
  overflow-x: hidden;
  .drop-for-category-item {
    border-bottom: 0.06944vw solid var(--timeline-color);
    padding:0 0 0 1vw;
    cursor: pointer;
  }
}
.view-actions{
    text-transform: uppercase;
    font-size: 0.8333vw;
    font-weight: 500;
    letter-spacing: 0.06944vw;
    margin-right: 0.06944*2vw;
}
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0; 
}
th{
    padding: 1vw 0;

}
tr{
    border-bottom: 0.06944vw solid var(--timeline-color);
}
.table-data{
padding: .5vw 0;
}

.filter-div-for-flag {
  position: absolute;
  width: 12vw;
  max-height: 11.5vw;
  top: 2vw;
  right: 0;
  overflow-y: scroll;
  z-index: 1001;
  ul {
    background-color: white;
    list-style: none;
    padding: 0;
    li {
      font-size: 0.6944vw;
      padding: 0.5vw;
      border-bottom: 0.06944vw solid var(--timeline-color);
    }
    li:hover {
      background-color: rgba(128, 128, 128, 0.493);
    }
  }
}

.flgSpan {
  margin-right: 0.2vw;
  cursor: pointer;
  display: flex;
  gap: .2vw;
}

.button-type {
  display: inline-block;
  text-align: center;
  min-width: 4.444vw;
  background: transparent;
  font-size: 0.833vw;
  /* font-weight: 600; */
  text-transform: uppercase;
  letter-spacing: 0.0694vw;
  color: #555;
  padding: 0.486vw 0.833vw;
  border: 0.0694vw solid #c8c8c8;
  border-radius: 1.527vw;
  margin-top: 0.2083vw;
  margin-bottom: 0.2083vw;
}
.drop-for-view {
  position: absolute;
  width: 100%;
  top: 2vw;
  right: 6vw;
  // text-align: left;
  z-index: 1000;
  animation: stick 0.15s ease;
  font-size: 0.902vw;
  box-shadow: 0 0.4166vw 0.833vw 0px rgb(50 50 50 / 7%);
  
  background: #ffffff;
  border: 0.06944vw solid #dadada;
  padding: 0;
  margin-bottom: 0;
  max-height: 12vw;
  overflow: auto;
  overflow-x: hidden;
  .drop-for-view-item {
    border-bottom: 0.06944vw solid var(--timeline-color);
    padding:0 0 0 1vw;
  }
}
.popup-for-confirmation {
  // position: absolute;
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: white;

  .popup-card {
    width: 40vw;
    // height: 100%;
    margin: auto;
    margin-top: 10vw;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
      rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    border-radius: 1.5vw;
    padding: 3vw 2vw;
	display: flex;
	flex-direction: column;
  }
  .popup-card-collection{
    width: 60vw;
    margin: auto;
    margin-top: 4vw;
    margin-bottom: 4vw;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
      rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    border-radius: 1.5vw;
    padding: 3vw 2vw;
	display: flex;
	flex-direction: column;
  }
}
.buttonList {
  margin-top: 3.47vw;
  margin-bottom: 1.042vw;

  .save {
    // display: block;
    width: 100%;
    outline: none;
    font-size: 1.25vw;
    // width: 20.56vw;
    background-color: transparent;
    color: var(--tertiary-font-color);
    padding: 0.833333vw 12.08333vw;
    border: 0.069vw solid var(--tertiary-font-color);
    border-radius: 1.46vw;
  }
  .save:hover {
    font-weight: 500;
  }
}
.field-value {
  margin-top: 2.08vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;
  input[type='text'] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  textarea {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type='date'] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    color: var(--quaternary-font-color);
    //   ::placeholder{
    // color: var(--quaternary-font-color);
    // }
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  input[type='password'] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  .input-container {
    width: 100%;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    .text-before {
      @media (max-width: 768px) {
        font-size: 4.34vw;
      }
    }
    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }
    button {
      outline: none;
      background: none;
      border: none;
    }
    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 2.08vw;
      top: 0;
      bottom: 0;
      margin: auto 0;
      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }
    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }
    input[type='text'] {
      // background: transparent;
      font-family: var(--secondary-font);
      border: 0.069vw solid var(--timeline-color);
      padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      border-radius: 0.14vw;
      height: 3.47vw;
      width: 100%;
      z-index: 0;
      @media (max-width: 768px) {
        height: 11.35vw;
      }
      &.selection {
        padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }
    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;
      @media (max-width: 768px) {
        top: 10.47vw;
      }
      ul {
        list-style: none;
        padding: 0.69vw 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;
        @media (max-width: 768px) {
          padding: 1.69vw 0;
        }
        li {
          padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;
          @media (max-width: 768px) {
            padding: 1.69vw 1.04vw;
          }
          .country-name {
            margin-left: 0.69vw;
            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }
        li:hover {
          background-color: var(--timeline-color);
        }
      }
      ul::-webkit-scrollbar {
        display: none;
      }
    }
    .dropdown-hidden {
      display: none;
    }
  }
  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }
  .placeholder {
    position: absolute;
    top: -0.4vw;
    left: 1.04vw;
    font-size: 0.8333vw;
    color: var(--quaternary-font-color);
    padding: 0 0.3vw;
    background-color: var(--primary-background-color);
    // background-color: #ededf1;
    @media (max-width: 768px) {
      top: -1.8vw;
      left: 2.04vw;
      font-size: 3.38vw;
    }
  }
  .send {
    margin-left: 2.08vw;
    color: var(--tertiary-font-color);
  }
}
.splitter {
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
  .field-value {
    width: 50%;
    @media (max-width: 768px) {
      width: 100%;
    }
  }
}
.tabbex{
  display: flex;
  flex-direction: row;
  padding: 1vw 0;
  border-bottom: 0.069vw solid var(--timeline-color); ;
  .tabPart1{
width: 30%;
float: right;
  }
  .tabPart2{
width: 70%;
display: flex;
flex-direction: column;
.part12{
  display: flex;
  flex-direction: column;
  width: 33%;
  
  .name-ff{

  }
  .input-ff{

  }
}
  }
}
.copy-hover:hover{
color: var(--tertiary-font-color);
}