import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { PrivateViewRoutingModule } from './private-view-routing.module';
import { AllComponent } from './all/all.component';
import { ListComponent } from './list/list.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ImageUploadModule } from 'src/app/core/image-upload/image-upload.module';
import { MultiInputModule } from 'src/app/core/multi-input/multi-input.module';
import { PlaceholderModule } from 'src/app/core/placeholder/placeholder.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { GalleryPreviewComponent } from './gallery-preview/gallery-preview.component';
import { ArtShowComponent } from './art-show/art-show.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { ArtworkComponentComp } from './artwork/artwok.component';
import { GalleryPreview2Component } from './gallery-preview2/gallery-preview.component';

@NgModule({
  declarations: [
    AllComponent,
    ListComponent,
    GalleryPreviewComponent,
    ArtShowComponent,
    ArtworkComponentComp,
    GalleryPreview2Component,
  ],
  imports: [
    CommonModule,
    PrivateViewRoutingModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    AngularEditorModule,
    // NgxQRCodeModule,
    MultiInputModule,
    ImageUploadModule,
    PlaceholderModule,
  ],
  providers:[]
})
export class PrivateViewModule {}
