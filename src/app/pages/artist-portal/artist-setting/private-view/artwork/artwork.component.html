<div class="main-content">
  <div class="section section-margin-bottom" style="overflow-y: scroll">
    <div class="section-inner">
      <div class="artwork-sec">
        <div
          class="media-container"
          (scroll)="scrollHandler($event)"
          #mediaContainer
          [ngStyle]="isMobile ? {} : { width: hideInfo ? '95vw' : '56.67vw' }"
        >
          <div *ngIf="artworkData?.file_type == 'image'" class="media-item">
            <div
              class="media-content"
              [ngStyle]="isMobile ? {} : { height: hideInfo ? '80vh' : '60vh' }"
            >
              <div class="image-wrapper">
                <img
                  [src]="
                    get_url_extension(artworkData?.primary_image[0]?.url) !=
                    'gif'
                      ? cloudflareLink(artworkData?.artwork_type) +
                        artworkData?.primary_image[0]?.url
                      : artworkData?.primary_image[0]?.url
                  "
                />
              </div>
              <div
                class="banner-chip"
                *ngIf="
                  privateViewData?.contents[0]?.data?.showAvailability &&
                  (artworkData?.sale_options === 'Sold' ||
                    artworkData?.sale_options === 'Reserved')
                "
              >
                {{ artworkData?.sale_options }}
              </div>
              <!-- <div *ngIf="!thukralandtagra" class="view-fullscreen">
                <a routerLink="fullscreen"
                  ><img src="assets/icons/fullscreen.png"
                /></a>
              </div> -->
            </div>
          </div>
          <div *ngIf="artworkData?.file_type == ''" class="media-item">
            <div
              class="media-content"
              [ngStyle]="isMobile ? {} : { height: hideInfo ? '80vh' : '60vh' }"
            >
              <div class="image-wrapper">
                <img
                  [src]="
                    get_url_extension(artworkData?.primary_image[0]?.url) !=
                    'gif'
                      ? cloudflareLink(artworkData?.artwork_type) +
                        artworkData?.primary_image[0]?.url
                      : artworkData?.primary_image[0]?.url
                  "
                />
              </div>
              <div
                class="banner-chip"
                *ngIf="
                  privateViewData?.contents[0]?.data?.showAvailability &&
                  (artworkData?.sale_options === 'Sold' ||
                    artworkData?.sale_options === 'Reserved')
                "
              >
                {{ artworkData?.sale_options }}
              </div>
              <!-- <div *ngIf="!thukralandtagra" class="view-fullscreen">
                <a routerLink="fullscreen"
                  ><img src="assets/icons/fullscreen.png"
                /></a>
              </div> -->
            </div>
          </div>
          <div *ngIf="artworkData?.file_type == 'video'" class="media-item">
            <div
              class="media-content"
              [ngStyle]="isMobile ? {} : { height: hideInfo ? '80vh' : '60vh' }"
            >
              <div class="image-wrapper" style="height: 100%">
                <iframe
                  [src]="embededUrl"
                  style="width: 100%; height: 100%"
                ></iframe>
              </div>

              <!-- width: 100%;
                  height: 0;
                  margin: auto;
                  padding-bottom: 56.25%;
                  align-self: center;
                  justify-self: center; -->
              <!-- width: 100%;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  align-self: center;
                  justify-self: center; -->
              <div
                class="banner-chip"
                *ngIf="
                  privateViewData?.contents[0]?.data?.showAvailability &&
                  (artworkData?.sale_options === 'Sold' ||
                    artworkData?.sale_options === 'Reserved')
                "
              >
                {{ artworkData?.sale_options }}
              </div>
            </div>
          </div>
          <div *ngIf="artworkData?.file_type == 'html_code'" class="media-item">
            <div
              class="media-content"
              [ngStyle]="isMobile ? {} : { height: hideInfo ? '80vh' : '60vh' }"
            >
              <div class="image-wrapper" style="height: 100%">
                <iframe
                  [src]="embededUrl"
                  style="width: 100%; height: 100%"
                ></iframe>
              </div>
              <div
                class="banner-chip"
                *ngIf="
                  privateViewData?.contents[0]?.data?.showAvailability &&
                  (artworkData?.sale_options === 'Sold' ||
                    artworkData?.sale_options === 'Reserved')
                "
              >
                {{ artworkData?.sale_options }}
              </div>
            </div>
          </div>
          <!-- <ng-container *ngIf="moreImages">
            <ng-container
              *ngIf="
                artworkData &&
                artworkData?.use_scale_wall &&
                !artworkData?.hide_3d_feature
              "
            >
              <ng-container *ngFor="let item of artVideos">
                <ng-container
                  *ngIf="!(artworkData?.artwork_type === 'Video' && i === 0)"
                >
                  <div class="media-item">
                    <div class="media-content">
                      <div class="image-wrapper">
                        <iframe
                          [src]="item?.video_url | safe: 'resourceUrl'"
                          style="
                            position: absolute;
                            width: 100% !important;
                            height: 100%;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            overflow: hidden;
                            background-color: none;
                          "
                          frameborder="0"
                          allow="autoplay; fullscreen"
                          allowfullscreen
                          #vimeoIframe
                        ></iframe>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </ng-container>
            </ng-container>
            <ng-container *ngFor="let item of artworkData?.additional_images">
              <div class="media-item">
                <div class="media-content">
                  <div class="image-wrapper">
                    <img
                      [src]="
                        'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                        item.url
                      "
                    />
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container> -->

          <!-- <div
            *ngIf="artworkData?.show_media"
            class="d-flex justify-content-center"
          >
            <div (click)="moreImages = !moreImages" class="more-images">
              {{ moreImages ? " Hide Media" : " More Media" }}

              <img
                [src]="
                  moreImages
                    ? 'assets/icons/accordion-open_2020-11-12/<EMAIL>'
                    : 'assets/icons/accordion-close_2020-11-12/<EMAIL>'
                "
              />
            </div>
          </div> -->
          <ng-container *ngIf="artworkData?.show_media">
            <ng-container *ngFor="let item of artworkData?.additional_images">
              <ng-container *ngIf="checkPublish(item)">
                <div class="media-item">
                  <div class="media-content">
                    <div class="image-wrapper">
                      <img
                        [src]="
                          'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                          item.url
                        "
                      />
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </div>
        <div class="slider-head-main">
          <div class="slider-heads">
            <div
              (click)="scrollSlider(0)"
              class="slider-head"
              [ngClass]="{ active: 0 === currentIndex }"
            ></div>
            <ng-container *ngIf="artworkData?.show_media">
              <div
                class="slider-head"
                *ngFor="
                  let item of artworkData?.additional_images;
                  let i = index
                "
                (click)="scrollSlider(i + 1)"
                [ngClass]="{
                  active: i === currentIndex - 1
                }"
              ></div>
            </ng-container>
          </div>
        </div>

        <div *ngIf="!hideInfo && !isViewInfo" class="artwork-details">
          <div>
            <h3 class="artist-name">
              <a
                *ngIf="artist?.publish"
                [routerLink]="['/discover/artists', artist?.url_name]"
                style="color: #808080"
              >
                {{ artist?.display_name }}
                <img
                  *ngIf="artist.artist_type == 'Terrain'"
                  src="assets/icons/tc badges_400px.png"
                  alt="verified" />
                <img
                  *ngIf="artist.artist_type == 'Open'"
                  src="assets/icons/To badges_400px.png"
                  alt="verified"
              /></a>
            </h3>

            <h3 *ngIf="!artist?.publish" class="artist-name">
              {{ artist?.display_name }}
            </h3>
            <h1 class="artwork-name">
              {{ artworkData?.artwork_title }},
              <span style="font-style: normal">{{ artworkData?.year }}</span>
            </h1>
            <fa-icon
              *ngIf="privateViewData?.contents[0]?.data?.showAvailability"
              [style.color]="
                artworkData?.sale_options == 'ForSale'
                  ? 'green'
                  : artworkData?.sale_options == 'Reserved'
                  ? 'yellow'
                  : 'red'
              "
              [icon]="faCircle"
            ></fa-icon>

            <div
              *ngIf="
                artworkData?.sale_price &&
                privateViewData?.contents[0]?.data?.showSalePrice
              "
              class="artwork-price"
            >
              INR
              {{ artworkData?.sale_price | number : "1.0" }} | USD
              {{
                artworkData?.sale_price | forex : "USD" | async | number : "1.0"
              }}
            </div>
            <div
              *ngIf="
                artworkData?.sale_price &&
                privateViewData?.contents[0]?.data?.showSalePrice &&
                privateViewData?.contents[0]?.data?.showCryptoPrice
              "
              class="artwork-price usd"
            >
              BTC
              {{
                artworkData?.sale_price | forex : "BTC" | async | number : "1.0"
              }}
              | ETH
              {{
                artworkData?.sale_price | forex : "ETH" | async | number : "1.0"
              }}
              | USDC
              {{
                artworkData?.sale_price
                  | forex : "USDC"
                  | async
                  | number : "1.0"
              }}
              <span
                (click)="currencyPopup = true"
                class="material-icons-outlined"
                style="cursor: pointer"
              >
                info
              </span>
            </div>

            <div
              *ngIf="
                privateViewData?.contents[0]?.data?.showMedium ||
                privateViewData?.contents[0]?.data?.showDimensions
              "
              class="box-content"
            >
              <div
                *ngIf="privateViewData?.contents[0]?.data?.showMedium"
                class="item"
              >
                <h3>Medium</h3>
                <p>
                  {{ artworkData?.medium }}
                </p>
              </div>
              <div
                *ngIf="privateViewData?.contents[0]?.data?.showDimensions"
                class="item"
              >
                <h3>Dimensions</h3>
                <p>
                  {{ artworkData?.dimensions }}
                </p>
              </div>
              <div *ngIf="artworkData?.edition_details" class="item">
                <h3>Edition Details</h3>
                <p>{{ artworkData?.edition_details }}</p>
              </div>
            </div>
          </div>
          <!-- <h3 *ngIf="artworkData?.minted">Nft Id</h3>
          <p *ngIf="artworkData?.minted">
            <a
              [href]="
                'https://ropsten.etherscan.io/token/******************************************?a=' +
                artworkData?.nft_id
              "
              target="_blank"
              >Terrain.Art_{{ artworkData?.nft_id }}
            </a>
            <abbr
              title="NFTs are minted on Ethereum test network, check out the FAQs for more information"
              ><span class="material-icons-outlined"> info </span></abbr
            >
          </p> -->

          <!-- <div
            *ngIf="!thukralandtagra"
            class="add-to-cart"
            [ngClass]="{
              sold:
                artworkData?.sale_options === 'Sold' ||
                artworkData?.sale_options === 'Reserved' ||
                isArtworkOnCart()
            }"
          >
            <a
              (click)="
                !isArtworkOnCart() &&
                  artworkData?.sale_options === 'ForSale' &&
                  addToCart()
              "
              style="cursor: pointer"
              >{{
                isArtworkOnCart()
                  ? "Added To Cart"
                  : artworkData?.sale_options === "Sold"
                  ? "Sold"
                  : artworkData?.sale_options === "Reserved"
                  ? "Reserved"
                  : "Add to Cart"
              }}</a
            >
          </div> -->
          <div>
            <ng-container *ngIf="thukralandtagra">
              <ng-container *ngIf="arakuStep == 0">
                <div class="input-field" style="width: 100%; margin-top: 0.5vw">
                  <!-- <label class="form-control-label">Email</label> -->
                  <input
                    [(ngModel)]="emailaraku"
                    type="text"
                    name="email"
                    class="form-control"
                    placeholder="Enter your email"
                  />
                </div>
                <div class="add-to-cart">
                  <a (click)="arakuFun()" style="cursor: pointer">
                    Register Email</a
                  >
                </div>
                <div *ngIf="!arakuError" class="faq-shipping">
                  Register your email to verify your purchase, and receive your
                  edition of the artwork.
                </div>
                <div *ngIf="arakuError" class="faq-shipping error">
                  Your email has already been registered, and a one-time
                  download link sent. Please check the Spam/Junk folder if not
                  received.
                </div>
              </ng-container>
              <ng-container *ngIf="arakuStep == 1">
                <div class="input-field" style="width: 100%; margin-top: 0.5vw">
                  <!-- <label class="form-control-label">Email</label> -->
                  <input
                    [value]="emailaraku"
                    type="text"
                    name="otp"
                    class="form-control"
                    disabled
                  />
                  <input
                    [(ngModel)]="otpAraku"
                    type="text"
                    name="otp"
                    class="form-control"
                    placeholder="Enter OTP"
                  />
                </div>
                <div class="add-to-cart">
                  <a (click)="arakuFun()" style="cursor: pointer">
                    Verify OTP</a
                  >
                </div>
                <div *ngIf="!arakuError" class="faq-shipping">
                  One Time Password sent to email provided, please check your
                  Spam/Junk folder if not received
                </div>
                <div *ngIf="arakuError" class="faq-shipping error">
                  OTP verification failed. Please confirm the One-time Password
                  sent to your email.
                </div>
              </ng-container>
              <ng-container *ngIf="arakuStep == 2">
                <div class="add-to-cart sold">
                  <a style="cursor: pointer"> Done</a>
                </div>
                <div class="faq-shipping">
                  Thank you! Your email has been verified, and a one-time
                  download link has been sent to your email.
                </div>
              </ng-container>
            </ng-container>

            <!-- <div class="faq-shipping">
            Excl. of shipping <a>View shipping details </a>
          </div> -->
            <div class="d-flex justify-content-between artwork-info-link">
              <a (click)="onViewArtworkInfo()" style="cursor: pointer"
                >Artwork Info ›</a
              >
            </div>
            <div class="hr-line"></div>
          </div>
        </div>
        <div *ngIf="isViewInfo" class="info-wrapper">
          <div class="info-container">
            <div class="close-button">
              <a (click)="isViewInfo = false" style="cursor: pointer"
                ><img src="assets/icons/grey-close.png"
              /></a>
            </div>
            <h2>Details</h2>
            <h3 *ngIf="artworkData?.show_artist_note">About the Artwork</h3>
            <p
              *ngIf="artworkData?.show_artist_note"
              [innerHTML]="artworkData?.description"
            ></p>
            <h3>Title</h3>
            <p>
              <i>{{ artworkData?.artwork_title }}</i>
            </p>
            <h3>Artist</h3>
            <p>
              {{ artist?.display_name }}
            </p>

            <h3 *ngIf="artworkData?.year">Date</h3>
            <p *ngIf="artworkData?.year">
              {{ artworkData?.year }}
            </p>
            <h3 *ngIf="privateViewData?.contents[0]?.data?.showLocation">
              Location
            </h3>
            <p *ngIf="privateViewData?.contents[0]?.data?.showLocation">
              {{ artworkData?.location }}
            </p>
            <h3 *ngIf="privateViewData?.contents[0]?.data?.showSignature">
              Signature
            </h3>
            <p *ngIf="privateViewData?.contents[0]?.data?.showSignature">
              {{ artworkData?.signature_details }}
            </p>
            <h3 *ngIf="privateViewData?.contents[0]?.data?.showCatalogId">
              Catalog Number
            </h3>
            <p *ngIf="privateViewData?.contents[0]?.data?.showCatalogId">
              {{ artworkData?.catalog_number }}
            </p>
            <h3 *ngIf="privateViewData?.contents[0]?.data?.showCopyrightLine">
              Copyright
            </h3>
            <p *ngIf="privateViewData?.contents[0]?.data?.showCopyrightLine">
              {{ artworkData?.credit_line }}
            </p>

            <!-- <h3 *ngIf="artworkData?.credit_line">Credit Line</h3>
            <p *ngIf="artworkData?.credit_line">
              {{ artworkData?.credit_line }}
            </p> -->
            <!-- <h3 *ngIf="artworkData?.usageRestrictions">Usage Restrictions</h3>
            <p *ngIf="artworkData?.usageRestrictions">
              {{ artworkData?.usageRestrictions }}
            </p> -->
          </div>
        </div>
      </div>
    </div>
    <div class="popup-close">
      <a (click)="closePopup.emit()" style="cursor: pointer">
        <img src="assets/images/close.png" />
      </a>
    </div>
    <div class="info-box" style="display: flex; justify-content: space-between">
      <div>
        <div *ngIf="hideInfo && !isMobile">
          <span style="font-style: italic">
            {{ artworkData?.artwork_title }},
          </span>
          <span>{{ artworkData?.year }}</span
          >. {{ artist?.display_name }}
        </div>
      </div>
      <div class="info-box-2">
        <div class="link">
          <a (click)="hideInfo = !hideInfo" class="viewin-room-btn"
            >{{ hideInfo ? "Show" : "Hide" }} info</a
          >
        </div>
        <div class="paginate-content">
          <button
            (click)="
              selectedArtwork != 0 && (selectedArtwork = selectedArtwork - 1);
              processInput()
            "
            class="btn-image-wrapper"
            [disabled]="selectedArtwork == 0"
          >
            <img
              src="assets/icons/back-chevron_2020-11-12/<EMAIL>"
              alt=""
            />
          </button>
          {{ selectedArtwork + 1 }}&nbsp; of&nbsp;
          {{ privateViewData?.artworkIds.length }} &nbsp;
          <button
            (click)="
              selectedArtwork != privateViewData?.artworkIds?.length - 1 &&
                (selectedArtwork = selectedArtwork + 1);
              processInput()
            "
            class="btn-image-wrapper"
            [disabled]="
              selectedArtwork == privateViewData?.artworkIds?.length - 1
            "
          >
            <img
              src="assets/icons/front-chevron_2020-11-12/<EMAIL>"
              alt=""
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
