import { artworksDemo } from 'src/app/demo/artworks.demo';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  PLATFORM_ID,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Event } from '@angular/router';
import {
  faAngleDoubleLeft,
  faAngleDoubleRight,
  faAngleLeft,
  faAngleRight,
  faCertificate,
  faClock,
  faHammer,
  faSearch,
  faTruck,
  faHeart as faHearts,
  faBookmark as faBookmarks,
  faTags,
  faKey,
  faChevronUp,
  faChevronDown,
  faReceipt,
  faCircle,
} from '@fortawesome/free-solid-svg-icons';
import { Web3Service } from 'src/app/shared/services/web3.service';
import { ArakuService } from 'src/app/shared/services/akaru.service';
import { type } from 'os';
import { DomSanitizer, Meta, Title } from '@angular/platform-browser';
import {
  faHeart,
  faBookmark,
  faShareSquare,
  faCopy,
} from '@fortawesome/free-regular-svg-icons';
import {
  faFacebook,
  faInstagram,
  faTwitter,
  faWhatsapp,
} from '@fortawesome/free-brands-svg-icons';
import { AuthService } from 'src/app/services/auth.service';
import { isPlatformBrowser } from '@angular/common';

declare let Swal: any;
@Component({
  selector: 'app-artwork-comp',
  templateUrl: './artwork.component.html',
  styleUrls: ['./artwork.component.scss'],
  animations: [
    trigger('fade', [
      state('in', style({ opacity: '1', transform: 'translateY(0)' })),
      state('out', style({ opacity: '0', transform: 'translateY(10%)' })),
      transition('* <=> *', [animate(500)]),
    ]),
  ],
})
export class ArtworkComponentComp implements OnInit, OnDestroy {
  tempAray = [
    { action: 'Burned' },
    { action: 'Minted' },
    { action: 'Made' },
    { action: 'Offer' },
    { action: 'Transfer' },
    { action: 'Sale' },
  ];
  te;
  faSearch = faSearch;
  faCertificate = faCertificate;
  faTruck = faTruck;
  limit = 12;
  Fiter_totalCount = 25;
  faHammer = faHammer;
  faClock = faClock;
  MuiBtn = true;
  faHeart = faHeart;
  faHearts = faHearts;
  faBookmark = faBookmark;
  faBookmarks = faBookmarks;
  faAngleDoubleRight = faAngleDoubleRight;
  faAngleRight = faAngleRight;
  faAngleLeft = faAngleLeft;
  faAngleDoubleLeft = faAngleDoubleLeft;
  faShareSquare = faShareSquare;
  faTwitter = faTwitter;
  faFacebook = faFacebook;
  faInstagram = faInstagram;
  faCopy = faCopy;
  faWhatsapp = faWhatsapp;
  faTags = faTags;
  faKey = faKey;
  faChevronUp = faChevronUp;
  faChevronDown = faChevronDown;
  faReceipt = faReceipt;
  faCircle = faCircle;

  artworkId: string;
  transaction =
    '0xbc064160be4cffb06494c3b76da7bba9d1540dbcd68218fcedc1e3126ed6848a';
  from = '0x4acD0E9813E916aEb62700eee343b0700454424A';
  to = '0x4acD0E9813E916aEb62700eee343b0700454424A';
  artworkData: any;
  @Input() privateViewData: any;
  @Input() selectedArtwork = 0;
  @ViewChild('primaryImage', { static: false })
  primaryImage: ElementRef;
  @ViewChild('mediaContainer', { static: false }) mediaContainer: ElementRef;

  iszoomImage = false;
  zoomImageSize: string;
  zoomImagePosition: string;

  primaryImageBoundary: any;
  mediaContainerBoundary: any;
  imgSrc = [];
  selectedSrc = 0;

  state = 'in';
  enableAnimation = false;
  counter = 0;
  direction = 1;

  artist: any;
  moreArtworks;
  page = 1;
  currencyPopup = false;
  nftAccordion = Array(10).fill(false);
  @ViewChild('Provenance') Provenance: ElementRef;
  @ViewChild('Exhibited') Exhibited: ElementRef;

  isOpen = {
    1: false,
  };

  isViewInfo = false;

  currentIndex = 0;

  isMobile = false;

  isAddedToCart = false;

  isArOn = false;

  embededContent;
  embededUrl;
  isOnWishList = false;
  isArtistFollower = false;

  viewShareList = false;

  Toast;

  hideInfo = false;

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    // event.target.innerWidth;
    const w = event.target.innerWidth;
    this.isMobile = this.getIsMobile(w);
  }

  next2(i): void {
    // if (i > 0) {
    //   if (this.index < Math.ceil(this.currentArtists.length / 12)) {
    //     this.index += i;
    //     this.updateCurrent();
    //   }
    // }
    // if (i === 0) {
    //   this.index = this.lastIndex;
    //   this.updateCurrent();
    // }
    // this.scrollBack();
  }

  next(i): void {
    // if ((this.page + i - 1) * this.limit < this.Fiter_totalCount) {
    //   this.offset = this.offset + this.limit * i;
    //   this.getData();
    //   this.scrollBack();
    // }
  }
  categories = [
    'Limited edition prints',
    'Paintings',
    'Sculpture',
    'Photography',
    'Collage',
    'New this week',
    'New Media',
    'Net art',
    'Canvas',
    'Acrylic paint',
    'Graphite',
    'Monoprint',
    'Etching',
    'Ink',
    'Watercolor painting',
  ];
  nftowner;
  moreImages = false;
  artVideos = [];
  thukralandtagra = false;
  emailaraku;
  otpAraku;
  arakuStep = 0;
  arakuimage;
  arakuError = false;
  cartData;
  userData;
  @Output() closePopup = new EventEmitter();
  constructor(
    private route: ActivatedRoute,
    private el: ElementRef,
    private web3Service: Web3Service,
    private arakuService: ArakuService,
    private sanitized: DomSanitizer,
    private titleService: Title,
    private metaService: Meta,
    private authService: AuthService,
    @Inject(PLATFORM_ID) private platformId: any
  ) { }
  ngOnDestroy(): void {
    document.body.style.overflow = 'auto';
  }

  ngOnInit(): void {
    document.body.style.overflow = 'hidden';
    if (isPlatformBrowser(this.platformId)) {
      const w = document.documentElement.clientWidth;
      this.isMobile = this.getIsMobile(w);
      this.Toast = Swal.mixin({
        toast: true,
        position: 'top-right',
        iconColor: 'white',
        customClass: {
          popup: 'colored-toast',
        },
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
      });
    }
    this.processInput();
  }

  processInput() {
    this.artworkData = this.privateViewData?.artworkIds[this.selectedArtwork];
    this.artist = this.artworkData.artist_id;
    this.embededContent = this.sanitized.bypassSecurityTrustHtml(
      this.artworkData?.artwork_embedded_code
    );
    this.embededUrl = this.sanitized.bypassSecurityTrustResourceUrl(
      this.artworkData?.artwork_embedded_code
    );
  }

  cloudflareLink(str: string) {
    const types = ['Digital', 'Video', 'Sculpture'];
    let check = types.includes(str);
    if ((check = false)) {
      return 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/';
    } else {
      return '';
    }
  }

  toggleState() {
    if (this.counter < 2) {
      this.state = this.state === 'in' ? 'out' : 'in';
      this.counter++;
    }
  }
  public get categoriesValues(): Array<string> {
    return this.categories.slice(0, 8);
  }

  scrollHandler($event) {
    const scrollValue = ($event.target.scrollLeft / window.innerWidth) * 100;
    console.log(scrollValue);

    if (scrollValue >= 0 && scrollValue <= 30) {
      this.currentIndex = 0;
    } else if (scrollValue > 30 && scrollValue <= 130) {
      this.currentIndex = 1;
    } else {
      this.currentIndex = 2;
    }
  }
  public getIsMobile(w): boolean {
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }
  get artistAboutVideo() {
    return this.artist?.intro_videos.filter((data) => {
      return data.index_number === '3';
    })[0];
  }

  public get artistAboutVideoType(): string {
    if (this.artistAboutVideo.url.includes('player.vimeo.com')) {
      return 'VIDEO';
    } else {
      return 'IMAGE';
    }
  }

  public isArtworkOnCart() {
    if (
      this.cartData?.find((a) => {
        return a?._id === this.artworkData?._id;
      })
    ) {
      return true;
    } else {
      return false;
    }
  }
  scrollSlider(index) {
    this.mediaContainer.nativeElement.scrollLeft = index * window.outerWidth;
  }
  arakuFun() {
    this.arakuError = false;
    if (this.arakuStep == 0) {
      this.arakuService
        .registerEmail(this.emailaraku, this.arakuimage)
        .then((data) => {
          this.arakuError = false;
          this.arakuStep = 1;
        })
        .catch((e) => {
          this.arakuError = true;
        });
    } else if (this.arakuStep == 1) {
      this.arakuService
        .verifyOTP(this.emailaraku, this.otpAraku, this.arakuimage)
        .then((data) => {
          this.arakuStep = 2;
        })
        .catch((e) => {
          this.arakuError = true;
        });
    }
  }

  public videoEmbed() {
    return this.sanitized.bypassSecurityTrustHtml(
      `<div style="padding:56.25% 0 0 0;position:relative;"><iframe src="https://player.vimeo.com/video/656548812?h=6f0654caea&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen style="position:absolute;top:0;left:0;width:100%;height:100%;" title="Jhoomar - Artchives"></iframe></div><script src="https://player.vimeo.com/api/player.js"></script>`
    );
  }
  setSEOTags() {
    this.titleService.setTitle(this.artworkData?.artwork_title);
    this.metaService.updateTag(
      {
        content:
          this.artworkData?.medium_keywords
            .map((a) => {
              return a.name;
            })
            .join(' ,') +
          ', ' +
          this.artworkData?.tags
            .map((a) => {
              return a;
            })
            .join(' ,') +
          ', ' +
          this.artworkData?.subjects_keywords
            .map((a) => {
              return a.name;
            })
            .join(' ,') +
          ', ' +
          this.artworkData?.movement
            .map((a) => {
              return a.name;
            })
            .join(' ,'),
      },
      'name="keywords"'
    );
    this.metaService.updateTag(
      {
        content: this.artworkData?.artist_note,
      },
      'name="description"'
    );
    this.metaService.updateTag(
      {
        content:
          'https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
          this.artworkData?.thumbnail_of_primary,
      },
      'property="og:image"'
    );
    this.metaService.updateTag(
      {
        content: this.artworkData?.artwork_title,
      },
      'property="og:title"'
    );
    this.metaService.updateTag(
      {
        content: this.artworkData?.artist_note,
      },
      'property="og:description"'
    );
  }
  get_url_extension(url) {
    return url.split(/[#?]/)[0].split('.').pop().trim();
  }

  get getWhatappLink() {
    return this.sanitized.bypassSecurityTrustUrl(
      `whatsapp://send?text=I would like to know more about ${encodeURIComponent(
        this.artworkData?.artwork_title
      )}, ${encodeURIComponent(this.artworkData?.year)} by ${encodeURIComponent(
        this.artist?.display_name
      )}. ${encodeURIComponent(window.location.href)}&phone=+919220522788`
    );
  }
  get geTwitterLink() {
    return this.sanitized.bypassSecurityTrustUrl(
      `https://twitter.com/share?text=View ${encodeURIComponent(
        this.artworkData?.artwork_title
      )}, ${encodeURIComponent(this.artworkData?.year)} by ${encodeURIComponent(
        this.artist?.display_name
      )} on Terrain Art, a blockchain-powered art ecosystem.&url=${encodeURIComponent(
        window.location.href
      )}&hashtags=NFTs,NFTArt,NFTMarketplace`
    );
  }
  get geFaceBookLink() {
    return this.sanitized.bypassSecurityTrustUrl(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        window.location.href
      )}&t=View ${encodeURIComponent(
        this.artworkData?.artwork_title
      )}, ${encodeURIComponent(this.artworkData?.year)} by ${encodeURIComponent(
        this.artist?.display_name
      )} on Terrain Art, a blockchain-powered art ecosystem.`
    );
  }
  copyUrl() {
    navigator.clipboard.writeText(window.location.href);
  }

  @HostListener('document:mouseup', ['$event'])
  onGlobalClick(event): void {
    if (this.viewShareList && !event.target.closest('.share-content')) {
      this.viewShareList = false;
    }
  }
  onViewArtworkInfo() {
    this.isViewInfo = true;
    console.log(this.mediaContainer.nativeElement.getBoundingClientRect().top);

    window.scroll({
      top:
        window.scrollY +
        this.mediaContainer.nativeElement.getBoundingClientRect().top,
      left: 0,
      behavior: 'smooth',
    });
  }
  checkPublish(item) {
    return (typeof item?.publish === 'undefined') ? true : item?.publish
  }
}
