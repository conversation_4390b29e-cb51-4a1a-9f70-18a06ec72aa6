.section {
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.8);
  .popup-close {
    position: absolute;
    right: 1vw;
    top: 1vw;
    font-size: 2.4vw;
    a {
      color: white;
      img {
        width: 2.8vw;
        height: 2.8vw;
      }
    }
    @media (max-width: 768px) {
      font-size: 4vw;
      a {
        img {
          width: 6.5vw;
          height: 6.5vw;
        }
      }
    }
    @media (max-width: 768px) {
      height: 100%;
    }
  }
  .info-box {
    position: absolute;
    right: 2vw;
    bottom: 2vw;
    width: 95%;
    @media (max-width: 768px) {
      position: sticky;
      padding-bottom: 5vw;
      padding-top: 3vw;
      bottom: 0;
      width: 100%;
      -webkit-backdrop-filter: blur(15px);
      backdrop-filter: blur(15px);
      background-color: #ffffff38;
    }
  }
  .info-box-2 {
    display: flex;
    align-items: center;
    justify-content: center;
    .link {
      margin-right: 1vw;
      a {
        cursor: pointer;
        color: var(--tertiary-font-color);
      }
    }
  }
}
.section-inner {
  width: 100%;
  min-height: 100vh;
  background-color: white;
  border-radius: 0.45vw;
  padding-top: 5vw;
}

.artwork-sec {
  overflow-y: scroll;
  overflow-x: hidden;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.media-container {
  width: 56.67vw;
  margin-left: var(--page-default-margin);
  transition: width 1s ease-in-out;
  .media-item {
    background-color: #ededed;
    overflow: hidden;
    .media-content {
      position: relative;
      width: 100%;
      height: 60vh;
      transition: height 1s ease-in-out;
    }
    .image-wrapper {
      img {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;

        margin: auto;
        max-width: 90%;
        max-height: 90%;
      }
    }
    .view-fullscreen {
      img {
        position: absolute;
        width: 2.22vw;
        height: 2.22vw;
        bottom: 2.08vw;
        right: 2.08vw;
      }
    }
  }
}
.artwork-details {
  width: 30.55vw;
  height: 100%;
  position: -webkit-sticky;
  position: sticky;
  top: 20px;
  margin-right: var(--page-default-margin);
  // display: flex;
  // flex-direction: column;
  // justify-content: space-between;
  // height: calc(100vh - 10vw);
  //adsfsdz
  h3 {
    font-size: 1.38vw;
    margin-bottom: 1.04vw;
  }
  p {
    margin-bottom: 2.08vw;
  }
  .artist-name {
    font-size: 1.25vw;
    line-height: 1.67;
    color: #808080;
    margin-bottom: 0.69vw;
    font-weight: 400;
    img {
      height: 1.388vw;
      width: 1.388vw;
      margin-left: 0.4vw;
      @media (max-width: 768px) {
        height: 4.388vw;
        width: 4.388vw;
        margin-left: 1vw;
      }
    }
  }
  .artwork-name {
    font-family: var(--secondary-font);
    font-size: 2.36vw;
    font-style: italic;
    word-wrap: break-word;
    margin-bottom: 1vw;
  }
  .boxed-tab {
    width: 100%;
    border-radius: 0.6vw;
    border-color: #dad7d7;
    border-style: solid;
    border-width: 1px;
    overflow: hidden;
    margin-bottom: 1vw;
    .tab {
      .tab-head {
        width: 100%;
        border-bottom-color: #dad7d7;
        border-bottom-style: solid;
        border-bottom-width: 1px;
        display: flex;
        justify-content: space-between;
        padding: 1.2vw 1.5vw;
      }
      .tab-content {
        //background-color: #eceef1;
        width: 100%;
        border-bottom-color: #dad7d7;
        border-bottom-style: solid;
        border-bottom-width: 1px;
        padding: 1.2vw 1.5vw;
        .nft-details {
          .item {
            display: flex;
            justify-content: space-between;
            .value {
              color: #808080;
            }
            a {
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  .share-list {
    cursor: pointer;
    color: #808080;
    font-size: 1.5vw;
    margin-bottom: 1.38vw;
    position: relative;
    .button {
      display: inline-block;
      border-radius: 2vw;
      cursor: pointer;
      padding: 0.5vw 1.5vw;
      margin-right: 0.5vw;
      color: #1f1f1f;
      font-size: 1.2vw;
      border-color: #004ddd;
      border-style: solid;
      border-width: 1px;
      box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.05);
      .share {
        display: block;
      }
    }
    .fa-icon {
      display: inline-block;
    }
    .share-content {
      font-size: 1vw;
      position: absolute;
      top: 4vw;
      left: 0vw;
      box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.05);
      // padding: 1vw 2vw;
      background-color: white;
      .item {
        cursor: pointer;
        display: flex;
        // justify-content: center;
        justify-items: center;
        padding: 1vw 2vw;
        //text-align: center;
        color: #808080;
        &:hover {
          background-color: #ddd;
        }
      }
    }
  }
  .artwork-price {
    font-size: 1.66vw;
    line-height: 1.25;
    margin-bottom: 0.69vw;
    &.usd {
      font-size: 1.25vw;
      line-height: 1.67;
      color: #808080;
      margin-bottom: 1.47vw;
      font-weight: 400;
    }
  }
  .add-to-cart {
    width: 100%;
    height: 3.05vw;
    border-radius: 2.08vw;
    border-color: var(--tertiary-font-color);
    border-style: solid;
    border-width: 0.07vw;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.04vw;
    a {
      text-align: center;
      cursor: pointer;
      color: var(--tertiary-font-color);
      font-size: 1.2vw;

      &:hover {
        text-decoration: underline;
      }
    }
    &.sold {
      border-color: var(--primary-border-color);
      background-color: var(--primary-border-color);
      a {
        cursor: not-allowed;
        color: var(--quaternary-font-color);
      }
    }
  }
  .faq-shipping {
    line-height: 1.88;
    color: #808080;
    margin-bottom: 0.34vw;
    a {
      color: var(--tertiary-font-color);
    }
    &.error {
      color: rgb(206, 50, 50);
    }
  }
  .artwork-info-link {
    //margin-top: 3.12vw;
    margin-bottom: 2.08vw;
    a {
      color: var(--tertiary-font-color);
    }
  }
  .hr-line {
    width: 100%;
    height: 0.07vw;
    background-color: #ededed;
  }
}
.info-wrapper {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  width: 36.11vw;
  height: 100%;
  background-color: #f6f6f6;
  // margin-top: -3.47vw;
  z-index: 15;
  .info-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 3.47vw 8.33vw;
    .close-button {
      position: absolute;
      top: 3.61vw;
      right: 2.08vw;
      img {
        width: 2.29vw;
        height: 2.29vw;
      }
    }
    h2 {
      font-size: 2.08vw;
      margin-bottom: 2.15vw;
    }
    h3 {
      font-size: 1.38vw;
      margin-bottom: 1.04vw;
    }
    p {
      margin-bottom: 2.08vw;
      font-family: var(--secondary-font);
    }
  }
}

.rounded-pill {
  a {
    text-align: center;
    cursor: pointer;
    color: var(--tertiary-font-color);
    font-size: 1vw;
    vertical-align: middle;
    color: var(--tertiary-font-color);
    padding: 0.69vw 1.38vw;
    background-color: transparent;
    margin-right: 0.8vw;
    border-radius: 2.08vw;
    border-color: var(--tertiary-font-color);
    border-style: solid;
    border-width: 1px;
    &:hover {
      text-decoration: underline;
    }
    &.disable {
      color: var(--quaternary-font-color);
      border-color: var(--secondary-background-color);
      background-color: var(--secondary-background-color);
    }
  }
  &.btn-group {
    width: 80%;
    margin-left: 10%;
    a {
      width: 50%;
    }
  }
}
.exhibiton {
  height: 38vw;
  width: 100vw;
  max-width: 100%;
  .exhibiton-contents {
    h1 {
      font-size: 2.09vw;
      margin-bottom: 4.17vw;
      .nav-icon {
        display: inline-block;
        & > img {
          width: 0.41vw;
          height: 0.83vw;
        }
      }
    }
    h2 {
      font-size: 1.67vw;
      margin-bottom: 0.7vw;
    }
    p {
      font-family: var(--secondary-font);
      color: var(--quaternary-font-color);
      line-height: 1.5;
    }
    h4 {
      color: var(--tertiary-font-color);
      cursor: pointer;
      font-size: 1.26vw;
      margin-top: 2.09vw;
      .rounded-icon {
        background-color: transparent;
      }
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .exhibiton-left {
    width: 57.77vw;
    padding-left: var(--page-default-margin);
    background-color: var(--secondary-background-color);
    .exhibiton-contents {
      width: 42.22vw;
      h1 {
        font-size: 2.09vw;
        margin-bottom: 3.47vw;
      }
      h2 {
        font-size: 1.67vw;
        margin-bottom: 1.38vw;
      }
      p {
        font-family: var(--secondary-font);
        color: var(--quaternary-font-color);
        line-height: 1.5;
      }
      .rounded-pill {
        margin-top: 2.09vw;
      }
      h4 {
        color: var(--tertiary-font-color);
        cursor: pointer;
        font-size: 1.26vw;

        .rounded-icon {
          background-color: transparent;
        }
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  .exhibiton-right {
    background-color: black;
    width: 42.22vw;
    position: relative;
  }
}
.panel {
  background-color: transparent;
  overflow: hidden;
  transition: max-height 0.4s ease-in-out;
  flex-wrap: wrap;
  p {
    padding: 0;
    margin-bottom: 0.34vw;
    font-family: var(--secondary-font);
  }
}
.artwork-info {
  background-color: #e7e3e3c4;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
  .content-wrapper {
    position: -webkit-sticky;
    position: sticky;
    top: 0px;
    min-height: 100vh;
    background-color: #f6f6f6;
    width: 36.11vw;
    height: 0vh;

    .info-container {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 3.47vw 8.33vw;
      .close-button {
        position: absolute;
        top: 3.61vw;
        right: 2.08vw;
        img {
          width: 2.29vw;
          height: 2.29vw;
        }
      }
      h2 {
        font-size: 2.08vw;
        margin-bottom: 2.15vw;
      }
      h3 {
        font-size: 1.38vw;
        margin-bottom: 1.04vw;
      }
      p {
        margin-bottom: 2.08vw;
        font-family: var(--secondary-font);
      }
    }
  }
}
.slider-head-main {
  display: none;
}
.main-content {
  position: relative;
}
.padding-3 {
  padding-top: 3.47vw;
}
.paginationForTable {
  margin-top: 1vw;
  display: flex;
  gap: 1.5vw;
  justify-content: end;
  align-items: center;
  font-size: 1.042vw;
}

.MuiBtn {
  color: #586a6d;
  // height: 2.222vw;
  margin-right: 2vw;
  padding: 0.4vw 0vw;
  font-size: 1.666vw;
  min-width: unset;
  background: transparent;
  min-height: unset;
  // border-radius: 69vw;
  border: 0;
}
.MuiBtn-selected {
  color: var(--primary-font-color);
  // background: #192c30;
  border-bottom: 0.18888vw solid #004ddd;
}
.Mui-content {
  margin-top: 3vw;
}
.Mui-Table {
  padding: 1.5vw;
  border: 0.069444vw solid #ddd;
  border-radius: 1.111vw;
}
.Mui-thead {
  padding-bottom: 1.5vw;
}
.Mui-td {
  padding: 1vw 0;
}
.Mui-trow {
  border-radius: 0.555vw;
  border: 0;
  padding: 1vw;
  // border-top-left-radius: 0.555vw;
  //   border-bottom-left-radius: 8px;
  padding: 1.111vw;
}
tr:nth-child(even) {
  background-color: #f8f8f8;
}
.Mui-Action-btn {
  color: var(--tertiary-font-color);
  background: transparent;
  padding: 0.2vw 0.69444vw;
  // font-size: 1.666vw;
  min-width: unset;
  min-height: unset;
  border-radius: 69vw;
  border: 0.069444vw solid var(--tertiary-font-color);
  margin-left: 0.4vw;
}

td {
  overflow: hidden;
  white-space: nowrap;
}

.paginate {
  margin-top: 1.74vw;
  hr {
    margin: 0;
    margin-bottom: 2.08vw;
    border: 0;
    border-top: 0.069vw solid var(--hr-primary-color);
  }
  .paginate-content {
    display: flex;
    justify-content: center;
  }
}

.nextPrev {
  cursor: pointer;
  border: none;
  background: none;
  color: var(--quaternary-font-color);
  padding: 0.4vw 0.6vw;
  margin-right: 0.625vw;
  border-radius: 50%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  float: right;
  width: 2vw;
  height: 2vw;
  font-size: 1vw;
  outline: none;
  font-weight: 500;
}

.nextPrev:hover {
  background: var(--primary-border-color);
}

.noHover {
  cursor: auto;
  background: none !important;
}
.paginate-content {
  .image-wrapper {
    outline: none;
    border: none;
    background: none;
    width: 2vw;
    height: 2vw;
    padding: 0vw;
    margin-right: 0.625vw;
    border-radius: 50%;
    img {
      border-radius: 50%;
      width: 2vw;
      height: 2vw;
    }
  }
  .image-wrapper:hover {
    background: var(--primary-border-color);
  }
  .image-wrapper:last-child {
    margin-right: 0;
  }
  .image-wrapper:disabled {
    background: var(--primary-border-color);
    color: var(--quaternary-font-color);
  }
}

.current {
  cursor: pointer;
  border: none;
  background: none;
  color: var(--tertiary-font-color);
  padding: 0.4vw 0.6vw;
  margin-right: 0.625vw;
  border-radius: 50%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  float: right;
  width: 2vw;
  height: 2vw;
  font-size: 1vw;
  outline: none;
  font-weight: 500;
}

.current:hover {
  background: var(--primary-border-color);
}

@media (max-width: 768px) {
  .paginate {
    hr {
      margin: 0;
      margin-bottom: 9.66vw;
      border: 0;
      border-top: 0.242vw solid var(--hr-primary-color);
    }
  }
  .paginate-content {
    .image-wrapper {
      width: 7.97vw;
      height: 7.97vw;
      margin-right: 3.62vw;
      img {
        width: 7.97vw;
        height: 7.97vw;
      }
    }
  }

  .nextPrev {
    padding: 0.4vw 0.6vw;
    margin-right: 3.62vw;
    width: 7.97vw;
    height: 7.97vw;
    font-size: 4.35vw;
  }
  .current {
    padding: 0.4vw 0.6vw;
    margin-right: 3.62vw;
    width: 7.97vw;
    height: 7.97vw;
    font-size: 4.35vw;
  }

  .slider-head-main {
    display: block;
    height: 7.72vw;
    position: relative;
    .slider-heads {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 0);
      display: flex;
      .slider-head {
        display: inline-block;
        margin: 0 1.25vw;
        width: 2.17vw;
        height: 2.17vw;
        background-color: var(--slider-color);
        border-radius: 50%;
        align-self: center;
        &.active {
          height: 2.9vw;
          width: 2.9vw;
        }
      }
    }
  }
  .padding-3 {
    padding-top: 0;
  }
  .artwork-sec {
    display: block;
  }

  .media-container {
    width: 100%;
    height: 74.84vw;
    margin-left: 0vw;
    margin-right: 0vw;
    scroll-behavior: smooth;
    overflow-x: scroll;
    overflow-y: hidden;
    // transition: width 1s ease-in-out;
    // &::-webkit-scrollbar {
    //   display: none;
    // }
    .media-item {
      display: table-cell;

      .media-content {
        position: relative;
        width: 100vw;
        height: 74.84vw;
      }
      .view-fullscreen {
        img {
          width: 7.72vw;
          height: 7.72vw;
          bottom: 4.83vw;
          right: 4.83vw;
        }
      }
    }
  }
  .artwork-details {
    width: calc(100% - 6vw);
    position: relative;
    margin-left: 5.797vw;
    margin-right: 6.04vw;
    top: 0;
    h3 {
      font-size: 4.83vw;
      margin-bottom: 3.62vw;
    }
    p {
      font-size: 3.86vw;
      padding-bottom: 7.24vw;
    }
    .artist-name {
      margin-top: 12.07vw;
      font-size: 3.86vw;
      margin-bottom: 2.41vw;
    }
    .artwork-name {
      font-size: 5.79vw;
      margin-bottom: 3.3vw;
    }

    .boxed-tab {
      width: calc(100% - 11.837vw);
      border-radius: 2vw;
      margin-bottom: 3vw;
      .tab {
        .tab-head {
          font-size: 3.2vw;
          padding: 2.4vw 3vw;
        }
        .tab-content {
          padding: 2.4vw 3vw;
          .nft-details {
            .item {
              font-size: 3vw;
            }
          }
        }
      }
    }
    .share-list {
      font-size: 3.5vw;
      margin-bottom: 1.38vw;
      .button {
        border-radius: 4vw;
        padding: 1.5vw 2.5vw;
        margin-right: 1vw;
        font-size: 3.5vw;
      }
      .share-content {
        font-size: 3.1vw;
        top: 8vw;
        left: 0vw;
        .item {
          padding: 2vw 3.5vw;
        }
      }
    }
    .artwork-price {
      font-size: 4.83vw;
      line-height: 1.25;
      margin-bottom: 2.41vw;

      &.usd {
        font-size: 3.86vw;
        margin-bottom: 4vw;
      }
    }
    .add-to-cart {
      width: calc(100% - 11.837vw);
      height: 12.07vw;
      border-radius: 6.08vw;
      margin-bottom: 4.83vw;
      a {
        font-size: 4.34vw;
      }
    }
    .faq-shipping {
      font-size: 3.86vw;
    }
    .artwork-info-link {
      //margin-top: 10vw;
      margin-bottom: 12.07vw;
      font-size: 4.83vw;
      flex-direction: column;
      a {
        margin-top: 2.07vw;
        color: var(--tertiary-font-color);
      }
    }
    .hr-line {
      width: calc(100% - 11.837vw);
    }
  }
  .exhibiton {
    height: auto;
    width: 100%;
    flex-wrap: wrap;
    .exhibiton-left {
      order: 2;
      width: 100%;
      padding-left: 5.797vw;
      padding-right: 6.04vw;
      .exhibiton-contents {
        width: 100%;
        h1 {
          margin-top: 12.07vw;
          font-size: 5.79vw;
          margin-bottom: 9.66vw;
        }
        h2 {
          font-size: 4.83vw;
          margin-bottom: 4.83vw;
        }
        p {
          font-size: 3.86vw;
        }
        .rounded-pill {
          margin-top: 9.66vw;
          width: 100%;
          display: flex;
          height: 12.07vw;
          justify-content: center;
          align-items: center;
          margin-bottom: 9.66vw;
          a {
            display: inline-block;
            font-size: 3.86vw;
            padding: 0vw 0vw;
            width: 100%;
            height: 12.07vw;
            line-height: 12.07vw;
            border-radius: 6.08vw;
          }
        }
      }
    }
    .exhibiton-right {
      order: 1;
      width: 100%;
      height: 76.32vw !important;
      overflow: hidden;
    }
  }
  .panel {
    p {
      font-size: 3.86vw;
      padding: 0;
      margin-bottom: 1.24vw;
    }
  }
  .info-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin-top: 0vw;
    z-index: 15;
    overflow-y: scroll;
    .info-container {
      padding: 12.07vw 5.79vw;
      .close-button {
        position: absolute;
        top: 10.86vw;
        right: 5.79vw;
        img {
          width: 9.66vw;
          height: 9.66vw;
        }
      }
      h2 {
        font-size: 5.79vw;
        margin-bottom: 9.66vw;
      }
      h3 {
        font-size: 4.83vw;
        margin-bottom: 3.62vw;
      }
      p {
        font-size: 3.86vw;
        padding-bottom: 7.24vw;
      }
    }
  }
  .viewin-room-btn {
    display: none;
  }

  // ===============================================================

  .paginationForTable {
    margin-top: 2vw;
    display: flex;
    gap: 3vw;
    justify-content: end;
    align-items: center;
    font-size: 2.084vw;
  }

  .MuiBtn {
    color: #586a6d;
    // height: 2.222vw;
    margin-right: 4vw;
    padding: 0.8vw 0vw;
    font-size: 5.8vw;
    min-width: unset;
    background: transparent;
    min-height: unset;
    // border-radius: 138vw;
    border: 0;
  }
  .MuiBtn-selected {
    color: var(--primary-font-color);
    // background: #192c30;
    border-bottom: 0.27vw solid #004ddd;
  }
  .Mui-content {
    font-size: 3.86vw;
    // font-size: 2.7vw;
    margin-top: 6vw;
  }
  .Mui-Table {
    padding: 3vw;
    border: 0.13333vw solid #ddd;
    border-radius: 2.222vw;
  }
  .Mui-thead {
    padding-bottom: 3vw;
  }
  .Mui-td {
    padding: 3vw 0;
  }
  .Mui-trow {
    border-radius: 1.111vw;
    border: 0;
    padding: 2vw;
    // border-top-left-radius: 0.555vw;
    //   border-bottom-left-radius: 8px;
    padding: 2.222vw;
  }
  tr:nth-child(even) {
    background-color: #f8f8f8;
  }
  .Mui-Action-btn {
    color: var(--tertiary-font-color);
    background: transparent;
    padding: 0.4vw 1vw;
    font-size: 3vw;
    min-width: unset;
    min-height: unset;
    border-radius: 138vw;

    border: 0.13888vw solid var(--tertiary-font-color);
    margin-left: 0.8vw;
  }
}
.more-images {
  font-size: 1vw;
  margin-top: 1.5vw;
  color: #004ddd;
  cursor: pointer;
  img {
    width: 1.6vw;
    height: 1.6vw;
    margin-left: 0.69vw;
  }
}
.input-field {
  input {
    margin-bottom: 1vw;
    height: 3.05vw;
    font-size: 1.2vw;
    @media (max-width: 768px) {
      width: calc(100% - 11.837vw);
      margin-bottom: 4.83vw;
      height: 12.07vw;
      font-size: 4.34vw;
    }
  }
}
@media (max-width: 768px) {
  .faq-shipping {
    width: calc(100% - 11.837vw);
  }
}
.banner-chip {
  position: absolute;
  top: 1.2vw;
  left: 1.04vw;
  padding: 0.69vw;
  width: 4.8vw;
  background: var(--chip-transparent-bg);
  font-size: 0.97vw;
  height: 2.36vw;
  text-align: center;
  border-radius: 1.8vw;
  @media (max-width: 768px) {
    position: absolute;
    top: 2.8vw;
    left: 2.42vw;
    padding: 1vw 2.42vw;
    width: auto;
    height: auto;
    background: var(--chip-transparent-bg);
    font-size: 2.9vw;
    text-align: center;
    border-radius: 2.4vw;
  }
}
.text-container {
  font-family: var(--secondary-font);
  font-size: 1.11vw;
  margin-bottom: 2.08vw;
  color: #808080;
  .title {
    color: #1f1f1f;
    font-size: 1.38vw;
    margin-bottom: 1.04vw;
    font-family: var(--primary-font);
  }

  @media (max-width: 768px) {
    font-size: 3.86vw;
    margin-bottom: 7.24vw;
    .title {
      font-size: 4.83vw;
      margin-bottom: 3.62vw;
    }
  }
}
.popup {
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  text-align: center;
  .tab {
    padding: 2.08vw 3.47vw;
    background-color: var(--primary-background-color);
    width: 15.63vw;
    // height: 21.32vw;
    transform: translate(32.19vw, 34.34vh);
    position: relative;
    .close {
      position: absolute;
      right: 2.08vw;
      top: 2.08vw;
      img {
        width: 2.29vw;
        height: 2.29vw;
      }
    }
    h1 {
      margin: 0;
      margin-top: 3.125vw;
      font-size: 1.67vw;
    }
    p {
      margin: 0;
      margin-top: 1.39vw;
      color: var(--quaternary-font-color);
      font-size: 1.11vw;
    }
    .buttonList {
      margin-top: 3.47vw;
      display: inline-grid;
      .save {
        outline: none;
        font-size: 1.25vw;
        width: 20.56vw;
        background-color: var(--primary-background-color);
        color: var(--tertiary-font-color);
        padding: 0.83vw 1.04vw;
        border: 0.069vw solid var(--tertiary-font-color);
        border-radius: 1.46vw;
      }
      .save:hover {
        font-weight: 600;
      }
      .cancel {
        outline: none;
        font-size: 1.25vw;
        background-color: var(--primary-background-color);
        color: var(--quaternary-font-color);
        padding: 1.39vw 2.78vw;
        border: none;
      }
      .cancel:hover {
        font-weight: 600;
        // color: var(--tertiary-font-color);
      }
    }
  }
  .tab1 {
    padding: 2.08vw 3.47vw;
    background-color: var(--primary-background-color);
    width: 35.63vw;
    height: auto;
    transform: translate(32.19vw, 34.34vh);
    position: relative;
    color: var(--secondary-font);
    .close {
      position: absolute;
      right: 2.08vw;
      top: 2.08vw;
      img {
        width: 2.29vw;
        height: 2.29vw;
      }
    }
    h1 {
      margin: 3.125vw 0;
      font-size: 1.67vw;
    }
  }
}
@media (max-width: 768px) {
  .popup {
    top: 0;
    .tab {
      padding: 20.05vw 9.66vw 2.42vw 9.66vw;
      width: 88.16vw;
      // height: 89.13vw;
      transform: translate(6.19vw, 25.34vh);
      .close {
        position: absolute;
        right: 4.84vw;
        top: 4.84vw;
        img {
          width: 7.97vw;
          height: 7.97vw;
        }
      }
      h1 {
        margin: 0;
        margin-top: 0;
        font-size: 5.797vw;
      }
      p {
        margin: 0;
        margin-top: 4.84vw;
        color: var(--quaternary-font-color);
        font-size: 3.87vw;
      }
      .buttonList {
        margin-top: 12.08vw;
        display: inline-grid;
        width: 100%;
        .save {
          font-size: 4.35vw;
          width: 100%;
          padding: 3.62vw 3.62vw;
          border: 0.242vw solid var(--tertiary-font-color);
          border-radius: 5.19vw;
        }
        .cancel {
          font-size: 3.87vw;
          padding: 4.84vw 4.84vw;
        }
      }
    }
    .tab1 {
      padding: 20.05vw 9.66vw;
      width: 88.16vw;
      transform: translate(6.19vw, 25.34vh);
      .close {
        position: absolute;
        right: 4.84vw;
        top: 4.84vw;
        img {
          width: 7.97vw;
          height: 7.97vw;
        }
      }
      h1 {
        margin: 0;
        margin-top: 0;
        font-size: 5.797vw;
      }
    }
  }
}
.accordion {
  margin-bottom: 1vw;
  overflow: hidden;
  .accodion-head {
    border-radius: 1px;
    border-color: #808080;
    border-style: solid;
    //text-align: left;
    padding: 0.6vw;
    border-radius: 0.6vw;
  }
  .accodion-body {
    background-color: #f6f6f6;
    padding: 1vw;
    .item {
      display: flex;
      justify-content: space-between;
      .value {
        color: #808080;
      }
      a {
        cursor: pointer;
      }
    }
  }
  @media (max-width: 768px) {
    margin-bottom: 4vw;
    width: calc(100% - 11.837vw);
    .accodion-head {
      padding: 2vw;
      border-radius: 1.2vw;
      font-size: 4.83vw;
    }
    .accodion-body {
      font-size: 3.5vw;
      padding: 3vw;
    }
  }
}

.nft {
  .item {
    margin-bottom: 0.6vw;
    a {
      display: inline-block !important;
    }
    .title {
      flex: 0 0 35%;
    }
    .value {
      flex: 0 0 65%;
    }
  }
}
@media (max-width: 768px) {
  .nft {
    .item {
      margin-bottom: 1.4vw;
    }
  }
}
.box-content {
  border-width: 1px;
  border-color: #bebaba;
  border-style: solid;
  padding: 0.6vw;
  border-radius: 0.6vw;
  display: flex;
  justify-content: space-between;
  margin-bottom: 1vw;
  .item {
    width: 100%;
    padding: 0.2vw;
    h3 {
      font-size: 0.9vw;
      color: #6b6666;
      margin-bottom: 0.3vw;
    }
    p {
      margin-bottom: 0;
    }
  }

  @media (max-width: 768px) {
    width: calc(100% - 11.837vw);
    padding: 2vw;
    border-radius: 1.2vw;
    flex-direction: column;
    .item {
      width: 100%;
      h3 {
        font-size: 2.8vw;
        margin-bottom: 0.6vw;
      }
      p {
        margin-bottom: 1vw;
        padding-bottom: 0;
      }
    }
  }
}
.btn-image-wrapper {
  outline: none;
  border: none;
  background: none;
  width: 2vw;
  height: 2vw;
  padding: 0vw;
  margin-right: 0.625vw;
  border-radius: 50%;
  img {
    border-radius: 50%;
    width: 2vw;
    height: 2vw;
  }
  @media (max-width: 768px) {
    width: 7.97vw;
    height: 7.97vw;
    margin-right: 3.62vw;
    img {
      width: 7.97vw;
      height: 7.97vw;
    }
  }
}
.paginate-content {
  @media (max-width: 768px) {
    font-size: 4vw;
  }
}
.btn-image-wrapper:hover {
  background: var(--primary-border-color);
}
.btn-image-wrapper:last-child {
  margin-right: 0;
}
.btn-image-wrapper:disabled {
  background: var(--primary-border-color);
  color: var(--quaternary-font-color);
}
