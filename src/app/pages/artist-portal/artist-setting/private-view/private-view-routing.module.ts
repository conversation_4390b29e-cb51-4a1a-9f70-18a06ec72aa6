import { ArtShowComponent } from './art-show/art-show.component';
import { GalleryPreviewComponent } from './gallery-preview/gallery-preview.component';
import { ListComponent } from './list/list.component';
import { AllComponent } from './all/all.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ArtistSettingComponent } from '../artist-setting.component';
import { GalleryPreview2Component } from './gallery-preview2/gallery-preview.component';

const routes: Routes = [
  {
    path: 'edit/:id',
    component: AllComponent,
  },
  {
    path: 'list',
    component: ArtistSettingComponent,
    children: [
      {
        path: '',
        component: ListComponent,
      },
    ],
  },
  {
    path: 'gallery',
    component: GalleryPreviewComponent,
  },
  {
    path: 'public',
    component: GalleryPreview2Component,
  },
  {
    path: 'gallery/show',
    component: ArtShowComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PrivateViewRoutingModule {}
