import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
  isDropDownOpen = Array(10).fill(false);
  isDropDownOpen2 = Array(10).fill(false);
  form: FormGroup;
  selectedFiles=[];
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
  ) { }




  ngOnInit(): void {
    this.form = this.formBuilder.group({
      TypeofTerms: new FormControl(),
      showImage: new FormControl(),
      showVideo: new FormControl(),
      relatedVideo: new FormControl(),
      matchingMedium: new FormControl(),
      matchingMovement: new FormControl(),
      matchingSubject: new FormControl(),
      publish: new FormControl(false),
    });
  }


  onFileSelect(files, key) {
    this.selectedFiles[key] = files;
    console.log(this.selectedFiles);
    if (files.length == 0) {
      this.selectedFiles[key] = [];
    } else {
      
    }
  }



  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen2[index] = false;
    }, 200);
  }


  onSubmit() {
  }
}