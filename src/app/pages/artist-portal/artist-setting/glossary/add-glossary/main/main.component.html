<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form [formGroup]="form">
                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Publish:</div>
                            <label class="switch">
                <input type="checkbox" formControlName="publish" />
                <span class="slider round"></span>
              </label>
                        </div>
                        <div class="input-info">
                            Choose whether to publish the Glossary term on the website.
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Glossary Term" />
                            <div class="placeholder">Glossary Term</div>
                            <div class="input-info">
                                Provide the name for the Glossary term. e.g.: Abstraction
                            </div>
                        </div>
                        <div class="field-value">
                            <div class="input-container" (focusout)="changeFocus(0)">
                                <input type="text" class="selection" formControlName="TypeofTerms" placeholder="Type of Terms" [readonly]="true" (focus)="isDropDownOpen2[0] = true" />
                                <div class="placeholder">Type of Terms</div>
                                <button (click)="isDropDownOpen2[0] = !isDropDownOpen2[0]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen2[0],
                    'dropdown-visible': isDropDownOpen2[0]
                  }">
                                    <ul>
                                        <li (click)="isDropDownOpen2[0] = false">
                                            <div class="country-name">Art Term</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen2[0] = false;
                        form.get('TypeofTerms').setValue('Movement')
                      ">
                                            <div class="country-name">Movement</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the type of art term to categorise on the Glossary list page
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 1.47vw; font-size: 1.25vw">Description</div>
                    <div class="field-value">
                        <angular-editor id="editor24"></angular-editor>
                        <div class="input-info">
                            Provide the description for the glossary term.
                        </div>
                    </div>

                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Image:</div>
                            <label class="switch">
                <input type="checkbox" formControlName="showImage" />
                <span class="slider round"></span>
              </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show an image representing the Glossary term
                        </div>
                    </div>

                    <div class="upload-head">Image Upload</div>
                    <div class="field-value flex-block" [hidden]="false">
                        <image-upload [accept]="'image/*'" [selectedData]="selectedFiles" (onFileChange)="onFileSelect($event, 'profileArr')" [fileSize]="8388608" [Disable]="false"></image-upload>
                        <div class="input-info">
                            Upload an image to be used for the Glossary page on the website. Image needs to be cropped to content, with no shadows/background space. Image will be optimised for website. Please name the image files in the following format:&lt;artist_name&gt;_&lt;artwork_title&gt;.
                        </div>
                    </div>

                    <div class="field-value">
                        <div class="input-container">
                            <div class="text-before">Show Video:</div>
                            <label class="switch">
                <input type="checkbox" formControlName="showVideo" />
                <span class="slider round"></span>
              </label>
                        </div>
                        <div class="input-info">
                            Choose whether to show video representing the Glossary term
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Url" />
                            <div class="placeholder">Video URL</div>
                            <div class="input-info">
                                Provide the Vimeo link of video.
                            </div>
                        </div>
                    </div>
                    <p style="text-align: center;margin-top: 2vw;margin-bottom: 0;">OR</p>
                    <div class="upload-head">Video Upload</div>
                    <div class="field-value flex-block" [hidden]="false">
                        <image-upload [accept]="'video/*'" [selectedData]="selectedFiles" (onFileChange)="onFileSelect($event, 'profileArr')" [fileSize]="8388608" [Disable]="false"></image-upload>
                        <div class="input-info">
                            Upload an image (JPEG, PNG, GIF) to represent the Glossary term on the website
                        </div>
                    </div>

                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <input type="text" placeholder="Url" />
                            <div class="placeholder">Page URL</div>
                            <div class="input-info">
                                Provide the URL to be used for the Glossary term. e.g.: acrylic-paint
                            </div>
                        </div>
                        <div class="field-value">
                            <div class="input-container" (focusout)="changeFocus(1)">
                                <input type="text" class="selection" placeholder="Related Video" formControlName="relatedVideo" readonly (focus)="isDropDownOpen2[1] = true" />
                                <div class="placeholder">Related Video</div>
                                <button (click)="isDropDownOpen2[1] = !isDropDownOpen2[1]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen2[1],
                    'dropdown-visible': isDropDownOpen2[1]
                  }">
                                    <ul>
                                        <li (click)="
                        isDropDownOpen2[1] = false;
                        form
                          .get('relatedVideo')
                          .setValue('list of ed series videos')
                      ">
                                            <div class="country-name">list of ed series videos</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen2[1] = false;
                        form
                          .get('relatedVideo')
                          .setValue('list of ed series videos')
                      ">
                                            <div class="country-name">list of ed series videos</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the related video from the Ed series videos
                            </div>
                        </div>
                    </div>
                    <div class="field-value">
                        <multi-input placeholder="Related Artists"></multi-input>
                        <div class="input-info">
                            Choose artists from the database to tag with the glossary term.
                        </div>
                    </div>
                    <div class="field-value">
                        <multi-input placeholder="Related Glossary"></multi-input>
                        <div class="input-info">
                            Choose related glossary terms to highlight on the page.
                        </div>
                    </div>
                    <div class="field-value">
                        <multi-input placeholder="Related Medium"></multi-input>
                        <div class="input-info">
                            Choose medium keywords related to the glossary term.
                        </div>
                    </div>
                    <div class="field-value">
                        <multi-input placeholder="Related Movement"></multi-input>
                        <div class="input-info">
                            Choose movement keywords related to the glossary term.
                        </div>
                    </div>
                    <div class="field-value">
                        <multi-input placeholder="Related Subject"></multi-input>
                        <div class="input-info">
                            Choose subject keywords related to the glossary term.
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <div class="input-container" (focusout)="changeFocus(2)">
                                <input type="text" class="selection" placeholder="Matching Movement" formControlName="matchingMovement" readonly (focus)="isDropDownOpen2[2] = true" />
                                <div class="placeholder">Matching Movement</div>
                                <button (click)="isDropDownOpen2[2] = !isDropDownOpen2[2]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen2[2],
                    'dropdown-visible': isDropDownOpen2[2]
                  }">
                                    <ul>
                                        <li (click)="
                        isDropDownOpen2[2] = false;
                        form
                          .get('matchingMovement')
                          .setValue('list of movement keywords')
                      ">
                                            <div class="country-name">list of movement keywords</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen2[2] = false;
                        form
                          .get('matchingMovement')
                          .setValue('list of movement keywords')
                      ">
                                            <div class="country-name">list of movement keywords</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the matching movement term (if applicable) to link content on the site.
                            </div>
                        </div>
                        <div class="field-value">
                            <div class="input-container" (focusout)="changeFocus(3)">
                                <input type="text" class="selection" placeholder="Matching Medium" formControlName="matchingMedium" readonly (focus)="isDropDownOpen2[3] = true" />
                                <div class="placeholder">Matching Medium</div>
                                <button (click)="isDropDownOpen2[3] = !isDropDownOpen2[3]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen2[3],
                    'dropdown-visible': isDropDownOpen2[3]
                  }">
                                    <ul>
                                        <li (click)="
                        isDropDownOpen2[3] = false;
                        form
                          .get('matchingMedium')
                          .setValue('list of medium keywords')
                      ">
                                            <div class="country-name">list of medium keywords</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen2[3] = false;
                        form
                          .get('matchingMedium')
                          .setValue('list of medium keywords')
                      ">
                                            <div class="country-name">list of medium keywords</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the matching medium term (if applicable) to link content on the site.
                            </div>
                        </div>
                    </div>
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw">
                            <div class="input-container" (focusout)="changeFocus(4)">
                                <input type="text" class="selection" formControlName="matchingSubject" readonly placeholder="Matching Subject" (focus)="isDropDownOpen2[4] = true" />
                                <div class="placeholder">Matching Subject</div>
                                <button (click)="isDropDownOpen2[4] = !isDropDownOpen2[4]" type="button">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                                <div [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen2[4],
                    'dropdown-visible': isDropDownOpen2[4]
                  }">
                                    <ul>
                                        <li (click)="
                        isDropDownOpen2[4] = false;
                        form
                          .get('matchingSubject')
                          .setValue('list of subject keywords')
                      ">
                                            <div class="country-name">list of subject keywords</div>
                                        </li>
                                        <li (click)="
                        isDropDownOpen2[4] = false;
                        form
                          .get('matchingSubject')
                          .setValue('list of subject keywords')
                      ">
                                            <div class="country-name">list of subject keywords</div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="input-info">
                                Choose the matching subject term (if applicable) to link content on the site.
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>