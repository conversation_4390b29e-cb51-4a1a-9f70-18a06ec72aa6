import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-add-glossary',
  templateUrl: './add-glossary.component.html',
  styleUrls: ['./add-glossary.component.scss'],
})
export class AddGlossaryComponent implements OnInit, OnDestroy {
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  routerObserver: Subscription;
  activeMenu = 'main';
  faArrowAltCircleLeft=faArrowAltCircleLeft;

  ngOnDestroy(): void {
    //this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/glossary/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/glossary/add/seo':
        this.activeMenu = 'seo';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }
}
