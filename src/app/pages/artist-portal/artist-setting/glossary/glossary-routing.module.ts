import { MainComponent as MainContent } from './add-glossary/main/main.component';
import { AddGlossaryComponent } from './add-glossary/add-glossary.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MainComponent } from './main/main.component';
import { SeoComponent } from './add-glossary/seo/seo.component';

const routes: Routes = [
   {
    path: '',
    component: MainComponent
  },
  {
    path: 'add',
    component: AddGlossaryComponent,
    data: {
      hidebread: true,
    },
    children: [
      {
        path: '',
        component: MainContent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'seo',
        component: SeoComponent,
        data: {
          hidebread: true,
        },
      },]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GlossaryRoutingModule { }
