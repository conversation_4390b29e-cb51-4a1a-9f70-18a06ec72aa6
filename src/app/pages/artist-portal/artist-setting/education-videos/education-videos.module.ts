import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { EducationVideosRoutingModule } from './education-videos-routing.module';
import { MainComponent } from './main/main.component';
import { AddVideosComponent } from './add-videos/add-videos.component';

import { MainComponent as MainContent } from './add-videos/main/main.component';
import { SeoComponent } from './add-videos/seo/seo.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { NgxPaginationModule } from 'ngx-pagination';
import { ImageUploadModule } from 'src/app/core/image-upload/image-upload.module';
import { MultiInputModule } from 'src/app/core/multi-input/multi-input.module';
import { PlaceholderModule } from 'src/app/core/placeholder/placeholder.module';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [MainComponent, AddVideosComponent,SeoComponent,MainContent],
  imports: [
    CommonModule,
    EducationVideosRoutingModule,
    NgxPaginationModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    AngularEditorModule,
    NgxQRCodeModule,
    MultiInputModule,
    ImageUploadModule,
    PlaceholderModule,
  ]
})
export class EducationVideosModule { }
