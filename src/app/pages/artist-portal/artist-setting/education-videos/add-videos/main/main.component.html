<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="title"
                type="text"
                placeholder="Page Title"
              />
              <div class="placeholder">Page Title</div>
              <div class="input-info">
                Provide the title to be used on the page.
              </div>
            </div>
            <div class="field-value">
              <input formControlName="url" type="text" placeholder="Page URL" />
              <div class="placeholder">Page URL</div>
              <div class="input-info">
                Provide the text to be used as page url. e.g.: abstraction.
              </div>
            </div>
          </div>
          <div style="margin-top: 1.47vw; font-size: 1.25vw">Thumbnail</div>
          <div class="field-value flex-block">
            <image-upload
              [accept]="'image/*'"
              [selectedData]="selectedFiles"
              (onFileChange)="onFileSelect($event)"
              [fileSize]="12582912"
              [placeholder]=""
              [hideAltText]="true"
            ></image-upload>
            <div class="input-info">
              Provide the banner image to be used on the profile page (Image
              will be centered and cropped to 1440x246 px for Desktop & 414x277
              px for Mobile).
            </div>
          </div>

          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="oneLineDesc"
                type="text"
                placeholder="One Line Desc"
              />
              <div class="placeholder">One Line Desc</div>
              <div class="input-info">
                Provide a one-line description to be used on the Video series
                page along with the thumbnails.
              </div>
            </div>
            <div class="field-value">
              <input
                formControlName="videoTitle"
                type="text"
                placeholder="Video Title"
              />
              <div class="placeholder">Video Title</div>
              <div class="input-info">
                Provide the title to be used on the Video thumbnail on the page.
                e.g.: What is Abstraction?.
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="videoUrl"
                type="text"
                placeholder="Video URL"
              />
              <div class="placeholder">Video URL</div>
              <div class="input-info">
                Provide the Vimeo player link for the video to be featured on
                the page. e.g.: https://www.player.vimeo.com/12342143.
              </div>
            </div>
            <div class="field-value">
              <input
                formControlName="videoText"
                type="text"
                placeholder="Video Text"
              />
              <div class="placeholder">Video Text</div>
              <div class="input-info">
                Provide the text to be used on the video, below the video title.
                Ideally 1 line about the subject.
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                *ngIf="!htmlcheckBox[0]"
                formControlName="description"
                type="text"
                placeholder="Description"
              />
              <angular-editor
                *ngIf="htmlcheckBox[0]"
                [placeholder]="'Description'"
                id="editor6"
                formControlName="description"
              ></angular-editor>
              <div class="placeholder">Description</div>
              <div class="input-info">
                Provide the full text description of the video/term. Use HTML
                styling where needed, to give hover hints.
                <div
                  style="display: inline-block; text-align: right; width: 100%"
                >
                  <input
                    type="checkbox"
                    [(ngModel)]="htmlcheckBox[0]"
                    [ngModelOptions]="{ standalone: true }"
                  />

                  HTML Editor
                </div>
              </div>
            </div>
          </div>
          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Publish Video:</div>
              <label class="switch">
                <input type="checkbox" formControlName="publish" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to publish the video on the website.
            </div>
          </div>
          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Video Text:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showVideoTtext" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show text description on the video thumbnail on
              the page.
            </div>
          </div>
          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Artists:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showArtists" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show Artists section on the video page.
            </div>
          </div>
          <div class="field-value" *ngIf="form.get('showArtists').value">
            <multi-input
              placeholder="Choose artists from the list"
              [itemsArray]="artist_select"
              [previosItems]="selected_artist"
              [newEntry]="false"
              [disableSearch]="true"
              (searchEvent)="artist_search = $event; getArtists()"
              (selectedItem)="getSelectedItem($event, 0)"
            ></multi-input>
            <div class="input-info">
              Choose 3 to 6 artists to feature on the video page who work in the
              field/medium described in the video.
            </div>
          </div>
          <!-- <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Related Artworks:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showRelatedArtworks" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show related Artworks to the subject.
            </div>
          </div> -->
          <!-- <div
            class="field-value"
            *ngIf="form.get('showRelatedArtworks').value"
          >
            <multi-input placeholder="Select Artworks"></multi-input>
            <div class="input-info">
              Choose 4 to 8 artworks to feature on the video page that are
              examples of the term described in the video.
            </div>
          </div> -->
          <div class="field-value">
            <multi-input
              placeholder="Other Terms"
              [itemsArray]="term_select"
              [previosItems]="selected_term"
              [newEntry]="false"
              [disableSearch]="true"
              (searchEvent)="term_search = $event; getTerms()"
              (selectedItem)="getSelectedItem2($event, 0)"
            ></multi-input>
            <div class="input-info">
              Choose 3 other videos to recommend to the user.
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
