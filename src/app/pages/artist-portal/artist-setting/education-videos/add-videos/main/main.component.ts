import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { async } from 'rxjs';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss']
})
export class MainComponent implements OnInit {
  // isDropDownOpen = Array(10).fill(false);
  // isDropDownOpen2 = Array(10).fill(false);
  form: FormGroup;
  selectedFiles: any[] = [];

  artist_search = '';
  artist_limit = 12;
  artist_select = [];
  selected_artist = [];


  term_search = '';
  term_limit = 12;
  term_select = [];
  selected_term = [];
  htmlcheckBox = Array(10).fill(false);

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private server: CollectorService,
  ) { }


  // async onFileSelect(index: number, files: FileList) {
  //   console.log(index);

  //   if (files[0].size < 2100000) {
  //     this.selectedFiles[index].push(files[0]);
  //   }
  //   console.log(this.selectedFiles);
  // }

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      title: new FormControl(''),
      url: new FormControl(''),
      thumbnail: new FormControl(''),
      oneLineDesc: new FormControl(''),
      videoTitle: new FormControl(''),
      videoUrl: new FormControl(''),
      videoText: new FormControl(''),
      description: new FormControl(''),
      artists: new FormControl(''),
      otherTerms: new FormControl(''),
      publish: new FormControl(false),
      showVideoTtext: new FormControl(false),
      showArtists: new FormControl(false),
      showRelatedArtworks: new FormControl(false),
    });
    if (localStorage.getItem('edVideoID')) {
      this.getVideo();
    }
    this.getArtists();
    this.getTerms();
  }
  getVideo() {
    let url = `ed-videos/${localStorage.getItem('edVideoID')}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.form.patchValue(res?.data);
        res.data?.artists?.forEach((a) => {
          this.selected_artist.push({ id: a._id, name: a.display_name });
        });
        res.data?.otherTerms?.forEach((a) => {
          this.selected_term.push({ id: a._id, name: a.title });
        });
        if (res.data.thumbnail) {
          this.selectedFiles.push({
            url: res.data.thumbnail,
            type: 'image',
            name: 'File',
            size: '',
          });
        }

      }
    });
  }

  getArtists() {
    let url = `api/artist?limit=${this.artist_limit}&search=${this.artist_search}&offset=1`;
    this.server.getApi(url).subscribe((res) => {
      this.artist_select = res.data.map((a) => {
        return {
          id: a._id,
          name: a.display_name,
        };
      });
    });
  }
  getTerms() {
    let url = `ed-videos/all?limit=${this.term_limit}&search=${this.term_search}&offset=1`;
    this.server.getApi(url).subscribe((res) => {
      this.term_select = res.data?.videos?.map((a) => {
        return {
          id: a._id,
          name: a?.title,
        };
      });
    });
  }

  getSelectedItem(a, i) {
    this.selected_artist = a;
  }
  getSelectedItem2(a, i) {
    this.selected_term = a;
  }
  onFileSelect(files) {
    this.selectedFiles = files;
    console.log(this.selectedFiles);
    if (files.length == 0) {
      this.selectedFiles = [];
    } else {
      this.uploadFile(files);
    }
  }

  // to upload file
  uploadFile(files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFiles[0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onSubmit() {
    let url = `ed-videos`;


    let req = {
      title: this.form.getRawValue().title,
      url: this.form.getRawValue().url,
      thumbnail: this.selectedFiles.length > 0
        ? this.selectedFiles[0].url
        : '',
      oneLineDesc: this.form.getRawValue().oneLineDesc,
      videoTitle: this.form.getRawValue().videoTitle,
      videoUrl: this.form.getRawValue().videoUrl,
      videoText: this.form.getRawValue().videoText,
      description: this.form.getRawValue().description,
      artists: this.selected_artist.map((a) => a.id),
      otherTerms: this.selected_term.map((a) => a.id),
      publish: this.form.getRawValue().publish,
      showVideoTtext: this.form.getRawValue().showVideoTtext,
      showArtists: this.form.getRawValue().showArtists,
      showRelatedArtworks: this.form.getRawValue().showRelatedArtworks,
    };
    if (localStorage.getItem('edVideoID')) {
      req['_id'] = localStorage.getItem('edVideoID');
    }
    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('term updated successfully!');
      }
    });
  }
}