import { AddVideosComponent } from './add-videos/add-videos.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MainComponent } from './main/main.component';
import { MainComponent as MainContent } from './add-videos/main/main.component';
import { SeoComponent } from './add-videos/seo/seo.component';
const routes: Routes = [
  {
   path: '',
   component: MainComponent
 },
 {
   path: 'add',
   component: AddVideosComponent,
   data: {
     hidebread: true,
   },
   children: [
     {
       path: '',
       component: MainContent,
       data: {
         hidebread: true,
       },
     },
     {
       path: 'seo',
       component: SeoComponent,
       data: {
         hidebread: true,
       },
     },]
 }
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EducationVideosRoutingModule { }
