<div id="mainContent" [formGroup]="form">
  <div class="heading">
    <div class="head">
      <div>
        Education Videos
        <a (click)="navigateTo()"><button class="saveBtn">Add +</button></a>
      </div>
      <div class="filter">
        <input
          type="text"
          placeholder="Search by Videos"
          name="search"
          [(ngModel)]="searchValue"
          [ngModelOptions]="{ standalone: true }"
          (keyup)="searchResult()"
        />
        <div
          class="input-container"
          style="display: flex; float: right; margin-top: 1.5vw"
        >
          <div
            class="text-before"
            [innerHtml]="
              form.get('showListView').value ? 'List view' : 'Grid view'
            "
          ></div>
          <label class="switch">
            <input type="checkbox" formControlName="showListView" />
            <span class="slider round"></span>
          </label>
        </div>
      </div>
    </div>
  </div>

  <div class="collector-form">
    <div class="interests">
      <div class="contents" *ngIf="!form.get('showListView').value">
        <div
          *ngFor="let art of current; let i = index"
          class="artist"
          style="position: relative"
        >
          <img
            (click)="editArtWork(art)"
            class="artworkImggg"
            [src]="
              'https://www.terrain.art/cdn-cgi/image/width=250,quality=52/' +
              art?.thumbnail
            "
          />
          <a (click)="showMenu[i] = !showMenu[i]">
            <div class="edit_grid"></div>
            <div *ngIf="showMenu[i]" class="dropdown-container">
              <div class="dropdown-content">
                <a (click)="editArtWork(art)"
                  ><div style="padding: 0.6vw; cursor: pointer; color: black">
                    Edit
                  </div></a
                >
                <a (click)="publishArtWork(art)"
                  ><div style="padding: 0.6vw; cursor: pointer; color: black">
                    Publish
                  </div></a
                >
              </div>
            </div>
          </a>
          <!-- <div class="icon-indicators">
              <div
                [ngClass]="art?.published ? 'published-dot' : 'unpublished-dot'"
              ></div>
              <div *ngIf="temporary.artwork_type == 'Edition'" class="edition">
                E
              </div>
              <div
                class="status-dot"
                [ngStyle]="dotColor(art?.sale_options)"
              ></div>
              <div
                [ngClass]="temporary.minted ? 'minted' : 'not-minted'"
                [ngStyle]="MintlockColor(temporary.minted)"
              ></div>
              <img
                class="blockchainIcon"
                *ngIf="temporary.minted"
                [src]="blockchainType(temporary.network)"
                alt="Blockchain Type"
              />
            </div> -->
          <div class="wrappp">
            <div class="name">
              <i>{{ art?.title }}</i>
            </div>
            <!-- <div class="name">₹ {{ art?.sale_price }}</div> -->
            <!-- <div class="name">NFT id : {{ temporary?.NFT_id }}</div> -->
          </div>
          <!-- <div class="wrappp">
              <div class="name">
                <i>{{ art?.artwork_title }}</i>, {{ art?.startYear }}
              </div>
              <div class="name">{{ art?.artist_id?.display_name }}</div>
              <div class="name">₹ {{ art?.sale_price }}</div>
              <div class="name">NFT id : {{ temporary?.NFT_id }}</div>
            </div> -->
        </div>
        <ng-container *ngIf="isLoading">
          <div *ngFor="let item of temporary2" class="artist">
            <app-placeholder height="10.69vw" width="14.93vw">
            </app-placeholder>
          </div>
        </ng-container>
      </div>

      <div class="contentList" *ngIf="form.get('showListView').value">
        <div class="Table">
          <div
            class="Table-row"
            *ngFor="let art of current; let i = index"
            (click)="openNav(i)"
            [ngStyle]="
              list_selection == i
                ? { 'background-color': '#b0c7dda3', color: '#fff' }
                : {}
            "
          >
            <div class="Table-row-item img-item" data-header="Header1">
              <img
                class="img-artwork-thumbnail"
                [src]="art?.thumbnail_of_primary"
                alt="thumbnail"
              />
            </div>
            <div class="Table-row-item" data-header="Header2">
              <div
                [ngClass]="art?.published ? 'published-dot' : 'unpublished-dot'"
              ></div>
              <div
                *ngIf="temporary.artwork_type == 'Edition'"
                class="editionList"
              >
                E
              </div>
              <div
                class="status-dot"
                [ngStyle]="dotColor(temporary.status)"
              ></div>
              <div
                [ngClass]="temporary.minted ? 'minted' : 'not-minted'"
                [ngStyle]="MintlockColor(temporary.minted)"
              ></div>
              <img
                class="blockchainIconList"
                *ngIf="temporary.minted"
                [src]="blockchainType(temporary.network)"
                alt="Blockchain Type"
              />
            </div>
            <!-- <div class="Table-row-item" data-header="Header2">
                <div [ngClass]="art?.published ? 'published-dot' : 'unpublished-dot'"></div>
                <div class="status-dot" [ngStyle]="dotColor(temporary.status)"></div>
                <div [ngClass]="temporary.minted ? 'minted' : 'not-minted'" [ngStyle]="lockColor(temporary.network)">
                </div>
              </div> -->
            <div
              class="Table-row-item title-art"
              [ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
              data-header="Header3"
            >
              <i>{{ art?.artwork_title }}</i> ,{{ art?.startYear }}
            </div>
            <div
              class="Table-row-item"
              [ngClass]="isSidemenuOpen ? 'u-Flex-grow2' : 'u-Flex-grow3'"
              data-header="Header4"
            >
              {{ art?.artist_id?.display_name }}
            </div>
            <div
              class="Table-row-item"
              *ngIf="!isSidemenuOpen"
              data-header="Header5"
            >
              <!-- ₹{{ art?.sale_price }} -->
            </div>

            <div class="Table-row-item">
              <a (click)="editArtWork(art)">
                <div class="aserd"></div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div
  id="mySidenav"
  class="sidenav"
  [style.width]="isSidemenuOpen ? '400px' : 0"
>
  <div class="sidenav-content">
    <div class="closebtn" (click)="closeNav()">&times;</div>
    <div class="head-wrapper">
      <div class="img-side">
        <img [src]="current[list_selection]?.thumbnail_of_primary" />
      </div>
      <div class="text-side">
        <div class="text-container" style="line-height: 1">
          <b> {{ current[list_selection]?.artist_id?.display_name }} </b>
        </div>
        <div class="text-container">
          {{ current[list_selection]?.artwork_title }},
          {{ current[list_selection]?.startYear }}
        </div>

        <div class="text-container">
          {{ current[list_selection]?.medium }}
        </div>
        <div class="text-container">
          {{ current[list_selection]?.dimensions }}
        </div>
        <div class="text-container description">
          {{ current[list_selection]?.description }}
        </div>
        <div class="text-container">
          {{ temporary?.NFT_id }}
        </div>
        <div class="text-container">
          ₹ {{ current[list_selection]?.sale_price }}
        </div>
      </div>
    </div>
  </div>
</div>
