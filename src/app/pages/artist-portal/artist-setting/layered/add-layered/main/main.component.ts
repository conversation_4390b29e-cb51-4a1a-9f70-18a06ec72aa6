import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit {
  form: FormGroup;
  isDropDownOpen = Array(10).fill(false);
  selectedFilesObj: any = {
    bannerImageArr: [],
    thumbnailArr: [],
    bannerMediaArr: [],
    bannerBackgroundArr: [],
    bannerImage: '',
    blogThumbnail: '',
  };
  placeholder: any = '';
  acceptType: any = 'image/*';
  permissionsObj: any = {};

  constructor(
    private formBuilder: FormBuilder,
    public server: CollectorService,
    private router: Router
  ) {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Exhibitions'
    );
    console.log(this.permissionsObj);
  }

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      publishExhibition: new FormControl(false),
      pageUrl: new FormControl(''),
      bannerType: new FormControl(''),
      headerColour: new FormControl(''),
      title: new FormControl(''),
      bannerTitle: new FormControl(''),
      titleSize: new FormControl(''),
      bannerTextColor: new FormControl(''),
      showAboveSubtitle: new FormControl(false),
      aboveSubText: new FormControl(''),
      aboveSubSize: new FormControl(''),
      showBelowSubtitle: new FormControl(false),
      belowSubSize: new FormControl(''),
      // position:new FormControl(''),
      belowSubText: new FormControl(''),
      showArtistName: new FormControl(false),
      artistName: new FormControl(''),
      artistFontSize: new FormControl(''),
      showDate: new FormControl(false),
      exhibitionDateStart: new FormControl(''),
      exhibitionDateEnd: new FormControl(''),
      exhibitionDateNote: new FormControl(''),
    });
    if (localStorage.getItem('exhibitionIdID')) {
      this.getExhibitionById();
    }
  }

  // to get exhibition by id
  getExhibitionById() {
    let url =
      apiUrl.exhibitions.getExhibitions +
      `/${localStorage.getItem('exhibitionIdID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.form.patchValue({
          pageUrl: res.data.pageUrl,
          publishExhibition: res.data.publish,
          headerColour: res.data.headerColor,
          title: res.data.title,
          bannerTitle: res.data.titleBanner.media,
          artistName: res.data.artistName,
          bannerType: res.data.titleBanner.type,
          bannerTextColor: res.data.titleBanner.textColor,
          titleSize: res.data.titleBanner.titleSize,
          showAboveSubtitle: res.data.titleBanner.aboveSubTitle.show,
          aboveSubText: res.data.titleBanner.aboveSubTitle.text,
          aboveSubSize: res.data.titleBanner.aboveSubTitle.size,

          showBelowSubtitle: res.data.titleBanner.belowSubTitle.show,
          belowSubText: res.data.titleBanner.belowSubTitle.text,
          belowSubSize: res.data.titleBanner.belowSubTitle.size,
          // "belowSubSizePosition": res.data.titleBanner.belowSubTitle.position,

          showArtistName: res.data.titleBanner.showArtistName,
          artistFontSize: res.data.titleBanner.artistNameSize,
          showDate: res.data.titleBanner.showDate,
          exhibitionDateStart: res.data.exhibitionDateStart,
          exhibitionDateEnd: res.data.exhibitionDateEnd,
          exhibitionDateNote: res.data.exhibitionDateNote,
        });
        if (res.data.bannerImage) {
          this.selectedFilesObj.bannerImageArr.push({
            url: res.data.bannerImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.thumbnail) {
          this.selectedFilesObj.thumbnailArr.push({
            url: res.data.thumbnail,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.titleBanner.media) {
          this.selectedFilesObj.bannerMediaArr.push({
            url: res.data.titleBanner.media,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.titleBanner.backgroundImage) {
          this.selectedFilesObj.bannerBackgroundArr.push({
            url: res.data.titleBanner.backgroundImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
      }
    });
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onMultiAdd(files, key) {
    this.selectedFilesObj[key] = files;
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    }
  }

  onSubmit() {
    if (!this.permissionsObj.update) {
      alert('Update permissions denied!');
      return;
    }
    let req = {
      pageUrl: this.form.value.pageUrl,
      publish: this.form.value.publishExhibition,
      thumbnail:
        this.selectedFilesObj.thumbnailArr.length > 0
          ? this.selectedFilesObj.thumbnailArr[0].url
          : '',
      bannerImage:
        this.selectedFilesObj.bannerImageArr.length > 0
          ? this.selectedFilesObj.bannerImageArr[0].url
          : '',
      headerColor: this.form.value.headerColour,
      title: this.form.value.title,
      artistName: this.form.value.artistName,
      exhibitionDateStart: this.form.value.exhibitionDateStart,
      exhibitionDateEnd: this.form.value.exhibitionDateEnd,
      exhibitionDateNote: this.form.value.exhibitionDateNote,
      titleBanner: {
        type: this.form.value.bannerType,
        media:
          this.form.value.bannerType === 'Video'
            ? this.form.value.bannerTitle
            : this.selectedFilesObj.bannerMediaArr.length > 0
            ? this.selectedFilesObj.bannerMediaArr[0].url
            : '',
        backgroundImage:
          this.selectedFilesObj.bannerBackgroundArr.length > 0
            ? this.selectedFilesObj.bannerBackgroundArr[0].url
            : '',
        textColor: this.form.value.bannerTextColor,
        titleSize: this.form.value.titleSize,
        aboveSubTitle: {
          show: this.form.value.showAboveSubtitle,
          text: this.form.value.aboveSubText,
          size: this.form.value.aboveSubSize,
        },
        belowSubTitle: {
          show: this.form.value.showBelowSubtitle,
          text: this.form.value.belowSubText,
          size: this.form.value.belowSubSize,
          // 'position' : this.form.value.position
        },
        showArtistName: this.form.value.showArtistName,
        artistNameSize: this.form.value.artistFontSize,
        showDate: this.form.value.showDate,
      },
    };
    let url = localStorage.getItem('exhibitionIdID')
      ? apiUrl.exhibitions.getExhibitions +
        `/${localStorage.getItem('exhibitionIdID')}`
      : apiUrl.exhibitions.getExhibitions;
    if (localStorage.getItem('exhibitionIdID')) {
      this.updateBlog(req, url);
      return;
    }
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('exhibitionIdID', res.data['_id']);
        alert(res.message);
      }
    });
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('exhibitionIdID', res.data['_id']);
        alert(res.message);
        this.router.navigate(['artist-portal/settings/layered']);
      }
    });
  }

  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 500);
  }
}
