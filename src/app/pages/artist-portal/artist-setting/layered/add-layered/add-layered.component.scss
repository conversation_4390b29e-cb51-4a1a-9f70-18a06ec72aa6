.footer-nav {
	height: 6.3vw;
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	-webkit-backdrop-filter: blur(10px);
	backdrop-filter: blur(10px);
	background-color: rgba(255, 255, 255, 0);
	@media (max-width: 768px) {
		padding-bottom: 11.24vw;
	}
	.button-group {
		//margin-left: 21.66vw;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		font-size: 1.25vw;
		@media (max-width: 768px) {
			font-size: 4.34vw;
			margin-left: 0vw;
			align-items: unset;
		}
		.next {
			width: 13.33vw;
			height: 3.05vw;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 1.49vw;
			border: solid 0.069vw #004ddd;
			color: #004ddd;
			@media (max-width: 768px) {
				width: 46.37vw;
				height: 10.62vw;
				border-radius: 6.08vw;
			}
		}
		.back {
			margin-left: 2.77vw;
			color: #808080;
		}
	}
}
.slider-header {
	h3 {
		cursor: pointer;
		font-size: 1.25vw;
		padding-bottom: 0.34vw;
		margin-right: 2.77vw;
		color: var(--quaternary-font-color);
		&.active {
			border-bottom: 2px solid var(--tertiary-font-color);
			color: var(--primary-font-color);
		}
		@media (max-width: 768px){
			font-size: 3.86vw;
			padding-bottom: 1.34vw;
			margin-right: 3.86vw;
		}
	}
	margin-bottom: 3.47vw;
}
a:hover {
	text-decoration: none;
}
