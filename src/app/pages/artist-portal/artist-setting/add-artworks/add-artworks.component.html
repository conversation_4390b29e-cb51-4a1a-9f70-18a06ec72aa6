<artwork-wiz
  [total_steps]="total_steps"
  [current_step]="current_step"
  (backStep)="current_step = current_step - 1"
>
  <ng-container [ngSwitch]="current_step">
    <basics-content
      [(form)]="form"
      *ngSwitchCase="1"
      (onNext)="onNextEvent()"
    ></basics-content>
    <artwork-info-content
      [(form)]="form"
      *ngSwitchCase="2"
      (onNext)="onNextEvent()"
    ></artwork-info-content>
    <financial-details
      [(form)]="form"
      (onNext)="onNextEvent()"
      *ngSwitchCase="3"
    ></financial-details>
    <artwork-preview-content
      [(form)]="form"
      *ngSwitchCase="4"
      (onNext)="onNextEvent()"
    ></artwork-preview-content>
  </ng-container>
</artwork-wiz>
