import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { apiUrl } from 'src/environments/environment.prod';
import { CollectorService } from 'src/app/services/collector.service';
import MetaMaskOnboarding from '@metamask/onboarding';
import { Web3Service } from 'src/app/shared/services/web3.service';
import { Router } from '@angular/router';

interface Itype {
  name: string;
  value: string;
}
@Component({
  selector: 'artwork-preview-content',
  templateUrl: './preview.component.html',
  styleUrls: ['./preview.component.scss'],
})
export class ArtworkPreviewComponent implements OnInit {
  @Input()
  form: FormGroup;

  @Output()
  formChange = new EventEmitter();

  @Output()
  onNext = new EventEmitter();

  artwork_type = 'Image';
  signed_status = 'No';
  artwork_file_type: Itype[] = [
    { name: 'Image', value: 'Image' },
    { name: 'Video', value: 'Video' },
    { name: '3D Asset', value: '3D Asset' },
    { name: 'HTML Code', value: 'HTML Code' },
  ];
  singature_: Itype[] = [
    { name: 'Yes', value: 'Yes' },
    { name: 'No', value: 'No' },
  ];

  onboarding;
  isMetamaskAvailable = false;
  selectedOption = 0;
  isCreator = false;
  walletConnected = false;

  isMiningAccess = -1;
  isMinted = false;
  isWaitMiniting = false;
  isTransfered = false;

  isAccessPopup = true;

  isTransferPopup = false;
  toAddress = null;
  artworksObj;

  networks = {
    ethereum_main_net: 1,
    polygon: 137,
    goerli_testnet: 5,
    polygon_testnet: 80001,
  };

  isWhiteListRequested = false;
  userDetailsObj;
  nftDetailsObj;

  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private web3Service: Web3Service,
    private ref: ChangeDetectorRef,
    private router: Router
  ) {
    this.onboarding = new MetaMaskOnboarding();
  }

  ngOnInit(): void {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetailsObj = JSON.parse(decode);
    this.web3Service.accountStatus$.subscribe((next) => {
      const address = next[0];
      console.log('address', address);
      if (address) {
        this.walletConnected = true;
        this.userDetailsObj['ethereum_account'] = address;
        this.web3Service.changeNetwork(this.form.get('network').value);
      }
    });
    this.web3Service.networkStatus$.subscribe(async (next) => {
      if (next == this.networks[this.form.get('network').value]) {
        this.isMiningAccess = -1;
        this.isAccessPopup = true;

        const isMiningAccess = await this.web3Service.checkMinter();
        isMiningAccess ? (this.isMiningAccess = 2) : (this.isMiningAccess = 1);
        this.ref.detectChanges();
        //this.isMiningAccess = await this.web3Service.checkMinter();
      }
    });
    this.isMetamaskAvailable = MetaMaskOnboarding.isMetaMaskInstalled();
    if (this.isMetamaskAvailable) {
      this.selectedOption = 1;
    } else {
      this.selectedOption = 0;
    }
  }

  onNextClick(publish: boolean, mint = false) {
    this.form.get('publish_artworks').setValue(publish);
    this.formChange.emit(this.form);
    //this.onNext.emit();
    let url = apiUrl.addArtwork;
    this.server.showSpinner();
    let req = this.form.value;
    req['artwork_group'] =
      this.userDetailsObj?.artist_type == 'Terrain' ||
      this.userDetailsObj?.artist_type == 'Gallery'
        ? 'curated'
        : 'open';
    this.server.postApi(url, req).subscribe(async (res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.artworksObj = res.data;
        if (!mint) {
          this.router.navigate(['/artist-portal/settings/artworks']);
          alert(res.message);
        } else {
          this.isWaitMiniting = true;
          const data = await this.web3Service.mintNFT(this.artworksObj._id);
          this.nftDetailsObj = {
            id: data.id,
            contract: data.contract,
            network: data.network,
            minter: data.minter,
          };
          this.isMinted = true;
          this.isWaitMiniting = false;
        }
      } else {
        alert('Something went wrong!');
      }
    });
  }
  onboardingMeta() {
    this.onboarding.startOnboarding();
  }

  async loginWithMeta() {
    this.isMiningAccess = 0;
    this.isAccessPopup = true;
    this.walletConnected = false;
    this.web3Service.connectAccount();
  }

  requestWhitelist() {
    this.isAccessPopup = false;
    let url = 'wallet-whitelist';
    this.server.showSpinner();
    let data = {
      address: this.userDetailsObj?.ethereum_account,
      email: this.userDetailsObj.email,
      network: this.form.get('network').value,
    };
    this.server.postApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.isWhiteListRequested = true;
      }
    });
  }
  saveWork() {
    this.isWhiteListRequested = false;
    this.isMinted = false;
    let url = apiUrl.addArtwork;
    let req = {
      nft: this.nftDetailsObj,
    };
    req['artworkId'] = this.artworksObj._id;

    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.router.navigate(['/artist-portal/settings/artworks']);
        alert(res.message);
      } else {
        alert('Something went wrong!');
      }
    });
  }
}
