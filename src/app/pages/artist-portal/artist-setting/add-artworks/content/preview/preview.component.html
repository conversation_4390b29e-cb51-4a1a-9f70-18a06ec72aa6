<p class="Heading">Preview</p>
<div class="profile-field">
  <div class="w-100 field-contents">
    <div class="container-box">
      <div class="image-container">
        <img [src]="form.get('primary_image').value[0]?.url" />
      </div>
      <div class="details-container">
        <div class="artwork-title">
          {{ form.get("artwork_title").value }}, {{ form.get("year").value }}
        </div>
        <div class="details">
          <div class="item">
            <img src="/assets/filter_icons/Sub_type.png" />
            {{ form.get("physicalOrDigital").value | titlecase }}
          </div>
          <div class="item">
            <img src="/assets/filter_icons/Work_type.png" />
            {{ form.get("work_type").value | titlecase }}
          </div>
          <div class="item">
            <img
              src="https://www.terrain.art/cdn-cgi/image/width=100,quality=72/https://ta-python.s3.us-east-2.amazonaws.com/1673040861025_lock-icon-transparent-8-1536x1536.png"
            />
            {{ form.get("mintType").value | titlecase }}
          </div>
          <div *ngIf="form.get('network').value" class="item">
            <img src="/assets/filter_icons/Network.png" />
            {{ form.get("network").value | titlecase }}
          </div>
        </div>
        <div class="price">
          INR {{ form.get("sale_price").value | number : "1.0" }}
        </div>
        <div class="box-content">
          <div class="item">
            <h3>Medium</h3>
            <p>{{ form.get("medium").value }}</p>
          </div>
          <div class="item">
            <h3>Dimensions</h3>
            <p>{{ form.get("dimensions").value }}</p>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="form.get('mintType').value !== 'mint_now'" class="section">
      <button
        (click)="onNextClick(false)"
        class="btn_save"
        style="margin-right: 1vw"
      >
        Save
      </button>
      <button
        *ngIf="
          userDetailsObj.role != 'REPRESENTED ARTIST' &&
          userDetailsObj.role != 'GALLERY ARTIST'
        "
        (click)="onNextClick(true)"
        class="btn_save"
      >
        Save & Publish
      </button>
    </div>
    <div *ngIf="form.get('mintType').value == 'mint_now'" class="section">
      <div class="buttonList" style="margin-top: 2.042vw; margin-bottom: 0">
        <button
          *ngIf="selectedOption === 0"
          class="save"
          style="padding: 0.833333vw 3.08333vw"
          (click)="onboardingMeta()"
        >
          Install Metamask
        </button>
        <button
          *ngIf="selectedOption === 1 && !walletConnected"
          class="save"
          style="padding: 0.833333vw 3.08333vw"
          (click)="loginWithMeta()"
        >
          Connect Metamask
        </button>
      </div>
    </div>
    <div
      *ngIf="
        selectedOption === 1 &&
        walletConnected &&
        isMiningAccess == 2 &&
        !isWaitMiniting &&
        !isMinted
      "
      class="section"
    >
      <button
        (click)="onNextClick(false, true)"
        class="btn_save"
        style="margin-right: 1vw"
      >
        Mint Now
      </button>
      <button
        *ngIf="userDetailsObj.role != 'REPRESENTED ARTIST'"
        (click)="onNextClick(true, true)"
        class="btn_save"
      >
        Mint & Publish
      </button>
    </div>
  </div>
</div>
<div>
  <div class="modelForCreate" *ngIf="walletConnected && isMiningAccess == -1">
    <div class="Content-Box">
      <span class="modHead">Checking whitelisted status, please wait.</span>

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>

  <div
    class="modelForCreate"
    *ngIf="isAccessPopup && walletConnected && isMiningAccess == 1"
  >
    <div class="Content-Box">
      <span class="modHead"
        >Your wallet address {{ userDetailsObj?.ethereum_account }} is not
        permitted to mint.
        <!-- please request whitelisting by writing to
        <EMAIL>. -->
      </span>

      <div class="btnn">
        <button (click)="isAccessPopup = false" class="simpe-type">
          Cancel
        </button>
        <button (click)="requestWhitelist()" class="button-type">
          Request Whitelist
        </button>
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isWhiteListRequested">
    <div class="Content-Box">
      <span class="modHead"
        >Your request has been submitted. <NAME_EMAIL> if
        the address hasn’t been whitelisted in 24 hours.
      </span>

      <div class="btnn">
        <button (click)="saveWork()" class="button-type">Ok</button>
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isWaitMiniting">
    <div class="Content-Box">
      <span class="modHead"
        >Minting initiated, awaiting confirmation from the blockchain network.
        Please do not close the pop-up or refresh the page.</span
      >

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isMinted">
    <div class="Content-Box">
      <span class="modHead"
        >NFT minted successfully! NFT ID is {{ nftDetailsObj.id }}. Please click
        'Save' to continue.</span
      >

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <button class="button-type" (click)="saveWork()">Save</button>
      </div>
    </div>
  </div>
</div>
