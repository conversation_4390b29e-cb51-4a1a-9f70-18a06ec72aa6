// .Container_ {
//     border: 0.06944vw solid var(--tertiary-font-color);
//     border-radius: 2vw;
//     padding: 3vw 6vw;
.Heading {
  font-size: 2.8vw;
  // font-weight: 600;
  text-align: center;
  margin-bottom: 4vw;
}

// }

.field-value {
  margin-top: 2.08vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;

  input[type="text"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;

    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }

  input[type="date"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    color: var(--quaternary-font-color);

    //   ::placeholder{
    // color: var(--quaternary-font-color);
    // }
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }

  input[type="password"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;

    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }

  .input-container {
    width: 100%;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;

    .text-before {
      @media (max-width: 768px) {
        font-size: 4.34vw;
      }
    }

    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }

    button {
      outline: none;
      background: none;
      border: none;
    }

    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 2.08vw;
      top: 0;
      bottom: 0;
      margin: auto 0;

      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }

    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }

    input[type="text"] {
      // background: transparent;
      font-family: var(--secondary-font);
      border: 0.069vw solid var(--timeline-color);
      padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      border-radius: 0.14vw;
      height: 3.47vw;
      width: 100%;
      z-index: 0;

      @media (max-width: 768px) {
        height: 11.35vw;
      }

      &.selection {
        padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }

    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;

      @media (max-width: 768px) {
        top: 10.47vw;
      }

      ul {
        list-style: none;
        padding: 0.69vw 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;

        @media (max-width: 768px) {
          padding: 1.69vw 0;
        }

        li {
          padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;

          @media (max-width: 768px) {
            padding: 1.69vw 1.04vw;
          }

          .country-name {
            margin-left: 0.69vw;

            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }

        li:hover {
          background-color: var(--timeline-color);
        }
      }

      ul::-webkit-scrollbar {
        display: none;
      }
    }

    .dropdown-hidden {
      display: none;
    }
  }

  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }

  .placeholder {
    position: absolute;
    top: -0.4vw;
    left: 1.04vw;
    font-size: 0.8333vw;
    color: var(--quaternary-font-color);
    padding: 0 0.3vw;
    background-color: var(--primary-background-color);

    // background-color: #ededf1;
    @media (max-width: 768px) {
      top: -1.8vw;
      left: 2.04vw;
      font-size: 3.38vw;
    }
  }

  .send {
    margin-left: 2.08vw;
    color: var(--tertiary-font-color);
  }
}

label .check {
  width: 1.25vw;
  height: 1.25vw;
  margin-right: 0.7vw;
  // border: 0.0069vw solid var(--checkbox-border-color);
  border-radius: 50%;
  position: relative;
  background-color: transparent;
  transition: all 0.15s cubic-bezier(0, 1.05, 0.72, 1.07);
  background-image: url("../../../../../../../assets/icons/checkbox/<EMAIL>");
  background-size: 1.25vw;

  &::after {
    content: "";
    width: 0.7vw;
    height: 0.7vw;
    border-radius: 50%;
    opacity: 0;
    position: absolute;
    // background-color: var(--tertiary-font-color);
    background-image: url("../../../../../../../assets/icons/check/<EMAIL>");
    background-size: 0.7vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

label {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 0.5vw;
}

.check-label {
  margin-bottom: 0;

  .colorRad {
    border: 0.0069vw solid var(--primary-border-color);
    outline: none;
    width: 1.56vw;
    height: 1.56vw;
    border-radius: 50%;
    // margin-right: 0.8vw;
  }

  .title {
    // margin-right: 2.08vw;
  }
}

.Inner {
  display: flex;
  // justify-content: space-around;
  margin-bottom: 1.5vw;
}

.gapped {
  justify-content: start;
  gap: 7vw;
}

.section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.button_drop {
  background-color: black;
  color: white;
  border: 0.06944vw solid white;
  padding: 1vw 2vw;
  border-radius: 2vw;
  display: inline-flex;
  gap: 0.5vw;

  fa-icon {
    transition: all 0.2s linear;
  }
}

.btn_save {
  padding: 0.69444vw 1.5vw;
  border-radius: 2vw;
  border: none;
  color: white;
  //font-weight: 600;
  outline: 0;
  background-color: var(--tertiary-font-color);
  &.disabled {
    cursor: not-allowed;
    background-color: var(--quaternary-font-color);
  }
}
.dflex {
  display: flex;
  align-items: center;
  gap: 1vw;
}
