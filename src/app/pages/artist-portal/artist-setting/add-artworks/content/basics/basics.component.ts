import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { faChevronUp, faChevronDown } from '@fortawesome/free-solid-svg-icons';
interface Itype {
  name: string;
  value: string;
}
@Component({
  selector: 'basics-content',
  templateUrl: './basics.component.html',
  styleUrls: ['./basics.component.scss'],
})
export class BasicsComponent implements OnInit {
  @Input()
  form: FormGroup;

  @Output()
  formChange = new EventEmitter();

  @Output()
  onNext = new EventEmitter();
  constructor(private formBuilder: FormBuilder) {}
  artworkType = 'Choose One';
  //workType = 'Work Type';
  workType = 'unique';
  subType = 'Sub Type';
  networkType = 'Network Type';
  ngOnInit(): void {}
  artwork_types: Itype[] = [
    { name: 'physical', value: 'physical' },
    { name: 'digital', value: 'digital' },
    { name: 'both', value: 'both' },
  ];
  art_types: Itype[] = [
    { name: 'Textile arts', value: 'textile arts' },
    { name: 'Painting', value: 'painting' },
    { name: 'Drawing', value: 'drawing' },
    { name: 'Photography', value: 'photography' },
    { name: 'Digital', value: 'digital' },
    { name: 'Print', value: 'print' },
    { name: 'Mixed media', value: 'mixed media' },
    { name: 'Video', value: 'video' },
    { name: 'Other', value: 'other' },
    { name: 'Other', value: 'other' },
    { name: 'Sculpture', value: 'sculpture' },
    { name: 'Installation', value: 'installation' },
  ];
  artType = 'Artwork Type';
  work_types: Itype[] = [
    { name: 'unique', value: 'unique' },
    { name: 'edition', value: 'edition' },
  ];
  sub_types: Itype[] = [
    { name: 'fashion', value: 'fashion' },
    { name: 'sports', value: 'sports' },
    { name: 'music', value: 'music' },
    { name: 'art', value: 'art' },
    { name: 'other', value: 'other' },
  ];
  network_types: Itype[] = [
    { name: 'polygon', value: 'polygon' },
    { name: 'ethereum', value: 'ethereum_main_net' },
    { name: 'mumbai', value: 'polygon_testnet' },
    { name: 'goerli', value: 'goerli_testnet' },
  ];
  state: string = 'default';
  rotate() {
    this.state = this.state === 'default' ? 'rotated' : 'default';
  }
  setArtworkType(val) {
    this.artworkType = val;
  }
  onNextClick() {
    console.log(this.form);

    this.formChange.emit(this.form);
    this.onNext.emit();
  }
  get isNextEnable() {
    if (
      this.form.get('mintType').value &&
      this.form.get('physicalOrDigital').value &&
      this.form.get('work_type').value
    ) {
      if (this.form.get('mintType').value !== 'not_minting') {
        if (this.form.get('network').value) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
}
