<p class="Heading">Basics</p>
<form [formGroup]="form">
  <div class="field-value">
    <p>Choose the minting option for the artwork</p>
    <div class="Inner gapped">
      <label class="check-label">
        <input formControlName="mintType" type="radio" value="not_minting" />
        <span class="title">Not Minting</span>
      </label>
      <label class="check-label">
        <input formControlName="mintType" type="radio" value="mint_now" />
        <span class="title">Mint now</span>
      </label>
      <label class="check-label">
        <input formControlName="mintType" type="radio" value="lazy_minting" />
        <span class="title">Lazy Minting</span>
      </label>
      <label class="check-label">
        <input
          formControlName="mintType"
          type="radio"
          value="minted_on_another_platform"
          disabled
        />
        <del><span class="title">Minted on another platform</span></del>
      </label>
    </div>
  </div>
  <div *ngIf="form.get('mintType').value !== 'not_minting'" class="Inner">
    <p class="dflex">
      Choose the blockchain network to mint the NFT on :
      <dropdown-button
        [dropdown_items]="network_types"
        (selectedValue)="form.get('network').setValue($event)"
      >
        {{
          (form.get("network").value ? form.get("network").value : networkType)
            | titlecase
        }}
      </dropdown-button>
    </p>
  </div>
  <div class="Inner">
    <p class="dflex">
      Physical or Digital :
      <dropdown-button
        [dropdown_items]="artwork_types"
        (selectedValue)="form.get('physicalOrDigital').setValue($event)"
      >
        {{
          (form.get("physicalOrDigital").value
            ? form.get("physicalOrDigital").value
            : artworkType
          ) | titlecase
        }}
      </dropdown-button>
    </p>
    <p class="dflex" style="margin-left: 1vw">
      Choose the artwork type:
      <dropdown-button
        [dropdown_items]="art_types"
        (selectedValue)="form.get('artwork_type').setValue($event)"
      >
        {{
          (form.get("artwork_type").value
            ? form.get("artwork_type").value
            : artType
          ) | titlecase
        }}
      </dropdown-button>
    </p>
  </div>
  <div class="Inner">
    <p class="dflex">
      Choose whether the work is unique or edition :
      <dropdown-button
        [dropdown_items]="work_types"
        (selectedValue)="form.get('work_type').setValue($event)"
      >
        {{
          (form.get("work_type").value ? form.get("work_type").value : workType)
            | titlecase
        }}
      </dropdown-button>
    </p>
  </div>
  <!-- <dropdown-button [dropdown_items]="sub_types" (selectedValue)="subType=$event">
                {{subType|titlecase}}
         </dropdown-button> -->

  <div class="section">
    <button
      [ngClass]="{ disabled: !isNextEnable }"
      (click)="isNextEnable && onNextClick()"
      class="btn_save"
    >
      Next
    </button>
  </div>
</form>
