<p class="Heading">Financial Details</p>
<div class="profile-field">
  <div class="w-100 field-contents">
    <form [formGroup]="form">
      <div class="splitter">
        <div class="field-value" style="padding-right: 0.5vw">
          <div class="Inner">
            <p class="dflex">
              Choose the primary currency for the listing :
              <dropdown-button
                [dropdown_items]="currency"
                (selectedValue)="setCurrency($event)"
              >
                {{ primary_currency | uppercase }}
              </dropdown-button>
            </p>
          </div>
        </div>
        <div class="field-value">
          <input
            formControlName="sale_price"
            type="number"
            (focusout)="checkDeci()"
            placeholder="sale_price"
          />
          <div class="placeholder">Sale Price</div>
          <div class="input-info">
            Provide the sale price in (currency chosen on the left)
          </div>
        </div>
      </div>
      <div class="splitter">
        <div class="field-value" style="padding-right: 0.5vw">
          <div class="Inner">
            <p class="dflex">
              Choose the Sale Type :
              <dropdown-button
                [dropdown_items]="sale_types"
                (selectedValue)="setSaleType($event)"
              >
                {{ saleType | titlecase }}
              </dropdown-button>
            </p>
          </div>
        </div>
        <!-- <div class="field-value">
          <div class="input-with-text">
            <div class="text-before">Launch: Later</div>
            <label class="switch">
              <input formControlName="publish_artworks" type="checkbox" />
              <span class="slider round"></span>
            </label>
            <div class="text-before">&nbsp; Now</div>
          </div>
        </div> -->
      </div>
    </form>

    <div class="section">
      <button
        [ngClass]="{ disabled: !isNextEnable }"
        (click)="isNextEnable && onNextClick()"
        class="btn_save"
      >
        Next
      </button>
    </div>
  </div>
</div>
