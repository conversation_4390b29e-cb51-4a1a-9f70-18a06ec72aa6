import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UpperCasePipe } from '@angular/common';
interface Itype {
  name: string;
  value: string;
}
@Component({
  selector: 'financial-details',
  templateUrl: './financial-details.component.html',
  styleUrls: ['./financial-details.component.scss'],
})
export class FinancialDetailsComponent implements OnInit {
  currency: Itype[] = [{ name: 'INR', value: 'INR' }];
  primary_currency = 'INR';
  non_fiat_currency = ['ETH', 'BTC', 'MATIC', 'BNB'];
  sale_types: Itype[] = [
    { name: 'Direct Sale', value: 'Direct Sale' },
    { name: 'Open Bid', value: 'Open Bid' },
    { name: 'Auction', value: 'Auction' },
  ];
  isFiat = true;
  saleType = 'Direct Sale';
  @Input()
  form: FormGroup;

  @Output()
  formChange = new EventEmitter();
  @Output()
  onNext = new EventEmitter();

  constructor(private formBuilder: FormBuilder) {}
  setCurrency(cur) {
    let capCur = cur.toUpperCase();
    if (!this.non_fiat_currency.includes(capCur)) {
      this.isFiat = true;
    } else {
      this.isFiat = false;
    }
    this.primary_currency = cur;
  }
  checkDeci() {
    if (this.isFiat) {
      let temp = this.form.get('sale_price').value;
      this.form
        .get('sale_price')
        .setValue(Math.round((temp + Number.EPSILON) * 100) / 100);
    }
  }
  setSaleType(type) {
    this.form.get('sale_price').setValue(type);
    this.saleType = type;
  }
  ngOnInit(): void {}

  onNextClick() {
    console.log(this.form);

    this.formChange.emit(this.form);
    this.onNext.emit();
  }
  get isNextEnable() {
    if (this.form.get('sale_price').value) {
      return true;
    } else {
      return false;
    }
  }
}
