import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { apiUrl } from 'src/environments/environment.prod';
import { CollectorService } from 'src/app/services/collector.service';
interface Itype {
  name: string;
  value: string;
}
@Component({
  selector: 'artwork-info-content',
  templateUrl: './artwork-info.component.html',
  styleUrls: ['./artwork-info.component.scss'],
})
export class ArtworkInfoComponent implements OnInit {
  @Input()
  form: FormGroup;

  @Output()
  formChange = new EventEmitter();

  @Output()
  onNext = new EventEmitter();

  artwork_type = 'Image';
  signed_status = 'No';
  artwork_file_type: Itype[] = [
    { name: 'Image', value: 'image' },
    { name: 'Video', value: 'video' },
    { name: '3D Asset', value: '3d_asset' },
    { name: 'HTML Code', value: 'html_code' },
  ];
  singature_: Itype[] = [
    { name: 'Yes', value: 'Yes' },
    { name: 'No', value: 'No' },
  ];
  selectedFilesObj: any = {
    profileArr: [],
    highResArr: [],
    profileImage: '',
    highResImage: '',
  };
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService
  ) {}

  ngOnInit(): void {
    this.selectedFilesObj.profileArr = this.form.get('primary_image').value;
    this.selectedFilesObj.highResArr = this.form.get(
      'original_artwork_file'
    ).value;
  }
  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onNextClick() {
    console.log(this.form);
    console.log(this.selectedFilesObj);
    let primary_image = this.selectedFilesObj.profileArr;
    delete primary_image[0].filedata;
    this.form.get('primary_image').setValue(primary_image);
    let original_artwork_file = this.selectedFilesObj.highResArr;
    delete original_artwork_file[0].filedata;
    this.form
      .get('original_artwork_file')
      .setValue(this.selectedFilesObj.highResArr);

    this.formChange.emit(this.form);
    this.onNext.emit();
  }
  get isNextEnable() {
    if (
      this.form.get('artwork_title').value &&
      this.form.get('year').value &&
      this.form.get('medium').value &&
      this.form.get('dimensions').value &&
      this.form.get('file_type').value &&
      this.selectedFilesObj['profileArr']?.[0]?.url &&
      this.selectedFilesObj['highResArr']?.[0]?.url
    ) {
      if (this.form.get('file_type').value !== 'Image') {
        if (this.form.get('artwork_embedded_code').value) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  }
  copyToOriginal(copyToOrigin) {
    if (copyToOrigin) {
      this.selectedFilesObj.highResArr = JSON.parse(
        JSON.stringify(this.selectedFilesObj.profileArr)
      );
    }
  }
}
