<p class="Heading">Artwork Info</p>
<div class="profile-field">
  <div class="w-100 field-contents">
    <form [formGroup]="form">
      <div class="splitter">
        <div class="field-value" style="padding-right: 0.5vw">
          <input
            formControlName="artwork_title"
            type="text"
            placeholder="Artwork Title"
          />
          <div class="placeholder">Artwork Title</div>
          <div class="input-info">
            Provide the complete title of the artwork
          </div>
        </div>
        <div class="field-value" style="padding-right: 0.5vw">
          <input formControlName="year" type="text" placeholder="Year" />
          <div class="placeholder">Year</div>
          <div class="input-info">
            Provide year (e.g.: 2018) or range (e.g.: 2016-2018)
          </div>
        </div>
        <div class="field-value" style="padding-right: 0.5vw">
          <input formControlName="medium" type="text" placeholder="Medium" />
          <div class="placeholder">Medium</div>
          <div class="input-info">
            Provide complete details of the artwork medium (as detailed as
            possible). e.g.: Digital Image, JPEG; Painting on canvas; Acrylic
            paint on paper
          </div>
        </div>
        <div class="field-value" style="padding-right: 0.5vw">
          <input
            formControlName="dimensions"
            type="text"
            placeholder="Dimensions"
          />

          <div class="placeholder">Dimensions</div>
          <div
            class="input-info"
            title=" Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
          each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x
          3.3d in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm
          (variable) | 7 x 5 x 1 in (variable); 1080x1080 px"
          >
            Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
            each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x 3.3d
            in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm (variable) | 7 x 5
            x 1 in (variable); 1080x1080 px
          </div>
        </div>
      </div>
      <div class="Inner">
        <p class="dflex">
          Choose the file type being uploaded as the artwork file :
          <dropdown-button
            [dropdown_items]="artwork_file_type"
            (selectedValue)="form.get('file_type').setValue($event)"
          >
            {{
              (form.get("file_type").value
                ? form.get("file_type").value
                : artwork_type
              ) | titlecase
            }}
          </dropdown-button>
        </p>
      </div>
      <div class="splitter">
        <div class="field-value" style="padding-right: 0.5vw">
          <div class="upload-head">Artwork Thumbnail</div>
          <image-upload
            [hideAltText]="true"
            [accept]="'image/*'"
            [selectedData]="selectedFilesObj?.profileArr"
            (onFileChange)="onFileSelect($event, 'profileArr')"
            [fileSize]="8388608"
            [placeholder]="'placeholder'"
          >
          </image-upload>
        </div>
        <div class="field-value" style="padding-right: 0.5vw">
          <div class="upload-head">Original Artwork File</div>
          <image-upload
            [hideAltText]="true"
            [accept]="'image/*'"
            [selectedData]="selectedFilesObj?.highResArr"
            (onFileChange)="onFileSelect($event, 'highResArr')"
            [fileSize]="8388608"
            [placeholder]="'placeholder'"
          >
            <div
              *ngIf="
                (!selectedFilesObj?.highResArr ||
                  selectedFilesObj?.highResArr?.length == 0) &&
                selectedFilesObj?.profileArr?.length > 0
              "
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <input
                type="checkbox"
                placeholder="Show Medium"
                style="margin-right: 0.4vw"
                (change)="copyToOriginal($event.currentTarget.checked)"
              />Copy from Thumbnail
            </div>
          </image-upload>
        </div>

        <div
          *ngIf="form.get('file_type').value !== 'Image'"
          class="field-value"
          style="padding-right: 0.5vw"
        >
          <div class="editor-head">Embed Code</div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              margin-top: 0.5vw;
            "
          >
            <div style="margin-top: unset">
              <textarea
                [placeholder]="'Embed Code'"
                formControlName="artwork_embedded_code"
                rows="80"
                cols="115"
                style="padding: 1vw; height: 20vw"
              ></textarea>

              <!-- <angular-editor [placeholder]="'Artist Note'"  formControlName="artistNote"></angular-editor> -->
              <div class="input-info">
                Provide the correct formatted responsive embed code, to display
                the artwork on the site. For videos, provide the responsive
                embed code from Vimeo or Youtube. Display of videos will depend
                on settings provided in Vimeo or Youtube.
              </div>
            </div>
            <!-- <div style="width: 48%">
              <div style="padding: 56.25% 0 0 0; position: relative">
                <iframe
                  [src]="videoLinkTextSanitized"
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                  "
                  frameborder="0"
                  allow="autoplay; fullscreen; picture-in-picture"
                  allowfullscreen
                ></iframe>
              </div>
            </div> -->
          </div>
        </div>
      </div>

      <!-- <div class="input-with-text">
        <div class="text-before">Generate thumbnail from uploaded image:</div>
        <label class="switch">
          <input formControlName="generate_thumbnail" type="checkbox" />
          <span class="slider round"></span>
        </label>
      </div> -->

      <!-- <div class="input-with-text">
        <div class="text-before">View Optional fields:</div>
        <label class="switch">
          <input formControlName="showEdition" type="checkbox" />
          <span class="slider round"></span>
        </label>
      </div> -->
      <!-- <div *ngIf="form.get('showEdition').value">
        <div class="Inner">
          <p class="dflex">
            Select if the work is signed or not :
            <dropdown-button
              [dropdown_items]="singature_"
              (selectedValue)="signed_status = $event"
            >
              {{ signed_status | titlecase }}
            </dropdown-button>
          </p>

          <div *ngIf="signed_status === 'Yes'" class="field-value">
            <div class="upload-head">Signature Details</div>
            <angular-editor
              id="editor27"
              [placeholder]="'Signature Details'"
              formControlName="signatureDetails"
            ></angular-editor>
            <div class="input-info">
              Provide signature/incription details for the work, including
              location of signature. e.g.: Signed dated "Artist 123, 2002" lower
              right
            </div>
          </div>

          <div class="field-value">
            <input
              formControlName="creditLine"
              type="text"
              placeholder="Credit Line"
            />
            <div class="placeholder">Credit Line</div>
            <div class="input-info">
              Provide the credit line/ Copyright information for the artwork to
              be displayed when the image is used
            </div>
          </div>
          <div class="upload-head">Artist Note</div>
          <angular-editor
            id="editor22"
            [placeholder]="'Artist note'"
            formControlName="artistNote"
          ></angular-editor>
          <div class="input-info">
            Provide a short description/note about the artwork (50-80 words max)
          </div>
        </div>
      </div> -->
    </form>
    <div class="section">
      <button
        [ngClass]="{ disabled: !isNextEnable }"
        (click)="isNextEnable && onNextClick()"
        class="btn_save"
      >
        Next
      </button>
    </div>
  </div>
</div>
