.Heading {
  font-size: 2.8vw;
  //font-weight: 600;
  text-align: center;
  margin-bottom: 4vw;
}
.profile-field {
  .field-contents {
    .field-heading {
      margin-bottom: 1vw;
      margin-bottom: 0;
      font-family: var(--secondary-font);
      font-size: 1.25vw;
    }
    .double {
      width: 35vw;
      .input-double {
        width: 17.01vw !important;
      }
    }
    .field-value {
      margin-bottom: 1.08vw;
      position: relative;
      //display: flex;
      justify-content: start;
      align-items: center;
      input[type="text"] {
        background: transparent;
        font-family: var(--secondary-font);
        border: none;
        border: 0.069vw solid var(--timeline-color);
        border-radius: 0.14vw;
        padding: 1.04vw 1.11vw;
        width: 100%;
        height: 3.47vw;
        @media (max-width: 768px) {
          height: 11.35vw;
          font-size: 3.86vw;
          padding: 1.04vw 2.11vw;
        }
      }
      textarea {
        background: transparent;
        font-family: var(--secondary-font);
        border: none;
        border: 0.069vw solid var(--timeline-color);
        border-radius: 0.14vw;
        padding: 1.04vw 1.11vw;
        width: 100%;
        height: 3.47vw;
        @media (max-width: 768px) {
          height: 11.35vw;
          font-size: 3.86vw;
          padding: 1.04vw 2.11vw;
        }
      }
      input[type="date"] {
        background: transparent;
        font-family: var(--secondary-font);
        border: none;
        border: 0.069vw solid var(--timeline-color);
        border-radius: 0.14vw;
        padding: 1.04vw 1.11vw;
        width: 35vw;
        height: 3.47vw;
        color: var(--quaternary-font-color);
        //   ::placeholder{
        // color: var(--quaternary-font-color);
        // }
        @media (max-width: 768px) {
          height: 11.35vw;
        }
      }
      input[type="password"] {
        background: transparent;
        font-family: var(--secondary-font);
        border: none;
        border: 0.069vw solid var(--timeline-color);
        border-radius: 0.14vw;
        padding: 1.04vw 1.11vw;
        width: 35vw;
        height: 3.47vw;
        @media (max-width: 768px) {
          height: 11.35vw;
        }
      }
      .input-container {
        width: 100%;
        position: relative;
        display: inline-flex;
        justify-content: start;
        align-items: center;
        flex-wrap: wrap;
        .text-before {
          @media (max-width: 768px) {
            font-size: 4.34vw;
          }
        }
        .flag-icon {
          position: absolute;
          left: 1.04vw;
        }
        button {
          outline: none;
          background: none;
          border: none;
        }
        .flag-arrow {
          position: absolute;
          max-width: 0.69vw;
          height: auto;
          right: 2.08vw;
          top: 0;
          bottom: 0;
          margin: auto 0;
          @media (max-width: 768px) {
            max-width: 1.95vw;
          }
        }
        .division {
          position: absolute;
          width: 0.069vw;
          height: 1.04vw;
          background-color: var(--timeline-color);
          left: 3.33vw;
        }
        input[type="text"] {
          // background: transparent;
          font-family: var(--secondary-font);
          border: 0.069vw solid var(--timeline-color);
          padding: 1.04vw 1.11vw 1.04vw 4.2vw;
          border-radius: 0.14vw;
          height: 3.47vw;
          width: 100%;
          z-index: 0;
          @media (max-width: 768px) {
            height: 11.35vw;
          }
          &.selection {
            padding: 1.04vw 1.11vw 1.04vw 1.04vw;
          }
        }
        .dropdown-visible {
          background-color: var(--primary-background-color);
          visibility: visible;
          position: absolute;
          top: 3.47vw;
          z-index: 1;
          box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
          width: 100%;
          @media (max-width: 768px) {
            top: 10.47vw;
          }
          ul {
            list-style: none;
            padding: 0.69vw 0;
            max-height: 27.97vw;
            margin: 0;
            overflow: hidden;
            overflow-y: scroll;
            @media (max-width: 768px) {
              padding: 1.69vw 0;
            }
            li {
              padding: 0.69vw 1.04vw;
              width: 34.9vw;
              display: flex;
              @media (max-width: 768px) {
                padding: 1.69vw 1.04vw;
              }
              .country-name {
                margin-left: 0.69vw;
                @media (max-width: 768px) {
                  font-size: 3.38vw;
                }
              }
            }
            li:hover {
              background-color: var(--timeline-color);
            }
          }
          ul::-webkit-scrollbar {
            display: none;
          }
        }
        .dropdown-hidden {
          display: none;
        }
      }
      .ph-flag {
        height: 1.25vw;
        // padding-right: 0.62vw;
        // border-right: solid 0.09vw var(--quaternary-font-color);
      }
      .placeholder {
        position: absolute;
        top: -0.4vw;
        left: 1.04vw;
        font-size: 0.8333vw;
        color: var(--quaternary-font-color);
        padding: 0 0.3vw;
        background-color: var(--primary-background-color);
        // background-color: #ededf1;
        @media (max-width: 768px) {
          top: -1.8vw;
          left: 2.04vw;
          font-size: 3.38vw;
        }
      }
      .send {
        margin-left: 2.08vw;
        color: var(--tertiary-font-color);
      }
    }
    .verify {
      margin-top: 2.78vw;
      font-family: var(--secondary-font);
      font-size: 1.25vw;
    }
    .partitioned {
      margin-top: 1.33vw;
      outline: none;
      padding-left: 0.8vw;
      letter-spacing: 0;
      border: 0;
      background-image: linear-gradient(
        to left,
        var(--timeline-color) 70%,
        rgba(255, 255, 255, 0) 0%
      );
      background-position: bottom;
      background-size: 3.2vw 0.069vw;
      width: 3.2vw;
      background-repeat: repeat-x;
      background-position-x: 2.2vw;
      height: 2vw;
      padding-bottom: 0.35vw;
      font-family: var(--secondary-font);
    }
    .last-changed {
      margin-top: 1.04vw;
      font-size: 0.97vw;
      color: var(--quaternary-font-color);
    }
    .upload-head {
      // margin-top: 1.47vw;
      // font-size: 1.2vw;
      margin-bottom: 0.5vw;
      @media (max-width: 768px) {
        font-size: 4.34vw;
      }
    }
  }
  .buttonList {
    margin-top: 3.47vw;
    margin-bottom: 1.042vw;

    .save {
      // display: block;
      width: 100%;
      outline: none;
      font-size: 1.25vw;
      // width: 20.56vw;
      background-color: transparent;
      color: var(--tertiary-font-color);
      padding: 0.833333vw 12.08333vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }
    .save:hover {
      font-weight: 500;
    }
  }
}

.splitter {
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
  .field-value {
    width: 31%;
    // width: 33.3333333%;
    @media (max-width: 768px) {
      width: 100%;
    }
  }
}
.input-info {
  font-size: 0.95vw;
  margin-top: 0.5vw;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  // -webkit-line-clamp: 2; /* number of lines to show */
  // line-clamp: 2;
  -webkit-box-orient: vertical;
  @media (max-width: 768px) {
    font-size: 3.38vw;
    margin-top: 0.76vw;
    margin-bottom: 4.62vw;
  }
}
.Inner {
  // display: flex;
  // justify-content: space-around;
  margin-bottom: 1.5vw;
}

.gapped {
  justify-content: start;
  gap: 7vw;
}

.dflex {
  display: flex;
  align-items: center;
  gap: 1vw;
}

.switch {
  position: relative;
  display: inline-block;
  width: 2.6vw;
  height: 1.11vw;
  margin-bottom: 0 !important;
  @media (max-width: 768px) {
    width: 10.6vw;
    height: 4.11vw;
  }
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 1vw;
  width: 1vw;
  left: 0.06vw;
  bottom: 0.06vw;
  background-color: white;
  transition: 0.4s;
  @media (max-width: 768px) {
    height: 3.58vw;
    width: 3.58vw;
    left: 0.4vw;
    bottom: 0.2vw;
  }
}

input:checked + .slider {
  background-color: var(--tertiary-font-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked + .slider:before {
  transform: translateX(1.4vw);
  @media (max-width: 768px) {
    transform: translateX(6.2vw);
  }
}

/* Rounded sliders */
.slider.round {
  border-radius: 3vw;
}

.slider.round:before {
  border-radius: 50%;
}
.text-before {
  margin-right: 0.5vw;
  @media (max-width: 768px) {
    font-size: 4.34vw;
    margin-right: 2.5vw;
  }
}
.input-with-text {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
  margin: 1.5vw 0;
}

.btn_save {
  padding: 0.69444vw 1.5vw;
  border-radius: 2vw;
  border: none;
  color: white;
  // font-weight: 600;
  outline: 0;
  background-color: var(--tertiary-font-color);
  &.disabled {
    cursor: not-allowed;
    background-color: var(--quaternary-font-color);
  }
}
.section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
