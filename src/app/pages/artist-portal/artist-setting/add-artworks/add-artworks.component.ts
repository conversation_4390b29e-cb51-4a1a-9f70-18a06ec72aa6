import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
@Component({
  selector: 'app-add-artworks',
  templateUrl: './add-artworks.component.html',
  styleUrls: ['./add-artworks.component.scss'],
})
export class AddArtworksComponent implements OnInit {
  current_step = 1;
  total_steps = 4;
  form: FormGroup;
  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private server: CollectorService
  ) {
    this.form = this.formBuilder.group({
      mintType: new FormControl(''),
      network: new FormControl(null),
      physicalOrDigital: new FormControl(null),
      work_type: new FormControl('unique'),
      artwork_title: new FormControl(''),
      year: new FormControl(''),
      dimensions: new FormControl(''),
      medium: new FormControl(''),
      primary_image: new FormControl(null),
      original_artwork_file: new FormControl(null),
      file_type: new FormControl('Image'),
      artwork_embedded_code: new FormControl(''),
      sale_price: new FormControl(''),
      publish_artworks: new FormControl(false),
      location: new FormControl(''),
      sale_options: new FormControl('ForSale'),
      created: new FormControl('Individually'),
      artwork_type: new FormControl(''),
    });
  }
  ngOnInit(): void {
    this.getUserDetails();
  }

  getUserDetails() {
    let url = apiUrl.getUserDetails;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        const userDetails = res.data || res.Data;
        let stringify = JSON.stringify(userDetails);
        let encode = btoa(unescape(encodeURIComponent(stringify)));
        localStorage.setItem('userDetails', encode);
        if (userDetails.work_location) {
          this.form.get('location').setValue(userDetails.work_location);
        }
      }
    });
  }

  onNextEvent() {
    if (this.current_step < 4) {
      this.current_step++;
    } else if ((this.current_step = 4)) {
      let url = apiUrl.addArtwork;
      this.server.showSpinner();
      this.server.postApi(url, this.form.value).subscribe((res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.router.navigate(['/artist-portal/settings/artworks']);
          alert(res.message);
        } else {
          alert('Something went wrong!');
        }
      });
    }
  }
}
