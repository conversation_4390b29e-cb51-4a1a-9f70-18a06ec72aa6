@import url("//use.fontawesome.com/releases/v5.7.2/css/all.css");
.filter {
    color: var(--quaternary-font-color);
    font-size: var(--default-font-size);
    input[type="text"] {
        padding: 0.7vw 1.32vw 0.7vw 1vw;
        -o-object-fit: contain;
        object-fit: contain;
        outline: none;
        width: 100%;
        font-family: Arial, FontAwesome;
        border-radius: 0.5vw;
        font-weight: 500;
        // margin-bottom: 2.08vw;
        color: var(--quaternary-font-color);
        height: 2.8vw;
        border: solid 0.07vw var(--secondary-font-color);
        text-indent: 1.73vw;
        background-position: 0 50%;
        background-repeat: no-repeat;
        background-position-x: 1.04vw;
        background-size: 1.04vw;
        background-image: url("../../../../../assets/icons/search/<EMAIL>");
    }
    input[type="text"]:focus {
        background-image: none;
        text-indent: 0px;
    }
}

.heading {
    margin-bottom: 2.47vw;
    position: relative;
    .head {
        // width: 82.5%;
        font-size: 1.66vw;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        // align-items: center;
        img {
            width: 2.29vw;
            height: 2.29vw;
            margin-right: 1.08vw;
        }
    }
    .sub {
        font-size: var(--default-font-size);
        font-family: var(--secondary-font);
        margin-top: 0.42vw;
        padding-left: 3.33vw;
    }
}

.flex-block {
    display: flex !important;
}

.blockchainIcon {
    width: 1.25vw;
    height: 1.25vw !important;
}

.blockchainIconList {
    width: auto !important;
    height: 1vw;
}

.collector-form {
    .search-input {
        font-size: var(--default-font-size);
        margin-top: 4.16vw;
        position: relative;
        input[type="text"] {
            width: 100%;
            padding: 0.34vw 0;
            border: none;
            text-indent: 2.08vw;
            color: var(--quaternary-font-color);
            border-bottom: 0.138vw solid var(--timeline-color);
            &:focus {
                outline: none;
            }
        }
        img {
            position: absolute;
            left: 0;
            top: 50%;
            height: 0.97vw;
            width: 0.97vw;
            transform: translate(0, -50%);
        }
    }
    .interests {
        width: auto;
        margin-left: 0;
        margin-top: 1.74vw;
        overflow-y: auto;
        //   height: calc(100vh - 44.75vw);
        padding-bottom: 8vw;
        .contents {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: .5vw;
            .artist {
                width: 15.06vw;
                padding: 1.74vw 0;
                .artworkImggg {
                    width: 14.93vw;
                    height: 10.69vw;
                    min-height: 10.69vw;
                    -o-object-fit: cover;
                    object-fit: cover;
                    border-radius: 0.41vw;
                    cursor: pointer;
                }
                .wrappp {
                    word-wrap: break-word;
                    .name {
                        font-size: 0.9027vw;
                        margin-top: 0.5vw;
                    }
                    &.active {
                        color: var(--tertiary-font-color);
                        img {
                            border: solid 0.013vw var(--tertiary-font-color);
                        }
                    }
                }
            }
            // .artwork-list {
            //   display: flex;
            //   flex-direction: row;
            //   .list-contain {
            //     display: flex;
            //     flex-direction: row;
            //     .artwork-img {
            //       width: 10%;
            //     }
            //     .artwork-indicators {
            //       width: 10%;
            //     }
            //     .artwork-title {
            //       width: 20%;
            //     }
            //     .artist-name {
            //       width: 20%;
            //     }
            //     .sale-price {
            //       width: 20%;
            //     }
            //     .nft-id {
            //       width: 20%;
            //     }
            //   }
            // }
            .icon-indicators {
                padding-top: 0.5vw;
                display: flex;
                align-items: center;
            }
        }
        .contentList {
            justify-content: space-between;
            flex-wrap: wrap;
            .Table {
                display: flex;
                flex-direction: column;
                border: 1px solid #f2f2f2;
                font-size: 1rem;
                // margin: 0.5rem;
                justify-content: space-between;
            }
            .Table-header {
                font-weight: 700;
                background-color: #defdec;
            }
            .Table-row {
                display: box;
                display: flex;
                // flex-flow: row nowrap;
                font-size: 0.9027vw;
                border-bottom: 1px solid #f2f2f2;
                height: 2.777vw;
                overflow: hidden;
                line-height: 1;
            }
            .Table-row-item {
                display: flex;
                // flex-grow: 0;
                width: 10%;
                padding: 0.5em;
                align-items: center;
                word-break: break-word;
                text-align: center;
                .img-item {
                    padding: 0.5em;
                    justify-content: start;
                }
                .img-artwork-thumbnail {
                    background-color: #80808054;
                    width: 50px;
                    height: 40px;
                    -o-object-fit: contain;
                    object-fit: contain;
                    border-radius: 0.14vw;
                }
                .edit_icon {
                    position: relative;
                }
                .edit_icon::before {
                    font-family: "Font Awesome 5 Free";
                    // color: #356ad2;
                    // content: "\f040";
                    // display: inline-block;
                    // // padding-right: 3px;
                    // font-size: 18px;
                    // vertical-align: middle;
                    // font-weight: 900;
                    content: "\f023";
                    // font-family: FontAwesome;
                    font-style: normal;
                    font-weight: normal;
                    text-decoration: inherit;
                    /*--adjust as necessary--*/
                    color: rgb(255, 5, 5);
                    font-size: 18px;
                    padding-right: 0.5em;
                    position: absolute;
                }
            }
            // .Table-row-item.title-art {
            //   // font-weight: 600;
            // }
            .u-Flex-grow2 {
                width: 20%;
            }
            .u-Flex-grow3 {
                width: 30%;
            }
            // .u-Flex-grow3 {
            //   -webkit-flex-grow: 3;
            //   -moz-flex-grow: 3;
            //   flex-grow: 3;
            //   -ms-flex-positive: 3;
            // }
            // table Close
        }
    }
    .buttonList {
        margin-top: 3.47vw;
        .save {
            outline: none;
            font-size: 1.25vw;
            // min-width: 20.56vw;
            background-color: transparent;
            color: var(--tertiary-font-color);
            padding: 0.83vw 1.04vw;
            border: 0.069vw solid var(--tertiary-font-color);
            border-radius: 1.46vw;
            margin-right: 1.5vw;
        }
        .save:hover {
            font-weight: 600;
        }
        .cancel {
            outline: none;
            font-size: 1.25vw;
            background-color: transparent;
            color: var(--quaternary-font-color);
            padding: 0.83vw 2.78vw;
            border: none;
        }
        .cancel:hover {
            font-weight: 600;
            // color: var(--tertiary-font-color);
        }
    }
}

.saveBtn {
    // display: block;
    //width: 4vw;
    outline: none;
    font-size: 1.25vw;
    // width: 20.56vw;
    margin-left: 2vw;
    background-color: transparent;
    color: var(--tertiary-font-color);
    padding: 0.533333vw 0.89vw;
    border: 0.069vw solid var(--tertiary-font-color);
    border-radius: 1.46vw;
    @media (max-width: 768px) {
        font-size: 3.86vw;
        margin-bottom: 4.83vw;
        border-radius: 10.1vw;
        padding: 3.45vw 6.89vw;
    }
}

.switch {
    position: relative;
    display: inline-block;
    width: 2.6vw;
    height: 1.11vw;
    margin-bottom: 0 !important;
}


/* Hide default HTML checkbox */

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}


/* The slider */

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--tertiary-font-color);
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 1vw;
    width: 1vw;
    left: 0.06vw;
    bottom: 0.06vw;
    background-color: white;
    transition: 0.4s;
}

input:checked+.slider {
    background-color: var(--tertiary-font-color);
}

input:focus+.slider {
    box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked+.slider:before {
    transform: translateX(1.4vw);
}


/* Rounded sliders */

.slider.round {
    border-radius: 3vw;
}

.slider.round:before {
    border-radius: 50%;
}

.text-before {
    font-size: 1.111vw;
    margin-right: 0.5vw;
    font-weight: 500;
}

.published-dot,
.unpublished-dot,
.status-dot,
.minted,
.not-minted,
.aserd,
.edit_grid {
    text-decoration: none;
    margin: 0 0.2vw;
}

.edition {
    font-weight: 600;
    font-size: 1.1vw;
    // margin-right: 0.2vw;
}

.editionList {
    font-weight: 600;
    font-size: 1vw;
    // margin-right: 0.2vw;
}

.edit_grid {
    position: absolute;
    top: 2vw;
    right: 0.7vw;
    padding: 0.5vw;
}

.edit_grid:hover {
    color: grey;
}

.edit_grid::before {
    font-family: "Font Awesome 5 Free";
    content: "\f142";
    color: #004ddd;
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
}

.aserd::before {
    font-family: "Font Awesome 5 Free";
    content: "\f304";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
}

.minted::before {
    font-family: "Font Awesome 5 Free";
    content: "\f023";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
}

.not-minted::before {
    font-family: "Font Awesome 5 Free";
    content: "\f3c1";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
}

.unpublished-dot::before {
    font-family: "Font Awesome 5 Free";
    content: "\f057";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
    background: -webkit-linear-gradient(#ebb1b1, #ee2828);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.published-dot::before {
    font-family: "Font Awesome 5 Free";
    content: "\f058";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
    // color: rgb(44, 135, 255);
    background: -webkit-linear-gradient(#5a8eff, #356ad2);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.status-dot::before {
    font-family: "Font Awesome 5 Free";
    content: "\f111";
    display: inline-block;
    // padding-right: 3px;
    vertical-align: middle;
    font-weight: 900;
}

.sidenav {
    box-sizing: border-box;
    height: 100%;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    background: linear-gradient(#dbe7ff, #fff);
    overflow: hidden;
    transition: 0.5s;
    // padding-top:px;
    .sidenav-content {
        padding: 0 20px 60px 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        // position: relative;
        .closebtn {
            text-align: right;
            display: inline-block;
            font-size: 36px;
            transition: 0.3s;
            cursor: pointer;
        }
        .closebtn:hover {
            color: var(--secondarary-font-color);
        }
        .head-wrapper {
            // min-height: 20.833vw;
            // max-height: 34.722vw;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .text-side {
                padding: 0.5vw;
                font-size: 1.111vw;
                text-align: left;
                .description {
                    max-height: 4vw;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .img-side {
                display: flex;
                align-items: center;
                img {
                    width: 100%;
                    -o-object-fit: contain;
                    object-fit: contain;
                }
            }
        }
        .divider {
            width: 100%;
            border-bottom: 0.0694vw solid var(--timeline-color);
        }
    }
}

// .sidenav-content a {
//   // padding: 8px 8px 8px 32px;
//   text-decoration: none;
//   font-size: 25px;
//   color: #818181;
//   display: inline;
//   // transition: 0.3s;
//   position: relative;
//   // margin-bottom: 25px;
// }
// .sidenav a:hover {
//   color: #f1f1f1;
// }
#mainContent {
    overflow-y: scroll;
    height: 100%;
    // transition: margin-right 0.5s;
    // padding: 16px;
}

.dropdown-container {
    display: block;
    height: auto;
    position: absolute;
    top: 2.9vw;
    right: 1.5vw;
    z-index: 20;
}

.dropdown-content {
    background-color: white;
    width: 8vw;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 20;
}