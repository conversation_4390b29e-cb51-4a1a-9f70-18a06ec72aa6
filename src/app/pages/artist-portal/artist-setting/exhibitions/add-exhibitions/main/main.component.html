<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="mandatory_elements">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Publish Exhibition :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="publishExhibition" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to publish the Exhibition on the website.
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <multi-input
                  placeholder="Choose artists from the list"
                  [itemsArray]="artist_select"
                  [previosItems]="selected_artist"
                  [newEntry]="false"
                  [disableSearch]="true"
                  (searchEvent)="artist_search = $event; getArtists()"
                  (selectedItem)="getSelectedItem($event, 0)"
                ></multi-input>
                <div class="placeholder">Artists</div>
                <div class="input-info"></div>
              </div>
              <!-- <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Url"
                  formControlName="pageUrl"
                  (keypress)="server.toCheckSpace($event, form.value.pageUrl)"
                />
                <div class="placeholder">Page URL</div>
                <div class="input-info">
                  Exact details to give in URL name for the collection (max two
                  words).
                </div>
              </div> -->
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="placeholder">Exhibition Thumbnail</div>
                <div class="flex-block">
                  <image-upload
                    [accept]="'image/*'"
                    [selectedData]="selectedFilesObj?.thumbnailArr"
                    (onFileChange)="onFileSelect($event, 'thumbnailArr')"
                    [fileSize]="12582912"
                    [placeholder]="placeholder"
                    [hideAltText]="true"
                  ></image-upload>

                  <div class="input-info">
                    Upload image or GIF to be used as Exhibition thumbnail.
                  </div>
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="placeholder">Exhibition Banner Image</div>
                <div class="flex-block">
                  <image-upload
                    [accept]="'image/*'"
                    [selectedData]="selectedFilesObj?.bannerImageArr"
                    (onFileChange)="onFileSelect($event, 'bannerImageArr')"
                    [fileSize]="12582912"
                    [placeholder]="placeholder"
                    [hideAltText]="true"
                  ></image-upload>

                  <div class="input-info">
                    Upload image or GIF to be used as banner on the website to
                    represent the exhibition. The image will be centered to
                    aspect ratio 1440x300 px on Desktop pages.
                  </div>
                </div>
              </div>
            </div>

            <div class="title_section">
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <div class="input-container" (focusout)="changeFocus(0)">
                    <input
                      (focusin)="isDropDownOpen[0] = true"
                      type="text"
                      class="selection"
                      placeholder="Title Banner Type"
                      formControlName="bannerType"
                      [readonly]="true"
                    />
                    <div class="placeholder">Title Banner Type</div>
                    <button
                      (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                      type="button"
                    >
                      <img
                        src="assets/icons/arrow-down.png"
                        class="flag-arrow"
                      />
                    </button>
                    <div
                      [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[0],
                        'dropdown-visible': isDropDownOpen[0]
                      }"
                    >
                      <ul>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('Fullscreen Image')
                          "
                        >
                          <div class="country-name">Fullscreen Image</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'video/*';
                            form.get('bannerType').setValue('Video')
                          "
                        >
                          <div class="country-name">Video</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('GIF')
                          "
                        >
                          <div class="country-name">GIF</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('Artwork on wall')
                          "
                        >
                          <div class="country-name">Artwork on wall</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('Half-Banner')
                          "
                        >
                          <div class="country-name">Half-Banner</div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="input-info">Choose the type of Banner to use</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <div class="input-container" (focusout)="changeFocus(2)">
                    <input
                      (focusin)="isDropDownOpen[2] = true"
                      type="text"
                      class="selection"
                      placeholder="Header Colour"
                      formControlName="headerColour"
                      [readonly]="true"
                    />
                    <div class="placeholder">Header Colour</div>
                    <button
                      (click)="isDropDownOpen[2] = !isDropDownOpen[2]"
                      type="button"
                    >
                      <img
                        src="assets/icons/arrow-down.png"
                        class="flag-arrow"
                      />
                    </button>
                    <div
                      [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[2],
                        'dropdown-visible': isDropDownOpen[2]
                      }"
                    >
                      <ul>
                        <li
                          (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Normal')
                          "
                        >
                          <div class="country-name">Normal</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Inverted')
                          "
                        >
                          <div class="country-name">Inverted</div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="input-info">
                    Choose whether to use regular logo with black font, or
                    inverted logo with white font (depending on the Banner Media
                    chosen)
                  </div>
                </div>
                <div class="field-value">
                  <div
                    class="image-type"
                    *ngIf="form.value.bannerType !== 'Video'"
                  >
                    <!-- Banner-->
                    <div class="placeholder">Title Banner media</div>
                    <div class="flex-block">
                      <image-upload
                        [accept]="acceptType"
                        [selectedData]="selectedFilesObj?.bannerMediaArr"
                        (onFileChange)="onFileSelect($event, 'bannerMediaArr')"
                        [fileSize]="12582912"
                        [placeholder]="placeholder"
                        [hideAltText]="true"
                      ></image-upload>
                      <div class="input-info">
                        Provide the media to be used in the banner.
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="form.value.bannerType === 'Video'"
                    style="padding-right: 0.5vw"
                  >
                    <input
                      type="text"
                      placeholder="Banner Title"
                      formControlName="bannerTitle"
                    />
                    <div class="placeholder">Title Banner Video</div>
                    <div class="input-info">
                      Provide the Vimeo player link for the video to be featured
                      as title banner. e.g.:
                      https://www.player.vimeo.com/12342143.
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="form.get('bannerType').value == 'Artwork on wall'">
                <div class="upload-head">Banner Background Image</div>
                <div class="splitter">
                  <div class="field-value flex-block">
                    <image-upload
                      [accept]="'image/*'"
                      [selectedData]="selectedFilesObj?.bannerBackgroundArr"
                      (onFileChange)="
                        onFileSelect($event, 'bannerBackgroundArr')
                      "
                      [fileSize]="12582912"
                      [placeholder]="placeholder"
                      [hideAltText]="true"
                    ></image-upload>
                    <div class="input-info">
                      Upload a wall background image to place the artwork on (a
                      gallery wall image)
                    </div>
                  </div>
                </div>
              </div>

              <!-- Banner Title-->

              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <!-- <div style="font-size: 1.25vw">Exhibition Title</div> -->
                  <div class="placeholder">Exhibition Title</div>
                  <div>
                    <angular-editor
                      *ngIf="htmlcheckBox[0]"
                      id="editor20"
                      formControlName="title"
                      (keypress)="server.toCheckSpace($event, form.value.title)"
                    ></angular-editor>
                    <textarea
                      *ngIf="!htmlcheckBox[0]"
                      [placeholder]="'Exhibition Title'"
                      formControlName="title"
                      rows="6"
                      style="padding: 1vw; width: 100%"
                    ></textarea>
                    <div class="input-info">
                      Provide the Exhibition title to be used in the banner.
                      Maximum 25 characters.
                      <div
                        style="
                          display: inline-block;
                          text-align: right;
                          width: 100%;
                        "
                      >
                        <input
                          type="checkbox"
                          [(ngModel)]="htmlcheckBox[0]"
                          [ngModelOptions]="{ standalone: true }"
                        />
                        HTML Editor
                      </div>
                    </div>
                  </div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    formControlName="titleSize"
                    placeholder="Text-Size"
                  />
                  <div class="placeholder">Title Size</div>
                  <div class="input-info">
                    Provide the font size for the Exhibition title on the banner
                    (between 1 and 3).
                  </div>
                </div>
                <div class="field-value" style="position: relative">
                  <input
                    type="color"
                    formControlName="bannerTextColor"
                    placeholder="HEX code"
                    style="inline-size: 25vw"
                  />
                  <!-- <div class="forHex"></div> -->
                  <div class="placeholder">Banner Text Colour</div>
                  <div class="input-info">
                    Provide the HEX code for the banner text colours (eg:
                    #6fa8dc).
                  </div>
                </div>
              </div>
            </div>

            <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">Show Above Subtitle :</div>
                  <label class="switch">
                    <input
                      type="checkbox"
                      formControlName="showAboveSubtitle"
                    />
                    <span class="slider round"></span>
                  </label>
                </div>
                <div class="input-info">
                  Choose whether to show description text.
                </div>
              </div>
              <div
                class="field-value"
                *ngIf="form.get('showAboveSubtitle').value"
                style="padding-right: 0.5vw"
              >
                <div class="placeholder">Above Title text</div>
                <div>
                  <angular-editor
                    *ngIf="htmlcheckBox[1]"
                    id="editor21"
                    formControlName="aboveSubText"
                  ></angular-editor>
                  <textarea
                    *ngIf="!htmlcheckBox[1]"
                    formControlName="aboveSubText"
                    rows="6"
                    style="padding: 1vw; width: 100%"
                  ></textarea>
                  <div class="input-info">
                    Provide the subtitle text to use above the title.
                    <div
                      style="
                        display: inline-block;
                        text-align: right;
                        width: 100%;
                      "
                    >
                      <input
                        type="checkbox"
                        [(ngModel)]="htmlcheckBox[1]"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      HTML Editor
                    </div>
                  </div>
                </div>
              </div>
              <div
                *ngIf="form.get('showAboveSubtitle').value"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <input
                  type="number"
                  placeholder="Font-Size"
                  formControlName="aboveSubSize"
                />
                <div class="placeholder">Above Subtitle size</div>
                <div class="input-info">
                  Provide the font size for the subtitle (decimal values between
                  1.0 and 2.0).
                </div>
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Show Below Subtitle :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showBelowSubtitle" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">Choose whether to show a subtitle.</div>
            </div>
            <div
              class="field-value"
              *ngIf="form.get('showBelowSubtitle').value"
              style="padding-right: 0.5vw"
            >
              <div class="placeholder">Below Title text</div>
              <div>
                <angular-editor
                  *ngIf="htmlcheckBox[2]"
                  id="editor22"
                  formControlName="belowSubText"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[2]"
                  formControlName="belowSubText"
                  rows="6"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide the subtitle text to use below the title.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[2]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              *ngIf="form.get('showBelowSubtitle').value"
            >
              <input
                type="number"
                placeholder="Font-Size"
                formControlName="belowSubSize"
              />
              <div class="placeholder">Below Subtitle size</div>
              <div class="input-info">
                Provide the font size for the subtitle (decimal values between
                1.0 and 2.0).
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Show Artist Name :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showArtistName" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to show the artist name on the banner.
              </div>
            </div>
            <div
              class="field-value"
              *ngIf="form.get('showArtistName').value"
              style="padding-right: 0.5vw"
            >
              <div class="placeholder">Artist Name</div>
              <div>
                <angular-editor
                  *ngIf="htmlcheckBox[3]"
                  id="editor23"
                  formControlName="artistName"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[3]"
                  formControlName="artistName"
                  rows="6"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide the artist details to be displayed on the banner.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[3]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
            <div
              *ngIf="form.get('showArtistName').value"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <input
                type="number"
                placeholder="Font-Size"
                formControlName="artistFontSize"
              />
              <div class="placeholder">Artist Name Size</div>
              <div class="input-info">
                Provide the font size for the Artist Name (between 1 and 2).
              </div>
            </div>
          </div>

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Dates:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showDate" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show the exhibition dates.
            </div>
          </div>
          <div *ngIf="form.get('showDate').value">
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  placeholder="Exhibition Date"
                  formControlName="exhibitionDateStart"
                />
                <div class="placeholder">Exhibition Start Date</div>
                <div class="input-info">Provide the exhibition start date.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  placeholder="Exhibition Date"
                  formControlName="exhibitionDateEnd"
                />
                <div class="placeholder">Exhibition End Date</div>
                <div class="input-info">Provide the exhibition end date.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Exhibition Details"
                  formControlName="exhibitionDateNote"
                />
                <div class="placeholder">Exhibition Details</div>
                <div class="input-info">
                  Provide the Exhibition Dates \ Details to be displayed on the
                  banner media.
                </div>
              </div>
            </div>
            <div class=""></div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
