import { Component, OnInit } from "@angular/core";
import { FormArray, FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import {
  faEye,
  faEyeSlash,
  faTrash,
  faGripVertical,
  faCheckCircle,
  faTimesCircle,
  faCircle,
  faLock,
  faUnlockAlt,
} from "@fortawesome/free-solid-svg-icons";
import { Subject } from "rxjs/internal/Subject";
import { debounceTime } from "rxjs/operators";
import { CollectorService } from "src/app/services/collector.service";
import { apiUrl } from "src/environments/environment.prod";
import {
  faAngleDown,
  faAngleLeft,
  faAngleRight,
  faFlag,
  faPencilAlt,
  faPlusCircle,
} from "@fortawesome/free-solid-svg-icons";
import { AngularEditorConfig } from "@kolkov/angular-editor";



@Component({
  selector: "app-add-ons",
  templateUrl: "./add-ons.component.html",
  styleUrls: ["./add-ons.component.scss"],
})
export class AddOnsComponent implements OnInit {

  editorConfig: AngularEditorConfig = {
    editable: true,
    // spellcheck: true,
    // translate: 'yes',
    // enableToolbar: true,
    // showToolbar: true,
    sanitize: false,
  };

  htmlcheckBox = Array(10).fill(false);
  faCheckCircle = faCheckCircle;
  faTimesCircle = faTimesCircle;
  faCircle = faCircle;
  faLock = faLock;
  faUnlockAlt = faUnlockAlt;
  faEllipsisV = faGripVertical;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faTrash = faTrash;
  dynamicForm: FormGroup;
  selectedIndex: any = 0;
  dataObj: any = {};
  search: Subject<any> = new Subject();
  searchKey: any = "";
  offset: number = 1;
  totalPage: number = 0;
  artworksArr: any = [];
  selectedArtworksArr: any = [
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
  ];
  //selectedArtworksArr = Array(30).fill([]);
  countObj: any = {
    textComponent: -1,
    introVideo: -1,
    bannerDivide: -1,
    audio: -1,
    quoteMedia: -1,
    videoComponent: -1,
    quote: -1,
    artworkSelect: -1,
    artistProfile1: -1,
    artistProfile2: -1,
    artworkFeature: -1,
  };
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private router: Router,
  ) { }
  isFilterDropdownOpen = false;
  isFilterDropdownOpen1 = false;
  isDropDownOpen;
  isPopupOpen: boolean = false;
  artistMediaType: any = "all";
  isArtworkPopupOpen;
  isArtworkDropdownOpen1;
  isArtworkDropdownOpen2;
  isArtworkDropdownOpen3;
  isQuoteMediaDropdownOpen1;
  isQuoteMediaDropdownOpen2;
  isQuoteMediaDropdownOpen3;
  isArtistProfile2DropdownOpen;
  isBannerDropdownOpen;
  artistMedia1 = Array(10).fill(false);
  bannerType1 = Array(10).fill(false);
  griddrop1 = Array(10).fill(false);
  imageTypeDrop = Array(10).fill(false);
  textPosition = Array(10).fill(false);
  componentName: string;
  postion = Array(10).fill(false);
  bannerTypeIndexed = [];
  form: FormGroup;
  selectedFiles: File[][] = [
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
  ];
  ForDeleteComponentName;
  ForDeleteIndex;
  MediaTypeImage = {
    banner: [false, false],
    titleBanner: false,
    quoteMedia: null,
    artistMedia: null,
  };
  placeholder: any = "";
  faAngleLeft = faAngleLeft;
  faAngleRight = faAngleRight;
  faCartArrowDown = faAngleDown;
  selectedFilesObj: any = {
    audioArr: [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []],
    artistMediaTypeArr: [[], [], [], [], [], [], [], [], [], [], [], [], []],
    bannerArr: [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []],
    imageUrls: [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []],
    gridUrls: [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []],
    quoteImageUrls: [
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
    ],
    profileImageArr: [
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
      [
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
        [],
      ],
    ],
    // 'profileImageArr0':[],
    // 'profileImageArr1':[],
    // 'profileImageArr2':[],
    // 'profileImageArr3':[],
    // 'profileImageArr4':[],
    // 'profileImageArr5':[],
  };
  accept: any = "all";
  filter: any = "Artwork Title";
  sort: any = "Desc";
  totalRecords: number = 0;
  totalPages: number = 0;

  // new
  componentSelector: any = "Choose Component";
  showdrop: boolean = false;

  artworkSelectedInput = false;

  changeImagePopup = {
    open: false,
    count: 0,
    index: 0,
    dropdown: false,
  };

  ngOnInit(): void {
    this.initialiseForm();
    this.searchByKey();
    if (sessionStorage.getItem("exhibitionIdID")) {
      this.getExhibitionById();
    }
    this.isArtworkPopupOpen = false;
    this.isArtworkDropdownOpen1 = Array(10).fill(false);
    this.isArtworkDropdownOpen2 = Array(10).fill(false);
    this.isArtworkDropdownOpen3 = Array(10).fill(false);

    this.isQuoteMediaDropdownOpen1 = Array(10).fill(false);
    this.isQuoteMediaDropdownOpen2 = Array(10).fill(false);
    this.isQuoteMediaDropdownOpen3 = Array(10).fill(false);
  }
  ComponentSelection = {
    titleBanner: false,
    description: false,
    introVideo: false,
    quote: false,
    audio: false,
    artistProfile1: false,
    artistProfile2: false,
    quoteMedia: false,
    artworkSelect: false,
    bannerDivide: false,
    threeDModel: false,
  };
  repeat = ["artworkSelect", "bannerDivide", "quote", "quoteMedia"];
  setDeleteSelection(componentname, index) {
    switch (componentname) {
      case "artworkSelect":
        this.ForDeleteComponentName = "artworkSelect";
        this.ForDeleteIndex = index;
        break;
      case "quote":
        this.ForDeleteComponentName = "quote";
        this.ForDeleteIndex = index;
        break;
      case "quoteMedia":
        this.ForDeleteComponentName = "quoteMedia";
        this.ForDeleteIndex = index;
        break;
      case "artistProfileMedia":
        this.ForDeleteComponentName = "artistProfileMedia";
        this.ForDeleteIndex = index;
        break;
      case "artistProfile2Media":
        this.ForDeleteComponentName = "artistProfile2Media";
        this.ForDeleteIndex = index;
        break;
      case "bannerDivide":
        if (this.selectBannerDivide.length < 2) {
          this.ForDeleteComponentName = "bannerDivide";
          this.ForDeleteIndex = index;
        }
        break;

      default:
        break;
    }
    this.isPopupOpen = true;
  }
  ComponentSelect(componentName, dropdownName) {
    this.form.get("componentSelector").setValue(dropdownName);
    this.componentName = componentName;
  }
  DeleteText(i, j) {
    this.mainArr.controls[i]["controls"]["textArr"].removeAt(j);
  }

  deleteComponent() {
    if (
      this.repeat.includes(this.ForDeleteComponentName) ||
      this.ForDeleteComponentName == "artistProfileMedia"
    ) {
      switch (this.ForDeleteComponentName) {
        case "artworkSelect":
          this.selectArtwork.removeAt(this.ForDeleteIndex);
          break;
        case "quote":
          this.selectQuote.removeAt(this.ForDeleteIndex);
          break;
        case "quoteMedia":
          this.selectQuoteMedia.removeAt(this.ForDeleteIndex);
          break;
        case "artistProfileMedia":
          this.selectArtistMedia2.removeAt(this.ForDeleteIndex);
          break;
        case "bannerDivide":
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.removeAt(this.ForDeleteIndex);
          }
          break;

        default:
          break;
      }

      this.isPopupOpen = false;
    }
    if (this.ForDeleteComponentName == "artistProfile2Media") {
      this.artistProfile2Media.removeAt(this.ForDeleteIndex);
    }
  }
  addComponent(componentName) {
    if (
      this.ComponentSelection[componentName] &&
      this.repeat.includes(componentName)
    ) {
      switch (componentName) {
        case "artworkSelect":
          this.selectArtwork.push(
            this.formBuilder.group({
              showArtworks: true,
              artworkGrid: { value: "", disabled: true },
              artworkInterior: { value: "Artwork Interior", disabled: true },
              artistSelect: { value: "Artwork Select", disabled: false },
              showPrice: false,
              showArtist: false,
            }),
          );
          break;
        case "quote":
          this.selectQuote.push(this.formBuilder.group({ showQuote: true }));
          break;
        case "quoteMedia":
          this.selectQuoteMedia.push(
            this.formBuilder.group({
              showQuoteMedia: true,
              quoteMediaPosition: {
                value: "Quote Media Position",
                disabled: true,
              },
              quoteMediaBackground: {
                value: "Quote Media Background",
                disabled: true,
              },
              quoteMedia: { value: "Quote Media", disabled: true },
            }),
          );
          break;
        case "bannerDivide":
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.push(
              this.formBuilder.group({
                showBanner: false,
                bannerType: { value: "Banner Type", disabled: true },
                showTitle: false,
                showButtons: false,
                showButton2: false,
                showButton3: false,
              }),
            );
          }
          break;

        default:
          break;
      }
    } else this.ComponentSelection[componentName] = true;
  }
  bannerTypeSelect(type, index) {
    this.selectBannerDivide.controls[index].get("bannerType").setValue(type);
    this.bannerTypeIndexed[index] =
      this.selectBannerDivide.controls[index].get("bannerType").value;
    if ((type = "Video")) {
      this.MediaTypeImage.banner[index] = false;
    } else {
      this.MediaTypeImage.banner[index] = true;
    }
  }
  artworkSelector(index, name) {
    this.selectArtwork.controls[index].get("artworkGrid").setValue(name);
  }
  artistSelector(index, name) {
    this.selectArtwork.controls[index].get("artistSelect").setValue(name);
  }
  addRepeater1() {
    this.selectArtistMedia2.push(
      this.formBuilder.group({
        showUrl: true,
        position: { value: "Position", disabled: true },
      }),
    );
  }
  get selectArtwork(): FormArray {
    return <FormArray>this.form.get("selectArtwork");
  }
  get selectQuoteMedia(): FormArray {
    return <FormArray>this.form.get("selectQuoteMedia");
  }
  get selectQuote(): FormArray {
    return <FormArray>this.form.get("selectQuote");
  }
  get selectArtistMedia2(): FormArray {
    return <FormArray>this.dynamicForm.get("selectArtistMedia2");
  }
  get selectBannerDivide(): FormArray {
    return <FormArray>this.form.get("selectBannerDivide");
  }

  // new code starts from here
  onComponentSelect(componentName, dropdownName) {
    this.componentName = componentName;
    this.componentSelector = dropdownName;
    this.showdrop = true;
  }

  initialiseForm() {
    this.dynamicForm = this.formBuilder.group({
      mainArr: this.formBuilder.array([]),
      // filter: new FormControl({value:'Artwork Title',disable:false}),
      // sort: new FormControl({value: 0 ,disable:false}),
    });
  }

  // to add into profile array
  addIntoProfileArr(index) {
    console.log(index);
    // this.artistProfileArr
    console.log(this.mainArr);
    this.mainArr.controls[index]["controls"]["artistProfileArr"].push(
      this.formBuilder.group({
        collapse: new FormControl(false),
        artistName: new FormControl(""),
        showUrl: new FormControl(false),
        profileUrl: new FormControl(""),
        position: new FormControl(""),
      }),
    );
  }

  addIntotextArr(index) {
    this.mainArr.controls[index]["controls"]["textArr"].push(
      this.formBuilder.group({
        showText: new FormControl(true),
        showTextData: new FormControl(""),
        textPosition: new FormControl(""),
        zoomLevel: new FormControl(""),
        xAxis: new FormControl(""),
        yAxis: new FormControl(""),
      }),
    );
  }

  addNewComponent(ele, index, type) {
    this.countObj[this.componentName]++;
    console.log(this.componentName);
    if (this.componentName == "Choose Component" || !this.componentName) {
      return;
    }
    this.mainArr.push(
      this.formBuilder.group({
        collapse: new FormControl(false),
        publish: new FormControl(false),
        compType: new FormControl(this.componentName),
        count: new FormControl(this.countObj[this.componentName]),

        // description

        // text component
        descriptionText: new FormControl(""),
        EnableReadMore: new FormControl(false),

        // 3d model
        link: new FormControl(""),
        // video
        exhibitionVideo: new FormControl(""),
        video_description: new FormControl(""),

        //artwork select
        artworkGrid: new FormControl(""),
        artworkInterior: new FormControl(""),
        artistSelect: new FormControl({ value: "", disabled: false }),
        showPrice: new FormControl(false),
        showArtist: new FormControl(false),
        mediaUrl: new FormControl(""),

        // quote
        quoteText: new FormControl(""),

        // quote media
        quoteData: new FormControl(""),
        quoteSrc: new FormControl(""),
        quoteSize: new FormControl(),
        quoteLinkOrText: new FormControl(),
        quoteMediaPosition: new FormControl(""),
        quoteMediaBackground: new FormControl(""),
        quoteMedia: new FormControl(""),

        //audio
        audioLoop: new FormControl(true),
        audioLevel: new FormControl(""),

        //artist profile 1
        artistMedia: new FormControl({ value: "" }),
        showProfileLink: new FormControl(true),
        btnText: new FormControl(""),
        profileText: new FormControl(""),
        profileUrl: new FormControl(""),
        profileMediaUrl: new FormControl(""),
        fieldTitleText: new FormControl(""),

        // banner
        videoUrlBanner: new FormControl(""),
        titleText: new FormControl(""),
        bannerType: new FormControl({ value: "" }),
        showTitle: new FormControl(false),
        showButtons: new FormControl(false),
        button1Text: new FormControl(""),
        button1Url: new FormControl(""),
        button2Text: new FormControl(""),
        button2Url: new FormControl(""),
        showButton2: new FormControl(false),
        showButton3: new FormControl(false),
        button3Text: new FormControl(""),
        button3Url: new FormControl(""),
        showArtworkFeature: new FormControl(false),
        imageType: new FormControl(""),
        griddimensions: new FormControl(""),

        // artist profile 2
        artistProfileText: new FormControl(""),
        artistProfileArr: this.formBuilder.array([]),
        textArr: this.formBuilder.array([]),
      }),
    );
    this.componentName = "";
    this.componentSelector = "Choose Component";
    if (type == "update") {
      this.patchComponent(ele, index);
    }
  }

  get mainArr(): FormArray {
    return <FormArray>this.dynamicForm.get("mainArr");
  }
  get artistProfileArr(): FormArray {
    return <FormArray>this.dynamicForm.get("mainArr").get("artistProfileArr");
  }
  get artistProfile2Media(): FormArray {
    return <FormArray>(
      this.dynamicForm.get("mainArr")["controls"][0]["controls"][
      "artistProfileArr"
      ]
    );
  }
  collapseForm(value, index) {
    (this.mainArr.at(index) as FormGroup).get("collapse").patchValue(!value);
  }
  deleteRow(index, compName) {
    this.selectedIndex = index;
    this.isPopupOpen = true;
  }

  deleteArtistMedia(index) {
    if (confirm("Are you sure you wish to remove the media?")) {
      this.artistProfile2Media.removeAt(index);
    }
  }
  confirmDelete() {
    this.mainArr.removeAt(this.selectedIndex);
    this.isPopupOpen = false;
  }
  value(item) {
    console.log(item);
  }
  changeFocus(dropname, index) {
    console.log("change Focus");
    switch (dropname) {
      case "isArtworkDropdownOpen1":
        {
          console.log(121212);
          setTimeout(() => {
            this.isArtworkDropdownOpen1[index] = false;
          }, 200);
        }
        break;

      default:
        break;
    }
  }
  chooseDrop() {
    setTimeout(() => {
      this.showdrop = false;
    }, 200);
  }
  getExhibitionById() {
    let url =
      apiUrl.exhibitions.getExhibitions +
      `/${sessionStorage.getItem("exhibitionIdID")}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.dataObj = res.data;
        res.data.contents.forEach((ele, index) => {
          // console.log(ele)
          if (ele.compType == "textComponent") {
            this.componentName = "textComponent";
          } else if (ele.compType == "introVideo") {
            this.componentName = "introVideo";
          } else if (ele.compType == "bannerDivide") {
            this.componentName = "bannerDivide";
          } else if (ele.compType == "audio") {
            this.componentName = "audio";
          } else if (ele.compType == "quoteMedia") {
            this.componentName = "quoteMedia";
          } else if (ele.compType == "videoComponent") {
            this.componentName = "videoComponent";
          } else if (ele.compType == "quote") {
            this.componentName = "quote";
          } else if (ele.compType == "artworkSelect") {
            this.componentName = "artworkSelect";
          } else if (ele.compType == "artistProfile1") {
            this.componentName = "artistProfile1";
          } else if (ele.compType == "artworkFeature") {
            this.componentName = "artworkFeature";
          } else if (ele.compType == "artistProfile2") {
            this.componentName = "artistProfile2";
          } else if (ele.compType == "threeDModel") {
            this.componentName = "threeDModel";
          }
          console.log(this.countObj);
          this.addNewComponent(ele, index, "update");
        });
      }
    });
  }

  patchComponent(ele, index) {
    this.dynamicForm["controls"].mainArr["controls"][index]
      .get("publish")
      .setValue(ele.data.publish);
    if (ele.compType == "textComponent") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("descriptionText")
        .setValue(ele.data.text);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("EnableReadMore")
        .setValue(ele.data.readMore);
    } else if (ele.compType == "introVideo") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("exhibitionVideo")
        .setValue(ele.data.src);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("video_description")
        .setValue(ele.data.description);
    } else if (ele.compType == "threeDModel") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("link")
        .setValue(ele.data.link);
    } else if (ele.compType == "artworkSelect") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("artworkGrid")
        .setValue(ele.data.artworkGrid);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("artworkInterior")
        .setValue(ele.data.artworkInterior);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showPrice")
        .setValue(ele.data.showPrice);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showArtist")
        .setValue(ele.data.showArtist);
      ele.data.selectedArtworks?.forEach((ele_sub) => {
        let obj = this.dataObj.artwork_containers.find(
          (x) => x["_id"] == ele_sub["_id"],
        );
        if (ele_sub?.changeImage && ele_sub?.alterImage) {
          obj["alterImage"] = ele_sub.alterImage;
          obj["changeImage"] = ele_sub.changeImage;
        }
        this.selectedArtworksArr[this.countObj[ele.compType]].push(obj);
      });
    } else if (ele.compType == "quoteMedia") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteData")
        .setValue(ele.data.quote);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteSrc")
        .setValue(ele.data.image);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteMediaPosition")
        .setValue(ele.data.mediaPosition);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteLinkOrText")
        .setValue(ele.data.mediaUrl);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteMediaBackground")
        .setValue(ele.data.mediaBackground);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteMedia")
        .setValue(ele.data.quoteMedia);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteSize")
        .setValue(ele.data.quoteSize);
      if (ele.data.quoteMedia != "Video" && ele.data.mediaUrl) {
        this.selectedFilesObj.quoteImageUrls[this.countObj[ele.compType]].push({
          url: ele.data.mediaUrl,
        });
      }
    } else if (ele.compType == "videoComponent") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("video_url")
        .setValue(ele.data.video_url);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("video_description")
        .setValue(ele.data.video_description);
    } else if (ele.compType == "quote") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("quoteText")
        .setValue(ele.data.text);
    } else if (ele.compType == "bannerDivide") {
      if (ele.data.bannerType != "Video" && ele.data.src) {
        this.selectedFilesObj.bannerArr[this.countObj[ele.compType]].push({
          url: ele.data.src,
        });
      } else {
        this.dynamicForm["controls"].mainArr["controls"][index]
          .get("videoUrlBanner")
          .setValue(ele.data.src);
      }
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("bannerType")
        .setValue(ele.data.bannerType);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showTitle")
        .setValue(ele.data.showTitle);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("titleText")
        .setValue(ele.data.titleText);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showButtons")
        .setValue(ele.data.showButtons);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button1Text")
        .setValue(ele.data.button1Text);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button1Url")
        .setValue(ele.data.button1Url);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showButton2")
        .setValue(ele.data.showButton2);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button2Text")
        .setValue(ele.data.button2Text);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button2Url")
        .setValue(ele.data.button2Url);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showButton3")
        .setValue(ele.data.showButton3);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button3Text")
        .setValue(ele.data.button3Text);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("button3Url")
        .setValue(ele.data.button3Url);
    } else if (ele.compType == "audio") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("audioLevel")
        .setValue(ele.data.audioLevel);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("audioLoop")
        .setValue(ele.data.loop);
      if (ele.data.src) {
        this.selectedFilesObj.audioArr[this.countObj[ele.compType]].push({
          url: ele.data.src,
        });
      }
    } else if (ele.compType == "artistProfile1") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("profileText")
        .setValue(ele.data.text);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("profileUrl")
        .setValue(ele.data.url);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("artistMedia")
        .setValue(ele.data.mediaType);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("showProfileLink")
        .setValue(ele.data.showProfileLink);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("btnText")
        .setValue(ele.data.btnText);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("fieldTitleText")
        .setValue(ele.data.fieldTitleText);
      if (ele.data.mediaType == "Video") {
        this.artistMediaType = "video/*";
        this.dynamicForm["controls"].mainArr["controls"][index]
          .get("mediaUrl")
          .setValue(ele.data.mediaUrl);
      } else {
        this.selectedFilesObj.artistMediaTypeArr[
          this.countObj[ele.compType]
        ].push({
          url: ele.data.mediaUrl,
        });
      }
    } else if (ele.compType == "artworkFeature") {
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("imageType")
        .setValue(ele.data.imageType);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("griddimensions")
        .setValue(ele.data.gridDimensions);
      if (ele.data.imageUrls.length > 0) {
        this.selectedFilesObj.imageUrls[this.countObj[ele.compType]].push({
          url: ele.data.imageUrls,
        });
      }
      if (ele.data.gridUrls.length > 0) {
        this.selectedFilesObj.gridUrls[this.countObj[ele.compType]].push({
          url: ele.data.gridUrls,
        });
      }

      ele.data.textArr.forEach((sub_ele) => {
        this.mainArr.controls[index]["controls"]["textArr"].push(
          this.formBuilder.group({
            showText: new FormControl(sub_ele.showText),
            showTextData: new FormControl(sub_ele.showTextData),
            textPosition: new FormControl(sub_ele.textPosition),
            xAxis: new FormControl(sub_ele.xAxis),
            yAxis: new FormControl(sub_ele.yAxis),
            zoomLevel: new FormControl(sub_ele.zoomLevel),
          }),
        );
      });
    } else if (ele.compType == "artistProfile2") {
      console.log(ele);
      this.dynamicForm["controls"].mainArr["controls"][index]
        .get("artistProfileText")
        .setValue(ele.data.artistProfileText);

      ele.data.artistProfileArr.forEach((sub_ele, sub_index) => {
        // let str = `profileImageArr${sub_index}`

        // this.selectedFilesObj[str].push({
        //   url : sub_ele['url']
        // })
        this.selectedFilesObj["profileImageArr"][this.countObj[ele.compType]][
          sub_index
        ].push({
          url: sub_ele["url"],
        });
        this.mainArr.controls[index]["controls"]["artistProfileArr"].push(
          this.formBuilder.group({
            artistName: new FormControl(sub_ele.artistName),
            showUrl: new FormControl(sub_ele.showUrl),
            profileUrl: new FormControl(sub_ele.profileUrl),
            position: new FormControl(sub_ele.position),
          }),
        );
      });
    }
  }

  onSubmit() {
    console.log(this.mainArr.controls);
    // this.selectedArtworksArr.forEach(ele_arr => {
    //   ele_arr
    // });
    // if(this.selectedArtworksArr.length > 3 || this.selectedArtworksArr.length == 0){
    //   alert('Require minimum artworks to be one and maximum to be three in ArtworksSelect section !')
    //   return;
    // }

    // console.log(this.mainArr.value)
    let arr = [];
    this.mainArr.controls.forEach((ele, index) => {
      let obj = {};
      if (ele["value"]["compType"] == "textComponent") {
        console.log(ele);
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "text-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          text: ele["value"]["descriptionText"],
          readMore: ele["value"]["EnableReadMore"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "introVideo") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "video-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          src: ele["value"]["exhibitionVideo"],
          title: "",
          description: ele["value"]["video_description"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "threeDModel") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "threeD-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          link: ele["value"]["link"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "artworkSelect") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "artwork-container";
        obj["model"] = "model-1";
        obj["data"] = {
          artworkGrid: ele["value"]["artworkGrid"],
          artworkInterior: ele["value"]["artworkInterior"],
          selectedArtworks: this.selectedArtworksArr[ele["value"]["count"]],
          showPrice: ele["value"]["showPrice"],
          showArtist: ele["value"]["showArtist"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "quote") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "quote-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          text: ele["value"]["quoteText"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "quoteMedia") {
        let imageUrl;
        if (ele["value"]["quoteMedia"] == "Video") {
          imageUrl = ele["value"]["quoteLinkOrText"];
        } else {
          imageUrl =
            this.selectedFilesObj.quoteImageUrls[ele["value"]["count"]]?.[0]
              ?.url;
        }
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "quote-cards";
        obj["model"] = "model-1";
        obj["data"] = {
          quote: ele["value"]["quoteData"],
          image: ele["value"]["quoteSrc"],
          mediaPosition: ele["value"]["quoteMediaPosition"],
          mediaBackground: ele["value"]["quoteMediaBackground"],
          mediaUrl: imageUrl,
          quoteMedia: ele["value"]["quoteMedia"],
          quoteSize: ele["value"]["quoteSize"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "audio") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "audio-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          src:
            this.selectedFilesObj.audioArr[ele["value"]["count"]].length > 0
              ? this.selectedFilesObj.audioArr[ele["value"]["count"]][0].url
              : "",
          audioLevel: ele["value"]["audioLevel"],
          loop: ele["value"]["audioLoop"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "artistProfile1") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "profile-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          text: ele["value"]["profileText"],
          url: ele["value"]["profileUrl"],
          mediaType: ele["value"]["artistMedia"],
          mediaUrl:
            this.artistMediaType == "video/*"
              ? ele["value"]["mediaUrl"]
              : this.selectedFilesObj.artistMediaTypeArr[ele["value"]["count"]]
                .length > 0
                ? this.selectedFilesObj.artistMediaTypeArr[
                  ele["value"]["count"]
                ][0].url
                : "",
          showProfileLink: ele["value"]["showProfileLink"],
          btnText: ele["value"]["btnText"],
          fieldTitleText: ele["value"]["fieldTitleText"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "bannerDivide") {
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "banner-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          bannerType: ele["value"]["bannerType"],
          showTitle: ele["value"]["showTitle"],
          src:
            ele["value"]["bannerType"] == "Video"
              ? ele["value"]["videoUrlBanner"]
              : this.selectedFilesObj.bannerArr[ele["value"]["count"]].length >
                0
                ? this.selectedFilesObj.bannerArr[ele["value"]["count"]][0].url
                : "",
          titleText: ele["value"]["titleText"],
          showButtons: ele["value"]["showButtons"],
          button1Text: ele["value"]["button1Text"],
          button1Url: ele["value"]["button1Url"],
          showButton2: ele["value"]["showButton2"],
          button2Text: ele["value"]["button2Text"],
          button2Url: ele["value"]["button2Url"],
          showButton3: ele["value"]["showButton3"],
          button3Text: ele["value"]["button3Text"],
          button3Url: ele["value"]["button3Url"],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "artworkFeature") {
        console.log(ele["value"]);
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "banner-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          imageType: ele["value"]["imageType"],
          imageUrls:
            this.selectedFilesObj.imageUrls[ele["value"]["count"]].length > 0
              ? this.selectedFilesObj.imageUrls[ele["value"]["count"]][0].url
              : "",
          gridDimensions: ele["value"]["griddimensions"],
          gridUrls:
            this.selectedFilesObj.gridUrls[ele["value"]["count"]].length > 0
              ? this.selectedFilesObj.gridUrls[ele["value"]["count"]][0].url
              : "",
          textArr: [...ele["value"]["textArr"]],
          publish: ele["value"]["publish"],
        };
      } else if (ele["value"]["compType"] == "artistProfile2") {
        console.log(
          this.selectedFilesObj["profileImageArr"],
          ele["value"]["count"],
        );
        ele["value"]["artistProfileArr"].forEach((sub_ele, index) => {
          // let str = `profileImageArr${index}`
          // console.log(str)
          sub_ele["url"] =
            this.selectedFilesObj["profileImageArr"][ele["value"]["count"]][
            index
            ][0]["url"];
        });
        obj["compType"] = ele["value"]["compType"];
        obj["component"] = "banner-containers";
        obj["model"] = "model-1";
        obj["data"] = {
          artistProfileText: ele["value"]["artistProfileText"],
          artistProfileArr: [...ele["value"]["artistProfileArr"]],
          publish: ele["value"]["publish"],
        };
      }
      arr.push(obj);
    });
    // to send artworks id
    let artworks_id = [];
    this.selectedArtworksArr.forEach((ele_arr) => {
      ele_arr.forEach((sub_ele) => {
        artworks_id.push(sub_ele["_id"]);
      });
    });
    let req = {
      contents: [...arr],
      artwork_containers: [...new Set(artworks_id)],
    };
    let url = sessionStorage.getItem("exhibitionIdID")
      ? apiUrl.exhibitions.getExhibitions +
      `/${sessionStorage.getItem("exhibitionIdID")}`
      : apiUrl.exhibitions.getExhibitions;
    if (sessionStorage.getItem("exhibitionIdID")) {
      this.updateBlog(req, url);
      return;
    }
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        sessionStorage.setItem("exhibitionIdID", res.data["_id"]);
        alert(res.message);
      }
    });
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem("blogID", res.data["_id"]);
        alert(res.message);
        //this.router.navigate(['artist-portal/settings/exhibitions']);
      }
    });
  }

  onFileSelect(files, key, count) {
    this.selectedFilesObj[key][count] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key][count] = [];
    } else {
      this.uploadFile(key, count, files);
    }
  }

  onFileSelectMulti(files, key, count, sub_count) {
    this.selectedFilesObj[key][count][sub_count] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key][count][sub_count] = [];
    } else {
      this.uploadFileMulti(key, count, sub_count, files);
    }
  }

  // to upload file
  uploadFileMulti(key, count, sub_count, files: any) {
    let formdata = new FormData();
    formdata.append("image", files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          console.log(this.selectedFilesObj, key);
          this.selectedFilesObj[key][count][sub_count][0]["url"] = res.data;
          alert("File uploaded successfully!");
        }
      },
      (err) => {
        alert(err.error.message);
      },
    );
  }

  // to upload file
  uploadFile(key, count, files: any) {
    let formdata = new FormData();
    formdata.append("image", files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          console.log(this.selectedFilesObj, key);
          this.selectedFilesObj[key][count][0]["url"] = res.data;
          alert("File uploaded successfully!");
        }
      },
      (err) => {
        alert(err.error.message);
      },
    );
  }

  // for artwork select
  searchByKey() {
    if (navigator.onLine) {
      this.search.pipe(debounceTime(1500)).subscribe((val) => {
        this.searchKey = val;
        this.offset = 1;
        this.totalPage = 0;
        this.getArtworks();
      });
    }
  }

  // to get Artworks
  getArtworks() {
    let url = apiUrl.getArtwork;
    let req = {
      search: this.searchKey,
      external: false,
      artworkIds: [],
      platform: "",
      userId: "",
      offset: this.offset,
      limit: 10,
      sort: this.sort == "Desc" ? 0 : 1,
      publish_artworks: false,
      priceMin: 0,
      priceMax: 0,
      sale_options: "",
      artwork_group: "",
      approved_status: "",
      artist: [],
      medium: [],
      subject: [],
      movement: [],
      tags: [],
      flag: null,
    };

    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        this.artworksArr = res.data;
        this.artworksArr.forEach((ele) => {
          ele["selected"] = false;
          let artIndex = this.selectedArtworksArr[this.selectedIndex].findIndex(
            (x) => x["_id"] == ele?.["_id"],
          );
          if (artIndex != -1) {
            ele["selected"] = true;
          }
        });
        this.totalRecords = res.fiter_totalCount;
        this.totalPage = Math.ceil(res.fiter_totalCount / 5);
      }
    });
  }

  searchResult(evt) {
    var charCode = evt.which ? evt.which : evt.keyCode;
    let string = evt.target.value;
    if (charCode == 13 || charCode == 8 || charCode == 16) {
      return;
    } else {
      this.search.next(string.trim());
    }
  }

  openArtworks(index) {
    this.selectedIndex = index;
    this.artworkSelectedInput = false;
    console.log(index);
    this.isArtworkPopupOpen = true;
    this.offset = 1;
    this.sort = "Desc";
    this.artworksArr = [];
    this.getArtworks();
  }

  // manage selected artworks
  manageSelectedArtworks() {
    console.log(this.selectedIndex);
    this.isArtworkPopupOpen = false;
    // this.artworksArr.forEach((ele) => {
    //   let index = this.selectedArtworksArr[this.selectedIndex].findIndex(
    //     (x) => x['_id'] == ele['_id']
    //   );
    //   if (
    //     ele.selected &&
    //     index == -1 &&
    //     this.selectedArtworksArr[this.selectedIndex].length < 3
    //   ) {
    //     this.selectedArtworksArr[this.selectedIndex].push(ele);
    //   }
    // });
  }
  onArtworkSelected(index) {
    if (this.artworksArr[index].selected) {
      let artIndex = this.selectedArtworksArr[this.selectedIndex].findIndex(
        (x) => x["_id"] == this.artworksArr[index]?.["_id"],
      );
      if (artIndex == -1) {
        this.selectedArtworksArr[this.selectedIndex].push(
          this.artworksArr[index],
        );
      }
    } else {
      let artIndex = this.selectedArtworksArr[this.selectedIndex].findIndex(
        (x) => x["_id"] == this.artworksArr[index]?.["_id"],
      );
      if (artIndex != -1) {
        this.selectedArtworksArr[this.selectedIndex].splice(artIndex, 1);
      }
    }
    console.log(this.selectedArtworksArr[this.selectedIndex]);
  }

  onSelctedArtworkshow() {
    if (this.artworkSelectedInput) {
      this.artworksArr = this.selectedArtworksArr[this.selectedIndex];
      this.artworksArr.forEach((ele) => {
        ele["selected"] = true;
      });
    } else {
      this.getArtworks();
    }
  }

  // to manage pagination
  managePagination(action) {
    if (action == "prev") {
      if (this.offset > 1) {
        this.offset = this.offset - 1;
      } else {
        return;
      }
    } else if (action == "next") {
      if (this.offset < this.totalPage) {
        this.offset = this.offset + 1;
      } else {
        return;
      }
    }
    this.getArtworks();
  }

  // to show all records
  findAll() {
    this.offset = 1;
    this.getArtworks();
  }
  onchangeImageSwitch(count, index) {
    if (this.selectedArtworksArr[count][index].changeImage) {
      this.changeImagePopup = {
        open: true,
        count,
        index,
        dropdown: false,
      };
      if (!this.selectedArtworksArr[count][index].alterImage) {
        this.selectedArtworksArr[count][index].alterImage = {
          type: "Video",
          link: "",
        };
      }
    } else {
      this.changeImagePopup = {
        open: false,
        count: 0,
        index,
        dropdown: false,
      };
    }
  }
  dotColor(status) {
    switch (status) {
      case "ForSale":
        return "#00FF00";
        break;
      case "Reserved":
        return "#FFFF00";
        break;
      case "OnReserve":
        return "#FFFF00";
        break;
      case "Sold":
        return "#FF0000";
        break;
      case "NotForSale":
        return "#101010";
        break;
      default:
        break;
    }
  }
  public trimSpaces(str) {
    let a = str.trim().replace("(", "_").replace(")", "_");
    let b = a.split(" ");
    return encodeURIComponent(b.join("_"));
  }
}
