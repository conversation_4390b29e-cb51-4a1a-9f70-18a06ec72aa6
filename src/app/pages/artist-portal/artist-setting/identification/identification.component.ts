import { Component, OnInit } from '@angular/core';
import { ArtistInfoService } from 'src/app/services/artist-info.service';

@Component({
	selector: 'app-identification',
	templateUrl: './identification.component.html',
	styleUrls: ['./identification.component.scss'],
})
export class IdentificationComponent implements OnInit {
	verified: boolean = true;
	data;
	constructor(private artistInfoService: ArtistInfoService) {}

	ngOnInit(): void {
		this.artistInfoService.getIdProof().subscribe((data) => {
			this.data = data;
		});
	}
}
