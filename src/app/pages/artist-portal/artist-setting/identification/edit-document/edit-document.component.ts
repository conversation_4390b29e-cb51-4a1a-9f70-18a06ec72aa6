import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-edit-document',
  templateUrl: './edit-document.component.html',
  styleUrls: ['./edit-document.component.scss']
})
export class EditDocumentComponent implements OnInit {


  isDropDown = false;
  isDropDown2 = false;
  replace = true;
  selectedFiles: File[] = [];
  constructor() { }
  ngOnInit(): void {
  }
  async onFileSelect(files: FileList) {
    if (files[0].size < 2100000) {
      this.selectedFiles.push(files[0]);
    }
  }
  removeItem(index) {
    this.selectedFiles.splice(index, 1);
  }

}
