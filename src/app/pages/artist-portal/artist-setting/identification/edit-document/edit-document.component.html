<div class="container__main">
  <p class="main__heading">
    <img src="../../../../../../assets/icons/<EMAIL>" alt=" Back" /> Edit
    Government ID
  </p>
  <p class="information">
    You have already uploaded your govenrment ID. If you want, you can upload
    another one, which will be used instead and will have to be reverified.
  </p>
  <div class="previous-document-container">
    <div class="headers">
      <p class="title">Passport</p>
      <p class="verified">
        <img
          src="../../../../../../assets/icons/<EMAIL>"
          alt="verified"
        />Verified
      </p>
    </div>
    <div class="document-preview">
      <img
        src="../../../../../../assets/<EMAIL>"
        alt="document"
      />
      <p class="document-name">Subahlakshmi_KYC_Passport.pdf</p>
    </div>
    <div *ngIf="replace" class="button-container">
        <div class="button">
          Replace file
        </div>
      </div>
  </div>

  <div  *ngIf="replace"  class="profile-field">
    <div class="w-100 field-contents">
      <form>

        
        <div class="field-value flex-block">
            <div class="input-container">
              <input
                type="text"
                class="selection"
                formControlName="documentType"
                placeholder="Document Type"
              />
              <button (click)="isDropDown2 = !isDropDown2">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDown2,
                  'dropdown-visible': isDropDown2
                }"
              >
                <ul>
                  <li>
                    <div class="country-name">Drivers License</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="placeholder">Document Type</div>
          </div>

          <div class="field-value flex-block">
            <div class="input-container file-upload">
              <input
                class="upload-input"
                type="file"
                (change)="onFileSelect($event.target.files)"
                accept="image/*,application/pdf"
              />
              <div *ngIf="selectedFiles.length <= 0" class="icon">
                <img src="assets/images/<EMAIL>" />
              </div>
              <div *ngIf="selectedFiles.length <= 0" class="text-content">
                <div class="title">
                  You can upload or drop your PDF file here.
                </div>
                <div class="sub-title">Maximum upload size: 2 MB</div>
              </div>
              <ng-container *ngIf="selectedFiles.length > 0">
                <ng-container *ngFor="let file of selectedFiles; let i = index">
                  <div class="text-content">
                    <div class="title">
                      {{ file.name }}
                    </div>
                    <div class="sub-title">
                      File size: {{ (file.size / 1048576).toFixed(2) }} MB
                    </div>
                    <div (click)="removeItem(i)" class="close">
                      <img src="assets/icons/close.png" />
                    </div>
                  </div>
                </ng-container>
              </ng-container>

              <div class="button-container">
                <div class="button">
                  {{ selectedFiles.length > 0 ? "Add file" : "Choose file" }}
                </div>
              </div>
            </div>
          </div>
          <p class="last-changed">
            Please upload a copy of a valid Government ID showing your full
            name, photograph, address and all relevant pages.
          </p>
          <div class="privacy-heading">
            Your privacy is important
          </div>
          <p class="last-changed">
            In order to prevent fraud and protect our artists and buyers we must
            receive a copy of a government issued photo ID before your work can
            be displayed for sale.
          </p>
          <p class="last-changed">
            Rest Assured that we will protect the image that you send us. Your
            ID file is encrypted from the time that it leaves your computer. It
            is stored in an encrypted state on our servers and can only be
            viewed by our administrative team.
          </p>
          <p class="last-changed">
            For more information, read our
            <span style="color: #004ddd">Privacy Policy </span>
          </p>
          <div class="buttonList">
            <button class="save">Save</button>
            <button class="cancel">Cancel</button>
        </div>
      </form>
    </div>
  </div>
</div>
