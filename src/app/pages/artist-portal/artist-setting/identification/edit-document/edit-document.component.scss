.container__main {
  padding-right: calc(35.111vw - var(--page-default-margin));
  padding-bottom: 6.3vw;
  @media (max-width: 768px){
    padding-right: calc(6.11vw - var(--page-default-margin));
    padding-bottom: 22.3vw;
  }
  .main__heading {

    img {
      width: 2.291vw;
      height: 2.291vw;
      margin-right: 1.042vw;
      cursor: pointer;
      @media (max-width: 768px) {
        display: none;
      }
    }
    font-size: 1.666vw;
    font-weight: 600;
    margin-bottom: 2.013vw;
    color: #000;
    @media (max-width: 768px) {
      font-size: 5.79vw;
      margin-bottom: 10.14vw;
    }
  }

  .information {
    font-family: var(--secondary-font);
    font-size: 1.111vw;
    color: #000;
    @media (max-width: 768px) {
      font-size: 3.86vw;
    }
  }
  .previous-document-container {
      margin-top: 4.513vw;
    padding: 2.083vw;
    border-radius: 2px;
  border: solid 1px var(--timeline-color);
  background-color: white;
  @media (max-width: 768px) {
    margin-top: 9.66vw;
    padding: 7.24vw;
  }
    .headers {
      display: flex;
      .title {
        font-size: 1.388vw;
        flex-grow: 1;
        color: #000;
        @media (max-width: 768px){
          font-size: 4.83vw;
        }
      }
      .verified {
          
          font-size: 0.972vw;
          color: var(--quaternary-font-color);
          @media (max-width: 768px){
            font-size: 3.38vw;
          }
        img {
          width: 1.388vw;
          height: 1.388vw;
          margin-right: 0.6944vw;
          @media (max-width: 768px){
            width: 3.62vw;
            height: 3.62vw;
          }
        }
      }
    }
    .document-preview{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        img{
            width: 15vw;
            height: 7.847vw;
            margin-bottom: 1.180vw;
            @media (max-width: 768px){
              width: 52.17vw;
              height: 27.29vw;
              margin-bottom: 3.62vw;
            }
        }
        .document-name{
            font-size: 0.972vw;
            color: #000;
            @media (max-width: 768px){
              font-size: 3.38vw;
              margin-bottom: 0;
            }
        }
    }
  }


  .profile-field {
    .field-contents {
      .field-heading {
        margin-top: 2.78vw;
        margin-bottom: 0;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
      }
      .double {
        width: 35vw;
        .input-double {
          width: 17.01vw !important;
        }
      }
      .field-value {
        margin-top: 2.08vw;
        position: relative;
        display: flex;
        justify-content: start;
        align-items: center;
        @media (max-width: 768px) {
          margin-top: 6.45vw;
          display: block;
        }
        input[type="text"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
          @media (max-width: 768px) {
            width: 87.16vw;
            height: 12.07vw;
            font-size: 3.86vw;
            padding-left: 3.62vw;
          }
        }
        input[type="date"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
          color: var(--quaternary-font-color);
          //   ::placeholder{
          // color: var(--quaternary-font-color);
          // }
        }
        input[type="password"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
        }
        .input-container {
          width: 100%;
          position: relative;
          display: inline-flex;
          justify-content: start;
          align-items: center;
          flex-wrap: wrap;
          .flag-icon {
            position: absolute;
            left: 1.04vw;
            @media (max-width: 768px){
              left: 3.62vw;
            }
          }
          button {
            outline: none;
            background: none;
            border: none;
          }
          .flag-arrow {
            position: absolute;
            max-width: 0.69vw;
            height: auto;
            right: 2.08vw;
            top: 0;
            bottom: 0;
            margin: auto;
            @media (max-width: 768px){
              max-width: 2.47vw;
              right: 5.08vw;
            }
          }
          .division {
            position: absolute;
            width: 0.069vw;
            height: 1.04vw;
            background-color: var(--timeline-color);
            left: 3.33vw;
            @media (max-width: 768px){
              width: 0.24vw;
              height: 4.58vw;
              left: 10.38vw;
            }
          }
          input[type="text"] {
            // background: transparent;
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 1.04vw 1.11vw 1.04vw 4.2vw;
            border-radius: 0.14vw;
            height: 3.47vw;
            width: 37vw;
            z-index: 0;
            @media (max-width: 768px) {
              width: 88.16vw;
              height: 12.07vw;
              // padding: 1.04vw 1.11vw;
              padding: 1.04vw 1.11vw 1.04vw 13.04vw;
            }
            &.selection {
              padding: 1.04vw 1.11vw 1.04vw 1.04vw;
              @media (max-width: 768px) {
                padding: 1.04vw 1.11vw 1.04vw 3.6vw;
              }
            }
          }
          .dropdown-visible {
            background-color: var(--primary-background-color);
            visibility: visible;
            position: absolute;
            top: 3.47vw;
            z-index: 1;
            box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
            @media (max-width: 768px){
              top: 12.47vw;
            }
            ul {
              list-style: none;
              padding: 0.69vw 0;
              max-height: 27.97vw;
              margin: 0;
              overflow: hidden;
              overflow-y: scroll;
              li {
                padding: 0.69vw 1.04vw;
                width: 34.9vw;
                display: flex;
                @media (max-width: 768px){
                  font-size: 3.38vw;
                  width: 86.9vw;
                  padding: 2vw 3.04vw;
                }
                .country-name {
                  margin-left: 0.69vw;
                }
              }
              li:hover {
                background-color: var(--timeline-color);
              }
            }
            ul::-webkit-scrollbar {
              display: none;
            }
          }
          .dropdown-hidden {
            display: none;
          }
        }
        .ph-flag {
          height: 1.25vw;
          // padding-right: 0.62vw;
          // border-right: solid 0.09vw var(--quaternary-font-color);
          @media (max-width: 768px) {
            height: 4.25vw;
            font-size: 3.86vw;
          }
        }
        .placeholder {
          position: absolute;
          top: -0.4vw;
          left: 1.04vw;
          font-size: 0.8333vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
          // background-color: #ededf1;
          @media (max-width: 768px) {
            top: -1.4vw;
            left: 3.62vw;
            font-size: 2.89vw;
          }
        }
        .send {
          margin-left: 2.08vw;
          color: var(--tertiary-font-color);
        }
      }
      .verify {
        margin-top: 2.78vw;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
      }
      .partitioned {
        margin-top: 1.33vw;
        outline: none;
        padding-left: 0.8vw;
        letter-spacing: 0;
        border: 0;
        background-image: linear-gradient(
          to left,
          var(--timeline-color) 70%,
          rgba(255, 255, 255, 0) 0%
        );
        background-position: bottom;
        background-size: 3.2vw 0.069vw;
        width: 3.2vw;
        background-repeat: repeat-x;
        background-position-x: 2.2vw;
        height: 2vw;
        padding-bottom: 0.35vw;
        font-family: var(--secondary-font);
      }
      .last-changed {
        margin-top: 1.04vw;
        font-size: 0.97vw;
        color: var(--quaternary-font-color);
        @media (max-width: 768px){
          margin-top: 3.62vw;
          font-size: 3.90vw;
        }
      }
      .privacy-heading{
        margin-top: 3.47vw;
         font-size: 1.25vw;
         @media (max-width: 768px){
          margin-top: 7.24vw;
          font-size: 4.34vw;
         margin-bottom: 3.62vw;
         }
      }
    }
    .buttonList {
      //   margin-top: 3.47vw;
        display: flex;
        margin-bottom: 1.042vw;
        margin-top: 2.47vw;
        @media (max-width: 768px) {
          display: block;
          margin-top: 12.06vw;
        }
        .save {
          // display: block;
          width: 100%;
          outline: none;
          font-size: 1.25vw;
          // width: 20.56vw;
          background-color: transparent;
          color: var(--tertiary-font-color);
          padding: 0.833333vw 6.89vw;
          border: 0.069vw solid var(--tertiary-font-color);
          border-radius: 1.46vw;
          @media (max-width: 768px) {
            font-size: 3.86vw;
            margin-bottom: 4.83vw;
            border-radius: 10.1vw;
            padding: 3.45vw 6.89vw;
          }
        }
        .cancel {
          width: 100%;
          outline: none;
          font-size: 1.25vw;
          // width:10.56vw;
          background-color: transparent;
          padding: 0.833333vw 2.777vw;
          border: 0.069vw solid transparent;
          border-radius: 1.46vw;
          color: var(--quaternary-font-color);
          @media (max-width: 768px) {
            font-size: 3.86vw;
          }
        }
        .save:hover {
          font-weight: 500;
        }
      }
  }



.file-upload {
  height: 100%;
  border-radius: 0.07vw;
  border: solid 0.07vw #ced4db;
  display: flex;
  justify-content: start;
  flex-direction: column;
  .icon {
    margin-top: 2.08vw;
    @media (max-width: 768px){
      margin-top: 7.24vw;
    }
    img {
      height: 1.38vw;
      width: 1.38vw;
      @media (max-width: 768px){
        height: 4.83vw;
        width: 4.83vw;
      }
    }
  }
  .text-content {
    text-align: center;
    margin-top: 1.38vw;
    width: 20.83vw;
    position: relative;
    padding-right: 1vw;
    padding-left: 1vw;
    @media (max-width: 768px){
      width: 100%;
      margin-top: 4.83vw;
    }
    .title {
      font-size: 1.11vw;
      color: #1f1f1f;
      @media (max-width: 768px){
        font-size: 3.86vw;
      }
    }
    .sub-title {
      color: #808080;
      margin-top: 0.69vw;
      font-size: 0.97vw;
      @media (max-width: 768px){
        margin-top: 2.415vw;
        font-size: 3.38vw;
      }
    }
    .close {
      position: absolute;
      right: 0;
      top: 0;
      padding: 0.5vw;
      img {
        cursor: pointer;
        width: 0.69vw;
        height: 0.69vw;
      }
    }
  }
}
.button-container {
  margin-top: 2.08vw;
  margin-bottom: 2.08vw;
  @media (max-width: 768px){
    margin-top: 12.07vw;
  }
  .button {
    // width: 13.33vw;
    // height: 3.05vw;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    padding: 0.89vw 4.33vw;
    border-radius: 1.49vw;
    border: solid 0.069vw #004ddd;
    color: #004ddd;
    cursor: pointer;
    text-align: center;
    @media (max-width: 768px){
      font-size: 3.86vw;
      padding: 2.89vw 22.33vw;
      border-radius: 7.49vw;
    }
  }
}
.upload-input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}



}
