import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
import { Subscription } from 'rxjs';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-add-collection',
  templateUrl: './add-collection.component.html',
  styleUrls: ['./add-collection.component.scss'],
})
export class AddCollectionComponent implements OnInit, OnDestroy {
  permissionsObj: any = {};
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  faArrowAltCircleLeft = faArrowAltCircleLeft;
  routerObserver: Subscription;
  activeMenu = 'main';

  ngOnDestroy(): void {
    //this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Exhibitions'
    );
    console.log(this.permissionsObj);
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/collection/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/collection/add/add-on':
        this.activeMenu = 'add-on';
        break;
      case '/artist-portal/settings/collection/add/seo':
        this.activeMenu = 'seo';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }

  // onMenuClick(index) {
  //   console.log(index);
  //   switch (index) {
  //     case 0:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/main']);
  //       break;
  //     case 1:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/add-on']);
  //       break;
  //     default:
  //       break;
  //   }
  // }

  navigateTo(path, type) {
    if (localStorage.getItem('collectionID')) {
      this.router.navigate([path]);
    } else {
      alert('Please create collection first!');
    }
  }
}
