<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="mandatory_elements">
            <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">Publish Collection:</div>
                  <label class="switch">
                    <input type="checkbox" formControlName="publish" />
                    <span class="slider round"></span>
                  </label>
                </div>
                <div class="input-info">
                  Choose whether to publish the Collection on the website.
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <!-- <div style="font-size: 1.25vw">Collection Title</div> -->
                <div>
                  <angular-editor
                    *ngIf="htmlcheckBox[0]"
                    id="editor20"
                    formControlName="title"
                    (keypress)="server.toCheckSpace($event, form.value.title)"
                  ></angular-editor>
                  <textarea
                    *ngIf="!htmlcheckBox[0]"
                    [placeholder]="'Collection Title'"
                    formControlName="title"
                    rows="6"
                    style="padding: 1vw; width: 100%"
                  ></textarea>
                  <div class="placeholder">Collection Title</div>
                  <div class="input-info">
                    Provide the Collection title to be used in the banner.
                    <div
                      style="
                        display: inline-block;
                        text-align: right;
                        width: 100%;
                      "
                    >
                      <input
                        type="checkbox"
                        [(ngModel)]="htmlcheckBox[0]"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      HTML Editor
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="input-container" (focusout)="changeFocus(2)">
                  <input
                    (focusin)="isDropDownOpen[2] = true"
                    type="text"
                    class="selection"
                    placeholder="Header Colour"
                    formControlName="headerColour"
                    [readonly]="true"
                  />
                  <div class="placeholder">Header Colour</div>
                  <button
                    (click)="isDropDownOpen[2] = !isDropDownOpen[2]"
                    type="button"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[2],
                      'dropdown-visible': isDropDownOpen[2]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          isDropDownOpen[2] = false;
                          form.get('headerColour').setValue('Normal')
                        "
                      >
                        <div class="country-name">Normal</div>
                      </li>
                      <li
                        (click)="
                          isDropDownOpen[2] = false;
                          form.get('headerColour').setValue('Inverted')
                        "
                      >
                        <div class="country-name">Inverted</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose whether to use regular logo with black font, or
                  inverted logo with white font (depending on the Banner Media
                  chosen)
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  formControlName="titleSize"
                  placeholder="Text-Size"
                />
                <div class="placeholder">Title Size</div>
                <div class="input-info">
                  Provide the font size for the Collection title on the banner
                  (between 1 and 3).
                </div>
              </div>
              <div class="field-value" style="position: relative">
                <input
                  type="color"
                  formControlName="bannerTextColor"
                  placeholder="HEX code"
                  style="inline-size: 25vw"
                />
                <!-- <div class="forHex"></div> -->
                <div class="placeholder">Banner Text Colour</div>
                <div class="input-info">
                  Provide the HEX code for the banner text colours (eg:
                  #6fa8dc).
                </div>
              </div>
            </div>

            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <!-- <div style="margin-top: 2.08vw; font-size: 1.25vw">
                  Collection Thumbnail
                </div> -->
                <div class="placeholder">Collection Thumbnail</div>
                <div class="flex-block">
                  <image-upload
                    [accept]="'image/*'"
                    [selectedData]="selectedFilesObj?.thumbnailArr"
                    (onFileChange)="onFileSelect($event, 'thumbnailArr')"
                    [fileSize]="12582912"
                    [placeholder]="placeholder"
                    [hideAltText]="true"
                  ></image-upload>

                  <div class="input-info">
                    Upload image or GIF to be used as Collection thumbnail.
                  </div>
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="placeholder">Collection Banner Image</div>
                <div class="flex-block">
                  <image-upload
                    [accept]="'image/*'"
                    [selectedData]="selectedFilesObj?.bannerImageArr"
                    (onFileChange)="onFileSelect($event, 'bannerImageArr')"
                    [fileSize]="12582912"
                    [placeholder]="placeholder"
                    [hideAltText]="true"
                  ></image-upload>

                  <div class="input-info">
                    Upload image or GIF to be used as banner on the website to
                    represent the collection. The image will be centered to
                    aspect ratio 1440x300 px on Desktop pages.
                  </div>
                </div>
              </div>
            </div>

            <div class="title_section">
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <div class="input-container" (focusout)="changeFocus(0)">
                    <input
                      (focusin)="isDropDownOpen[0] = true"
                      type="text"
                      class="selection"
                      placeholder="Title Banner Type"
                      formControlName="bannerType"
                      [readonly]="true"
                    />
                    <div class="placeholder">Title Banner Type</div>
                    <button
                      (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                      type="button"
                    >
                      <img
                        src="assets/icons/arrow-down.png"
                        class="flag-arrow"
                      />
                    </button>
                    <div
                      [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[0],
                        'dropdown-visible': isDropDownOpen[0]
                      }"
                    >
                      <ul>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('Fullscreen Image')
                          "
                        >
                          <div class="country-name">Fullscreen Image</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'video/*';
                            form.get('bannerType').setValue('Video')
                          "
                        >
                          <div class="country-name">Video</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('bannerType').setValue('GIF')
                          "
                        >
                          <div class="country-name">GIF</div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="input-info">Choose the type of Banner to use</div>
                </div>

                <div class="field-value">
                  <div
                    class="image-type"
                    *ngIf="form.value.bannerType !== 'Video'"
                  >
                    <!-- Banner-->
                    <div class="placeholder">Title Banner media</div>
                    <div class="flex-block">
                      <image-upload
                        [accept]="acceptType"
                        [selectedData]="selectedFilesObj?.bannerMediaArr"
                        (onFileChange)="onFileSelect($event, 'bannerMediaArr')"
                        [fileSize]="12582912"
                        [placeholder]="placeholder"
                        [hideAltText]="true"
                      ></image-upload>
                      <div class="input-info">
                        Provide the media to be used in the banner.
                      </div>
                    </div>
                  </div>
                  <div
                    *ngIf="form.value.bannerType === 'Video'"
                    style="padding-right: 0.5vw"
                  >
                    <input
                      type="text"
                      placeholder="Banner Title"
                      formControlName="bannerTitle"
                      (change)="onchangesText()"
                    />
                    <div class="placeholder">Title Banner Video</div>
                    <div class="input-info">
                      Provide the Vimeo player link for the video to be featured
                      as title banner. e.g.:
                      https://www.player.vimeo.com/12342143.
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="form.value.bannerType === 'Video'"
                  class="field-value"
                >
                  <div style="width: 100%">
                    <div style="padding: 56.25% 0 0 0; position: relative">
                      <iframe
                        [src]="videoLinkTextSanitized"
                        style="
                          position: absolute;
                          top: 0;
                          left: 0;
                          width: 100%;
                          height: 100%;
                        "
                        frameborder="0"
                        allow="autoplay; fullscreen; picture-in-picture"
                        allowfullscreen
                      ></iframe>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="form.get('bannerType').value == 'Artwork on wall'">
                <div class="upload-head">Banner Background Image</div>
                <div class="splitter">
                  <div class="field-value flex-block">
                    <image-upload
                      [accept]="'image/*'"
                      [selectedData]="selectedFilesObj?.bannerBackgroundArr"
                      (onFileChange)="
                        onFileSelect($event, 'bannerBackgroundArr')
                      "
                      [fileSize]="12582912"
                      [placeholder]="placeholder"
                      [hideAltText]="true"
                    ></image-upload>
                    <div class="input-info">
                      Upload a wall background image to place the artwork on (a
                      gallery wall image)
                    </div>
                  </div>
                </div>
              </div>

              <!-- Banner Title-->
            </div>

            <!-- <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">Show Above Subtitle :</div>
                  <label class="switch">
                    <input
                      type="checkbox"
                      formControlName="showAboveSubtitle"
                    />
                    <span class="slider round"></span>
                  </label>
                </div>
                <div class="input-info">
                  Choose whether to show description text.
                </div>
              </div>
              <div
                class="field-value"
                *ngIf="form.get('showAboveSubtitle').value"
                style="padding-right: 0.5vw"
              >
                <div class="placeholder">Above Subtitle text</div>
                <div>
                  <angular-editor
                    *ngIf="htmlcheckBox[1]"
                    id="editor21"
                    formControlName="aboveSubText"
                  ></angular-editor>
                  <textarea
                    *ngIf="!htmlcheckBox[1]"
                    formControlName="aboveSubText"
                    rows="6"
                    style="padding: 1vw; width: 100%"
                  ></textarea>
                  <div class="input-info">
                    Provide the subtitle text to use above the title.
                    <div
                      style="
                        display: inline-block;
                        text-align: right;
                        width: 100%;
                      "
                    >
                      <input
                        type="checkbox"
                        [(ngModel)]="htmlcheckBox[1]"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      HTML Editor
                    </div>
                  </div>
                </div>
              </div>
              <div
                *ngIf="form.get('showAboveSubtitle').value"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <input
                  type="number"
                  placeholder="Font-Size"
                  formControlName="aboveSubSize"
                />
                <div class="placeholder">Above Subtitle size</div>
                <div class="input-info">
                  Provide the font size for the subtitle (decimal values between
                  1.0 and 2.0).
                </div>
              </div>
            </div> -->
          </div>
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Subtitle :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showBelowSubtitle" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">Choose whether to show a subtitle.</div>
            </div>
            <div
              class="field-value"
              *ngIf="form.get('showBelowSubtitle').value"
              style="padding-right: 0.5vw"
            >
              <div class="placeholder">Subtitle text</div>
              <div>
                <angular-editor
                  *ngIf="htmlcheckBox[2]"
                  id="editor22"
                  formControlName="belowSubText"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[2]"
                  formControlName="belowSubText"
                  rows="6"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide the subtitle text to use below the title.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[2]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              *ngIf="form.get('showBelowSubtitle').value"
            >
              <input
                type="number"
                placeholder="Font-Size"
                formControlName="belowSubSize"
              />
              <div class="placeholder">Subtitle size</div>
              <div class="input-info">
                Provide the font size for the subtitle (decimal values between
                1.0 and 2.0).
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Show Artist Name :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showArtistName" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to show the artist name on the banner.
              </div>
            </div>
            <div
              class="field-value"
              *ngIf="form.get('showArtistName').value"
              style="padding-right: 0.5vw"
            >
              <div class="placeholder">Artist Name</div>
              <div>
                <angular-editor
                  *ngIf="htmlcheckBox[3]"
                  id="editor23"
                  formControlName="artistName"
                ></angular-editor>
                <textarea
                  *ngIf="!htmlcheckBox[3]"
                  formControlName="artistName"
                  rows="6"
                  style="padding: 1vw; width: 100%"
                ></textarea>
                <div class="input-info">
                  Provide the artist details to be displayed on the banner.
                  <div
                    style="
                      display: inline-block;
                      text-align: right;
                      width: 100%;
                    "
                  >
                    <input
                      type="checkbox"
                      [(ngModel)]="htmlcheckBox[3]"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    HTML Editor
                  </div>
                </div>
              </div>
            </div>
            <div
              *ngIf="form.get('showArtistName').value"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <input
                type="number"
                placeholder="Font-Size"
                formControlName="artistFontSize"
              />
              <div class="placeholder">Artist Name Size</div>
              <div class="input-info">
                Provide the font size for the Artist Name (between 1 and 2).
              </div>
            </div>
          </div>

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Dates:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showDate" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show the collection dates.
            </div>
          </div>
          <div *ngIf="form.get('showDate').value">
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  placeholder="Collection Date"
                  formControlName="dateStart"
                />
                <div class="placeholder">Collection Start Date</div>
                <div class="input-info">Provide the collection start date.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  placeholder="Collection Date"
                  formControlName="dateEnd"
                />
                <div class="placeholder">Collection End Date</div>
                <div class="input-info">Provide the Collection end date.</div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Collection Details"
                  formControlName="dateNote"
                />
                <div class="placeholder">Collection Details</div>
                <div class="input-info">
                  ‘Provide the Collection Dates \ Details to be displayed on the
                  banner media.
                </div>
              </div>
            </div>
            <div class=""></div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
