import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit {
  form: FormGroup;
  isDropDownOpen = Array(10).fill(false);
  htmlcheckBox = Array(10).fill(false);
  selectedFilesObj: any = {
    bannerImageArr: [],
    thumbnailArr: [],
    bannerMediaArr: [],
    bannerBackgroundArr: [],
    bannerImage: '',
    blogThumbnail: '',
  };
  placeholder: any = '';
  acceptType: any = 'image/*';
  permissionsObj: any = {};
  artist_search = '';
  artist_limit = 12;
  artist_select = [];
  selected_artist = [];

  videoLinkTextSanitized: SafeResourceUrl;

  constructor(
    private formBuilder: FormBuilder,
    public server: CollectorService,
    private router: Router,
    private domSanitizer: DomSanitizer
  ) {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
  }

  onchangesText() {
    this.videoLinkTextSanitized =
      this.domSanitizer.bypassSecurityTrustResourceUrl(this.form.getRawValue()?.bannerTitle);
  }

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      publish: new FormControl(false),
      bannerType: new FormControl(''),
      headerColour: new FormControl(''),
      title: new FormControl(''),
      bannerTitle: new FormControl(''),
      titleSize: new FormControl(''),
      bannerTextColor: new FormControl(''),
      showAboveSubtitle: new FormControl(false),
      aboveSubText: new FormControl(''),
      aboveSubSize: new FormControl(''),
      showBelowSubtitle: new FormControl(false),
      belowSubSize: new FormControl(''),
      belowSubText: new FormControl(''),
      showArtistName: new FormControl(false),
      artistName: new FormControl(''),
      artistFontSize: new FormControl(''),
      showDate: new FormControl(false),
      dateStart: new FormControl(''),
      dateEnd: new FormControl(''),
      dateNote: new FormControl(''),
    });
    if (localStorage.getItem('collectionID')) {
      this.getExhibitionById();
    }
  }

  // to get exhibition by id
  getExhibitionById() {
    let url = `collections?id=${localStorage.getItem('collectionID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.videoLinkTextSanitized =
          this.domSanitizer.bypassSecurityTrustResourceUrl(res.data?.data?.titleBannerMedia);
        this.form.patchValue({
          publish: res.data?.publish,
          headerColour: res.data?.data?.headerColour,
          title: res.data?.data?.title,
          bannerTitle: res.data?.data?.titleBannerMedia,
          artistName: res.data?.data?.artistName,
          bannerType: res.data?.data?.bannerType,
          bannerTextColor: res.data?.data?.bannerTextColor,
          titleSize: res.data?.data?.titleSize,
          showAboveSubtitle: res.data?.data?.showAboveSubtitle,
          aboveSubText: res.data?.data?.aboveSubText,
          aboveSubSize: res.data?.data?.aboveSubSize,

          showBelowSubtitle: res.data?.data?.showBelowSubtitle,
          belowSubText: res.data?.data?.belowSubText,
          belowSubSize: res.data?.data?.belowSubSize,

          showArtistName: res.data?.data?.showArtistName,
          artistFontSize: res.data?.data?.artistFontSize,
          showDate: res.data?.data?.showDate,
          dateStart: this.formatDate(res.data?.data?.dateStart),
          dateEnd: this.formatDate(res.data?.data?.dateEnd),
          dateNote: res.data?.data?.dateNote,
        });

        if (res.data?.data?.bannerImage) {
          this.selectedFilesObj.bannerImageArr.push({
            url: res.data?.data?.bannerImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data?.data?.thumbnail) {
          this.selectedFilesObj.thumbnailArr.push({
            url: res.data?.data?.thumbnail,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data?.data?.titleBannerMedia) {
          this.selectedFilesObj.bannerMediaArr.push({
            url: res.data?.data?.titleBannerMedia,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data?.data?.titleBannerBg) {
          this.selectedFilesObj.bannerBackgroundArr.push({
            url: res.data?.data?.titleBannerBg,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
      }
    });
  }

  // to format date
  formatDate(date) {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();
    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    return [year, month, day].join('-');
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }
  getSelectedItem(a, i) {
    this.selected_artist = a;
  }

  onMultiAdd(files, key) {
    this.selectedFilesObj[key] = files;
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    }
  }

  onSubmit() {
    // if (!this.permissionsObj.update) {
    //   alert('Update permissions denied!');
    //   return;
    // }
    let req = {
      publish: this.form.value.publish,
      title: this.form.value.title,
      data: this.form.value,
    };
    req['data']['thumbnail'] =
      this.selectedFilesObj.thumbnailArr.length > 0
        ? this.selectedFilesObj.thumbnailArr[0].url
        : '';
    req['data']['bannerImage'] =
      this.selectedFilesObj.bannerImageArr.length > 0
        ? this.selectedFilesObj.bannerImageArr[0].url
        : '';
    req['data']['titleBannerMedia'] =
      this.form.value.bannerType === 'Video'
        ? this.form.value.bannerTitle
        : this.selectedFilesObj.bannerMediaArr.length > 0
          ? this.selectedFilesObj.bannerMediaArr[0].url
          : '';
    req['data']['titleBannerBg'] =
      this.selectedFilesObj.bannerBackgroundArr.length > 0
        ? this.selectedFilesObj.bannerBackgroundArr[0].url
        : '';
    if (localStorage.getItem('collectionID')) {
      req['_id'] = localStorage.getItem('collectionID');
    }

    this.server.postApi(`collections/add`, req).subscribe((res) => {
      if (res.statusCode == 200) {
        //localStorage.setItem('collectionID', res.data['_id']);
        alert(res.message);
      }
    });
  }

  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 500);
  }
}
