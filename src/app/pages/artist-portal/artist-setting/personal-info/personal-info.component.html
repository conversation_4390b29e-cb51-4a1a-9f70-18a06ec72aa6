<div class="container__main">
    <p class="main__heading">Personal information</p>

    <p class="sub__text">
        Get in touch with us at <a href="#"> <EMAIL></a> to update your artist profile information
    </p>
    <div class="content-section">
        <div class="content">
            <div class="wrapper">
                <p class="label">Name</p>
                <p class="element">
                    {{ userDetailsObj?.first_name }} {{ userDetailsObj?.last_name }}
                </p>
            </div>
            <!-- <div class="wrapper">
        <p class="label">Gender</p>
        <p class="element">{{ userDetailsObj?.gender }}</p>
      </div> -->
            <!-- <div class="wrapper">
        <p class="label">Date of Birth</p>
        <p class="element">
          {{ userDetailsObj?.dateOfBirth | date: "dd/MM/yy" }}
        </p>
      </div> -->
            <div class="wrapper">
                <p class="label">Email</p>
                <p class="element">{{ userDetailsObj?.email }}</p>
            </div>
            <div class="wrapper">
                <p class="label">Country of Origin</p>
                <p class="element">
                    <span>{{ flag }}</span>
                    <span class="divider"></span>
                    <span>{{ userDetailsObj?.country }}</span>
                </p>
            </div>
        </div>
        <a routerLink="/artist-portal/settings/edit-personal"><img src="../../../../../assets/icons/edit.png" alt="" /></a>
    </div>
    <div class="division"></div>
    <div class="content-section">
        <div class="content">
            <div class="wrapper">
                <p class="label">Bio</p>
                <p *ngIf="!userDetailsObj.display_name" class="element">
                    Please update Bio
                </p>
                <p *ngIf="userDetailsObj.display_name" class="element">
                    {{ userDetailsObj.display_name }}
                </p>
            </div>
        </div>
        <a (click)="navigateTo()" href="javascript:;"><img src="../../../../../assets/icons/edit.png" alt="" /></a>
    </div>
    <!-- <div class="division"></div>
  <div class="content-section">
    <div class="content">
      <div class="wrapper">
        <p class="label">Phone number</p>
        <p class="element">
          <span>{{ flag }}</span>
          <span class="divider"></span>
          {{ userDetailsObj.phone }}
        </p>
      </div>
    </div>
    <a routerLink="/artist-portal/settings/edit-phone">
      <img src="../../../../../assets/icons/edit.png" alt=""
    /></a>
  </div> -->
    <div class="division"></div>
    <div class="content-section">
        <div class="content">
            <div class="wrapper">
                <p class="label">Ethereum Address</p>
                <p *ngIf="!userDetailsObj.ethereum_account" class="element">
                    Not linked
                </p>
                <p *ngIf="userDetailsObj.ethereum_account" class="element">
                    {{ userDetailsObj.ethereum_account }}
                </p>
            </div>
        </div>
        <a routerLink="/artist-portal/settings/create-wallet">
            <img src="../../../../../assets/icons/edit.png" alt="" /></a>
    </div>
    <div class="division"></div>
    <div class="content-section">
        <div class="content">
            <div class="wrapper">
                <p class="label">Password</p>
                <p class="element">••••••••</p>
                <p class="last-changed">
                    Last changed on {{ userDetailsObj.updatedAt | date: "dd/MM/yy" }}
                </p>
            </div>
        </div>
        <a routerLink="/artist-portal/settings/change-password">
            <img src="../../../../../assets/icons/edit.png" alt="" />
        </a>
    </div>
</div>