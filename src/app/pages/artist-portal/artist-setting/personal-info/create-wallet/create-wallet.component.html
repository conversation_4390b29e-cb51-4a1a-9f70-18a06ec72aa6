<div>
  <div class="d-flex">
    <div class="section left-container" style="width: 100%; min-height: 90vh">
      <div class="section-inner">
        <div class="section">
          <div class="heading">
            <a routerLink="/artist-portal/settings"
              ><img src="../../../../../../assets/icons/<EMAIL>" alt=""
            /></a>
            <!-- <p class="main__heading">Link Wallet</p> -->
          </div>
          <div class="section-inner">
            <div class="d-flex justify-content-center stepper-wrapper">
              <div
                class="collector-form"
                [ngClass]="{ 'section-margin-horizontal': isMobile }"
              >
                <div class="header">Generate your Ethereum Wallet :</div>
                <div style="padding-bottom: 8vh">
                  <div class="sub-header" style="margin-bottom: 1.2vw">
                    Your Ethereum Wallet allows you to purchase and own NFTs.
                    You can choose to link your existing Ethereum Wallet using
                    Metamask (https://metamask.io/), or create a new Ethereum
                    account using Metamask. <br /><br /><br />
                    Note: Terrain.art does not store your wallet Private Key for
                    security reasons. If you lose or misplace this key, there is
                    no option to retrieve access to the wallet and your digital
                    assets. Please ensure that you write down or print your
                    keys, and store them securely, along with downloading the
                    key details. Please ensure that this information does not
                    fall into the hands of others.
                  </div>
                  <div class="Wallet-Button-Group">
                    <button
                      class="Wallet-Button"
                      [ngClass]="{ 'disabled-wallet-button': walletVerified }"
                      [disabled]="walletVerified"
                      (click)="generateNewWallet()"
                    >
                      Create New Wallet
                    </button>
                    <button
                      class="Wallet-Button"
                      [ngClass]="{ 'disabled-wallet-button': walletVerified }"
                      [disabled]="walletVerified"
                      (click)="whitellistPopup = true"
                    >
                      Whitelist your Wallet
                    </button>
                  </div>
                  <ng-container *ngIf="walletVerified">
                    <div class="Address-section">
                      <div class="Address-part">
                        <span>{{ ukey }}</span>
                        <fa-icon
                          (click)="
                            server.copyMessage(userDetailsObj.ethereum_account)
                          "
                          style="cursor: pointer"
                          title="Copy Address"
                          [icon]="faCopy"
                        ></fa-icon>
                        <div class="placeholder">Public Key</div>
                      </div>
                    </div>
                  </ng-container>

                  <div
                    class="d-flex justify-content-around align-items-center w-100 wallet-options"
                    style="display: none !important"
                  >
                    <div (click)="!isMetamaskAvailable && (selectedOption = 0)">
                      <img
                        class="field-checkbox"
                        [src]="
                          selectedOption == 0
                            ? 'assets/images/radio.png'
                            : 'assets/images/oval.png'
                        "
                      /><span
                        [ngStyle]="{
                          'text-decoration': isMetamaskAvailable
                            ? 'line-through'
                            : ''
                        }"
                        (click)="generate()"
                        >Create new Wallet</span
                      >
                    </div>
                    <div (click)="selectedOption = 1">
                      <img
                        class="field-checkbox"
                        [src]="
                          selectedOption == 1
                            ? 'assets/images/radio.png'
                            : 'assets/images/oval.png'
                        "
                      />Link existing Wallet
                    </div>
                  </div>
                  <div hidden style="width: 40%; display: block">
                    <div class="w-100 field-contents">
                      <div class="flex-block">
                        <div
                          class="field-value"
                          *ngIf="userDetailsObj?.ethereum_nounce"
                        >
                          <input
                            [(ngModel)]="pkey"
                            type="text"
                            placeholder="Private Key"
                          />
                          <div class="placeholder">Private Key</div>
                        </div>
                      </div>
                      <div class="flex-block" style="margin-top: 1vw">
                        <div
                          class="field-value"
                          *ngIf="userDetailsObj?.ethereum_account"
                        >
                          <input
                            [(ngModel)]="ukey"
                            type="text"
                            placeholder="Address"
                          />
                          <div class="placeholder">Address</div>
                        </div>
                      </div>

                      <div class="flex-block buttonList">
                        <button
                          (click)="onboardingMeta()"
                          style="margin-right: 1.1vw"
                          [ngClass]="{ save: !ukey, disable: ukey }"
                        >
                          Install metamask
                        </button>
                        <!-- <button
                          (click)="ukey && download()"
                          [ngClass]="{ save: ukey, disable: !ukey }"
                        >
                          Download
                        </button> -->
                      </div>
                    </div>
                  </div>
                  <div
                    hidden
                    *ngIf="selectedOption == 0"
                    class="w-100"
                    style="margin-top: 2.2vw"
                  >
                    <div
                      class="sub-header"
                      style="margin-bottom: 1.2vw; display: block"
                    >
                      Follow the instructions on the MetaMask page after
                      installing the extension, to create the wallet and
                      securely download the Seed Phrase information.
                      <br /><br />
                      After creating the wallet on MetaMask, return to the
                      Terrain page and click on Link Wallet to continue linking
                      the new wallet you’ve created. Choose MetaMask, and
                      approve when prompted.
                    </div>

                    <!-- <div style="width: 40%">
                      <div class="verify">Transaction Pin</div>

                      <input
                        #input1
                        [(ngModel)]="otp1"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(1)"
                      />
                      <input
                        #input2
                        [(ngModel)]="otp2"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(2)"
                      />
                      <input
                        #input3
                        [(ngModel)]="otp3"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(3)"
                      />
                      <input
                        #input4
                        [(ngModel)]="otp4"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(4)"
                      />
                      <input
                        #input5
                        [(ngModel)]="otp5"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(5)"
                      />
                      <input
                        #input6
                        [(ngModel)]="otp6"
                        class="partitioned"
                        type="text"
                        [maxLength]="1"
                        (ngModelChange)="keyUpEvent(6)"
                      />
                    </div> -->
                  </div>
                  <div
                    hidden
                    *ngIf="selectedOption == 1"
                    class="d-flex justify-content-between align-items-center w-100"
                    style="margin-top: 2.2vw"
                  >
                    <div style="width: 40%">
                      <div class="flex-block buttonList">
                        <button
                          (click)="!islogged && loginWithMeta()"
                          class="save"
                        >
                          {{ islogged ? "Logged in" : "Login" }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="footer-nav" style="padding: 6vw">
    <div class="button-group">
      <div (click)="save()" class="next">Save</div>
    </div>
  </div> -->
</div>

<ngx-qrcode
  id="qrcode"
  [elementType]="'url'"
  [value]="ukey"
  cssClass="aclass"
  errorCorrectionLevel="L"
  [ngStyle]="{ display: 'none' }"
>
</ngx-qrcode>
<ngx-qrcode
  id="qrcode2"
  [elementType]="'url'"
  [value]="pkey"
  cssClass="aclass"
  errorCorrectionLevel="L"
  [ngStyle]="{ display: 'none' }"
>
</ngx-qrcode>

<ng-container *ngIf="whitellistPopup">
  <div class="popup-whitelist">
    <div class="whitelist-card field-contents">
      <div class="whitelist-card-header">
        <div>Provide Public key</div>
        <fa-icon
          (click)="whitellistPopup = false"
          [icon]="faTimes"
          style="cursor: pointer"
        ></fa-icon>
      </div>
      <div class="field-value">
        <input [(ngModel)]="pkey" type="text" placeholder="Private Key" />
        <div class="placeholder">Public Key</div>
      </div>
      <div class="buttonList" style="margin-top: 4.47vh">
        <button
          class="save"
          style="min-width: 14.56vw"
          (click)="whitelistAdd()"
        >
          Whitelist and Save
        </button>
      </div>
    </div>
  </div>
</ng-container>
