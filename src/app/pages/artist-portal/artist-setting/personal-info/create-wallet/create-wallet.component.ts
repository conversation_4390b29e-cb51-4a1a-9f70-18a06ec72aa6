import { Web3Service } from 'src/app/shared/services/web3.service';
import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';
import * as CryptoJS from 'crypto-js';
import jspdf from 'jspdf';
import html2canvas from 'html2canvas';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { ArtistService } from '../../../services/artist.service';
import { apiUrl, walletDetails } from 'src/environments/environment.prod';
import MetaMaskOnboarding from '@metamask/onboarding';
import { CollectorService } from 'src/app/services/collector.service';
import { faCopy, faTimes } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'create-wallet',
  templateUrl: './create-wallet.component.html',
  styleUrls: ['./create-wallet.component.scss'],
})
export class CreateWalletComponent implements OnInit, OnDestroy {
  faTimes=faTimes;
  faCopy=faCopy;
  whitellistPopup=false;
  otp1 = '';
  otp2 = '';
  otp3 = '';
  otp4 = '';
  otp5 = '';
  otp6 = '';
  @ViewChild('input1') otp1El: ElementRef;
  @ViewChild('input2') otp2El: ElementRef;
  @ViewChild('input3') otp3El: ElementRef;
  @ViewChild('input4') otp4El: ElementRef;
  @ViewChild('input5') otp5El: ElementRef;
  @ViewChild('input6') otp6El: ElementRef;
walletVerified=false;
  pkey:any = '';
  ukey:any= '';
  islogged = false;
  sign;
  selectedOption = -1;
  isMobile;
  userDetailsObj: any = {};

  onboarding;
  isMetamaskAvailable = false;

  constructor(
    private footerService: FooterService,
    private route: Router,
    private web3Service: Web3Service,
    private artistInfoService: ArtistInfoService,
    public server: CollectorService,
    private router: Router
  ) {
    this.onboarding = new MetaMaskOnboarding();
    this.selectedOption = 1;
  }
  ngOnDestroy(): void {
    // this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  generateNewWallet(){
    this.walletVerified=true;
  }
  whitelistAdd(){
     this.walletVerified=true; alert('Address Whitelisted !');
     this.whitellistPopup=false;
    
  }
  ngOnInit(): void {
    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    if (localStorage.getItem('userDetails')) {
      this.userDetailsObj = JSON.parse(decode);
      if(this.userDetailsObj.ethereum_account){
        this.ukey = this.userDetailsObj.ethereum_account
      }
      if(this.userDetailsObj.ethereum_nounce){
        this.pkey = this.userDetailsObj.ethereum_nounce
      }
    }
    this.isMetamaskAvailable = MetaMaskOnboarding.isMetaMaskInstalled();
    if (this.isMetamaskAvailable) {
      this.selectedOption = 1;
    } else {
      this.selectedOption = 0;
    }
    // this.isMobile = this.getIsMobile();
    // window.onresize = () => {
    // 	this.isMobile = this.getIsMobile();
    // };
  }
  public getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  generate() {
    if(this.userDetailsObj['ethereum_account']){
      alert('Address already  exists!')
      return;
    }
    let url = walletDetails.generateETHAddress;
    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    let req = {
      password: JSON.parse(decode)['_id'],
    };
    console.log(req);
    this.server.postThirdPartyApi(url, req).subscribe((res) => {
      if (res.code == 200) {
        this.ukey = res.Result.address;
        this.pkey = res.Result.privateKey;
        this.walletVerified=true;
        alert('Wallet address generated successfully!');
        this.updateProfile();
      }
    });
    // this.web3Service.generateAccount().then((data) => {
    // 	this.ukey = data.address;
    // 	this.pkey = data.privateKey;
    // });
  }

  // to update profile
  updateProfile() {
    this.userDetailsObj['ethereum_account'] = this.ukey;
    this.userDetailsObj['ethereum_nounce'] = this.pkey;
    let url = apiUrl.signup;
    this.server.showSpinner()
    this.server.putApi(url, this.userDetailsObj).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode) {
        this.server.getUserDetails()
        alert('Wallet address saved!');
      }
    });
  }

  async loginWithMeta() {
    // this.web3Service.signMeta().then((data) => {
    //   console.log(data);

    //   this.sign = data;
    //   this.islogged = true;
    // });
    this.web3Service.accountStatus$.subscribe((next) => {
      const address = next[0];
      if (address) {
        this.userDetailsObj['ethereum_account'] = address;
        let url = apiUrl.signup;
        this.server.showSpinner()
        this.server.putApi(url, this.userDetailsObj).subscribe((res) => {
          this.server.hideSpinner()
          if (res.statusCode) {
            localStorage.setItem(
              'userDetails',
              JSON.stringify(this.userDetailsObj)
            );
            if (confirm('Wallet address saved!') == true) {
              this.router.navigate(['artist-portal/settings']);
            } else {
            }
          }
        });
      }
    });
    this.web3Service.connectAccount();
  }

  getBase64Image(img) {
    var canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    var ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0);
    var dataURL = canvas.toDataURL('image/png');
    return dataURL;
  }

  public download() {
    const qrcode = document.getElementById('qrcode');
    const qrcode2 = document.getElementById('qrcode2');
    var doc = new jspdf('p', 'pt');

    doc.setFont('helvetica', 'bold');
    doc.text('Public Keys', 20, 50);
    doc.setFont('courier', 'normal');
    doc.text(this.ukey, 20, 90);
    console.log(qrcode);

    let imageData = this.getBase64Image(qrcode.firstChild.firstChild);
    doc.addImage(imageData, 'JPG', 10, 120, 300, 300);

    doc.setFont('helvetica', 'bold');
    doc.text('Private Keys', 20, 450);
    doc.setFont('courier', 'normal');
    doc.setFontSize(12);
    doc.text(this.pkey, 20, 490);
    console.log(qrcode);

    let imageData2 = this.getBase64Image(qrcode2.firstChild.firstChild);
    doc.addImage(imageData2, 'JPG', 10, 520, 300, 300);

    doc.save('keys.pdf');
  }
  keyUpEvent(index) {
    switch (index) {
      case 1:
        if (this.otp1) this.otp2El.nativeElement.focus();
        else this.otp1El.nativeElement.focus();
        break;
      case 2:
        if (this.otp2) this.otp3El.nativeElement.focus();
        else this.otp2El.nativeElement.focus();
        break;
      case 3:
        if (this.otp3) this.otp4El.nativeElement.focus();
        else this.otp3El.nativeElement.focus();
        break;
      case 4:
        if (this.otp4) this.otp5El.nativeElement.focus();
        else this.otp4El.nativeElement.focus();
        break;
      case 5:
        if (this.otp5) this.otp6El.nativeElement.focus();
        else this.otp5El.nativeElement.focus();
        break;
      default:
        break;
    }
  }
  save() {
    if ((this.ukey && this.otp1 && this.otp6) || this.sign) {
      const type = this.selectedOption == 1 ? 'metamask' : 'private';
      let mData;

      if (this.selectedOption == 1) {
        mData = this.sign;
      } else {
        const hash = CryptoJS.AES.encrypt(
          this.pkey,
          this.otp1 + this.otp2 + this.otp3 + this.otp4 + this.otp5 + this.otp6
        ).toString();
        mData = JSON.stringify({
          hash,
          address: this.ukey,
        });
      }

      this.artistInfoService.addEtherem(type, mData).subscribe((data) => {
        console.log('dfg');

        this.route.navigate(['/artist-portal/settings']);
      });
    }
  }
  onboardingMeta() {
    this.onboarding.startOnboarding();
  }
}
