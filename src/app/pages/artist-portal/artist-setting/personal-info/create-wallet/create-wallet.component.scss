.footer-nav {
  height: 6.3vw;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0);

  @media (max-width: 768px) {
    padding-bottom: 11.24vw;
  }

  .button-group {
    //margin-left: 21.66vw;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 1.25vw;

    @media (max-width: 768px) {
      font-size: 4.34vw;
      margin-left: 0vw;
    }

    .next {
      width: 13.33vw;
      height: 3.05vw;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 1.49vw;
      border: solid 0.069vw #004ddd;
      color: #004ddd;

      @media (max-width: 768px) {
        width: 46.37vw;
        height: 10.62vw;
        border-radius: 6.08vw;
      }
    }

    .back {
      margin-left: 2.77vw;
      color: #808080;
    }
  }
}

.slider-header {
  h3 {
    cursor: pointer;
    font-size: 1.25vw;
    padding-bottom: 0.34vw;
    margin-right: 2.77vw;
    color: var(--quaternary-font-color);

    &.active {
      border-bottom: 2px solid var(--tertiary-font-color);
      color: var(--primary-font-color);
    }
  }

  margin-bottom: 3.47vw;
}

a:hover {
  text-decoration: none;
}

.collector-form {
  width: 64.44vw;

  .step-count {
    color: var(--tertiary-font-color);
    margin-bottom: 0.34vw;
    font-weight: 600;
  }

  .header {
    font-size: 2.5vw;
    //font-weight: 600;
    margin-bottom: 1.5vw;
    width: 40vw;
  }

  .sub-header {
    color: #676767;
  }

  .search-input {
    margin-top: 4.16vw;
    position: relative;

    input[type="text"] {
      width: 100%;
      padding: 0.34vw 0;

      border: none;
      border-bottom: 0.138vw solid black;

      &:focus {
        outline: none;
      }
    }

    img {
      position: absolute;
      right: 0;
      top: 50%;
      height: 0.97vw;
      width: 0.97vw;
      transform: translate(0, -50%);
    }
  }

  .interests {
    margin-top: 6.59vw;
    overflow-y: auto;
    height: calc(100vh - 25.471vw);
    padding-bottom: 8vw;

    // padding-right: 2vw;
    .hr-line {
      width: 100%;
      height: 0.069vw;
      background-color: #bfbfbf;
      border: solid 0.013vw #bfbfbf;
    }

    .content {
      cursor: pointer;
      margin-top: 2.04vw;
      margin-bottom: 2.04vw;
      font-size: 1.66vw;

      .selected {
        font-weight: 600;
        color: var(--tertiary-font-color);
      }
    }

    .checkbox {
      width: 2.43vw;
      height: 2.43vw;
    }

    .artist {
      width: 15.06vw;
      margin-top: 2.77vw;

      img {
        width: 15.06vw;
        height: 10.83vw;
        min-height: 10.83vw;
        -o-object-fit: cover;
        object-fit: cover;
        border-radius: 0.41vw;
        cursor: pointer;
      }

      .name {
        margin-top: 1.04vw;
      }

      &.active {
        color: var(--tertiary-font-color);

        img {
          border: solid 0.013vw var(--tertiary-font-color);
        }
      }
    }
  }
}

.stepper-wrapper {
  //width: 100vw;
  //height: calc(100vh - 12.77vw);
  position: relative;

  // overflow: hidden;
  .stepper-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100vw;
    height: 6.38vw;
    background-color: rgba(255, 255, 255, 0.8);

    .stepper-load {
      height: 0.416vw;
      background-color: #d8d8d8;

      .active {
        background-color: #004ddd;
        width: 33.33vw;
      }
    }

    .button {
      cursor: pointer;
      width: 11.25vw;
      height: 3.54vw;
      padding: 1.11vw 4.37vw;
      background-color: #004ddd;
      color: #ffffff;
      margin-right: 2.77vw;
      border-radius: 0.138vw;
    }

    .skip {
      background-color: #fff;
      cursor: pointer;
      width: 11.25vw;
      height: 3.54vw;
      padding: 1.11vw 4.37vw;
      margin-right: 2.77vw;
    }
  }
}

@media (max-width: 768px) {
  .section.section-margin-top {
    margin-top: 9.66vw;
  }

  .section-margin-horizontal {
    margin-left: 5.797vw;
    margin-right: 6.04vw;
  }

  .collector-form {
    width: 100%;

    .step-count {
      font-size: 3.87vw;
      margin-bottom: 1.93vw;
    }

    .header {
      font-size: 5.8vw;
      margin-bottom: 1.21vw;
      width: 100%;
    }

    .sub-header {
      font-size: 3.87vw;
    }
  }

  .stepper-wrapper {
    //width: 100%;
    height: calc(100vh - 27.35vw);

    .stepper-nav {
      height: 22.95vw;

      .stepper-load {
        height: 1.45vw;

        .active {
          width: 33.33vw;
        }
      }

      .button {
        font-size: 3.87vw;
        width: 28.02vw;
        height: 9.66vw;
        padding: 2.42vw 9.66vw;
        margin-right: 6.04vw;
        border-radius: 0.484vw;
      }

      .skip {
        font-size: 3.87vw;
        width: 28.02vw;
        height: 9.66vw;
        padding: 2.42vw 7.25vw;
        margin-right: 0;
      }
    }
  }
}

.flex-block {
  display: flex !important;
}

.field-contents {
  .field-value {
    position: relative;

    input[type="text"] {
      font-family: var(--secondary-font);
      border: none;
      border: 0.069vw solid var(--timeline-color);
      border-radius: 0.14vw;
      padding: 1.04vw 1.11vw;
      min-width: 20.56vw;
      height: 3.47vw;
    }

    .placeholder {
      position: absolute;
      top: -0.4vw;
      left: 1.04vw;
      font-size: 0.69vw;
      color: var(--quaternary-font-color);
      padding: 0 0.3vw;
      background-color: var(--primary-background-color);
    }
  }

  .field-value:nth-child(2) {
    margin-left: 1.04vw;
    margin-top: 0;
  }
}

.buttonList {
  margin-top: 3.47vw;

  .save {
    outline: none;
    font-size: 1.25vw;
    min-width: 20.56vw;
    background-color: var(--primary-background-color);
    color: var(--tertiary-font-color);
    padding: 0.83vw 1.04vw;
    border: 0.069vw solid var(--tertiary-font-color);
    border-radius: 1.46vw;
  }


  .disable {
    outline: none;
    font-size: 1.25vw;
    min-width: 20.56vw;
    color: var(--quaternary-font-color);
    padding: 0.83vw 1.04vw;
    border: 0.069vw solid var(--quaternary-font-color);
    border-radius: 1.46vw;
  }

  .save:hover {
    font-weight: 600;
  }

  .cancel {
    outline: none;
    font-size: 1.25vw;
    background-color: var(--primary-background-color);
    color: var(--quaternary-font-color);
    padding: 0.83vw 2.78vw;
    border: none;
  }

  .cancel:hover {
    font-weight: 600;
    // color: var(--tertiary-font-color);
  }
}
.disabled-wallet-button{
  color: var(--quaternary-font-color) !important;
  // padding: 0.83vw 1.04vw;
  background-color: transparent !important;
  border: 0.069vw solid var(--quaternary-font-color) !important;
}
.verify {
  margin-top: 2.78vw;
  font-family: var(--secondary-font);
  font-size: 1.25vw;
}

.partitioned {
  margin-top: 1.33vw;
  outline: none;
  padding-left: 0.8vw;
  letter-spacing: 0;
  border: 0;
  background-image: linear-gradient(to left,
      var(--timeline-color) 70%,
      rgba(255, 255, 255, 0) 0%);
  background-position: bottom;
  background-size: 3.2vw 0.069vw;
  width: 3.2vw;
  background-repeat: repeat-x;
  background-position-x: 2.2vw;
  height: 2vw;
  padding-bottom: 0.35vw;
  font-family: var(--secondary-font);
}

.wallet-options {
  font-size: 1.3vw;

  &>div {
    font-weight: 500;
    cursor: pointer;

    &:hover {
      color: var(--tertiary-font-color);
    }
  }
}

.field-checkbox {
  margin-right: 2vw;
  width: 1.5vw;
  height: 1.5vw;
}

.heading {
  display: inline-flex;
  margin-bottom: 1.392vw;

  img {
    width: 2.291vw;
    height: 2.291vw;
    margin-right: 1.042vw;
  }

  .main__heading {
    flex-grow: 1;
    font-size: 1.666vw;
    margin-bottom: 1.042vw;

    @media (max-width: 768px) {
      font-size: 5.79vw;
      margin-bottom: 10.14vw;
    }

    // font-weight: 600;
  }
}

.Wallet-Button-Group {
  margin-top: 4vw;
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 3.5vw;
  align-items: center;

  .Wallet-Button {
    background-color: var(--tertiary-font-color);
    color: white;
    border: 0.069444vw solid white;
    border-radius: 5vw;
    padding: .8vw 1.5vw;
    font-size: 1.25;
  }
  .Wallet-Button:hover{
     background-color: white;
    color:var(--tertiary-font-color);
    border: 0.069444vw solid var(--tertiary-font-color);
  }
}

.Address-section {
  width: 100%;
  display: flex;
  justify-content: center;

  .Address-part {
    margin: 1vw;
    margin-top: 3vw;
    border: 0.069444vw solid gray;
    padding: 1vw;
    display: flex;
    gap: 2vw;
    position: relative;

    .placeholder {
      position: absolute;
      top: -0.4vw;
      left: 1.04vw;
      font-size: 0.69vw;
      color: var(--quaternary-font-color);
      padding: 0 0.3vw;
      background-color: var(--primary-background-color);
    }
  }
}
.popup-whitelist{
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: white;
  // margin: auto;
  z-index: 1; /* Sit on top */
  left: 0;
  top: 0;
  
  .whitelist-card{
    margin: 15% auto;
    width: 32vw;
    height: 30vh;
    padding: 1vw;
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    display: flex;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    .whitelist-card-header{
      display: flex;
      width: 100%;
      justify-content: space-between;
      margin-bottom: 5vh;
      font-size: 1.25vw;
    }
  }
}