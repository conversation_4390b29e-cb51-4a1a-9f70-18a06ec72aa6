<div class="container__main">
  <div class="profile-field">
    <div class="w-100 field-contents">
      <form [formGroup]="form" autocomplete="off">
        <div class="info-123">
          We will use this information for all Tax purposes.
        </div>

        <div class="field-value">
          <input type="text" formControlName="GSTNo" placeholder="GST No" />
          <div class="placeholder">GST No. (if applicable)</div>
          <div class="input-info">Provide the GST number if available.</div>
        </div>

        <div class="field-value">
          <input type="text" formControlName="PANNo" placeholder="PAN No" />
          <div class="placeholder">PAN No. (if applicable)</div>
          <div class="input-info">Provide the PAN number if available.</div>
        </div>
        <div class="field-value">
          <textarea
            formControlName="otherTaxInfo"
            id="otherTaxInfo"
            cols="30"
            rows="10"
          ></textarea>
          <div class="placeholder">Any other Tax information</div>
          <div class="input-info">
            Provide any other relevant information for tax declarations.
          </div>
        </div>

        <div class="field-value flex-block">
          <div class="input-container">
            <image-upload
              style="width: 100%"
              [accept]="'all'"
              [selectedData]="selectedFilesObj.docArr"
              [fileSize]="8388608"
              [placeholder]="''"
              (onFileChange)="onFileSelect($event, 'docArr')"
            ></image-upload>
          </div>
          <div class="placeholder">Upload File</div>
        </div>

        <!-- <div class="buttonList">
            <button (click)="updateProfile()" class="save">Save details</button>
            <a routerLink="/artist-portal/settings"
              ><button class="cancel">Cancel</button></a
            >
          </div> -->
      </form>
    </div>
  </div>
</div>
<div class="footer-nav">
  <div class="button-group">
    <!-- <div (click)="isPopupOpen = false"  class="next">Cancel</div> -->
    <div (click)="updateProfile()" class="next">Save</div>
    <a routerLink="/artist-portal/settings"
      ><button class="next">Cancel</button></a
    >
  </div>
</div>
