import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-edit-tax',
  templateUrl: './edit-tax.component.html',
  styleUrls: ['./edit-tax.component.scss'],
})
export class EditTaxComponent implements OnInit {
  form: FormGroup;
  userDetailsObj: any = {};
  selectedFilesObj: any = {
    docArr: [],
  };
  constructor(
    private server: CollectorService,
    private router: Router,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getUserDetails();
  }

  // to initialise form
  initForm() {
    this.form = this.formBuilder.group({
      GSTNo: new FormControl(''),
      PANNo: new FormControl('', [Validators.required]),
      otherTaxInfo: new FormControl(''),
    });
  }

  // to get user details
  getUserDetails() {
    let url = apiUrl.getUserDetails;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.userDetailsObj = res.data || res.Data;
        if (this.userDetailsObj['role'] == 'SUPERADMIN') {
          this.userDetailsObj['role_id']['permissions'] = apiUrl.modules;
        }
        let stringify = JSON.stringify(this.userDetailsObj);
        let encode = btoa(unescape(encodeURIComponent(stringify)));
        localStorage.setItem('userDetails', encode);
        this.patchFormValues();
      }
    });
  }

  // to patch values
  patchFormValues() {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    if (localStorage.getItem('userDetails')) {
      this.userDetailsObj = JSON.parse(decode);
      this.form.patchValue({
        GSTNo: this.userDetailsObj.GSTNo,
        PANNo: this.userDetailsObj.PANNo,
        otherTaxInfo: this.userDetailsObj.otherTaxInfo,
      });
      if (this.userDetailsObj.taxFile) {
        this.selectedFilesObj.docArr.push({
          url: this.userDetailsObj.taxFile,
          size: '',
          name: 'File',
          type: 'image',
          file_event: '',
          filedata: '',
        });
      }
    }
  }

  // to update profile
  updateProfile() {
    console.log(this.form.value);
    if (this.form.invalid) {
      alert('Form invalid !');
      return;
    }
    let req = {
      GSTNo: this.form.value.GSTNo,
      PANNo: this.form.value.PANNo,
      otherTaxInfo: this.form.value.otherTaxInfo,
      taxFile:
        this.selectedFilesObj.docArr.length > 0
          ? this.selectedFilesObj.docArr[0].url
          : '',
    };
    let url = apiUrl.signup;
    this.server.showSpinner();
    this.server.putApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Profile updated successfully!');
        //this.router.navigate(['/artist-portal/settings']);
      }
    });
  }
  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }
  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }
}
