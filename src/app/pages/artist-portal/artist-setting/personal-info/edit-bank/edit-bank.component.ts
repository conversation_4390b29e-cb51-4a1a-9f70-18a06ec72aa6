import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-edit-bank',
  templateUrl: './edit-bank.component.html',
  styleUrls: ['./edit-bank.component.scss'],
})
export class EditBankComponent implements OnInit {
  form: FormGroup;
  userDetailsObj: any = {};
  constructor(
    private server: CollectorService,
    private router: Router,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.getUserDetails();
  }

  // to initialise form
  initForm() {
    this.form = this.formBuilder.group({
      accountHolderName: new FormControl('', [Validators.required]),
      bankName: new FormControl('', [Validators.required]),
      bankBranchDetails: new FormControl('', [Validators.required]),
      accountNumber: new FormControl('', [Validators.required]),
      IFSCcode: new FormControl('', [Validators.required]),
      releventInfo: new FormControl(''),
    });
  }

  // to get user details
  getUserDetails() {
    let url = apiUrl.getUserDetails;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.userDetailsObj = res.data || res.Data;
        if (this.userDetailsObj['role'] == 'SUPERADMIN') {
          this.userDetailsObj['role_id']['permissions'] = apiUrl.modules;
        }
        let stringify = JSON.stringify(this.userDetailsObj);
        let encode = btoa(unescape(encodeURIComponent(stringify)));
        localStorage.setItem('userDetails', encode);
        this.patchFormValues();
      }
    });
  }

  // to patch values
  patchFormValues() {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    if (localStorage.getItem('userDetails')) {
      this.userDetailsObj = JSON.parse(decode);
      this.form.patchValue({
        accountHolderName: this.userDetailsObj.accountHolderName,
        bankName: this.userDetailsObj.bankName,
        bankBranchDetails: this.userDetailsObj.bankBranchDetails,
        accountNumber: this.userDetailsObj.accountNumber,
        IFSCcode: this.userDetailsObj.IFSCcode,
        releventInfo: this.userDetailsObj.releventInfo,
      });
    }
  }

  // to update profile
  updateProfile() {
    console.log(this.form.value);
    if (this.form.invalid) {
      alert('Form invalid !');
      return;
    }
    let req = {
      accountHolderName: this.form.value.accountHolderName,
      bankName: this.form.value.bankName,
      bankBranchDetails: this.form.value.bankBranchDetails,
      accountNumber: this.form.value.accountNumber,
      IFSCcode: this.form.value.IFSCcode,
      releventInfo: this.form.value.releventInfo,
    };
    let url = apiUrl.signup;
    this.server.showSpinner();
    this.server.putApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Profile updated successfully!');
        //this.router.navigate(['/artist-portal/settings']);
      }
    });
  }
}
