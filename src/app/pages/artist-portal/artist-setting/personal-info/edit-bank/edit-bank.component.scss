::placeholder{
  color: var(--quaternary-font-color);
}
.container__main {
    ::placeholder {
      color: var(--primary-font-color);
    }
    padding-right: calc(36.111vw - var(--page-default-margin));
    padding-bottom: 6.3vw;
    @media (max-width: 768px) {
      padding-right: calc(6.111vw - var(--page-default-margin));
      padding-bottom: 28.019vw;
    }
    .division {
      height: 0.069vw;
      width: 100%;
      background-color: var(--timeline-color);
    }
    .heading {
      display: inline-flex;
      margin-bottom: 1.392vw;
      img {
        width: 2.291vw;
        height: 2.291vw;
        margin-right: 1.042vw;
      }
  
      .main__heading {
        flex-grow: 1;
        font-size: 1.666vw;
        margin-bottom: 1.042vw;
        @media (max-width: 768px){
          font-size: 5.79vw;
          margin-bottom: 10.14vw;
        }
        // font-weight: 600;
      }
    }
  
    .profile-field {
      .field-contents {
        .field-heading {
          margin-top: 2.78vw;
          margin-bottom: 0;
          font-family: var(--secondary-font);
          font-size: 1.25vw;
        }
        .double {
          width: 35vw;
          .input-double {
            width: 17.01vw !important;
          }
        }
        .field-value {
          margin-top: 2.08vw;
          position: relative;
          //display: flex;
          justify-content: start;
          align-items: center;
          &.doted {
            border: 0.069vw dotted var(--timeline-color);
            padding-left: 1vw;
            padding-right: 1vw;
            padding-bottom: 0.5vw;
          }
          input[type="text"] {
            background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 100%;
            height: 3.47vw;
          }
            input[type="text"]::placeholder{
              color: var(--quaternary-font-color);
            }
          textarea{
              background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 100%;
            height: 6vw;
          }
          input[type="date"] {
            background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 100%;
            height: 3.47vw;
          }
          // input[type="date"] {
          //   background: transparent;
          //   font-family: var(--secondary-font);
          //   border: none;
          //   border: 0.069vw solid var(--timeline-color);
          //   border-radius: 0.14vw;
          //   padding: 1.04vw 1.11vw;
          //   width: 35vw;
          //   height: 3.47vw;
          //   color: var(--quaternary-font-color);
          //   //   ::placeholder{
          //   // color: var(--quaternary-font-color);
          //   // }
          // }
          input[type="password"] {
            background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 35vw;
            height: 3.47vw;
          }
          .input-container {
            width: 102%;
            position: relative;
            display: inline-flex;
            justify-content: start;
            align-items: center;
            .flag-icon {
              position: absolute;
              left: 1.04vw;
            }
            button {
              outline: none;
              background: none;
              border: none;
            }
            .flag-arrow {
              position: absolute;
              max-width: 0.69vw;
              height: auto;
              right: 2.08vw;
            }
            .division {
              position: absolute;
              width: 0.069vw;
              height: 1.04vw;
              background-color: var(--timeline-color);
              left: 3.33vw;
            }
            input[type="text"] {
              // background: transparent;
              font-family: var(--secondary-font);
              border: 0.069vw solid var(--timeline-color);
              padding: 1.04vw 1.11vw 1.04vw 4.2vw;
              border-radius: 0.14vw;
              height: 3.47vw;
              width: 100%;
              z-index: 0;
              &.selection {
                padding: 1.04vw 1.11vw 1.04vw 1.04vw;
              }
            }
            input[type="text"]::placeholder{
              color: var(--quaternary-font-color);
            }

            input[type="date"] {
              // background: transparent;
              font-family: var(--secondary-font);
              border: 0.069vw solid var(--timeline-color);
              padding: 1.04vw 1.11vw 1.04vw 4.2vw;
              border-radius: 0.14vw;
              height: 3.47vw;
              width: 100%;
              z-index: 0;
              &.selection {
                padding: 1.04vw 1.11vw 1.04vw 1.04vw;
              }
            }
            .dropdown-visible {
              background-color: var(--primary-background-color);
              visibility: visible;
              position: absolute;
              top: 3.47vw;
              z-index: 1;
              box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
              width: 100%;
              @media (max-width: 768px) {
                top: 10.47vw;
              }
              ul {
                list-style: none;
                padding: 0.69vw 0;
                max-height: 27.97vw;
                margin: 0;
                overflow: hidden;
                overflow-y: scroll;
                @media (max-width: 768px) {
                  padding: 1.69vw 0;
                }
                li {
                  padding: 0.69vw 1.04vw;
                  width: 34.9vw;
                  display: flex;
                  @media (max-width: 768px) {
                    padding: 1.69vw 1.04vw;
                  }
                  .country-name {
                    margin-left: 0.69vw;
                    @media (max-width: 768px) {
                      font-size: 3.38vw;
                    }
                  }
                }
                li:hover {
                  background-color: var(--timeline-color);
                }
              }
              ul::-webkit-scrollbar {
                display: none;
              }
            }
            .dropdown-hidden {
              display: none;
            }
          }
          .ph-flag {
            height: 1.25vw;
            // padding-right: 0.62vw;
            // border-right: solid 0.09vw var(--quaternary-font-color);
          }
          .placeholder {
            position: absolute;
            top: -0.4vw;
            left: 1.04vw;
            font-size: 0.8333vw;
            color: var(--quaternary-font-color);
            padding: 0 0.3vw;
            background-color: var(--primary-background-color);
            // background-color: #ededf1;
          }
          .send {
            margin-left: 2.08vw;
            color: var(--tertiary-font-color);
          }
        }
        .verify {
          margin-top: 2.78vw;
          font-family: var(--secondary-font);
          font-size: 1.25vw;
        }
        .partitioned {
          margin-top: 1.33vw;
          outline: none;
          padding-left: 0.8vw;
          letter-spacing: 0;
          border: 0;
          background-image: linear-gradient(
            to left,
            var(--timeline-color) 70%,
            rgba(255, 255, 255, 0) 0%
          );
          background-position: bottom;
          background-size: 3.2vw 0.069vw;
          width: 3.2vw;
          background-repeat: repeat-x;
          background-position-x: 2.2vw;
          height: 2vw;
          padding-bottom: 0.35vw;
          font-family: var(--secondary-font);
        }
        .last-changed {
          margin-top: 1.04vw;
          font-size: 0.97vw;
          color: var(--quaternary-font-color);
          @media (max-width: 768px) {
            margin-top: 2.04vw;
            font-size: 3.62vw;
          }
        }
      }
      .buttonList {
          margin-top: 3.47vw;
          display: flex;
          margin-bottom: 1.042vw;
          @media (max-width: 768px) {
            margin-top: 6.47vw;
            display: block;
          }
          .save {
            // display: block;
            width: 100%;
            outline: none;
            font-size: 1.25vw;
            // width: 20.56vw;
            background-color: transparent;
            color: var(--tertiary-font-color);
            padding: 0.833333vw 5.45vw;
            border: 0.069vw solid var(--tertiary-font-color);
            border-radius: 1.46vw;
            @media (max-width: 768px) {
              font-size: 3.86vw;
              margin-bottom: 4.83vw;
              border-radius: 10.1vw;
              padding: 3.45vw 6.89vw;
            }
          }
          .cancel {
            width: 100%;
            outline: none;
            font-size: 1.25vw;
            // width:10.56vw;
            background-color: transparent;
            padding: 0.833333vw 2.777vw;
            border: 0.069vw solid transparent;
            border-radius: 1.46vw;
            color: var(--quaternary-font-color);
            @media (max-width: 768px) {
              font-size: 3.86vw;
            }
          }
          .save:hover {
            font-weight: 500;
          }
        }
    }
  
    .splitter {
      display: flex;
      // justify-content: space-between;
      @media (max-width: 768px) {
        display: block;
      }
      .field-value {
        width: 50%;
        input {
          width: 17.1vw;
        }
      }
      .fname {
        padding-right: 0.5vw;
      }
      .mname {
        padding-left: 0.5vw;
        @media (max-width: 768px) {
          padding-left: 0vw;
        }
      }
    }
  
    .radio-wrapper {
      margin-bottom: 1vw;
      font-family: var(--secondary-font);
      @media (max-width: 768px) {
        margin-bottom: 7.24vw;
        font-size: 3.68vw;
      }
      label {
        margin-bottom: unset;
      }
      input[type="radio"] {
        height: 1.25vw;
        width: 1.25vw;
        margin-right: 1.042vw;
        //   margin-top: -1px;
        vertical-align: middle;
        @media (max-width: 768px) {
          height: 4vw;
          width: 4vw;
          margin-right: 2.5vw;
        }
      }
    }
    .edit-section {
      margin-top: 3.472vw;
      // margin-bottom: 3.472vw;
      .title {
        font-size: 1.25vw;
        margin-bottom: 1.291vw;
        @media (max-width: 768px) {
          font-size: 4.34vw;
          margin-bottom: 7.72vw;
        }
      }
    }
    .other_gender_input{
      input[type="text"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border-bottom: 0.069vw solid var(--timeline-color);
          // border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
           @media (max-width: 768px) {
            width: 87.16vw;
            height: 12.07vw;
            font-size: 3.86vw;
            padding-left: 3.62vw;
           }
        }
    }
  }
  .footer-nav {
    height: 6.3vw;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0);
    @media (max-width: 768px) {
      padding-bottom: 11.24vw;
    }
    .button-group {
      //margin-left: 21.66vw;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 1.25vw;
      @media (max-width: 768px) {
        font-size: 4.34vw;
        margin-left: 0vw;
        align-items: unset;
      }
      .next {
        margin: 0 0.5vw;
        width: 9vw;
        height: 3.05vw;
        cursor:pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 1.49vw;
        border: solid 0.069vw #004ddd;
        color: #004ddd;
        @media (max-width: 768px) {
          width: 46.37vw;
          height: 10.62vw;
          border-radius: 6.08vw;
        }
      }
      a{
          .next {
        margin: 0 0.5vw;
        width: 9vw;
        height: 3.05vw;
        cursor:pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 1.49vw;
        border: solid 0.069vw #004ddd;
        color: #004ddd;
        @media (max-width: 768px) {
          width: 46.37vw;
          height: 10.62vw;
          border-radius: 6.08vw;
        }
      }
      }
      .back {
        margin-left: 2.77vw;
        color: #808080;
      }
    }
  }
  
  .info-123 {
    font-size: 0.972vw;
    color: var(--quaternary-font-color);
    @media (max-width: 768px) {
      font-size: 3.90vw;
    }
    //   span {
    // font-size: 0.972vw;
    // color: var(--quaternary-font-color);
    .blue {
      color: var(--tertiary-font-color);
    }
    //   }
  }
  
  .input-info {
    font-size: 0.95vw;
    margin-top: 0.5vw;
  }
  // jks 19-7-21 
  @media (max-width: 768px) {
    input::-webkit-input-placeholder {
      // font-size: 3.86vw;
    }
  }
  ::-webkit-input-placeholder {
    color: #000;
  }
  ::-moz-placeholder {
    color: #000;
  }
  :-ms-input-placeholder {
    color: #000;
  }
  ::-ms-input-placeholder {
    color: #000;
  }
  ::placeholder {
    color: #000;
  }
  