<div class="container__main">
    <div class="profile-field">
        <div class="w-100 field-contents">
            <form [formGroup]="form" autocomplete="off">
                <div class="info-123">We will use this information to set up any payment settlements for sale of your works via Terrain.art.</div>
                <div class="field-value">
                    <input type="text" formControlName="accountHolderName" placeholder="Account Holder Name" />
                    <div class="placeholder">Account Holder Name</div>
                    <div class="input-info">Provide the full name of the Account holder as provided in the bank documents.</div>
                </div>

                <div class="field-value">
                    <input type="text" formControlName="bankName" placeholder="Bank Name" />
                    <div class="placeholder">Bank Name</div>
                    <div class="input-info">Provide the name of the Bank.</div>
                </div>
                <div class="field-value">
                    <textarea formControlName="bankBranchDetails" cols="30" rows="10"></textarea>
                    <div class="placeholder">Bank Branch Details</div>
                    <div class="input-info">Provide the Bank branch details (branch name, address).</div>
                </div>

                <div class="field-value">
                    <input type="text" formControlName="accountNumber" placeholder="Account Number" />
                    <div class="placeholder">Account Number</div>
                    <div class="input-info">Provide the bank account number.</div>
                </div>
                <div class="field-value">
                    <input type="text" formControlName="IFSCcode" placeholder="IFSC code" />
                    <div class="placeholder">IFSC code</div>
                    <div class="input-info">Provide the bank IFSC code for receiving funds.</div>
                </div>
                <div class="field-value">
                    <textarea formControlName="releventInfo" cols="30" rows="10"></textarea>
                    <div class="placeholder">Any other relevant information</div>
                    <div class="input-info">Provide any other relevant information required for receiving funds.</div>
                </div>
                <!-- <div class="buttonList">
            <button (click)="updateProfile()" class="save">Save details</button>
            <a routerLink="/artist-portal/settings"
              ><button class="cancel">Cancel</button></a
            >
          </div> -->
            </form>
        </div>
    </div>
</div>
<div class="footer-nav">
    <div class="button-group">
        <!-- <div (click)="isPopupOpen = false"  class="next">Cancel</div> -->
        <div (click)="updateProfile()" class="next">Save</div>
        <a routerLink="/artist-portal/settings"><button  class="next">Cancel</button></a
    >
    </div>
  </div>