.container__main {
  ::placeholder {
    color: var(--primary-font-color);
  }
  padding-right: calc(43.33vw - var(--page-default-margin));
  padding-bottom: 6.3vw;
  // .division {
  //   height: 0.069vw;
  //   width: 100%;
  //   background-color: var(--timeline-color);
  // }
  @media (max-width: 768px) {
    padding-right: calc(6.111vw - var(--page-default-margin));
    margin-bottom: 40vw;
  }
  .heading {
    display: inline-flex;
    margin-bottom: 1.392vw;
    @media (max-width: 768px) {
      display: block;
    }
    img {
      width: 2.291vw;
      height: 2.291vw;
      margin-right: 1.042vw;
      @media (max-width: 768px) {
        display: none;
      }
    }

    .main__heading {
      flex-grow: 1;
      font-size: 1.666vw;
      margin-bottom: 1.042vw;
      @media (max-width: 768px) {
        font-size: 5.79vw;
        margin-bottom: 12.07vw;
        color: #000;
      }
      font-weight: 600;
    }
  }

  .profile-field {
    .field-contents {
      .field-heading {
        margin-top: 2.78vw;
        margin-bottom: 0;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
      }
      .double {
        width: 35vw;
        .input-double {
          width: 17.01vw !important;
        }
      }
      .field-value {
        margin-top: 2.08vw;
        position: relative;
        display: flex;
        justify-content: start;
        align-items: center;
        width: 27.77vw;
        @media (max-width: 768px) {
          margin-top: 6.45vw;
          display: block;
          width: 100%;
        }
        input[type="text"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 3.68vw 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          position: relative;
          @media (max-width: 768px) {
            width: 87.16vw;
            height: 12.07vw;
            font-size: 3.86vw;
            padding-left: 3.62vw;
          }
        }
        input[type="password"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 3.68vw 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          position: relative;
          @media (max-width: 768px) {
            width: 87.16vw;
            height: 12.07vw;
            font-size: 3.86vw;
            padding-left: 3.62vw;
          }
        }

        .input-container {
          width: 60%;
          position: relative;
          display: inline-flex;
          justify-content: start;
          align-items: center;
          .flag-icon {
            position: absolute;
            left: 1.04vw;
          }
          button {
            outline: none;
            background: none;
            border: none;
          }
          .flag-arrow {
            position: absolute;
            max-width: 0.69vw;
            height: auto;
            right: 2.08vw;
          }
          .division {
            position: absolute;
            width: 0.069vw;
            height: 1.04vw;
            background-color: var(--timeline-color);
            left: 3.33vw;
          }
          input[type="text"] {
            // background: transparent;
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 1.04vw 1.11vw 1.04vw 4.2vw;
            border-radius: 0.14vw;
            height: 3.47vw;
            width: 35vw;
            z-index: 0;
          }
          .dropdown-visible {
            background-color: var(--primary-background-color);
            visibility: visible;
            position: absolute;
            top: 3.47vw;
            z-index: 1;
            box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
            ul {
              list-style: none;
              padding: 0.69vw 0;
              max-height: 27.97vw;
              margin: 0;
              overflow: hidden;
              overflow-y: scroll;
              li {
                padding: 0.69vw 1.04vw;
                width: 34.9vw;
                display: flex;
                .country-name {
                  margin-left: 0.69vw;
                }
              }
              li:hover {
                background-color: var(--timeline-color);
              }
            }
            ul::-webkit-scrollbar {
              display: none;
            }
          }
          .dropdown-hidden {
            display: none;
          }
        }
        .eye-button {
          position: absolute;
          top: 1.241vw;
          right: 1.041vw;
          height: 0.972vw;
          width: 1.597vw;
          @media (max-width: 768px) {
            top: 0;
            right: 4.58vw;
            bottom: 0;
            margin: auto;
            height: 3.38vw;
            width: 5.55vw;
          }
        }
        .placeholder {
          position: absolute;
          top: -0.4vw;
          left: 1.04vw;
          font-size: 0.8333vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
          // background-color: #ededf1;
          @media (max-width: 768px) {
            top: -1.4vw;
            left: 3.62vw;
            font-size: 2.89vw;
          }
        }
        .send {
          margin-left: 2.08vw;
          color: var(--tertiary-font-color);
          @media (max-width: 768px) {
            margin-left: unset;
            font-size: 3.86vw;
            line-height: 10.62vw;
          }
        }
      }
      .verify {
        margin-top: 2.78vw;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
        @media (max-width: 768px) {
          margin-top: 9.66vw;
          font-size: 4.34vw;
          margin-bottom: 7.49vw;
        }
      }
      .partitioned {
        margin-top: 1.33vw;
        outline: none;
        padding-left: 0.8vw;
        letter-spacing: 0;
        border: 0;
        background-image: linear-gradient(
          to left,
          var(--timeline-color) 70%,
          rgba(255, 255, 255, 0) 0%
        );
        background-position: bottom;
        background-size: 3.2vw 0.069vw;
        width: 3.2vw;
        background-repeat: repeat-x;
        background-position-x: 2.2vw;
        height: 2vw;
        padding-bottom: 0.35vw;
        font-family: var(--secondary-font);
      }
      .last-changed {
        margin-top: 1.04vw;
        font-size: 0.97vw;
        color: var(--quaternary-font-color);
        margin-bottom: 3.4722vw;
        @media (max-width: 768px) {
          margin-top: 3.62vw;
          font-size: 4vw;
          margin-bottom: 7.24vw;
        }
      }
    }
    .buttonList {
      margin-top: 3.47vw;
      display: flex;
      margin-bottom: 1.042vw;
      @media (max-width: 768px) {
        display: block;
        margin-top: 6.47vw;
      }
      .save {
        // display: block;
        width: 100%;
        outline: none;
        font-size: 1.25vw;
        // width: 20.56vw;
        background-color: transparent;
        color: var(--tertiary-font-color);
        padding: 0.833333vw 5.41vw;
        border: 0.069vw solid var(--tertiary-font-color);
        border-radius: 1.46vw;
        @media (max-width: 768px) {
          font-size: 3.86vw;
          margin-bottom: 4.83vw;
          border-radius: 10.1vw;
          padding: 3.45vw 6.89vw;
        }
        &.grey {
          color: var(--quaternary-font-color);
          border: 0.069vw solid var(--quaternary-font-color);
        }
      }

      .cancel {
        width: 100%;
        outline: none;
        font-size: 1.25vw;
        // width:10.56vw;
        background-color: transparent;
        padding: 0.833333vw 2.777vw;
        border: 0.069vw solid transparent;
        border-radius: 1.46vw;
        color: var(--quaternary-font-color);
        @media (max-width: 768px) {
          font-size: 3.86vw;
        }
      }
      .save:hover {
        font-weight: 500;
      }
    }
  }
}
