import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../../services/artist.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {
  form: FormGroup;

  showInput1 = false;
  showInput2 = false;

  constructor(private formBuilder: FormBuilder, private server: ArtistService

  ) { }

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      old_password: new FormControl('', [Validators.required]),
      new_password: new FormControl('', [Validators.required]),
      confirm_new_password: new FormControl('', [Validators.required]),
    });
  }

  updatePassword() {
    if (this.form.value.new_password != this.form.value.confirm_new_password) {
      alert('New Passsword and Confirm Password must be same');
      return;
    }
    if (!this.form.valid) {
      alert('Form not valid!');
      return;
    }
    let url = apiUrl.changePassword
    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    let req = {
      "userId": JSON.parse(decode)['_id'],
      "password": this.form.value.new_password,
      "confirmPassword": this.form.value.confirm_new_password,
      "oldPassword": this.form.value.old_password,
    }
    this.server.postApi(url, req).subscribe(res => {
      if (res.statusCode == 200) {
        alert('Password updated successfully!')
      }
    })
  }

}
