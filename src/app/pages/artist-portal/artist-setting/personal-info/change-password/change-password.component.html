<div class="container__main">
  <div class="heading">
    <a routerLink="/artist-portal/settings"
      ><img src="../../../../../../assets/icons/<EMAIL>" alt=""
    /></a>
    <p class="main__heading">Change password</p>
  </div>

  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="field-value">
            <input
              [type]="showInput1 ? 'text' : 'password'"
              formControlName="old_password"
            />
            <img
              (click)="showInput1 = !showInput1"
              class="eye-button"
              src="../../../../../../assets/icons/<EMAIL>"
              alt="eye_button"
            />
            <div class="placeholder">Enter old password</div>
          </div>
          <div class="field-value">
            <input
              [type]="showInput2 ? 'text' : 'password'"
              formControlName="new_password"
            />
            <img
              (click)="showInput2 = !showInput2"
              class="eye-button"
              src="../../../../../../assets/icons/<EMAIL>"
              alt="eye_button"
            />
            <div class="placeholder">Enter new password</div>
          </div>

          <div class="last-changed" style="margin-bottom: 0.2vw">
            Use 8 or more characters with a mix of numbers, letters and symbols
          </div>
          <div class="field-value">
            <input type="password" formControlName="confirm_new_password" />
            <div class="placeholder">Confirm new password</div>
          </div>

          <div class="buttonList">
            <button
              [ngClass]="{
                grey:
                  !form.get('old_password').value ||
                  !form.get('new_password').value ||
                  form.get('new_password').value !=
                    form.get('confirm_new_password').value ||
                  form.get('new_password').value.length < 8
              }"
              (click)="
                form.get('old_password').value &&
                  form.get('new_password').value &&
                  form.get('new_password').value ==
                    form.get('confirm_new_password').value &&
                  form.get('new_password').value.length >= 8 &&
                  updatePassword()
              "
              class="save"
            >
              Change password
            </button>
            <a routerLink="/artist-portal/settings"
              ><button class="cancel">Cancel</button></a
            >
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
