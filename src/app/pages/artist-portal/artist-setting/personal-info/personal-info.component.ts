import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../services/artist.service';

@Component({
	selector: 'app-personal-info',
	templateUrl: './personal-info.component.html',
	styleUrls: ['./personal-info.component.scss'],
})
export class PersonalInfoComponent implements OnInit {
	aristInfo;
	idProof;
	ethAcc;
	userDetailsObj: any={};
	flag:any=''
	nationalityArr: any=[];
	constructor(private server: CollectorService , private router:Router) { }

	ngOnInit(): void {	
		// this.getNationality()
		this.getUserDetails()	
	}
	
	 // to get user details
	 getUserDetails() {
		let url = apiUrl.getUserDetails;
		this.server.getApi(url).subscribe((res) => {
		  if (res.statusCode == 200) {
			
			this.userDetailsObj = res.data || res.Data;
			if (this.userDetailsObj['role'] == 'SUPERADMIN') {
				this.userDetailsObj['role_id']['permissions'] = apiUrl.modules;
			} 
			let stringify = JSON.stringify(this.userDetailsObj);
			let encode =btoa(unescape(encodeURIComponent(stringify)))
			localStorage.setItem('userDetails',encode);
			this.getNationality();
		  }
		});
	  }


	// to get nationality
	getNationality() {
		let url = 'assets/json/country.json' 
		this.server.getJson(url).subscribe(res=>{
			this.nationalityArr = res;
			this.flag =this.nationalityArr.findIndex(x=>x.name == this.userDetailsObj.country) !=-1 ? (this.nationalityArr.find(x=>x.name == this.userDetailsObj.country))['emoji'] : ''
		})
	}

	navigateTo() {
		this.router.navigate(['/artist-portal/settings/artwork/profile-page','personal'])
	}

}
