<div class="container__main">
  <!-- <div class="heading">
    <a routerLink="/artist-portal/settings"
      ><img src="../../../../../../assets/icons/<EMAIL>" alt=""
    /></a>
    <p class="main__heading">Edit personal details</p>
  </div> -->
  <div class="profile-field">
    <div class="w-100 field-contents">
      <form [formGroup]="form" autocomplete="off">
        <div class="info-123">
          Please ensure that all fields match your Government issued ID. This is
          for verification purposes and will not be made public.
        </div>
        <div class="splitter">
          <div class="field-value fname">
            <input
              type="text"
              formControlName="firstName"
              placeholder="First Name"
            />
            <div class="placeholder">First Name</div>
            <div class="input-info">
              First Name as per Government issued ID.
            </div>
          </div>
          <div class="field-value mname">
            <input
              type="text"
              formControlName="middle_name"
              placeholder="Middle Name"
            />
            <div class="placeholder">Middle Name</div>
            <div class="input-info">
              Middle Name as per Goverment issued ID.
            </div>
          </div>
        </div>
        <div class="field-value">
          <input
            type="text"
            formControlName="lastName"
            placeholder="Last Name"
          />
          <div class="placeholder">Last Name</div>
          <div class="input-info">Last Name as per Goverment issued ID.</div>
        </div>
        <div class="field-value">
          <div class="input-container" (focusout)="changeFocus(0)">
            <input
              type="text"
              formControlName="countrySelected"
              placeholder="Nationality"
              [readonly]="true"
              (focus)="isDropDownOpen[0] = true"
            />
            <div class="flag-icon">
              <div class="ph-flag">{{ flag }}</div>
            </div>
            <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>
            <div class="division"></div>
            <div
              [ngClass]="{
                'dropdown-hidden': !isDropDownOpen[0],
                'dropdown-visible': isDropDownOpen[0]
              }"
            >
              <ul>
                <li
                  (click)="
                    form.get('countrySelected').setValue(item.name);
                    flag = item.emoji;
                    isDropDownOpen[0] = false
                  "
                  *ngFor="let item of nationalityArr"
                >
                  {{ item.emoji }}
                  <div class="country-name">
                    {{ item.name }}
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div class="placeholder">Nationality</div>
        </div>
        <div class="field-value">
          <input
            type="text"
            formControlName="mobile"
            placeholder="Not verified"
          />
          <div class="placeholder">Mobile Number</div>
          <!-- <img
            src="../../../../../../assets/icons/edit.png"
            alt="Edit"
            style="height: 3.5vw; width: 3.5vw; position: absolute; right: -5vw"
          /> -->
          <!--   [readonly]="true" (click)="openMobModal()" -->
          <!-- <div class="input-info">Last Name as per Goverment issued ID.</div> -->
        </div>
        <div class="edit-section">
          <p class="title">Gender</p>
          <div class="radio-wrapper">
            <input
              type="radio"
              id="female"
              name="gender"
              value="female"
              formControlName="gender"
              (change)="changeGender($event)"
            />
            <label for="female">Female</label>
          </div>
          <div class="radio-wrapper">
            <input
              type="radio"
              id="male"
              name="gender"
              value="male"
              formControlName="gender"
              (change)="changeGender($event)"
            />
            <label for="male">Male</label>
          </div>
          <div class="radio-wrapper">
            <input
              type="radio"
              id="other"
              name="gender"
              value="other"
              formControlName="gender"
              (change)="changeGender($event)"
            />
            <label for="other">Other/prefer to self describe</label>
          </div>

          <!-- <div *ngIf="other_gender" class="other_gender_input">
            <input formControlName="other_gender" type="text" maxlength="50" />
             <p class="last-changed">0/50</p>
          </div> -->
        </div>
        <div class="field-value" *ngIf="other_gender">
          <input
            type="text"
            formControlName="other_gender"
            placeholder="Gender"
          />
          <div class="placeholder">Other Gender</div>
        </div>
        <!-- <div class="edit-section">
          <p class="title">Date of Birth</p>
        </div> -->
        <div class="field-value">
          <input
            type="date"
            formControlName="dateOfBirth"
            [ngModel]="BirthDate | date : 'yyyy-MM-dd'"
            placeholder="13/10/97"
          />
          <div class="placeholder">Date Of Birth</div>
        </div>

        <!-- <div class="edit-section">
          <p class="title">Country of Origin</p>

          <div class="field-value flex-block">
            <div class="input-container" (focusout)="changeFocus(0)">
              <input
                type="text"
                formControlName="countrySelected"
                placeholder="Nationality"
                [readonly]="true"
                (focus)="isDropDownOpen[0] = true"
              />
              <div class="flag-icon">
                <div class="ph-flag">{{ flag }}</div>
              </div>
              <button (click)="isDropDownOpen[0]  = !isDropDownOpen[0] ">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div class="division"></div>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDownOpen[0] ,
                  'dropdown-visible': isDropDownOpen[0]
                }"
              >
                <ul>
                  <li
                    (click)="

                      form.get('countrySelected').setValue(item.name);flag=item.emoji;
                      isDropDownOpen[0] = false;
                    "
                    *ngFor="let item of nationalityArr"
                  >
                    {{ item.emoji }}
                    <div class="country-name">
                      {{ item.name }}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="placeholder">Nationality</div>
          </div>
        </div> -->
        <!-- <div class="buttonList">
          <button (click)="updateProfile()" class="save">Save details</button>
          <a routerLink="/artist-portal/settings"
            ><button class="cancel">Cancel</button></a
          >
        </div> -->

        <div
          id="myModal"
          class="modal"
          [style.display]="isPopupOpen ? 'block' : 'none'"
        >
          <div class="modal-content">
            <div class="field-value">
              <div class="input-container" (focusout)="changeFocus(1)">
                <input
                  type="text"
                  formControlName="mobileNumber"
                  placeholder="9876543210"
                  (focus)="isDropDownOpen[1] = true"
                />
                <div class="flag-icon">
                  <div class="ph-flag">{{ CodeFlag }}</div>
                </div>
                <button (click)="isDropDownOpen[1] = !isDropDownOpen[1]">
                  <img
                    src="assets/icons/arrow-down.png"
                    class="flag-arrow"
                    style="left: 4.6vw; top: 1.6vw"
                  />
                </button>
                <div class="division" style="left: 5.7vw"></div>
                <div
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[1],
                    'dropdown-visible': isDropDownOpen[1]
                  }"
                >
                  <ul>
                    <li
                      (click)="
                        form.get('codeSelected').setValue(item.code);
                        CodeFlag = item.code;
                        isDropDownOpen[0] = false
                      "
                      *ngFor="let item of countryPhoneCodeArr"
                    >
                      {{ item.code }}
                      <div class="country-name">
                        {{ item.name }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="placeholder">Mobile Number</div>
            </div>
            <!-- <a
              class="send"
              style="margin-top: 1vw; margin-bottom: 1vw ;cursor: pointer;"
              (click)="sendOTP()"
              [innerHtml]="OtpSend ? 'Resend Code' : 'Send Code'"
            ></a> -->
            <div class="otpEnterSection" [hidden]="!OtpSend">
              <div class="verify" style="margin-top: 0">Verify your phone</div>
              <div class="Otpdiv" style="display: flex">
                <input
                  #input1
                  [(ngModel)]="otp1"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(1)"
                  onchange="keyUpEvent(1)"
                />
                <input
                  #input2
                  [(ngModel)]="otp2"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(2)"
                />
                <input
                  #input3
                  [(ngModel)]="otp3"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(3)"
                />
                <input
                  #input4
                  [(ngModel)]="otp4"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(4)"
                />
                <input
                  #input5
                  [(ngModel)]="otp5"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(5)"
                />
                <input
                  #input6
                  [(ngModel)]="otp6"
                  class="partitioned"
                  type="number"
                  [maxLength]="1"
                  (ngModelChange)="keyUpEvent(6)"
                />
              </div>
              <div class="last-changed">
                A six digit code has been sent to your phone
              </div>
            </div>

            <div class="buttonKeeper">
              <button type="button" class="btnn" (click)="isPopupOpen = false">
                Cancel</button
              ><button type="button" class="btnn">Save</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
<div class="footer-nav">
  <div class="button-group">
    <!-- <div (click)="isPopupOpen = false"  class="next">Cancel</div> -->
    <div (click)="updateProfile()" class="next">Save</div>
    <a routerLink="/artist-portal/settings"
      ><button class="next">Cancel</button></a
    >
  </div>
</div>
