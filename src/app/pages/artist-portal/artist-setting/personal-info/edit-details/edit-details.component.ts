import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../../services/artist.service';

@Component({
  selector: 'app-edit-details',
  templateUrl: './edit-details.component.html',
  styleUrls: ['./edit-details.component.scss'],
})
export class EditDetailsComponent implements OnInit {
  @ViewChild('input1') otp1El: ElementRef;
  @ViewChild('input2') otp2El: ElementRef;
  @ViewChild('input3') otp3El: ElementRef;
  @ViewChild('input4') otp4El: ElementRef;
  @ViewChild('input5') otp5El: ElementRef;
  @ViewChild('input6') otp6El: ElementRef;

  mobile = '(*************';
  other_gender = false;
  isCancel = false;
  isDropDown = false;
  form: FormGroup;
  BirthDate;
  userDetailsObj: any = {};
  nationalityArr: any = [];
  flag: any;
  CodeFlag = '+91';
  isDropDownOpen = Array(10).fill(false);
  isPopupOpen = false;
  OtpSend = false;
  otp1 = '';
  otp2 = '';
  otp3 = '';
  otp4 = '';
  otp5 = '';
  otp6 = '';
  countryPhoneCodeArr = [
    {
      code: '******',
      name: 'Abkhazia',
    },
    {
      code: '+93',
      name: 'Afghanistan',
    },
    {
      code: '+355',
      name: 'Albania',
    },
    {
      code: '+213',
      name: 'Algeria',
    },
    {
      code: '******',
      name: 'American Samoa',
    },
    {
      code: '+376',
      name: 'Andorra',
    },
    {
      code: '+244',
      name: 'Angola',
    },
    {
      code: '******',
      name: 'Anguilla',
    },
    {
      code: '******',
      name: 'Antigua and Barbuda',
    },
    {
      code: '+54',
      name: 'Argentina',
    },
    {
      code: '+374',
      name: 'Armenia',
    },
    {
      code: '+297',
      name: 'Aruba',
    },
    {
      code: '+247',
      name: 'Ascension',
    },
    {
      code: '+61',
      name: 'Australia',
    },
    {
      code: '+672',
      name: 'Australian External Territories',
    },
    {
      code: '+43',
      name: 'Austria',
    },
    {
      code: '+994',
      name: 'Azerbaijan',
    },
    {
      code: '+1 242',
      name: 'Bahamas',
    },
    {
      code: '+973',
      name: 'Bahrain',
    },
    {
      code: '+880',
      name: 'Bangladesh',
    },
    {
      code: '+1 246',
      name: 'Barbados',
    },
    {
      code: '******',
      name: 'Barbuda',
    },
    {
      code: '+375',
      name: 'Belarus',
    },
    {
      code: '+32',
      name: 'Belgium',
    },
    {
      code: '+501',
      name: 'Belize',
    },
    {
      code: '+229',
      name: 'Benin',
    },
    {
      code: '+1 441',
      name: 'Bermuda',
    },
    {
      code: '+975',
      name: 'Bhutan',
    },
    {
      code: '+591',
      name: 'Bolivia',
    },
    {
      code: '+387',
      name: 'Bosnia and Herzegovina',
    },
    {
      code: '+267',
      name: 'Botswana',
    },
    {
      code: '+55',
      name: 'Brazil',
    },
    {
      code: '+246',
      name: 'British Indian Ocean Territory',
    },
    {
      code: '+1 284',
      name: 'British Virgin Islands',
    },
    {
      code: '+673',
      name: 'Brunei',
    },
    {
      code: '+359',
      name: 'Bulgaria',
    },
    {
      code: '+226',
      name: 'Burkina Faso',
    },
    {
      code: '+257',
      name: 'Burundi',
    },
    {
      code: '+855',
      name: 'Cambodia',
    },
    {
      code: '+237',
      name: 'Cameroon',
    },
    {
      code: '+1',
      name: 'Canada',
    },
    {
      code: '+238',
      name: 'Cape Verde',
    },
    {
      code: '+ 345',
      name: 'Cayman Islands',
    },
    {
      code: '+236',
      name: 'Central African Republic',
    },
    {
      code: '+235',
      name: 'Chad',
    },
    {
      code: '+56',
      name: 'Chile',
    },
    {
      code: '+86',
      name: 'China',
    },
    {
      code: '+61',
      name: 'Christmas Island',
    },
    {
      code: '+61',
      name: 'Cocos-Keeling Islands',
    },
    {
      code: '+57',
      name: 'Colombia',
    },
    {
      code: '+269',
      name: 'Comoros',
    },
    {
      code: '+242',
      name: 'Congo',
    },
    {
      code: '+243',
      name: 'Congo, Dem. Rep. of (Zaire)',
    },
    {
      code: '+682',
      name: 'Cook Islands',
    },
    {
      code: '+506',
      name: 'Costa Rica',
    },
    {
      code: '+385',
      name: 'Croatia',
    },
    {
      code: '+53',
      name: 'Cuba',
    },
    {
      code: '+599',
      name: 'Curacao',
    },
    {
      code: '+537',
      name: 'Cyprus',
    },
    {
      code: '+420',
      name: 'Czech Republic',
    },
    {
      code: '+45',
      name: 'Denmark',
    },
    {
      code: '+246',
      name: 'Diego Garcia',
    },
    {
      code: '+253',
      name: 'Djibouti',
    },
    {
      code: '+1 767',
      name: 'Dominica',
    },
    {
      code: '+1 809',
      name: 'Dominican Republic',
    },
    {
      code: '+670',
      name: 'East Timor',
    },
    {
      code: '+56',
      name: 'Easter Island',
    },
    {
      code: '+593',
      name: 'Ecuador',
    },
    {
      code: '+20',
      name: 'Egypt',
    },
    {
      code: '+503',
      name: 'El Salvador',
    },
    {
      code: '+240',
      name: 'Equatorial Guinea',
    },
    {
      code: '+291',
      name: 'Eritrea',
    },
    {
      code: '+372',
      name: 'Estonia',
    },
    {
      code: '+251',
      name: 'Ethiopia',
    },
    {
      code: '+500',
      name: 'Falkland Islands',
    },
    {
      code: '+298',
      name: 'Faroe Islands',
    },
    {
      code: '+679',
      name: 'Fiji',
    },
    {
      code: '+358',
      name: 'Finland',
    },
    {
      code: '+33',
      name: 'France',
    },
    {
      code: '+596',
      name: 'French Antilles',
    },
    {
      code: '+594',
      name: 'French Guiana',
    },
    {
      code: '+689',
      name: 'French Polynesia',
    },
    {
      code: '+241',
      name: 'Gabon',
    },
    {
      code: '+220',
      name: 'Gambia',
    },
    {
      code: '+995',
      name: 'Georgia',
    },
    {
      code: '+49',
      name: 'Germany',
    },
    {
      code: '+233',
      name: 'Ghana',
    },
    {
      code: '+350',
      name: 'Gibraltar',
    },
    {
      code: '+30',
      name: 'Greece',
    },
    {
      code: '+299',
      name: 'Greenland',
    },
    {
      code: '+1 473',
      name: 'Grenada',
    },
    {
      code: '+590',
      name: 'Guadeloupe',
    },
    {
      code: '+1 671',
      name: 'Guam',
    },
    {
      code: '+502',
      name: 'Guatemala',
    },
    {
      code: '+224',
      name: 'Guinea',
    },
    {
      code: '+245',
      name: 'Guinea-Bissau',
    },
    {
      code: '+595',
      name: 'Guyana',
    },
    {
      code: '+509',
      name: 'Haiti',
    },
    {
      code: '+504',
      name: 'Honduras',
    },
    {
      code: '+852',
      name: 'Hong Kong SAR China',
    },
    {
      code: '+36',
      name: 'Hungary',
    },
    {
      code: '+354',
      name: 'Iceland',
    },
    {
      code: '+91',
      name: 'India',
    },
    {
      code: '+62',
      name: 'Indonesia',
    },
    {
      code: '+98',
      name: 'Iran',
    },
    {
      code: '+964',
      name: 'Iraq',
    },
    {
      code: '+353',
      name: 'Ireland',
    },
    {
      code: '+972',
      name: 'Israel',
    },
    {
      code: '+39',
      name: 'Italy',
    },
    {
      code: '+225',
      name: 'Ivory Coast',
    },
    {
      code: '+1 876',
      name: 'Jamaica',
    },
    {
      code: '+81',
      name: 'Japan',
    },
    {
      code: '+962',
      name: 'Jordan',
    },
    {
      code: '+7 7',
      name: 'Kazakhstan',
    },
    {
      code: '+254',
      name: 'Kenya',
    },
    {
      code: '+686',
      name: 'Kiribati',
    },
    {
      code: '+965',
      name: 'Kuwait',
    },
    {
      code: '+996',
      name: 'Kyrgyzstan',
    },
    {
      code: '+856',
      name: 'Laos',
    },
    {
      code: '+371',
      name: 'Latvia',
    },
    {
      code: '+961',
      name: 'Lebanon',
    },
    {
      code: '+266',
      name: 'Lesotho',
    },
    {
      code: '+231',
      name: 'Liberia',
    },
    {
      code: '+218',
      name: 'Libya',
    },
    {
      code: '+423',
      name: 'Liechtenstein',
    },
    {
      code: '+370',
      name: 'Lithuania',
    },
    {
      code: '+352',
      name: 'Luxembourg',
    },
    {
      code: '+853',
      name: 'Macau SAR China',
    },
    {
      code: '+389',
      name: 'Macedonia',
    },
    {
      code: '+261',
      name: 'Madagascar',
    },
    {
      code: '+265',
      name: 'Malawi',
    },
    {
      code: '+60',
      name: 'Malaysia',
    },
    {
      code: '+960',
      name: 'Maldives',
    },
    {
      code: '+223',
      name: 'Mali',
    },
    {
      code: '+356',
      name: 'Malta',
    },
    {
      code: '+692',
      name: 'Marshall Islands',
    },
    {
      code: '+596',
      name: 'Martinique',
    },
    {
      code: '+222',
      name: 'Mauritania',
    },
    {
      code: '+230',
      name: 'Mauritius',
    },
    {
      code: '+262',
      name: 'Mayotte',
    },
    {
      code: '+52',
      name: 'Mexico',
    },
    {
      code: '+691',
      name: 'Micronesia',
    },
    {
      code: '+1 808',
      name: 'Midway Island',
    },
    {
      code: '+373',
      name: 'Moldova',
    },
    {
      code: '+377',
      name: 'Monaco',
    },
    {
      code: '+976',
      name: 'Mongolia',
    },
    {
      code: '+382',
      name: 'Montenegro',
    },
    {
      code: '+1664',
      name: 'Montserrat',
    },
    {
      code: '+212',
      name: 'Morocco',
    },
    {
      code: '+95',
      name: 'Myanmar',
    },
    {
      code: '+264',
      name: 'Namibia',
    },
    {
      code: '+674',
      name: 'Nauru',
    },
    {
      code: '+977',
      name: 'Nepal',
    },
    {
      code: '+31',
      name: 'Netherlands',
    },
    {
      code: '+599',
      name: 'Netherlands Antilles',
    },
    {
      code: '+1 869',
      name: 'Nevis',
    },
    {
      code: '+687',
      name: 'New Caledonia',
    },
    {
      code: '+64',
      name: 'New Zealand',
    },
    {
      code: '+505',
      name: 'Nicaragua',
    },
    {
      code: '+227',
      name: 'Niger',
    },
    {
      code: '+234',
      name: 'Nigeria',
    },
    {
      code: '+683',
      name: 'Niue',
    },
    {
      code: '+672',
      name: 'Norfolk Island',
    },
    {
      code: '+850',
      name: 'North Korea',
    },
    {
      code: '+1 670',
      name: 'Northern Mariana Islands',
    },
    {
      code: '+47',
      name: 'Norway',
    },
    {
      code: '+968',
      name: 'Oman',
    },
    {
      code: '+92',
      name: 'Pakistan',
    },
    {
      code: '+680',
      name: 'Palau',
    },
    {
      code: '+970',
      name: 'Palestinian Territory',
    },
    {
      code: '+507',
      name: 'Panama',
    },
    {
      code: '+675',
      name: 'Papua New Guinea',
    },
    {
      code: '+595',
      name: 'Paraguay',
    },
    {
      code: '+51',
      name: 'Peru',
    },
    {
      code: '+63',
      name: 'Philippines',
    },
    {
      code: '+48',
      name: 'Poland',
    },
    {
      code: '+351',
      name: 'Portugal',
    },
    {
      code: '+1 787',
      name: 'Puerto Rico',
    },
    {
      code: '+974',
      name: 'Qatar',
    },
    {
      code: '+262',
      name: 'Reunion',
    },
    {
      code: '+40',
      name: 'Romania',
    },
    {
      code: '+7',
      name: 'Russia',
    },
    {
      code: '+250',
      name: 'Rwanda',
    },
    {
      code: '+685',
      name: 'Samoa',
    },
    {
      code: '+378',
      name: 'San Marino',
    },
    {
      code: '+966',
      name: 'Saudi Arabia',
    },
    {
      code: '+221',
      name: 'Senegal',
    },
    {
      code: '+381',
      name: 'Serbia',
    },
    {
      code: '+248',
      name: 'Seychelles',
    },
    {
      code: '+232',
      name: 'Sierra Leone',
    },
    {
      code: '+65',
      name: 'Singapore',
    },
    {
      code: '+421',
      name: 'Slovakia',
    },
    {
      code: '+386',
      name: 'Slovenia',
    },
    {
      code: '+677',
      name: 'Solomon Islands',
    },
    {
      code: '+27',
      name: 'South Africa',
    },
    {
      code: '+500',
      name: 'South Georgia and the South Sandwich Islands',
    },
    {
      code: '+82',
      name: 'South Korea',
    },
    {
      code: '+34',
      name: 'Spain',
    },
    {
      code: '+94',
      name: 'Sri Lanka',
    },
    {
      code: '+249',
      name: 'Sudan',
    },
    {
      code: '+597',
      name: 'Suriname',
    },
    {
      code: '+268',
      name: 'Swaziland',
    },
    {
      code: '+46',
      name: 'Sweden',
    },
    {
      code: '+41',
      name: 'Switzerland',
    },
    {
      code: '+963',
      name: 'Syria',
    },
    {
      code: '+886',
      name: 'Taiwan',
    },
    {
      code: '+992',
      name: 'Tajikistan',
    },
    {
      code: '+255',
      name: 'Tanzania',
    },
    {
      code: '+66',
      name: 'Thailand',
    },
    {
      code: '+670',
      name: 'Timor Leste',
    },
    {
      code: '+228',
      name: 'Togo',
    },
    {
      code: '+690',
      name: 'Tokelau',
    },
    {
      code: '+676',
      name: 'Tonga',
    },
    {
      code: '+1 868',
      name: 'Trinidad and Tobago',
    },
    {
      code: '+216',
      name: 'Tunisia',
    },
    {
      code: '+90',
      name: 'Turkey',
    },
    {
      code: '+993',
      name: 'Turkmenistan',
    },
    {
      code: '+1 649',
      name: 'Turks and Caicos Islands',
    },
    {
      code: '+688',
      name: 'Tuvalu',
    },
    {
      code: '+1 340',
      name: 'U.S. Virgin Islands',
    },
    {
      code: '+256',
      name: 'Uganda',
    },
    {
      code: '+380',
      name: 'Ukraine',
    },
    {
      code: '+971',
      name: 'United Arab Emirates',
    },
    {
      code: '+44',
      name: 'United Kingdom',
    },
    {
      code: '+1',
      name: 'United States',
    },
    {
      code: '+598',
      name: 'Uruguay',
    },
    {
      code: '+998',
      name: 'Uzbekistan',
    },
    {
      code: '+678',
      name: 'Vanuatu',
    },
    {
      code: '+58',
      name: 'Venezuela',
    },
    {
      code: '+84',
      name: 'Vietnam',
    },
    {
      code: '+1 808',
      name: 'Wake Island',
    },
    {
      code: '+681',
      name: 'Wallis and Futuna',
    },
    {
      code: '+967',
      name: 'Yemen',
    },
    {
      code: '+260',
      name: 'Zambia',
    },
    {
      code: '+255',
      name: 'Zanzibar',
    },
    {
      code: '+263',
      name: 'Zimbabwe',
    },
  ];
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private artistInfoService: ArtistInfoService,
    private artistService: ArtistService,
    private server: CollectorService
  ) {}
  sendOTP() {
    this.OtpSend = true;
  }
  keyUpEvent(index) {
    switch (index) {
      case 1:
        if (this.otp1) this.otp2El.nativeElement.focus();
        else this.otp1El.nativeElement.focus();
        break;
      case 2:
        if (this.otp2) this.otp3El.nativeElement.focus();
        else this.otp2El.nativeElement.focus();
        break;
      case 3:
        if (this.otp3) this.otp4El.nativeElement.focus();
        else this.otp3El.nativeElement.focus();
        break;
      case 4:
        if (this.otp4) this.otp5El.nativeElement.focus();
        else this.otp4El.nativeElement.focus();
        break;
      case 5:
        if (this.otp5) this.otp6El.nativeElement.focus();
        else this.otp5El.nativeElement.focus();
        break;
      default:
        break;
    }
  }
  ngOnInit(): void {
    this.getNationality();
    this.form = this.formBuilder.group({
      firstName: new FormControl('', [Validators.required]),
      middle_name: new FormControl('', [Validators.required]),
      lastName: new FormControl('', [Validators.required]),
      dateOfBirth: new FormControl('', [Validators.required]),
      gender: new FormControl('', [Validators.required]),
      other_gender: new FormControl('', [Validators.required]),
      countrySelected: new FormControl('', [Validators.required]),
      codeSelected: new FormControl('', [Validators.required]),
      mobile: new FormControl('', [Validators.required]),
    });
    // this.getUserDetails()
  }
  openMobModal() {
    this.OtpSend = false;

    this.isPopupOpen = true;
  }
  // to get nationality
  getNationality() {
    // let decode = decodeURIComponent(
    //   escape(window.atob(localStorage.getItem('userDetails')))
    // );
    let url = 'assets/json/country.json';
    this.artistService.getJson(url).subscribe((res) => {
      this.nationalityArr = res;
      // this.userDetailsObj = localStorage.getItem('userDetails')
      //   ? JSON.parse(decode)
      //   : {};
      this.getUserDetails();
    });
  }

  // to get user details
  getUserDetails() {
    let url = apiUrl.getUserDetails;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.userDetailsObj = res.data || res.Data;
        if (this.userDetailsObj['role'] == 'SUPERADMIN') {
          this.userDetailsObj['role_id']['permissions'] = apiUrl.modules;
        }
        let stringify = JSON.stringify(this.userDetailsObj);
        let encode = btoa(unescape(encodeURIComponent(stringify)));
        localStorage.setItem('userDetails', encode);
        this.patchFormValues();
      }
    });
  }

  // to patch values
  patchFormValues() {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    if (localStorage.getItem('userDetails')) {
      this.userDetailsObj = JSON.parse(decode);
      this.form.patchValue({
        firstName: this.userDetailsObj.first_name,
        lastName: this.userDetailsObj.last_name,
        middleName: this.userDetailsObj.middle_name,
        dateOfBirth: this.userDetailsObj.dateOfBirth,
        countrySelected: this.userDetailsObj.country,
        mobile: this.userDetailsObj.mobile,
        gender: this.userDetailsObj.gender.includes('other')
          ? 'other'
          : this.userDetailsObj.gender,
        middle_name: this.userDetailsObj.middle_name,
      });
    }
    this.flag =
      this.nationalityArr.findIndex(
        (x) => x.name == this.userDetailsObj.country
      ) != -1
        ? this.nationalityArr.find(
            (x) => x.name == this.userDetailsObj.country
          )['emoji']
        : '';

    if (this.userDetailsObj.gender.includes('other')) {
      let arr = this.userDetailsObj.gender.split('/');
      this.other_gender = true;
      this.form.patchValue({ other_gender: arr[1] });
    }
  }
  changeFocus(index) {
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }
  changeGender(e) {
    if (e.target.value == 'other') {
      this.other_gender = true;
    } else {
      this.other_gender = false;
    }
  }

  // to update profile
  updateProfile() {
    console.log(this.form.value);
    let req = {
      fullName: this.form.value.firstName + ' ' + this.form.value.lastName,
      first_name: this.form.value.firstName,
      last_name: this.form.value.lastName,
      dateOfBirth: this.form.value.dateOfBirth,
      gender:
        this.form.value.gender != 'male' && this.form.value.gender != 'female'
          ? this.form.value.gender + '/' + this.form.value.other_gender
          : this.form.value.gender,
      country: this.form.value.countrySelected,
      mobile: this.form.value.mobile,
      middle_name: this.form.value.middle_name,
      // "addresses": [
      // 	{
      // 		"name": this.form.value.firstName + ' ' + this.form.value.lastName,
      // 		"phone": this.userDetailsObj.phone || '',
      // 	}
      // ]
    };
    let url = apiUrl.signup;
    this.server.showSpinner();
    this.artistService.putApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        alert('Profile updated successfully!');
        //	this.router.navigate(['/artist-portal/settings']);
      }
    });
  }
}
