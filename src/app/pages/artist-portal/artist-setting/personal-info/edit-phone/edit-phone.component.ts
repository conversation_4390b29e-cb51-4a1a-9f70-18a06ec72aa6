import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistInfoService } from 'src/app/services/artist-info.service';

@Component({
	selector: 'app-edit-phone',
	templateUrl: './edit-phone.component.html',
	styleUrls: ['./edit-phone.component.scss'],
})
export class EditPhoneComponent implements OnInit {
	@ViewChild('input1') otp1El: ElementRef;
	@ViewChild('input2') otp2El: ElementRef;
	@ViewChild('input3') otp3El: ElementRef;
	@ViewChild('input4') otp4El: ElementRef;
	@ViewChild('input5') otp5El: ElementRef;
	@ViewChild('input6') otp6El: ElementRef;

	mobile = '(*************';

	other_gender = false;

	flag = flagData[0].flag;
	code = flagData[0].dial_code;
	number = flagData[0].code;
	flagData = flagData;
	isCancel = false;
	isDropDown = false;
	otp1 = '';
	otp2 = '';
	otp3 = '';
	otp4 = '';
	otp5 = '';
	otp6 = '';
	form: any;

	constructor(
		private router: Router,
		private formBuilder: FormBuilder,
		private artistInfoService: ArtistInfoService
	) { }

	ngOnInit(): void {
		this.form = this.formBuilder.group({
			mobile: new FormControl('', [Validators.required]),
		});
		// this.artistInfoService.getPersonalInfo().subscribe((data) => {
		// 	this.form.patchValue(data);
		// });
	}

	changeGender(e) {
		console.log(e.target.value);
		if (e.target.value == 'other') {
			this.other_gender = true;
		} else {
			this.other_gender = false;
		}
	}

	keyUpEvent(index) {
		switch (index) {
			case 1:
				if (this.otp1) this.otp2El.nativeElement.focus();
				else this.otp1El.nativeElement.focus();
				break;
			case 2:
				if (this.otp2) this.otp3El.nativeElement.focus();
				else this.otp2El.nativeElement.focus();
				break;
			case 3:
				if (this.otp3) this.otp4El.nativeElement.focus();
				else this.otp3El.nativeElement.focus();
				break;
			case 4:
				if (this.otp4) this.otp5El.nativeElement.focus();
				else this.otp4El.nativeElement.focus();
				break;
			case 5:
				if (this.otp5) this.otp6El.nativeElement.focus();
				else this.otp5El.nativeElement.focus();
				break;
			default:
				break;
		}
	}
	saveData() {
		const { mobile } = this.form.value;
		this.artistInfoService.patchPersonalInfo({ mobile }).subscribe((data) => {
			this.router.navigate(['/artist-portal/settings']);
		});
	}
}
