<div class="container__main">
  <div class="heading">
    <a routerLink="/artist-portal/settings"
      ><img src="../../../../../../assets/icons/<EMAIL>" alt=""
    /></a>
    <p class="main__heading">Edit phone</p>
  </div>

  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form" autocomplete="off">
          <div class="field-value">
            <div class="input-container">
              <input
                type="text"
                formControlName="mobile"
                placeholder="(*************"
                id="myInput"
              />
              <img
                src="assets/icons/flag-circle.png"
                alt="image"
                class="placeholder-institution"
              />

              <div class="division"></div>

              <button (click)="isDropDown = !isDropDown">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDown,
                  'dropdown-visible': isDropDown
                }"
              >
                <ul>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                  <li class="data-item">
                    <img
                      src="../../../../../../assets/icons/flag-circle.png"
                      alt="image"
                    />
                    <p>India (+91)</p>
                  </li>
                </ul>
              </div>
            </div>
            <div class="placeholder">Mobile</div>
            <a href="#" class="send">Resend Code</a>
          </div>

          <div class="verify">Verify your phone</div>

          <input
            #input1
            [(ngModel)]="otp1"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(1)"
          />
          <input
            #input2
            [(ngModel)]="otp2"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(2)"
          />
          <input
            #input3
            [(ngModel)]="otp3"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(3)"
          />
          <input
            #input4
            [(ngModel)]="otp4"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(4)"
          />
          <input
            #input5
            [(ngModel)]="otp5"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(5)"
          />
          <input
            #input6
            [(ngModel)]="otp6"
            class="partitioned"
            type="text"
            [maxLength]="1"
            (ngModelChange)="keyUpEvent(6)"
          />
          <div class="last-changed">
            A six digit code has been sent to your phone
          </div>

          <div class="buttonList">
            <button (click)="saveData()" class="save">Save</button>
            <a routerLink="/artist-portal/settings"
              ><button class="cancel">Cancel</button></a
            >
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
