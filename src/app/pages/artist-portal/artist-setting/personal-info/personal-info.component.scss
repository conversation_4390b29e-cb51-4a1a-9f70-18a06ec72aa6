.container__main {
	//   padding-right: calc(33.263vw - var(--page-default-margin));
	padding-bottom: 6.3vw;
	@media (max-width: 768px) {
		padding-bottom: 28.019vw;
	}
	.division {
		height: 0.069vw;
		width: 100%;
		background-color: #ced4db;
	}
	.main__heading {
		font-size: 1.666vw;
		margin-bottom: 1.042vw;
		color: #000;
		@media (max-width: 768px) {
			font-size: 5.79vw;
			margin-bottom: 3.62vw;
		}
	}
	.sub__text {
		font-size: 0.972vw;
		color: #000;
		@media (max-width: 768px) {
			font-size: 3.38vw;
			margin-bottom: 7.24vw;
		}
		font-weight: 500;
	}
	.content-section {
		width: 100%;
		display: flex;
		justify-content: space-between;
		
		margin-top: 2.083vw;
		@media (max-width: 768px) {
			margin-top: 7.24vw;
		}
		.content {
			float: left;
			flex-grow: 1;
			.wrapper {
				margin-bottom: 2.083vw;
				@media (max-width: 768px) {
					margin-bottom: 7.24vw;
				}
			}
			.label {
				font-family: var(--primary-font);
				font-size: 1.388vw;
				@media (max-width: 768px) {
					font-size: 4.83vw;
					margin-bottom: 3.62vw;
				}
				font-weight: 500;
			}
			.element {
				font-family: var(--secondary-font);
				font-size: 1.25vw;
				@media (max-width: 768px) {
					font-size: 4.34vw;
				}
				img {
					width: 1.25vw;
					height: 1.25vw;
					@media (max-width: 768px) {
						width: 4.34vw;
						height: 4.34vw;
					}
				}
				.divider {
					display: inline-block;
					width: 0.069vw;
					height: 1.041vw;
					background-color: var(--timeline-color);
					margin-left: 0.6944vw;
					margin-right: 0.416vw;
					@media (max-width: 768px) {
						width: 0.24vw;
						height: 3.38vw;
						margin-left: 2.41vw;
						margin-right: 2.41vw;
					}
				}
			}
			.last-changed {
				font-family: var(--primary-font);
				font-size: 0.972vw;
				color: var(--quaternary-font-color);
				@media (max-width: 768px) {
					font-size: 3.38vw;
				}
			}
		}
		a{
			float: right;
		img {
			width: 2.847vw;
			height: 2.847vw;
			@media (max-width: 768px) {
				width: 9.66vw;
				height: 9.66vw;
			}
		}	
		}
		
	}
}
