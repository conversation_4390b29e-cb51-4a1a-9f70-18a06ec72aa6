import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-edit-personal',
  templateUrl: './edit-personal.component.html',
  styleUrls: ['./edit-personal.component.scss'],
})
export class EditPersonalComponent implements OnInit, OnDestroy {
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  routerObserver: Subscription;
  activeMenu = 'main';

  ngOnDestroy(): void {
    // this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/edit-personal':
        this.activeMenu = 'Personal Information';
        break;

      case '/artist-portal/settings/edit-personal/id-proof':
        this.activeMenu = 'ID Proof';
        break;
      case '/artist-portal/settings/edit-personal/financial':
        this.activeMenu = 'Financial Information';
        break;
      case '/artist-portal/settings/edit-personal/bank':
        this.activeMenu = 'Bank Details';
        break;
      case '/artist-portal/settings/edit-personal/tax':
        this.activeMenu = 'Tax Details';
        break;

      default:
        this.activeMenu = 'PersonalInformation';
        break;
    }
  }

  // onMenuClick(index) {
  //   console.log(index);
  //   switch (index) {
  //     case 0:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/main']);
  //       break;
  //     case 1:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/add-on']);
  //       break;
  //     default:
  //       break;
  //   }
  // }
}
