<div>
    <div class="d-flex">
      <div class="section left-container" style="width: 100%">
        <div class="section-inner">
          <div class="d-flex justify-content-start slider-header">
            <a [routerLink]="'/artist-portal/settings/edit-personal'">
              <h3
                [ngClass]="{
                  active:
                  activeMenu ===
                      'Personal Information'
                }"
              >
              Personal Information
              </h3>
            </a>
            <a [routerLink]="'/artist-portal/settings/edit-personal/id-proof'"
              ><h3
                [ngClass]="{
                  active:
                  activeMenu ===
                    'ID Proof'
                }"
              >
              ID Proof
              </h3></a
            >
            <!-- <a [routerLink]="'/artist-portal/settings/edit-personal/financial'">
              <h3
                [ngClass]="{
                  active:
                  activeMenu ===
                      'Financial Information'
                }"
              >
              Financial Information
              </h3>
            </a> -->
            <a [routerLink]="'/artist-portal/settings/edit-personal/bank'">
              <h3
                [ngClass]="{
                  active:
                  activeMenu ===
                      'Bank Details'
                }"
              >
              Bank Details
              </h3>
            </a>
            <a [routerLink]="'/artist-portal/settings/edit-personal/tax'">
              <h3
                [ngClass]="{
                  active:
                  activeMenu ===
                      'Tax Details'
                }"
              >
              Tax Details
              </h3>
            </a>
            
          </div>
          <div class="content-inside" #scrollTop>
            <router-outlet></router-outlet>
          </div>
        </div>
      </div>
    </div>
  </div>
  