import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { Web3Service } from 'src/app/shared/services/web3.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../services/artist.service';

@Component({
  selector: 'app-approve-wallet',
  templateUrl: './approve-wallet.component.html',
  styleUrls: ['./approve-wallet.component.scss'],
})
export class ApproveWalletComponent implements OnInit {
  limit: any = 10;
  offset: any = 0;
  userList: any = [];
  isPopupOpen: boolean = false;
  selectedUserId: any;
  modulesArr: any = [];
  selectedRoleId: any;
  total: any = 0;
  status: any = 'ACTIVE';

  networks = {
    ethereum_main_net: 1,
    polygon: 137,
    goerli_testnet: 5,
    polygon_testnet: 80001,
  };

  checkingAdminAccess = false;
  notAccess = false;

  transactionSubmitted = false;

  isSaving = false;

  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private web3Service: Web3Service
  ) {}

  ngOnInit(): void {
    this.getUsers();
  }

  // to get users
  getUsers() {
    this.userList = [];
    let url = 'wallet-whitelist' + `?limit=${this.limit}&offset=${this.offset}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.userList = res.data;
        // this.total = res.total;
      }
    });
  }

  // to manage pagination
  managePagination(dir) {
    if (dir) {
      this.offset += this.limit;
      this.getUsers();
    } else {
      this.offset -= this.limit;
      this.getUsers();
    }
  }

  rejectWallet(index) {
    let url = 'wallet-whitelist';
    this.server.showSpinner();
    let data = this.userList[index];
    data.status = 'REJECTED';
    this.server.postApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.getUsers();
        alert('Success');
        // this.total = res.total;
      }
    });
  }

  approveWallet(index) {
    this.checkingAdminAccess = true;
    // let url = 'wallet-whitelist';
    // this.server.showSpinner();

    let data = this.userList[index];
    // data.status = 'APPROVED';
    // this.server.postApi(url, data).subscribe((res) => {
    //   this.server.hideSpinner();
    //   if (res.statusCode == 200) {
    //     this.getUsers();
    //     alert('Success');
    //     // this.total = res.total;
    //   }
    // });

    let subscription = this.web3Service.accountStatus$.subscribe((next) => {
      console.log(next);

      const address = next[0];
      if (address) {
        subscription.unsubscribe();

        subscription = this.web3Service.networkStatus$.subscribe(
          async (next) => {
            console.log(next);
            if (next == this.networks[data.network]) {
              subscription.unsubscribe();
              const isApprovalAccess = await this.web3Service.checkAdmin();
              this.checkingAdminAccess = false;
              console.log(isApprovalAccess);

              if (isApprovalAccess) {
                this.transactionSubmitted = true;
                const isSuccess = await this.web3Service.grantMinters(
                  data.address
                );
                if (isSuccess) {
                  this.transactionSubmitted = false;
                  this.isSaving = true;
                  let url = 'wallet-whitelist';
                  this.server.showSpinner();
                  data.status = 'APPROVED';
                  this.server.postApi(url, data).subscribe((res) => {
                    this.server.hideSpinner();
                    if (res.statusCode == 200) {
                      this.isSaving = false;
                      this.getUsers();
                      alert('Success');
                      // this.total = res.total;
                    }
                  });
                }
              } else {
                this.notAccess = true;
              }
            }
          }
        );

        this.web3Service.changeNetwork(data.network);
      }
    });
    this.web3Service.connectAccount();
  }

  // to confirm activate user
  confirmActivate() {
    let url = apiUrl.approveUser + `/${this.selectedUserId}`;
    // let data = {
    //   "user_id": this.selectedUserId,
    //   "status": "ACTIVE"
    // }
    this.server.showSpinner();
    this.server.getApi(url).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          alert(res.message || res.Message);
          this.offset = 1;
          this.getUsers();
        } else {
          alert(res.Message || res.message);
        }
      },
      (err) => {
        alert(err.error.Message || err.error.message);
      }
    );
  }
}
