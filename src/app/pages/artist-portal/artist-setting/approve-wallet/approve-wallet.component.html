<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form>
          <div class="field-value doted">
            <div>
              <div>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Email</th>
                      <th class="text-center">Address</th>
                      <th class="text-center">Network</th>
                      <th class="text-center">Action</th>
                    </tr>
                  </thead>

                  <tbody id="MyTable">
                    <tr *ngFor="let item of userList; let i = index">
                      <td>{{ item?.email }}</td>
                      <td class="text-center">{{ item?.address }}</td>
                      <td class="text-center">{{ item?.network }}</td>
                      <td *ngIf="item?.status == 'PENDING'" class="text-center">
                        <button class="btn-success" (click)="approveWallet(i)">
                          Approve
                        </button>
                        <button class="btn-danger" (click)="rejectWallet(i)">
                          Reject
                        </button>
                      </td>
                      <td
                        *ngIf="item?.status == 'REJECTED'"
                        class="text-center"
                      >
                        <button class="btn-info">REJECTED</button>
                      </td>
                      <td
                        *ngIf="item?.status == 'APPROVED'"
                        class="text-center"
                      >
                        <button class="btn-info">APPROVED</button>
                      </td>
                    </tr>
                    <tr *ngIf="userList.length == 0">
                      <td class="text-center" colspan="3">No record found!</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="pagination">
            <li class="pagination-next">
              <a
                (click)="managePagination(false)"
                style="color: #467ffb; cursor: pointer; margin-right: 1vw"
              >
                Previous</a
              >
              <a
                (click)="managePagination(true)"
                style="color: #467ffb; cursor: pointer"
              >
                Next</a
              >
            </li>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div *ngIf="checkingAdminAccess" class="modelForCreate">
    <div class="Content-Box">
      <span class="modHead">Checking Admin status, please wait. </span>
    </div>
  </div>
  <div *ngIf="isSaving" class="modelForCreate">
    <div class="Content-Box">
      <span class="modHead">Saving Details to DB, please wait. </span>
    </div>
  </div>
  <div *ngIf="notAccess" class="modelForCreate">
    <div class="Content-Box">
      <span class="modHead">Your wallet address has no admin access. </span>
    </div>
    <div class="btnn">
      <button (click)="notAccess = false" class="simpe-type">Cancel</button>
    </div>
  </div>
  <div class="modelForCreate" *ngIf="transactionSubmitted">
    <div class="Content-Box">
      <span class="modHead"
        >Approval initiated, awaiting confirmation from the blockchain network.
        Please do not close the pop-up or refresh the page.</span
      >

      <div class="btnn"></div>
    </div>
  </div>
</div>
