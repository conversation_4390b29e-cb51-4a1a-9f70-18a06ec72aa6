import { getTestBed } from '@angular/core/testing';
import { Data } from './../../../../core/accordion/accordion.model';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { CollectorService } from 'src/app/services/collector.service';

import {
  FooterService,
  FooterType,
} from 'src/app/shared/services/footer.service';
import { EditNotifyService } from './edit-notify.service';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
@Component({
  selector: 'app-new-artworks',
  templateUrl: './new-artworks.component.html',
  styleUrls: ['./new-artworks.component.scss'],
})
export class NewArtworksComponent implements OnInit {
  @ViewChild('scrollTop') scrollTop: ElementRef;
  editedMenu;
  promtVar;
  mobile = '(*************';
  flag = flagData[0].flag;
  code = flagData[0].dial_code;
  number = flagData[0].code;
  flagData = flagData;
  isCancel = false;
  isDropDown = false;
  isDropDown2 = false;
  form: FormGroup;
  id;
  activePath;
faArrowAltCircleLeft=faArrowAltCircleLeft;
  selectedFiles: File[] = [];
  permissionsObj: any = {};
  selectedTab: any = 'Main';
  constructor(
    private router: Router,
    private server: CollectorService,
    private footerService: FooterService,
    private editNotifyService: EditNotifyService
  ) {}
  ngOnDestroy(): void {
    // this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.editNotifyService.data.subscribe((data) => {
      this.editedMenu = data;
    });
    // this.editedMenu
    // this.editNotifyService.get
    // this.route.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 	} else {
    // 		this.id = null;
    // 	}
    // });
    // this.activePath = this.router.url;
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Artworks'
    );
    console.log(this.permissionsObj);
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.scrollTop.nativeElement.scrollTo(0, 0);
        this.activePath = event.urlAfterRedirects;
      }
    });
    // this.form = this.formBuilder.group({
    // 	title: new FormControl('', [Validators.required]),
    // 	year: new FormControl('', [Validators.required]),
    // 	dimensions: new FormControl('', [Validators.required]),
    // 	medium: new FormControl('', [Validators.required]),
    // 	height: new FormControl('', [Validators.required]),
    // 	width: new FormControl('', [Validators.required]),
    // 	depth: new FormControl('', [Validators.required]),
    // 	type: new FormControl('', [Validators.required]),
    // 	priceList: new FormControl('', [Validators.required]),
    // });
  }

  PromptSave(name) {
    this.promtVar = false;
    // if (this.promtVar) {
    for (const property in this.editedMenu) {
      if (this.editedMenu[property] == true) {
        this.promtVar = true;
        break;
      }
    }
    if (this.promtVar == true) {
      if (
        confirm(
          'There is unsaved content on this tab, are you sure you wish to continue without saving?'
        )
      ) {
        this.editNotifyService.reset();
        this.onMenuClick(name);
      }
    } else {
      this.onMenuClick(name);
    }
  }

  // addItem(newItem: string) {
  //  console.log(newItem);
  // }

  // async onFileSelect(files: FileList) {
  // 	if (files[0].size < 2100000) {
  // 		this.selectedFiles.push(files[0]);
  // 	}
  // }
  // removeItem(index) {
  // 	this.selectedFiles.splice(index, 1);
  // }

  // select(i) {
  // 	switch (i) {
  // 		case 1:
  // 			this.form.get('type').setValue('3D');
  // 			break;
  // 		case 2:
  // 			this.form.get('type').setValue('NFTs');
  // 			break;

  // 		default:
  // 			break;
  // 	}
  // }
  // submit() {
  // 	const data = this.form.value;
  // 	data.priceList = [{ price: data.priceList }];
  // 	console.log(data);

  // 	this.artistInfoService.addArtwork(data, this.selectedFiles).subscribe();
  // }
  // onNext() {
  // 	this.artworkService.navToNext(this.activePath);
  // }

  onMenuClick(name) {
    this.selectedTab = name;
    switch (name) {
      case 'Main':
        this.router.navigate(['/artist-portal/settings/artwork/add']);
        break;
      case 'Details':
        this.router.navigate(['/artist-portal/settings/artwork/add/details']);
        break;
      case 'Features':
        this.router.navigate(['/artist-portal/settings/artwork/add/features']);
        break;
      case 'Financial':
        this.router.navigate(['/artist-portal/settings/artwork/add/financial']);
        break;
      case 'Media':
        this.router.navigate(['/artist-portal/settings/artwork/add/media']);
        break;
      case 'Provenance':
        this.router.navigate([
          '/artist-portal/settings/artwork/add/provenance',
        ]);
        break;
      case 'Shipping':
        this.router.navigate(['/artist-portal/settings/artwork/add/shipping']);
        break;
      case 'Tagging':
        this.router.navigate(['/artist-portal/settings/artwork/add/tagging']);
        break;
      case 'Edition':
        this.router.navigate(['/artist-portal/settings/artwork/add/edition']);
        break;
      case 'Publish':
        this.router.navigate(['/artist-portal/settings/artwork/add/publish']);
        break;
      case 'SEO':
        this.router.navigate(['/artist-portal/settings/artwork/add/seo']);
        break;
      default:
        this.router.navigate(['/artist-portal/settings/artwork/add']);
        break;
    }
  }
}
