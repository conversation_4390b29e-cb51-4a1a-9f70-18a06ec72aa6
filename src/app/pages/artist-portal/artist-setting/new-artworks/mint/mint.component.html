<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div class="field-value" style="width: 49%">
              <div
                class="input-container"
                style="
                  flex-direction: column;
                  justify-content: unset;
                  align-items: unset;
                "
              >
                <div class="text-before" style="margin-bottom: 0.5vw">
                  Publish Artwork:
                </div>
                <label class="switch">
                  <input formControlName="published" type="checkbox" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to publish the artwork to the site
              </div>
            </div>
            <div
              class="field-value"
              style="
                width: 51%;
                border: 1px solid gray;
                padding: 1vw 0.5vw;
                border-radius: 10px;
                background: #f1eded;
              "
            >
              <div class="input-container" style="">
                <div class="text-before" style="font-size: 0.95vw">
                  Wallet Address:
                </div>
                <span class="">
                  {{ userDetailsObj?.ethereum_account }}
                </span>
                <fa-icon
                  (click)="server.copyMessage(userDetailsObj.ethereum_account)"
                  style="cursor: pointer"
                  title="Copy Address"
                  [icon]="faCopy"
                ></fa-icon>
              </div>
              <div class="input-container mt10">
                <!-- <div class="text-before" style="font-size: 0.95vw">
                  Wallet Balance:
                  {{ userDetailsObj?.ethereum_balance || 0 }}
                  {{ form.value.network == "matic" ? " Matic" : " rETH" }}
                  <span
                    class="update"
                    (click)="
                      getBalance(
                        userDetailsObj.ethereum_account,
                        form.value.network
                      )
                    "
                    >Update</span
                  >
                </div> -->
                <!-- <label class="switch"></label> -->
              </div>
              <!-- <div class="splitter">
                <div class="field-value">

                </div>
              </div> -->
            </div>
          </div>

          <!-- <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="name"
                type="text"
                placeholder="NFT Name"
              />
              <div class="placeholder">NFT Name</div>
              <div class="input-info">Provide a name for your NFT</div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="symbol"
                type="text"
                placeholder="Symbol"
              />
              <div class="placeholder">Symbol</div>
              <div class="input-info">Provide a symbol for your NFT</div>
            </div>
          </div> -->
          <div *ngIf="!artworksObj?.nft?.id">
            <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">
                    Are you minting on Terrain.art, or are you listing an NFT
                    that's minted on another platform?
                  </div>
                </div>
              </div>
            </div>
            <div class="splitter">
              <div class="field-value">
                <div class="gender-inner">
                  <label class="check-label">
                    <input
                      formControlName="platform"
                      type="radio"
                      value="minting_on_terrain"
                    />
                    <span class="check"></span>
                    <span class="title">Minting on Terrain.art</span>
                  </label>
                  <label class="check-label">
                    <input
                      formControlName="platform"
                      type="radio"
                      value="other_platform"
                      disabled
                    />
                    <span class="check"></span>
                    <span class="title"><del>Other platform</del></span>
                  </label>
                </div>
              </div>
            </div>

            <div
              class="Other-platform-section"
              [hidden]="form.get('platform').value == 'minting_on_terrain'"
            >
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <input type="text" placeholder="NFT/Token Id" />
                  <div class="placeholder">NFT/Token Id</div>
                  <div class="input-info">Provide the NFT/Token Id</div>
                </div>
                <div class="field-value">
                  <input type="text" placeholder="Contract Address" />
                  <div class="placeholder">Contract Address:</div>
                  <div class="input-info">Provide Contract Address</div>
                </div>
              </div>
              <div class="splitter">
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">Network minted on ?</div>
                  </div>
                </div>
              </div>
              <div class="">
                <div class="field-value">
                  <div class="gender-inner">
                    <label class="check-label">
                      <input
                        formControlName="Other_Platform_Network"
                        type="radio"
                        value="binance"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title">Binance</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="Other_Platform_Network"
                        type="radio"
                        value="matic"
                      />
                      <span class="check"></span>
                      <span class="title">Polygon</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="Other_Platform_Network"
                        type="radio"
                        value="ethereum_main_net"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title">Ethereum Main Net</span>
                    </label>

                    <label class="check-label">
                      <input
                        formControlName="Other_Platform_Network"
                        type="radio"
                        value="ropsten"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title"> <del>Ropsten Testnet</del></span>
                    </label>

                    <label class="check-label">
                      <input
                        formControlName="Other_Platform_Network"
                        type="radio"
                        value="solana"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title">Solana</span>
                    </label>
                  </div>
                </div>
              </div>
              <div
                class="buttonList"
                style="margin-top: 2.042vw; margin-bottom: 0"
              >
                <button
                  class="save"
                  style="width: 24%; padding: 0.833333vw 3.08333vw"
                >
                  Search for token
                </button>
              </div>

              <div class="splitter">
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">Platform: <b>Template</b></div>
                  </div>
                </div>
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">Network: <b>Solana</b></div>
                  </div>
                </div>
              </div>
              <div
                class="field-value"
                style="display: flex; align-items: flex-start"
              >
                <input
                  type="checkbox"
                  style="margin-right: 1vw; margin-top: 0.25vw"
                  value="1"
                />
                <label style="width: 70%"
                  >I confirm that this NFT minted on &lt; platform &#62; is my
                  original work, and that I am choosing to list the work on
                  Terrain.art. Once the work has been sold, it is my
                  responsibility to transfer the NFT to the buyer.</label
                >
              </div>

              <div
                class="buttonList"
                style="margin-top: 2.042vw; margin-bottom: 0"
              >
                <button
                  class="save"
                  style="width: 35%; padding: 0.833333vw 3.08333vw"
                >
                  List NFT | Remove from Listing
                </button>
              </div>
            </div>

            <div
              class="terrain-platform-section"
              [hidden]="form.get('platform').value == 'other_platform'"
            >
              <div class="splitter">
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">
                      How would you like to mint your NFT?
                    </div>
                  </div>
                </div>
              </div>

              <div class="">
                <div class="field-value">
                  <div class="gender-inner">
                    <label class="check-label">
                      <input
                        formControlName="mintType"
                        type="radio"
                        value="ERC-721"
                      />
                      <span class="check"></span>
                      <span class="title">Standard ERC-721 NFT</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="mintType"
                        type="radio"
                        value="ERC-1155"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title"
                        ><del>Editions NFT ERC-1155</del></span
                      >
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="mintType"
                        type="radio"
                        value="fractional"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title"><del>Fractional NFT</del></span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="mintType"
                        type="radio"
                        value="layered"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title"><del>Layered NFT</del></span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="mintType"
                        type="radio"
                        value="sectional"
                        disabled
                      />
                      <span class="check"></span>
                      <span class="title"><del>Sectional NFT</del></span>
                    </label>
                  </div>
                </div>
              </div>
              <div [hidden]="!form.get('mintType').value" class="splitter">
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">
                      Which network do you wish to mint on?
                    </div>
                  </div>
                </div>
              </div>
              <div [hidden]="!form.get('mintType').value" class="">
                <div class="field-value">
                  <div class="gender-inner">
                    <label class="check-label">
                      <input
                        formControlName="network"
                        type="radio"
                        value="ethereum_main_net"
                        (change)="walletConnected = false"
                      />
                      <span class="check"></span>
                      <span class="title">Ethereum Main Net</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="network"
                        type="radio"
                        value="polygon"
                        (change)="walletConnected = false"
                      />
                      <span class="check"></span>
                      <span class="title">Polygon</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="network"
                        type="radio"
                        value="goerli_testnet"
                        (change)="walletConnected = false"
                      />
                      <span class="check"></span>
                      <span class="title">Goerli Testnet</span>
                    </label>

                    <label class="check-label">
                      <input
                        formControlName="network"
                        type="radio"
                        value="polygon_testnet"
                        (change)="walletConnected = false"
                      />
                      <span class="check"></span>
                      <span class="title">Polygon Testnet</span>
                    </label>
                  </div>
                </div>
              </div>
              <!-- <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">Wallet Balance:</div>
                  <label class="switch"> 0 </label>
                </div>
              </div>
            </div> -->
              <div [hidden]="!form.get('network').value" class="splitter">
                <div class="field-value">
                  <div class="input-container">
                    <div class="text-before">
                      Do you wish to mint the NFT now, or prefer Lazy Minting
                      (minting at the time of sale)?
                    </div>
                  </div>
                </div>
              </div>
              <div [hidden]="!form.get('network').value" class="splitter">
                <div class="field-value">
                  <div class="gender-inner">
                    <label class="check-label">
                      <input
                        formControlName="preferType"
                        type="radio"
                        name="preferType"
                        value="mint_now"
                      />
                      <span class="check"></span>
                      <span class="title">Mint Now</span>
                    </label>
                    <label class="check-label">
                      <input
                        formControlName="preferType"
                        type="radio"
                        name="preferType"
                        value="lazy_minting"
                      />
                      <span class="check"></span>
                      <span class="title">Lazy Minting</span>
                    </label>
                  </div>
                </div>
              </div>

              <div
                [hidden]="form.get('preferType').value !== 'mint_now'"
                class="buttonList"
                style="margin-top: 2.042vw; margin-bottom: 0"
              >
                <!-- metamask -->
                <!-- <div *ngIf="!isCreator">Only Artist can mint this artwork</div> -->
                <!-- <div *ngIf="isCreator"> -->
                <button
                  *ngIf="selectedOption === 0"
                  class="save"
                  style="width: 35%; padding: 0.833333vw 3.08333vw"
                  (click)="onboardingMeta()"
                >
                  Install Metamask
                </button>
                <button
                  *ngIf="selectedOption === 1 && !walletConnected"
                  class="save"
                  style="width: 35%; padding: 0.833333vw 3.08333vw"
                  (click)="loginWithMeta()"
                >
                  Connect Metamask
                </button>
                <button
                  *ngIf="
                    selectedOption === 1 &&
                    walletConnected &&
                    isMiningAccess == 2 &&
                    !isWaitMiniting &&
                    !isMinted
                  "
                  class="save"
                  style="width: 35%; padding: 0.833333vw 3.08333vw"
                  (click)="mintNFT()"
                >
                  Mint Now
                </button>
                <!-- <div *ngIf="walletConnected && !isMiningAccess">
                  You don't have minting access, <NAME_EMAIL>
                </div>
                <div *ngIf="isWaitMiniting">
                  Waiting for blockchain confirmation, don't close the window...
                </div>
                <div *ngIf="isMinted">
                  NFT minted, Id: {{ nftDetailsObj.id }}
                </div> -->
                <!-- </div> -->
                <!-- <button

                class="save"
                style="width: 35%; padding: 0.833333vw 3.08333vw"
                (click)="openConfirmationModal()"
              >
                Mint NFT
              </button> -->
              </div>
              <div
                class="NFT-infos"
                [hidden]="!this.artworksObj?.nft?.nftAddress"
              >
                <div class="field-value">
                  <div class="input-container" style="margin-top: 1vw">
                    <div class="text-before">NFT ID :</div>
                    <label class="switch" style="width: unset">
                      NFT#1231242345
                    </label>
                  </div>
                </div>
                <div class="field-value" style="margin-top: 0.2vw">
                  <div class="input-container">
                    <div class="text-before">Contract Address :</div>
                    <label class="switch" style="width: unset">
                      Contract Address
                    </label>
                  </div>
                </div>
                <div class="field-value" style="margin-top: 0.2vw">
                  <div class="input-container">
                    <div class="text-before">Transaction Hash:</div>
                    <label class="switch" style="width: unset"> Hash </label>
                  </div>
                </div>
                <div class="field-value" style="margin-top: 0.2vw">
                  <div class="input-container">
                    <div class="text-before">Current Owner:</div>
                    <label class="switch" style="width: unset"> Owner </label>
                  </div>
                </div>
                <div class="field-value" style="margin-top: 0.2vw">
                  <div class="input-container">
                    <a
                      [href]="
                        this.artworksObj?.network == 'matic'
                          ? 'https://mumbai.polygonscan.com/address/' +
                            this.artworksObj?.nft?.nftAddress
                          : 'https://ropsten.etherscan.io/address' +
                            this.artworksObj?.nft?.nftAddress
                      "
                      target="_blank"
                    >
                      <label class="switch" style="width: unset">
                        View on Blockchain
                      </label></a
                    >
                  </div>
                </div>
              </div>

              <div
                [hidden]="!isArtworkMinted"
                class="buttonList"
                style="margin-top: 2.042vw; margin-bottom: 0"
              >
                <button
                  class="save"
                  style="width: 35%; padding: 0.833333vw 3.08333vw"
                  (click)="isWithdrawConfirmationOpen = true"
                >
                  Withdraw from Sale
                </button>
              </div>
            </div>
          </div>
          <div *ngIf="artworksObj?.nft?.id">
            <div class="splitter">
              <div class="field-value">
                <div class="input-container">
                  <div class="text-before">
                    NFT Id:
                    {{ nft_name(nftDetailsObj.network, nftDetailsObj.id)
                    }}<br />
                    Contract: {{ nftDetailsObj.contract }} <br />
                    Minted by: {{ nftDetailsObj.minter }} <br />
                    Current Owner:
                    {{ currentOwner }}
                    <br />
                    Network: {{ artworksObj.network }}
                  </div>
                </div>
              </div>
            </div>
            <div
              class="buttonList"
              style="margin-top: 2.042vw; margin-bottom: 0"
            >
              <button
                *ngIf="selectedOption === 0"
                class="save"
                style="width: 35%; padding: 0.833333vw 3.08333vw"
                (click)="onboardingMeta()"
              >
                Install Metamask
              </button>
              <button
                *ngIf="selectedOption === 1 && !walletConnected"
                class="save"
                style="width: 35%; padding: 0.833333vw 3.08333vw"
                (click)="loginWithMeta()"
              >
                Connect Metamask
              </button>
              <button
                *ngIf="
                  selectedOption === 1 &&
                  walletConnected &&
                  userDetailsObj['ethereum_account'] == currentOwner
                "
                class="save"
                style="width: 35%; padding: 0.833333vw 3.08333vw"
                (click)="isTransferPopup = true"
              >
                Transfer Now
              </button>
            </div>
          </div>

          <!-- <div class="splitter">
			  <div class="field-value" style="padding-right: 0.5vw" [hidden]="!showObj?.createdAs">
				<div class="input-container">
				  <input
					formControlName="createdAs"
					type="text"
					class="selection"
					placeholder="Created"
					(focus)="isDropDownOpen[0] = true"
					(focusout)="changeFocus(0)"
				  />
				  <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
					<img src="assets/icons/arrow-down.png" class="flag-arrow" />
				  </button>
				  <div
					[ngClass]="{
					  'dropdown-hidden': !isDropDownOpen[0],
					  'dropdown-visible': isDropDownOpen[0]
					}"
				  >
					<ul>
					  <li
						(click)="
						  form.get('createdAs').setValue('individually');
						  isDropDownOpen[0] = false
						"
					  >
						<div class="country-name">individually</div>
					  </li>
					  <li
						(click)="
						  form.get('createdAs').setValue('collaboratively');
						  isDropDownOpen[0] = false
						"
					  >
						<div class="country-name">collaboratively</div>
					  </li>
					</ul>
				  </div>
				</div>
				<div class="input-info">
				  Choose whether the artwork was created individually or in
				  collaboration with another artist
				</div>
			  </div>
			  <div
				*ngIf="form.get('createdAs').value == 'Collaboratively'"
				class="field-value"
			  >
				<input
				  formControlName="createdBy"
				  type="text"
				  placeholder="Only if applicable"
				/>
				<div class="placeholder">Created By</div>
				<div class="input-info"></div>
			  </div>
			</div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.title"
            >
              <input type="text" placeholder="NFT/Token Id" />
              <div class="placeholder">NFT/Token Id</div>
              <div class="input-info">
                Provide the complete title of the artwork
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.year">
              <input type="text" placeholder="Year" />
              <div class="placeholder">Year</div>
              <div class="input-info">
                Provide year (e.g.: 2018) or range (e.g.: 2016-2018)
              </div>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.title"
            >
              <input type="text" placeholder="NFT/Token Id" />
              <div class="placeholder">NFT/Token Id</div>
              <div class="input-info">
                Provide the complete title of the artwork
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.year">
              <input type="text" placeholder="Year" />
              <div class="placeholder">Year</div>
              <div class="input-info">
                Provide year (e.g.: 2018) or range (e.g.: 2016-2018)
              </div>
            </div>
          </div> -->
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
            <div (click)="getValueWithAsync()" class="next">Save & Close</div>
            <div [hidden]="true" class="next" style="margin-right: 1.5vw">
              Duplicate
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--  -->

<div class="popup-for-confirmation" [hidden]="!isMintConfirmationOpen">
  <div class="popup-card">
    <p style="font-size: 1.25vw">
      Please confirm before publishing NFT for sale. Ownership of the NFT gets
      transferred to the sale contract, and will be transferred to the buyer on
      confirmation of payment.
    </p>
    <!-- <div class="tab-content nft">
      <div class="nft-details">
        <div class="item">
          <div class="title">Estimated Gas Cost:</div>
          <div class="value">0.012 ETH</div>
        </div>
        <div class="item">
          <div class="title">Estimated transaction time:</div>
          <div class="value">2 Minutes</div>
        </div>
      </div>
    </div> -->

    <div class="boxed-tab">
      <div class="tab" [hidden]="form.get('mintType').value !== 'fractional'">
        <div (click)="nftAccordion[2] = !nftAccordion[2]" class="tab-head">
          <div>
            <fa-icon [icon]="faHammer" style="padding-right: 0.5vw"></fa-icon>
            Fraction Details
          </div>
          <div>
            <fa-icon
              *ngIf="nftAccordion[2]"
              [icon]="faChevronUp"
              style="padding-left: 0.5vw"
            ></fa-icon>
            <fa-icon
              *ngIf="!nftAccordion[2]"
              [icon]="faChevronDown"
              style="padding-left: 0.5vw"
            ></fa-icon>
          </div>
        </div>
        <div *ngIf="nftAccordion[2] && !tokenURL" class="tab-content nft">
          <div class="nft-details">
            <div class="item" style="margin-top: 0.5vw">
              <div class="title">Supply</div>
              <div class="value">
                <input
                  type="number"
                  [(ngModel)]="fractionalObj.supply"
                  (keyup)="layeredSupply($event)"
                  (change)="layeredSupply($event)"
                  min="1"
                  [ngModelOptions]="{ standalone: true }"
                  name="Supply"
                  id="Supply"
                />
              </div>
            </div>
            <!-- <div class="item" style="margin-top: 0.5vw">
              <div class="title">Reserve Price</div>
              <div class="value">
                <input type="number" name="ReservePrice" id="ReservePrice" />
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <div class="tab" [hidden]="form.get('mintType').value !== 'sectional'">
        <div (click)="nftAccordion[3] = !nftAccordion[3]" class="tab-head">
          <div>
            <fa-icon [icon]="faHammer" style="padding-right: 0.5vw"></fa-icon>
            Sectional Details
          </div>
          <div>
            <fa-icon
              *ngIf="nftAccordion[3]"
              [icon]="faChevronUp"
              style="padding-left: 0.5vw"
            ></fa-icon>
            <fa-icon
              *ngIf="!nftAccordion[3]"
              [icon]="faChevronDown"
              style="padding-left: 0.5vw"
            ></fa-icon>
          </div>
        </div>
        <div *ngIf="nftAccordion[3] && !tokenURL" class="tab-content nft">
          <div class="nft-details">
            <div class="item" style="margin-top: 0.5vw">
              <div class="title">Supply</div>
              <div class="value">
                <input
                  type="number"
                  (keyup)="layeredSupply($event)"
                  (change)="layeredSupply($event)"
                  name="Supply"
                  min="1"
                  [(ngModel)]="fractionalObj.supply"
                  [ngModelOptions]="{ standalone: true }"
                  *ngIf="!tokenUrl"
                />
                <input type="number" disabled *ngIf="tokenUrl" />
              </div>
            </div>
            <!-- <div class="item" style="margin-top: 0.5vw">
              <div class="title">Reserve Price</div>
              <div class="value">
                <input type="number" name="ReservePrice" id="ReservePrice" />
              </div>
            </div> -->
          </div>
          <div *ngIf="imgData.length != 0" class="section-upload-section">
            <div
              class="sectional-sections"
              [dragula]="'task-group'"
              [(dragulaModel)]="tokenUrl ? [] : imgData"
            >
              <div
                *ngFor="let alias of imgData; let i = index"
                (click)="layeredimageSource = alias.url ? alias.url : ''"
              >
                <label
                  class="section-label"
                  [ngClass]="{ 'acive-label': alias?.url }"
                  for="aliasa-{{ i }}"
                  style="cursor: grab"
                  ><div>
                    <fa-icon
                      style="margin: 0 0.5vw"
                      [icon]="faGripVertical"
                    ></fa-icon>

                    {{
                      alias.url
                        ? "Uploaded Image " + (1 + i)
                        : "Upload Image " + (1 + i)
                    }}
                  </div>
                  <fa-icon
                    [icon]="alias?.url ? faCheckCircle : faUpload"
                  ></fa-icon>
                </label>
                <input
                  *ngIf="!alias.url"
                  id="aliasa-{{ i }}"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  (change)="readURLforLayered($event, i)"
                />
              </div>
            </div>

            <div class="sectional-image-preview">
              <img
                id="blah"
                [src]="
                  layeredimageSource || 'http://via.placeholder.com/640x360'
                "
                alt="Image Preview"
                style="width: 100%; object-fit: contain"
              />
            </div>
          </div>
          <!-- (change)="readURL($event);" -->
        </div>
      </div>
      <div class="tab" [hidden]="form.get('mintType').value !== 'layered'">
        <div (click)="nftAccordion[4] = !nftAccordion[4]" class="tab-head">
          <div>
            <fa-icon [icon]="faHammer" style="padding-right: 0.5vw"></fa-icon>
            Layered Details
          </div>
          <div>
            <fa-icon
              *ngIf="nftAccordion[4]"
              [icon]="faChevronUp"
              style="padding-left: 0.5vw"
            ></fa-icon>
            <fa-icon
              *ngIf="!nftAccordion[4]"
              [icon]="faChevronDown"
              style="padding-left: 0.5vw"
            ></fa-icon>
          </div>
        </div>
        <div *ngIf="nftAccordion[4] && !tokenURL" class="tab-content nft">
          <div class="nft-details">
            <div class="item" style="margin-top: 0.5vw">
              <div class="title">Supply</div>
              <div class="value">
                <input
                  type="number"
                  (keyup)="layeredSupply($event)"
                  (change)="layeredSupply($event)"
                  name="Supply"
                  min="1"
                  [(ngModel)]="fractionalObj.supply"
                  [ngModelOptions]="{ standalone: true }"
                  *ngIf="!tokenUrl"
                />
                <input type="number" disabled *ngIf="tokenUrl" />
              </div>
            </div>
            <!-- <div class="item" style="margin-top: 0.5vw">
              <div class="title">Reserve Price</div>
              <div class="value">
                <input type="number" name="ReservePrice" id="ReservePrice" />
              </div>
            </div> -->
          </div>
          <div *ngIf="imgData.length != 0" class="section-upload-section">
            <div
              class="sectional-sections"
              [dragula]="'task-group'"
              [(dragulaModel)]="tokenUrl ? [] : imgData"
            >
              <div
                *ngFor="let alias of imgData; let i = index"
                (click)="layeredimageSource = alias.url ? alias.url : ''"
              >
                <label
                  class="section-label"
                  [ngClass]="{ 'acive-label': alias?.url }"
                  for="aliasa-{{ i }}"
                  style="cursor: grab"
                  ><div>
                    <fa-icon
                      style="margin: 0 0.5vw"
                      [icon]="faGripVertical"
                    ></fa-icon>

                    {{
                      alias.url
                        ? "Uploaded Image " + (1 + i)
                        : "Upload Image " + (1 + i)
                    }}
                  </div>
                  <fa-icon
                    [icon]="alias?.url ? faCheckCircle : faUpload"
                  ></fa-icon>
                </label>
                <input
                  *ngIf="!alias?.url"
                  id="aliasa-{{ i }}"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  (change)="readURLforLayered($event, i)"
                />
              </div>
            </div>

            <div class="sectional-image-preview">
              <img
                id="blah"
                [src]="
                  layeredimageSource || 'http://via.placeholder.com/640x360'
                "
                alt="Image Preview"
                style="width: 100%; object-fit: contain"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="tab">
        <div (click)="nftAccordion[1] = !nftAccordion[1]" class="tab-head">
          <div>
            <fa-icon [icon]="faImage" style="padding-right: 0.5vw"></fa-icon>
            Artwork Details
          </div>
          <div>
            <fa-icon
              *ngIf="nftAccordion[1]"
              [icon]="faChevronUp"
              style="padding-left: 0.5vw"
            ></fa-icon>
            <fa-icon
              *ngIf="!nftAccordion[1]"
              [icon]="faChevronDown"
              style="padding-left: 0.5vw"
            ></fa-icon>
          </div>
        </div>
        <div *ngIf="nftAccordion[1]" class="tab-content nft">
          <div class="nft-details">
            <div class="item">
              <div class="title">Artwork Title</div>
              <div class="value">{{ artworksObj?.artwork_title }}</div>
            </div>
            <div class="item">
              <div class="title">Artist Name</div>
              <div class="value">{{ userDetailsObj?.display_name }}</div>
            </div>
            <div class="item">
              <div class="title">Year</div>
              <div class="value">{{ artworksObj?.year }}</div>
            </div>
            <div class="item">
              <div class="title">Artwork File</div>
              <div class="value">
                <a
                  [href]="artworksObj?.original_artwork_file[0].url"
                  target="_blank"
                  >{{ artworksObj?.original_artwork_file[0].url }}</a
                >
              </div>
            </div>
            <div class="item" *ngIf="artworksObj.medium_keywordsStr">
              <div class="title">Medium</div>
              <div class="value">{{ artworksObj?.medium_keywordsStr }}</div>
            </div>
            <div class="item">
              <div class="title">Dimensions</div>
              <div class="value">{{ artworksObj?.dimensions }}</div>
            </div>
            <!-- <div class="item">
              <div class="title">Image Url</div>
              <div class="value">Parent Image Link</div>
            </div> -->
            <div class="item" *ngIf="artworksObj.created_location">
              <div class="title">Created Location</div>
              <div class="value">{{ artworksObj?.created_location }}</div>
            </div>

            <div
              class="item"
              *ngIf="
                form.get('mintType').value == 'layered' ||
                form.get('mintType').value == 'sectional' ||
                form.get('mintType').value == 'fractional'
              "
            >
              <table>
                <thead>
                  <tr *ngIf="imgData.length > 0">
                    <th style="width: 50%">
                      {{ form.get("mintType").value }} order
                    </th>
                    <th style="width: 50%">Preview Links</th>
                    <th
                      style="width: 50%"
                      *ngIf="form.get('mintType').value != 'fractional'"
                    >
                      Token Links
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of imgData; let i = index">
                    <td>{{ form.get("mintType").value }} Image {{ i + 1 }}</td>
                    <td>
                      <a [href]="item.url" target="_blank" *ngIf="item.url"
                        >Link</a
                      >
                    </td>
                    <td>
                      <a
                        [href]="item.tokenUrl"
                        target="_blank"
                        *ngIf="
                          item.tokenUrl &&
                          form.get('mintType').value != 'fractional'
                        "
                        >Link</a
                      >
                    </td>
                  </tr>
                </tbody>

                <tbody></tbody>
              </table>
            </div>
            <div class="item">
              <span
                class="btn-verify"
                (click)="createTokenUrl()"
                *ngIf="!tokenURL"
                >Verify Artwork Details</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="tab">
        <div (click)="nftAccordion[0] = !nftAccordion[0]" class="tab-head">
          <div>
            <fa-icon [icon]="faKey" style="padding-right: 0.5vw"></fa-icon>
            NFT Details
          </div>
          <div>
            <fa-icon
              *ngIf="nftAccordion[0]"
              [icon]="faChevronUp"
              style="padding-left: 0.5vw"
            ></fa-icon>
            <fa-icon
              *ngIf="!nftAccordion[0]"
              [icon]="faChevronDown"
              style="padding-left: 0.5vw"
            ></fa-icon>
          </div>
        </div>
        <div *ngIf="nftAccordion[0]" class="tab-content nft">
          <div class="nft-details">
            <div class="item">
              <div class="title">Blockchain</div>
              <div class="value">{{ form.value.network | titlecase }}</div>
            </div>
            <!-- <div class="item">
              <div class="title">Contract</div>
              <div class="value">
                <a>0x15c2..468d </a>
                <a (click)="copyUrl()" class="item">
                  <fa-icon
                    [ngStyle]="{ color: '#004ddd' }"
                    [icon]="faCopy"
                    style="padding-left: 0.5vw"
                  ></fa-icon>
                </a>
              </div>
            </div> -->
            <!-- <div class="item">
              <div class="title">NFT id</div>
              <div class="value">123</div>
            </div> -->
            <div class="item">
              <div class="title">Parent Token Url</div>
              <div class="value">
                <a [href]="tokenURL" target="_blank">{{ tokenURL }}</a>
              </div>
            </div>
            <div *ngIf="form.get('mintType').value != 'fractional'">
              <div class="item" *ngFor="let item of imgData; let t = index">
                <div class="title">Token Url {{ t + 1 }}</div>
                <div class="value">
                  <a [href]="item?.tokenUrl" target="_blank">{{
                    item?.tokenUrl
                  }}</a>
                </div>
              </div>
            </div>
            <div class="item">
              <div class="title">Token Standard</div>
              <div class="value">{{ form.value.mintType }}</div>
            </div>
            <!-- <div class="item">
              <div class="title">Created By</div>
              <div class="value">
                <a>0x15c2..468d</a>
                <a (click)="copyUrl()" class="item">
                  <fa-icon
                    [ngStyle]="{ color: '#004ddd' }"
                    [icon]="faCopy"
                    style="padding-left: 0.5vw"
                  ></fa-icon>
                </a>
              </div>
            </div> -->
            <!-- <div class="item">
              <div class="title">Minted At</div>
              <div class="value"><a>{{presentDate | date:'medium'}}</a></div>
            </div> -->
            <!-- <div class="item">
              <div class="title">Current Owner</div>
              <div class="value">
                <a>0x15c2..468d</a>
                <a (click)="copyUrl()" class="item">
                  <fa-icon
                    [ngStyle]="{ color: '#004ddd' }"
                    [icon]="faCopy"
                    style="padding-left: 0.5vw"
                  ></fa-icon>
                </a>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

    <div
      class="field-value"
      style="display: flex; align-items: flex-start; margin-top: 2vw"
    >
      <input
        type="checkbox"
        [(ngModel)]="mintPublishTerm"
        [ngModelOptions]="{ standalone: true }"
        style="
          margin-right: 1vw;
          margin-top: 0.25vw;
          width: 1.1vw;
          height: 1.1vw;
        "
      />
      <label style="width: 100%"
        >I confirm I wish to mint the NFT for this work, and transfer the NFT to
        the sale contract to publish on the marketplace</label
      >
    </div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="save"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="isMintConfirmationOpen = false"
      >
        Cancel
      </button>
      <button
        class="save"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="mintPublish()"
      >
        Mint and Publish
      </button>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!isWithdrawConfirmationOpen">
  <div class="popup-card">
    <p style="font-size: 1.25vw">
      Please confirm before withdrawing NFT from sale. Ownership of the NFT gets
      transferred back to you, and will get removed from Marketplace.
    </p>
    <!-- <span style="text-align: center"><b>Estimated Gas Cost: 0</b></span>
    <span style="text-align: center"
      ><b>Estimated transaction time: 0 Minutes </b></span> -->

    <div
      class="field-value"
      style="display: flex; align-items: flex-start; margin-top: 2vw"
    >
      <input
        type="checkbox"
        style="
          margin-right: 1vw;
          margin-top: 0.25vw;
          width: 1.1vw;
          height: 1.1vw;
        "
        value="1"
      />
      <label style="width: 100%"
        >I confirm I wish to withdraw the NFT from sale</label
      >
    </div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="save"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="isWithdrawConfirmationOpen = false"
      >
        Cancel
      </button>
      <button
        class="save"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="
          form.get('published').setValue(false);
          isWithdrawConfirmationOpen = false
        "
      >
        Withdraw from Sale
      </button>
    </div>
  </div>
</div>
<div *ngIf="!artworksObj?.nft?.id">
  <div class="modelForCreate" *ngIf="walletConnected && isMiningAccess == -1">
    <div class="Content-Box">
      <span class="modHead">Checking whitelisted status, please wait.</span>

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>

  <div
    class="modelForCreate"
    *ngIf="isAccessPopup && walletConnected && isMiningAccess == 1"
  >
    <div class="Content-Box">
      <span class="modHead"
        >Your wallet address {{ userDetailsObj?.ethereum_account }} is not
        permitted to mint.
        <!-- please request whitelisting by writing to
        <EMAIL>. -->
      </span>

      <div class="btnn">
        <button (click)="isAccessPopup = false" class="simpe-type">
          Cancel
        </button>
        <button (click)="requestWhitelist()" class="button-type">
          Request Whitelist
        </button>
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isWhiteListRequested">
    <div class="Content-Box">
      <span class="modHead"
        >Your request has been submitted. <NAME_EMAIL> if
        the address hasn’t been whitelisted in 24 hours.
      </span>

      <div class="btnn">
        <button (click)="saveWork()" class="button-type">Ok</button>
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isWaitMiniting">
    <div class="Content-Box">
      <span class="modHead"
        >Minting initiated, awaiting confirmation from the blockchain network.
        Please do not close the pop-up or refresh the page.</span
      >

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>

  <div class="modelForCreate" *ngIf="isMinted">
    <div class="Content-Box">
      <span class="modHead"
        >NFT minted successfully! NFT ID is {{ nftDetailsObj.id }}. Please click
        'Save' to continue.</span
      >

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <button class="button-type" (click)="saveWork()">Save</button>
      </div>
    </div>
  </div>
</div>
<div *ngIf="artworksObj?.nft?.id">
  <div class="modelForCreate" *ngIf="isTransferPopup">
    <div class="Content-Box">
      <span class="modHead">Wallet Address</span>
      <div class="field-value" style="padding-right: 0.5vw">
        <input
          type="text"
          [(ngModel)]="toAddress"
          placeholder="Wallet address"
          style="width: 100%; padding-top: 0.5vw"
        />
      </div>

      <div class="btnn">
        <button (click)="isTransferPopup = false" class="simpe-type">
          Cancel
        </button>
        <button class="button-type" (click)="transferToken()">Transfer</button>
      </div>
    </div>
  </div>
  <div class="modelForCreate" *ngIf="isWaitMiniting">
    <div class="Content-Box">
      <span class="modHead"
        >Transfer initiated, awaiting confirmation from the blockchain network.
        Please do not close the pop-up or refresh the page.</span
      >

      <div class="btnn">
        <!-- <button (click)="isAccessPopup = false" class="simpe-type">Cancel</button> -->
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>
  <div class="modelForCreate" *ngIf="isTransfered">
    <div class="Content-Box">
      <span class="modHead">Successfully Transferred to {{ toAddress }}.</span>

      <div class="btnn">
        <button (click)="isTransfered = false" class="simpe-type">
          Cancel
        </button>
        <!-- <button class="button-type">Confirm</button> -->
      </div>
    </div>
  </div>
</div>
