import { EditNotifyService } from './../edit-notify.service';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  NgModule,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { display } from 'html2canvas/dist/types/css/property-descriptors/display';
import { ImageUploadComponent } from 'src/app/core/image-upload/image-upload.component';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistAuthService } from 'src/app/services/artist-auth.service';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { AuthService } from 'src/app/services/auth.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl, walletDetails } from 'src/environments/environment.prod';
import { ArtistService } from '../../../services/artist.service';
import { ArtworkService } from '../../../services/artwork.service';
import {
  faChevronUp,
  faCopy,
  faTruck,
  faChevronDown,
  faHammer,
  faKey,
  faCheckCircle,
  faUpload,
  faGripVertical,
} from '@fortawesome/free-solid-svg-icons';
import { faImage } from '@fortawesome/free-regular-svg-icons';

import MetaMaskOnboarding from '@metamask/onboarding';
import { Web3Service } from 'src/app/shared/services/web3.service';
@Component({
  selector: 'app-artworks-mint',
  templateUrl: './mint.component.html',
  styleUrls: ['./mint.component.scss'],
})
export class MintArtworksComponent implements OnInit {
  supplysectional: number;
  layeredsectional: number;
  imageSource: string;
  layeredimageSource: string;
  sectionalArray = [];
  faGripVertical = faGripVertical;
  layeredArray = [];
  UploadArrays = Array(20).fill(false);
  faChevronDown = faChevronDown;
  faHammer = faHammer;
  faCheckCircle = faCheckCircle;
  faUpload = faUpload;
  faKey = faKey;
  faImage = faImage;
  faChevronUp = faChevronUp;
  faCopy = faCopy;
  faTruck = faTruck;
  dropDownValue = Array(10).fill(false);
  nftAccordion = Array(10).fill(false);
  isSubmited = false;
  isDropDownOpen = Array(10).fill(false);
  selectedFiles: File[] = [];
  form: FormGroup;
  isArtworkMinted = false;
  isMintConfirmationOpen = false;
  isWithdrawConfirmationOpen = false;
  id;
  showMovement: boolean = false;
  mintPublishTerm: boolean = false;
  artwork_type = {
    'textile arts': 1,
    painting: 2,
    drawing: 3,
    photography: 4,
    digital: 5,
    print: 6,
    'mixed media': 7,
    video: 8,
    other: 9,
    sculpture: 10,
    installation: 11,
  };
  userType;

  currentOwner;

  @ViewChild(ImageUploadComponent)
  private imageUploadComponent: ImageUploadComponent;
  primaryImage: any;
  moveArr: any = [];
  selectedMovements: any = [];
  profileImage: any;
  permissionsObj: any = {};
  showObj: any = {};
  disableObj: any = {};
  userDetailsObj: any = {};
  Menuinit: any;
  artworksObj: any = {};
  OpenFractionalNFT: boolean = false;
  fractionalObj: any = {
    supply: null,
    listPrice: null,
    fee: null,
  };
  nftDetailsObj: any = {
    hash: '',
  };
  presentDate: any = '';
  tokenURL: any;
  imgData: any = [];
  collectionID: any;

  onboarding;
  isMetamaskAvailable = false;
  selectedOption = 0;
  isCreator = false;
  walletConnected = false;

  isMiningAccess = -1;
  isMinted = false;
  isWaitMiniting = false;
  isTransfered = false;

  isAccessPopup = true;

  isTransferPopup = false;
  toAddress = null;

  networks = {
    ethereum_main_net: 1,
    polygon: 137,
    goerli_testnet: 5,
    polygon_testnet: 80001,
  };

  isWhiteListRequested = false;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    public server: CollectorService,
    private editNotifyService: EditNotifyService,
    private web3Service: Web3Service,
    private ref: ChangeDetectorRef
  ) {
    this.onboarding = new MetaMaskOnboarding();
    this.presentDate = new Date();
  }
  copyUrl() {
    navigator.clipboard.writeText(window.location.href);
  }
  get sectionItems() {
    return this.form.get('sectionUpload') as FormArray;
  }
  get layeredItems() {
    return this.form.get('layeredUpload') as FormArray;
  }
  ngOnInit(): void {
    this.web3Service.accountStatus$.subscribe((next) => {
      const address = next[0];
      if (address) {
        this.walletConnected = true;
        this.userDetailsObj['ethereum_account'] = address;
        if (this.artworksObj?.nft?.id) {
          this.web3Service.changeNetworkByID(this.artworksObj?.nft?.network);
        } else {
          this.web3Service.changeNetwork(this.form.get('network').value);
        }
      }
    });
    this.web3Service.networkStatus$.subscribe(async (next) => {
      if (next == this.networks[this.form.get('network').value]) {
        this.isMiningAccess = -1;
        this.isAccessPopup = true;

        const isMiningAccess = await this.web3Service.checkMinter();
        isMiningAccess ? (this.isMiningAccess = 2) : (this.isMiningAccess = 1);
        this.ref.detectChanges();
        //this.isMiningAccess = await this.web3Service.checkMinter();
      }
    });

    this.getAllMovements();
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)
      ['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Main');
    this.userDetailsObj = JSON.parse(decode);
    console.log(this.userDetailsObj);
    if (this.userDetailsObj.ethereum_account) {
      this.getBalance(this.userDetailsObj.ethereum_account, 'matic');
    }
    // this.artistAuthService.me().subscribe((data) => {
    // 	this.userType = data.data.data.userData.role;
    // });
    // this.artworkService.nextButton$.subscribe((data) => {
    // 	if (
    // 		data === '/artist-portal/settings/artwork/add' ||
    // 		data === '/artist-portal/settings/artwork/edit/' + this.id
    // 	) {
    // 		this.onSubmit();
    // 	}
    // });
    this.form = this.formBuilder.group({
      published: new FormControl({ value: false }),
      platform: new FormControl('minting_on_terrain'),
      // tokenType: new FormControl(''),
      mintType: new FormControl('ERC-721'),
      network: new FormControl('matic'),
      preferType: new FormControl('lazy_minting'),
      Other_Platform_Network: new FormControl(''),

      originalPrice: new FormControl(''),
      workType: new FormControl(''),
      sectionUpload: this.formBuilder.array([]),
      layeredUpload: this.formBuilder.array([]),
    });

    // this.form.patchValue({
    // 	published: false,
    // 	platform: 'minting_on_terrain',
    // })
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;

      // if(ele.formControl && ele.show && ele.mandatory) {
      // 	this.form.controls[ele.name].setValidators([Validators.required]);
      // }
    });

    // this.route.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getMainData(this.id).subscribe(async (data) => {
    // 			const art = data;
    // 			this.form.patchValue(art);
    // 			// this.selectedFiles.push(
    // 			// 	new File([''], art.primaryImage.originalname)
    // 			// );
    // 			let response = await fetch(
    // 				'https://api.artist.terrain.art/' + art.primaryImage.path
    // 			);
    // 			let da = await response.blob();
    // 			let metadata = {
    // 				type: 'image/jpeg',
    // 			};
    // 			let file = new File([da], art.primaryImage.originalname, metadata);
    // 			this.imageUploadComponent.onFileSelect([file]);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });

    this.form.valueChanges.subscribe((x) => {
      // ('firstname value changed')
      //   console.log(x)
      // this.newItemEvent.emit("true");
      // Menuinit;
      if (this.Menuinit == null) {
        this.Menuinit = x;
      } else {
        this.editNotifyService.setOption('Publish', true);
      }
    });

    console.log(this.disableObj);

    this.isMetamaskAvailable = MetaMaskOnboarding.isMetaMaskInstalled();
    if (this.isMetamaskAvailable) {
      this.selectedOption = 1;
    } else {
      this.selectedOption = 0;
    }
  }

  getBalance(addr, type) {
    let url = walletDetails.getMaticBalance;

    if (type == 'matic') {
      url = walletDetails.getMaticBalance;
    } else if (type == 'ropsten') {
      url = walletDetails.getETHBalance;
    }
    let req = {
      address: addr,
    };
    console.log(req);
    this.server.showSpinner();
    this.server.postThirdPartyApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.code == 200) {
        this.userDetailsObj['ethereum_balance'] = res.balance;
      }
    });
    // this.web3Service.generateAccount().then((data) => {
    // 	this.ukey = data.address;
    // 	this.pkey = data.privateKey;
    // });
  }

  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.artworksObj = res.data;
        this.artworksObj['movementStr'] = this.artworksObj.movement.join(',');
        this.artworksObj['medium_keywordsStr'] =
          this.artworksObj.medium_keywords.join(',');
        this.artworksObj['subjects_keywordsStr'] =
          this.artworksObj.subjects_keywords.join(',');
        this.artworksObj['tagsStr'] =
          this.artworksObj.subjects_keywords.join(',');
        this.artworksObj['coloursStr'] = this.artworksObj.colours.join(',');
        if (this.userDetailsObj?._id == this.artworksObj?.artist_id?._id) {
          this.isCreator = true;
        } else {
          this.isCreator = false;
        }
        this.nftDetailsObj = this.artworksObj.nft;
        if (this.artworksObj?.nft?.id) {
          this.getCurrentOwner(
            this.nftDetailsObj.network,
            this.nftDetailsObj.id
          );
        }
        this.form.patchValue({
          published: res.data.publish_artworks == true ? true : false,
          platform: res.data.platform || 'minting_on_terrain',
          mintType: res.data.mintType,
          network: res.data.network,
          preferType: res.data.preferType,
          Other_Platform_Network: '',

          originalPrice: '',
          workType: '',
        });
      }
    });
  }

  // to get all moments
  getAllMovements() {
    let url = apiUrl.getMovement + '?limit=999&offset=1';
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.moveArr = res.data;
      }
    });
  }

  // onSubmit() {
  // 	const data = this.form.value;
  // 	data.published = data.published === true ? 1 : 0;
  // 	data.showEdition = data.showEdition === true ? 1 : 0;
  // 	console.log(data.type);

  // 	data.type = this.artwork_type[data.type];

  // 	if (this.id) {
  // 		this.artistInfoService
  // 			.addMainData(this.form.value, this.selectedFiles, this.id)
  // 			.subscribe((data) => {
  // 				this.router.navigate([
  // 					'/artist-portal/settings/artwork/edit/' + this.id + '/details',
  // 				]);
  // 			});
  // 	} else {
  // 		this.artistInfoService
  // 			.addMainData(this.form.value, this.selectedFiles)
  // 			.subscribe((data) => {
  // 				localStorage.setItem('artID', data?.id);
  // 				this.router.navigate(['/artist-portal/settings/artwork/add/details']);
  // 			});
  // 	}
  // }

  onSubmit() {
    console.log(this.form.value);
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }

    console.log(this.form.value);
    this.mintPublishTerm = false;
    if (
      this.form.value.platform == 'minting_on_terrain' &&
      (this.form.value.mintType == 'standard' ||
        this.form.value.mintType == 'fractional') &&
      (this.form.value.network == 'matic' ||
        this.form.value.network == 'ropsten') &&
      this.form.value.preferType == 'mint_now'
    ) {
      this.isMintConfirmationOpen = true;
      return;
    }

    let url = apiUrl.addArtwork;
    let req = {
      publish_artworks: this.form.value.published == true ? true : false,
      platform: this.form.value.platform,
      mintType: this.form.value.mintType,
      network: this.form.value.network,
      preferType: this.form.value.preferType,
      nft: this.nftDetailsObj,
      tab: 'Publish',
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }

    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        this.editNotifyService.reset();
        this.isSubmited = true;
        this.getArtWork();
        alert(res.message);
      } else {
        this.isSubmited = false;
      }
    });
  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }
  // if (this.id) {
  // 	this.artistInfoService
  // 		.addMainData(this.form.value, this.selectedFiles, this.id)
  // 		.subscribe((data) => {
  // 			this.router.navigate([
  // 				'/artist-portal/settings/artwork/edit/' + this.id + '/details',
  // 			]);
  // 		});
  // } else {
  // 	this.artistInfoService
  // 		.addMainData(this.form.value, this.selectedFiles)
  // 		.subscribe((data) => {
  // 			localStorage.setItem('artID', data?.id);
  // 			this.router.navigate(['/artist-portal/settings/artwork/add/details']);
  // 		});
  // }
  // }

  // to add to items
  addToItems(obj) {
    let index = this.selectedMovements.findIndex((x) => x['_id'] == obj['_id']);
    console.log(index);
    if (index == -1) {
      this.selectedMovements.push(obj);
    }
    this.showMovement = false;
  }

  // to remove items
  removeItems(index) {
    this.selectedMovements.splice(index, 1);
  }

  changeFocus(index) {
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }

  createTokenUrl() {
    console.log(this.form.value);
    if (this.form.invalid) {
      console.log(this.form.value);
      alert('Please fill required values!');
      return;
    }

    let url_index = this.imgData.findIndex((x) => x.url == '');
    if (url_index != -1) {
      alert(`Please Upload Image ${url_index + 1}`);
      return;
    }
    // if (!this.mintPublishTerm) {
    // 	alert('Please accept the terms!')
    // 	return;
    // }
    let req = {
      // "estimatedGasPrice": 0,
      // "estimatedTransactionTime": 0,
      platform: this.form.value.platform,
      artworkTitle: this.artworksObj.artwork_title,
      year: this.artworksObj.year,
      artistName: this.userDetailsObj?.display_name,
      medium: '',
      dimensions: this.artworksObj.dimensions,
      // "externalUrl": '',
      image: this.artworksObj.original_artwork_file[0].url, // original artwork url
      createdLocation: this.artworksObj.created_location,
      salePrice: this.artworksObj.sale_price,
      movementKeywords: this.artworksObj.movementStr,
      mediumKeywords: this.artworksObj.medium_keywordsStr,
      subjectKeywords: this.artworksObj.subjects_keywordsStr,
      otherTags: this.artworksObj.tagsStr,
      primaryColour: this.artworksObj.colour,
      colours: this.artworksObj.coloursStr,
    };
    let supply = [];
    this.imgData.forEach((ele) => {
      console.log(ele);
      supply.push(ele.url);
    });
    if (
      this.form.value.mintType == 'layered' ||
      this.form.value.minType == 'sectional'
    ) {
      req['supplyData'] = [...supply];
    }
    let url = apiUrl.uploadMeta;
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        req['url'] = res.data;
        this.tokenURL = res.data;
        alert('Verified suceessfully!');
        // this.callMintApi(req)
      }
    });
  }
  sectionalSupply(event: Event) {
    let eventObj = event.target as HTMLInputElement;
    let supply: any = eventObj.value;
    // let frmArray = this.invoiceForm.get('invoiceparticulars') as FormArray;
    // this.form.get('sectionUpload').setValue()= this.formBuilder.array([]);
    this.form.controls.sectionUpload = this.formBuilder.array([]);
    for (let index = 0; index < supply; index++) {
      this.sectionItems.push(this.formBuilder.control(''));
    }
    this.sectionalArray = Array(supply).fill(false);
  }
  layeredSupply(event) {
    console.log(event.target.value);
    this.imgData = [];
    this.layeredimageSource = '';
    if (event.target.value) {
      for (let index = 0; index < this.fractionalObj.supply; index++) {
        this.imgData.push({
          url:
            this.form.value.mintType == 'fractional'
              ? this.artworksObj.original_artwork_file[0].url
              : '',
        });
      }
    }
    // let eventObj = event.target as HTMLInputElement;
    // let supply: any = eventObj.value;
    // this.form.controls.layeredUpload=this.formBuilder.array([]);
    // for (let index = 0; index < supply; index++) {
    // 	this.layeredItems.push(this.formBuilder.control(''));
    // }
    // this.layeredArray= Array(supply).fill(false);
  }
  readURL(event: Event, i): void {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
      const file = target.files[0];

      const reader = new FileReader();
      reader.onload = (e) => (this.imageSource = reader.result as string);

      reader.readAsDataURL(file);
    }
  }
  readURLforLayered(event: Event, index): void {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
      const file = target.files[0];

      const reader = new FileReader();
      reader.onload = (e) =>
        (reader.onloadend = () => {
          this.layeredimageSource = reader.result as string;

          (this.imgData[index]['filedata'] = reader.result),
            (this.imgData[index]['url'] = ''),
            (this.imgData[index]['tokenUrl'] = ''),
            (this.imgData[index]['name'] = 'Image File'),
            (this.imgData[index]['size'] = ''),
            (this.imgData[index]['file_event'] = file),
            (this.imgData[index]['type'] = 'image/*'),
            this.uploadFile(file, index);
        });

      reader.readAsDataURL(file);
    }
  }

  // to upload file
  uploadFile(file, index) {
    let formdata = new FormData();
    formdata.append('image', file);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          console.log(this.imgData, index);
          this.imgData[index]['url'] = res.data;
          this.createSubTokenUrl(index);
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // to create sub url
  createSubTokenUrl(index) {
    if (this.form.invalid) {
      console.log(this.form.value);
      alert('Please fill required values!');
      return;
    }
    let req = {
      platform: this.form.value.platform,
      artworkTitle: this.artworksObj.artwork_title,
      year: this.artworksObj.year,
      artistName: this.userDetailsObj?.display_name,
      medium: '',
      dimensions: this.artworksObj.dimensions,
      image: this.imgData[index].url, // original artwork url
      createdLocation: this.artworksObj.created_location,
      salePrice: this.artworksObj.sale_price,
      movementKeywords: this.artworksObj.movementStr,
      mediumKeywords: this.artworksObj.medium_keywordsStr,
      subjectKeywords: this.artworksObj.subjects_keywordsStr,
      otherTags: this.artworksObj.tagsStr,
      primaryColour: this.artworksObj.colour,
      colours: this.artworksObj.coloursStr,
    };
    let url = apiUrl.uploadMeta;
    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        console.log(this.imgData, index);
        this.imgData[index]['tokenUrl'] = res.data;
        alert('File uploaded successfully!');
      }
    });
  }

  // call mint APi
  mintPublish() {
    if (!this.mintPublishTerm) {
      alert('Please accept the terms!');
      return;
    } else if (!this.tokenURL) {
      alert('Please verify artwork details first !');
      return;
    } else if (this.artworksObj.sale_options != 'ForSale') {
      alert(
        'Artwork is not for sale ! Please update sale options in financial tab'
      );
      return;
    }
    if (
      this.fractionalObj.supply == null &&
      this.form.value.mintType == 'fractional'
    ) {
      alert('Please enter supply!');
      return;
    }

    if (
      this.form.value.mintType == 'layered' ||
      this.form.value.mintType == 'sectional' ||
      this.form.value.mintType == 'fractional'
    ) {
      this.createRecords();
    } else {
      this.mintPub();
    }
  }

  mintPub() {
    let req = {
      address: this.userDetailsObj.ethereum_account,
      private_key: this.userDetailsObj.ethereum_nounce,
      url: this.tokenURL,
      name: this.form.value.name,
      symbol: this.form.value.symbol,
      network: this.form.value.network,
      minting_type: this.form.value.preferType,
    };
    let url = apiUrl.mintPublish;
    this.server.showSpinner();
    this.server.postThirdPartyApi(url, req).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.trx_hash) {
          this.isArtworkMinted = true;
          alert(res.message);
          if (
            this.form.value.mintType == 'fractional' ||
            this.form.value.mintType == 'layered' ||
            this.form.value.mintType == 'sectional'
          ) {
            this.nftDetailsObj['nftAddress'] = res.trx_hash;
            this.updateMintStatus('deployed');
            this.approveNFT(res.trx_hash);
          } else {
            this.nftDetailsObj['nftAddress'] = res.trx_hash;
            this.isMintConfirmationOpen = false;
          }
        } else {
          alert(res.message);
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // to approve NFT
  approveNFT(hash) {
    let req = {
      address: this.userDetailsObj.ethereum_account,
      nft_address: hash,
      private_key: this.userDetailsObj.ethereum_nounce,
      network: this.form.value.network,
    };
    let url = apiUrl.approveNFT;
    this.server.showSpinner();
    this.server.postThirdPartyApi(url, req).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.trx_hash) {
          this.isArtworkMinted = true;
          this.isMintConfirmationOpen = false;
          alert('NFT Approved!');
          if (
            this.form.value.mintType == 'fractional' ||
            this.form.value.mintType == 'layered' ||
            this.form.value.mintType == 'sectional'
          ) {
            this.nftDetailsObj['hash'] = res.trx_hash;
            this.updateMintStatus('approved');
            this.createNFT();
          }
        } else {
          alert(res.message);
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // to create NFT
  createNFT() {
    if (
      this.form.value.mintType == 'layered' ||
      this.form.value.mintType == 'sectional'
    ) {
      this.fractionalObj.supply = this.imgData.length;
    }
    if (!this.fractionalObj.supply) {
      alert('Please enter supply!');
      return;
    }

    let req = {
      address: this.userDetailsObj.ethereum_account,
      nft_address: this.nftDetailsObj['nftAddress'],
      name: this.form.value.name,
      symbol: this.form.value.symbol,
      private_key: this.userDetailsObj.ethereum_nounce,
      supply: this.fractionalObj.supply,
      // "list_price": this.fractionalObj.listPrice.toFixed(18),
      // "fee": this.fractionalObj.fee.toFixed(18),
      token_id: this.nftDetailsObj['hash'],
      network: this.form.value.network,
    };
    let url = apiUrl.createNFT;
    this.server.showSpinner();
    this.server.postThirdPartyApi(url, req).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.trx_hash) {
          this.OpenFractionalNFT = false;
          alert('NFT created!');
          this.updateMintStatus('created');
        } else {
          alert(res.message);
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // to create records
  createRecords() {
    // if (!this.fractionalObj.supply) {
    // 	alert('Please enter supply!');
    // 	return;
    // } else if (!this.fractionalObj.listPrice) {
    // 	alert('Please enter list price!');
    // 	return;
    // } else if (!this.fractionalObj.fee) {
    // 	alert('Please enter fee!');
    // 	return;
    // }
    let arr = [];
    this.imgData.forEach((ele) => {
      arr.push({
        primary_image: [
          {
            url: ele.url,
            filedata: '',
            name: '',
            size: '',
            type: 'image',
            tokenUrl: ele.tokenUrl,
          },
        ],
      });
    });
    let req = {
      records: [...arr],
    };
    let url;
    if (this.form.value.mintType == 'layered') {
      url =
        apiUrl.artworks.createLayered + `/${localStorage.getItem('artworkID')}`;
    } else if (this.form.value.mintType == 'sectional') {
      url =
        apiUrl.artworks.createSectional +
        `/${localStorage.getItem('artworkID')}`;
    } else if (this.form.value.mintType == 'fractional') {
      url =
        apiUrl.artworks.createFractional +
        `/${localStorage.getItem('artworkID')}`;
    }
    this.server.showSpinner();
    this.server.postApi(url, req).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.collectionID = res.data.collection_id;
          alert(res.message);
          this.mintPub();
        } else {
          alert(res.message);
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  // to open confirmation modal on click mint now
  openConfirmationModal() {
    if (this.form.invalid) {
      alert('Please fill required fields!');
      return;
    }
    this.fractionalObj.supply = null;
    this.tokenURL = '';
    this.layeredimageSource = '';
    this.isMintConfirmationOpen = true;
    this.imgData = [];
    this.nftAccordion = Array(10).fill(false);
  }

  // to update collection mint status
  updateMintStatus(status) {
    let url =
      apiUrl.artworks.updateCollectionStatus +
      `/${this.collectionID}?mintStatus=${status}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
      }
    });
  }

  onboardingMeta() {
    this.onboarding.startOnboarding();
  }

  async loginWithMeta() {
    this.isMiningAccess = 0;
    this.isAccessPopup = true;
    // this.web3Service.signMeta().then((data) => {
    //   console.log(data);

    //   this.sign = data;
    //   this.islogged = true;
    // });
    this.walletConnected = false;
    this.web3Service.connectAccount();
  }

  public nft_name(network, id): string {
    switch (network) {
      case '1':
        return `TRA_ETH_${id}`;
      case '5':
        return `TRA_GOERLI_${id}`;
      case '137':
        return `TRA_MATIC_${id}`;
      case '80001':
        return `TRA_MUMBAI_${id}`;

      default:
        return ``;
    }
  }

  public async getCurrentOwner(network, id): Promise<string> {
    this.currentOwner = await this.web3Service.getOwner(network, id);
    return this.currentOwner;
  }

  async mintNFT() {
    this.isWaitMiniting = true;
    const data = await this.web3Service.mintNFT(this.artworksObj._id);
    this.nftDetailsObj = {
      id: data.id,
      contract: data.contract,
      network: data.network,
      minter: data.minter,
    };
    this.isMinted = true;
    this.isWaitMiniting = false;
  }

  saveWork() {
    this.onSubmit();
    this.isWhiteListRequested = false;
    this.isMinted = false;
  }
  async transferToken() {
    this.isWaitMiniting = true;
    const data = await this.web3Service.transferNFT(
      this.nftDetailsObj.network,
      this.nftDetailsObj.id,
      this.toAddress
    );
    this.isTransferPopup = false;
    this.isWaitMiniting = false;
    this.isTransfered = true;
    this.getArtWork();
  }

  requestWhitelist() {
    this.isAccessPopup = false;
    let url = 'wallet-whitelist';
    this.server.showSpinner();
    let data = {
      address: this.userDetailsObj?.ethereum_account,
      email: this.userDetailsObj.email,
      network: this.form.get('network').value,
    };
    this.server.postApi(url, data).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.isWhiteListRequested = true;
      }
    });
  }
}
