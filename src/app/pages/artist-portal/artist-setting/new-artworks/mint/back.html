<form [formGroup]="form">
  <div class="splitter">
    <div class="field-value" style="padding-right: 0.5vw">
      <div class="input-container">
        <input
          formControlName="platform"
          type="text"
          class="selection"
          placeholder="Minting Platform"
          disabled
        />
        <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
          <img src="assets/icons/arrow-down.png" class="flag-arrow" />
        </button>
        <div
          [ngClass]="{'dropdown-hidden': !isDropDownOpen[0],'dropdown-visible': isDropDownOpen[0]}"
        >
          <ul>
            <li
              (click)="form.get('platform').setValue('Terrain.art');isDropDownOpen[0] = false"
            >
              <div class="country-name">Terrain.art</div>
            </li>
            <li
              (click)="form.get('platform').setValue('SuperRare');isDropDownOpen[0] = false"
            >
              <div class="country-name">SuperRare</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="input-info">
        <!-- Choose whether the artwork was created individually or in
		collaboration with another artist -->
      </div>
    </div>
    <div class="field-value" style="padding-right: 0.5vw">
      <div class="input-container">
        <input
          formControlName="network"
          type="text"
          class="selection"
          placeholder="Minting Network"
          disabled
        />
        <button (click)="isDropDownOpen[1] = !isDropDownOpen[1]">
          <img src="assets/icons/arrow-down.png" class="flag-arrow" />
        </button>
        <div
          [ngClass]="{'dropdown-hidden': !isDropDownOpen[1],'dropdown-visible': isDropDownOpen[1]}"
        >
          <ul>
            <li
              (click)="form.get('network').setValue('Terrain.art');isDropDownOpen[1] = false"
            >
              <div class="country-name">Main Net</div>
            </li>
            <li
              (click)="form.get('network').setValue('Ropsten');isDropDownOpen[1] = false"
            >
              <div class="country-name">Ropsten</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="input-info">
        <!-- Choose whether the artwork was created individually or in
		collaboration with another artist -->
      </div>
    </div>
  </div>
</form>
