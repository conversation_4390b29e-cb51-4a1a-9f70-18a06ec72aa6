.footer-nav {
  height: 6.3vw;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0);
  // background-color: white;
  @media (max-width: 768px) {
    padding-bottom: 11.24vw;
  }
  .button-group {
    //margin-left: 21.66vw;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 1.25vw;
    @media (max-width: 768px) {
      font-size: 4.34vw;
      margin-left: 0vw;
      align-items: unset;
    }
    .next {
      width: 9vw;
      height: 3.05vw;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 1.49vw;
      border: solid 0.069vw #004ddd;
      color: #004ddd;
      margin-left: 1.5vw;
      @media (max-width: 768px) {
        width: 46.37vw;
        height: 10.62vw;
        border-radius: 6.08vw;
      }
    }
    .back {
      margin-left: 2.77vw;
      color: #808080;
    }
  }
}

.edited-tooltip {
  width: 1.1vw;
  height: 1.1vw;
  margin-left: 0.3vw;
  margin-top: 0.1vw;
  position: absolute;
  top: -0.8vw;
  right: -0.8vw;
}

.content-inside {
  // max-height: 40vw;
  // height: 72.5vh;
  height: 79.5vh;
  padding-right: 1vw;
  overflow: scroll;
  padding-top: 2vw;
  scroll-behavior: smooth;
  // padding-bottom: 10vw;
}
.slider-header {
  h3 {
    cursor: pointer;
    font-size: 1.25vw;
    padding-bottom: 0.34vw;
    margin-right: 2.15vw;
    color: var(--quaternary-font-color);
    display: flex;
    // margin-top: 1vw;
    position: relative;
    &.active {
      border-bottom: 2px solid var(--tertiary-font-color);
      color: var(--primary-font-color);
    }
    @media (max-width: 768px) {
      font-size: 3.86vw;
      padding-bottom: 1.34vw;
      margin-right: 3.86vw;
    }
  }
  // margin-bottom: 3.47vw;
  background-color: white;
  position: absolute;
  z-index: 1;
}
a:hover {
  text-decoration: none;
}

.saveBtn {
  // display: block;
  //width: 4vw;
  outline: none;
  font-size: 1.25vw;
  // width: 20.56vw;
  margin-left: 2vw;
  background-color: transparent;
  color: var(--tertiary-font-color);
  padding: 0.533333vw 0.89vw;
  border: 0.069vw solid var(--tertiary-font-color);
  border-radius: 1.46vw;
  @media (max-width: 768px) {
    font-size: 3.86vw;
    margin-bottom: 4.83vw;
    border-radius: 10.1vw;
    padding: 3.45vw 6.89vw;
  }
}
