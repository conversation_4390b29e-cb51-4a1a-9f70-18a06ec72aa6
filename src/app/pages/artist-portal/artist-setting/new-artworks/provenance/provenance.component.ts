import { EditNotifyService } from './../edit-notify.service';
import {
  Component,
  ElementRef,
  NgModule,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtistService } from '../../../services/artist.service';
import { ArtworkService } from '../../../services/artwork.service';
import { faLock } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-artworks-provenance',
  templateUrl: './provenance.component.html',
  styleUrls: ['./provenance.component.scss'],
})
export class ProvenanceArtworksComponent implements OnInit {
  faLock = faLock;
  dropDownValue = Array(10).fill(null);
  htmlContent = Array(10).fill(null);
  isSubmited = false;
  isDropDownOpen = Array(10).fill(false);
  ownershipHistoryObj: any = {
    owner_name: '',
    location: '',
    from: '',
    to: '',
    details: '',
  };
  selectedFiles: File[] = [];

  form: FormGroup;
  id;
  exhibitionHistoryObj: any = {
    description: '',
    year: '',
  };
  publicationHistoryObj: any = {
    description: '',
    year: '',
  };
  permissionsObj: any = {};
  showObj: any = {};
  Menuinit: any;
  disableObj: any = {};
  listCurrentPage;
  listTotalPage;
  isArtReadOnly = false;
  userDetails;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private artworkService: ArtworkService,
    private artistInfoService: ArtistInfoService,
    private server: CollectorService,
    private editNotifyService: EditNotifyService
  ) { }

  ngOnInit(): void {
    this.listCurrentPage = Number(localStorage.getItem('artworkCMSoffset'));
    this.listTotalPage = Number(localStorage.getItem('artworkCMStotalPage'));
    // this.artworkService.nextButton$.subscribe((data) => {
    // 	if (
    // 		data === '/artist-portal/settings/artwork/add/provenance' ||
    // 		data ===
    // 		'/artist-portal/settings/artwork/edit/' + this.id + '/provenance'
    // 	) {
    // 		this.onSubmit();
    // 	}
    // });
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetails = JSON.parse(decode);

    this.permissionsObj = JSON.parse(decode)
    ['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Provenance');

    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }

    this.form = this.formBuilder.group({
      ownershipHistory: this.formBuilder.array([
        // this.formBuilder.group({
        // 	// isOpen: new FormControl(true),
        // 	ownerName: new FormControl(''),
        // 	location: new FormControl(''),
        // 	from: new FormControl(''),
        // 	to: new FormControl(''),
        // 	details: new FormControl(''),
        // }),
      ]),
      exhibitionHistory: this.formBuilder.array([
        // this.formBuilder.group({
        // 	// isOpen: new FormControl(true),
        // 	description: new FormControl(''),
        // 	year: new FormControl(''),
        // }),
      ]),
      publicationHistory: this.formBuilder.array([
        // this.formBuilder.group({
        // 	// isOpen: new FormControl(true),
        // 	description: new FormControl(''),
        // 	year: new FormControl(''),
        // }),
      ]),
    });
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled'] =
          ele.disabled == 'true' || ele.disabled == true ? true : false;
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
      }
    });
    // this.route.parent.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getProvenanceData(this.id).subscribe((data) => {
    // 			const art = data;
    // 			art.ownershipHistory.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater();
    // 				}
    // 			});
    // 			art.exhibitionHistory.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater2();
    // 				}
    // 			});
    // 			art.publicationHistory.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater3();
    // 				}
    // 			});
    // 			this.form.patchValue(art);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });

    this.form.valueChanges.subscribe((x) => {
      if (this.Menuinit == null) {
        this.Menuinit = x;
      } else {
        this.editNotifyService.setOption('Provenance', true);
      }
    });

    console.log(this.disableObj);
  }

  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        if (res.data.publish_artworks && (this.userDetails.role == 'REPRESENTED ARTIST' || this.userDetails.role == 'TERRAIN CONSIGNED')) {
          this.isArtReadOnly = true;
        }
        res.data.ownership_history.forEach((ele, index) => {
          this.addRepeater();
          this.form['controls'].ownershipHistory['controls'][index]
            .get('ownerName')
            .setValue(ele.ownerName);
          this.form['controls'].ownershipHistory['controls'][index]
            .get('location')
            .setValue(ele.location);
          this.form['controls'].ownershipHistory['controls'][index]
            .get('from')
            .setValue(ele.from);
          this.form['controls'].ownershipHistory['controls'][index]
            .get('to')
            .setValue(ele.to);
          this.form['controls'].ownershipHistory['controls'][index]
            .get('details')
            .setValue(ele.details);
        });
        res.data.exhibition_history.forEach((ele, index) => {
          this.addRepeater2();
          this.form['controls'].exhibitionHistory['controls'][index]
            .get('year')
            .setValue(ele.year);
          this.form['controls'].exhibitionHistory['controls'][index]
            .get('description')
            .setValue(ele.description);
        });
        res.data.publication_history.forEach((ele, index) => {
          this.addRepeater3();
          this.form['controls'].publicationHistory['controls'][index]
            .get('year')
            .setValue(ele.year);
          this.form['controls'].publicationHistory['controls'][index]
            .get('description')
            .setValue(ele.description);
        });
      }
    });
  }

  get ownershipHistory(): FormArray {
    return <FormArray>this.form.get('ownershipHistory');
  }
  get exhibitionHistory(): FormArray {
    return <FormArray>this.form.get('exhibitionHistory');
  }
  get publicationHistory(): FormArray {
    return <FormArray>this.form.get('publicationHistory');
  }

  addRepeater() {
    this.ownershipHistory.push(
      this.formBuilder.group({
        // isOpen: new FormControl(true),
        ownerName: new FormControl(this.ownershipHistoryObj.owner_name),
        location: new FormControl(this.ownershipHistoryObj.location),
        from: new FormControl(
          this.ownershipHistoryObj.from.split('-').reverse().join('/')
        ),
        to: new FormControl(
          this.ownershipHistoryObj.to.split('-').reverse().join('/')
        ),
        details: new FormControl(this.ownershipHistoryObj.details),
      })
    );
  }
  addRepeater2() {
    this.exhibitionHistory.push(
      this.formBuilder.group({
        // isOpen: new FormControl(true),
        description: new FormControl(this.exhibitionHistoryObj.description),
        year: new FormControl(this.exhibitionHistoryObj.year),
      })
    );
  }
  addRepeater3() {
    this.publicationHistory.push(
      this.formBuilder.group({
        // isOpen: new FormControl(true),
        description: new FormControl(this.publicationHistoryObj.description),
        year: new FormControl(this.publicationHistoryObj.year),
      })
    );
  }

  async onFileSelect(files: FileList) {
    if (files[0].size < 2100000) {
      this.selectedFiles.push(files[0]);
    }
  }
  removeItem(index) {
    this.selectedFiles.splice(index, 1);
  }

  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill form correctly !');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'ownershipHistory')
        .mandatory &&
      this.form.value.ownershipHistory.length == 0
    ) {
      alert('Please enter ownership history!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'publicationHistory')
        .mandatory &&
      this.form.value.publicationHistory.length == 0
    ) {
      alert('Please enter publication history!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'exhibitionHistory')
        .mandatory &&
      this.form.value.exhibitionHistory.length == 0
    ) {
      alert('Please enter exhibition history!');
      return;
    }
    console.log(this.form.value.priceHistory);
    let url = apiUrl.addArtwork;
    let req = {
      ownership_history: this.form.getRawValue().ownershipHistory,
      exhibition_history: this.form.getRawValue().exhibitionHistory,
      publication_history: this.form.getRawValue().publicationHistory,
      tab: 'Provenance',
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }

    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        this.editNotifyService.reset();
        this.isSubmited = true;
        alert(res.message);
      } else {
        this.isSubmited = false;
      }
    });
  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }
}
