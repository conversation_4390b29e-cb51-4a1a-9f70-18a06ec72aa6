<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form *ngIf="form" [formGroup]="form">
          <div style="margin-top: 3.47vw; font-size: 1.25vw">
            <div
              class="sub-head"
              [hidden]="!showObj?.ownershipHistory"
              [ngClass]="{ 'Disabled-Color': disableObj.ownershipHistory }"
            >
              <p>Ownership History</p>
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [(ngModel)]="ownershipHistoryObj.owner_name"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Owner Name"
                    [disabled]="disableObj.ownershipHistory"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.ownershipHistory && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Owner Name</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [(ngModel)]="ownershipHistoryObj.location"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Location"
                    [disabled]="disableObj.ownershipHistory"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.ownershipHistory && !isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">Location</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="date"
                    [(ngModel)]="ownershipHistoryObj.from"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="From"
                    [disabled]="disableObj.ownershipHistory"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.ownershipHistory && !isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">From</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="date"
                    [(ngModel)]="ownershipHistoryObj.to"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="To"
                    [disabled]="disableObj.ownershipHistory"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.ownershipHistory && !isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">To</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [(ngModel)]="ownershipHistoryObj.details"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Details"
                    [disabled]="disableObj.ownershipHistory"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.ownershipHistory && !isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">Details</div>
                </div>
              </div>
              <div class="input-info">
                Provide ownership history for the work (giving artist's details
                as current owner for unsold works)
              </div>
              <p (click)="!isArtReadOnly && addRepeater()">
                <span
                  ><img class="plus-icon" src="assets/images/load-more.png"
                /></span>
                <span class="sub-text">Add to ownership history</span>
              </p>
            </div>
            <div class="outer-box" *ngIf="ownershipHistory.controls.length > 0">
              <div class="row">
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <label class="tb-head ml-35">Owner Name</label>
                </div>
                <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                  <label class="tb-head">Location</label>
                </div>
                <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                  <label class="tb-head">From</label>
                </div>
                <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                  <label class="tb-head">To</label>
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <label class="tb-head">Details</label>
                </div>
              </div>
              <div
                formArrayName="ownershipHistory"
                [dragula]="'task-group'"
                [(dragulaModel)]="ownershipHistory.controls"
              >
                <div
                  class="row mb-15 inner-box"
                  formGroupName="{{ i }}"
                  *ngFor="let item of ownershipHistory.controls; let i = index"
                >
                  <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                    <span class="cross-icon" style="cursor: grab !important">
                      <i class="fa fa-ellipsis-v mr2"></i>
                      <i class="fa fa-ellipsis-v"></i>
                    </span>
                    <input
                      formControlName="ownerName"
                      type="text"
                      class="full-width"
                    />
                  </div>
                  <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                    <input
                      formControlName="location"
                      type="text"
                      class="full-width"
                    />
                  </div>
                  <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                    <input
                      formControlName="from"
                      type="text"
                      class="full-width"
                    />
                  </div>
                  <div class="col-md-2 col-sm-2 col-lg-2 text-left">
                    <input
                      formControlName="to"
                      type="text"
                      class="full-width"
                    />
                  </div>
                  <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                    <input
                      formControlName="details"
                      type="text"
                      class="full-width"
                    />

                    <span
                      (click)="ownershipHistory.removeAt(i, 1)"
                      class="cross-icon"
                      >X</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="field-value doted" [dragula]='"task-group-1"' [(dragulaModel)]='ownershipHistory.controls'>
            <div formArrayName="ownershipHistory" id="selected" *ngFor="let item of ownershipHistory.controls; let i = index"
              class="wrap-collabsible">
              <div formGroupName="{{ i }}">
                <input formControlName="isOpen" class="toggle" type="checkbox" />
                <div (click)="
                    ownershipHistory.controls[i]
                      .get('isOpen')
                      .setValue(
                        !ownershipHistory.controls[i].get('isOpen').value
                      )
                  " class="lbl-toggle">
                  {{ i + 1 }} ) Owner Name: &nbsp;{{
                  ownershipHistory.controls[i].get("ownerName").value
                  }}
                  &nbsp;, Location :&nbsp;
                  {{ ownershipHistory.controls[i].get("location").value }}
                </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="ownerName" type="text" placeholder="Owner Name" />
                      <div class="placeholder">Owner Name</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="location" type="text" placeholder="Location" />
                      <div class="placeholder">Location</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="from" type="date" placeholder="From" />
                      <div class="placeholder">From</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="to" type="date" placeholder="To" />
                      <div class="placeholder">To</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="details" type="text" placeholder="Details" />
                      <div class="placeholder">Details</div>
                    </div>
                    <img class="cross-icon" (click)="ownershipHistory.removeAt(i,1)"
										src="assets/images/cross.png" *ngIf="i != 0">

                </div>
              </div>
            </div>
            <div class="input-info">
              Provide ownership history for the work (giving artist's details as
              current owner for unsold works)
            </div>
          </div> -->
          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.exhibitionHistory"
            [ngClass]="{ 'Disabled-Color': disableObj.exhibitionHistory }"
          >
            <div class="sub-head">
              <p>Exhibition History</p>
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [(ngModel)]="exhibitionHistoryObj.year"
                    [disabled]="disableObj.exhibitionHistory"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Year"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.exhibitionHistory && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Year</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [disabled]="disableObj.exhibitionHistory"
                    [(ngModel)]="exhibitionHistoryObj.description"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Description"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.exhibitionHistory && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Description</div>
                </div>
              </div>
              <div class="input-info">
                Provide exhibition history for the work (giving artist's details
                as current owner for unsold works)
              </div>
              <p (click)="!isArtReadOnly && addRepeater2()">
                <span
                  ><img class="plus-icon" src="assets/images/load-more.png"
                /></span>
                <span class="sub-text">Add to exhibition history</span>
              </p>
            </div>
            <div
              class="outer-box"
              *ngIf="exhibitionHistory.controls.length > 0"
            >
              <div class="row">
                <div class="col-md-6 col-sm-6 col-lg-6 text-left">
                  <label class="tb-head">Year</label>
                </div>
                <div class="col-md-6 col-sm-6 col-lg-6 text-left">
                  <label class="tb-head">Description</label>
                </div>
              </div>
              <div
                formArrayName="exhibitionHistory"
                [dragula]="'task-group-2'"
                [(dragulaModel)]="exhibitionHistory.controls"
              >
                <div
                  class="row mb-15 inner-box"
                  formGroupName="{{ i }}"
                  *ngFor="let item of exhibitionHistory.controls; let i = index"
                >
                  <div class="col-md-6 col-sm-6 col-lg-6 text-left d-flex">
                    <span class="cross-icon" style="cursor: grab !important">
                      <i class="fa fa-ellipsis-v mr2"></i>
                      <i class="fa fa-ellipsis-v"></i>
                    </span>
                    <input
                      formControlName="year"
                      type="text"
                      class="full-width"
                    />
                  </div>

                  <div class="col-md-6 col-sm-6 col-lg-6 text-left d-flex">
                    <input
                      formControlName="description"
                      type="text"
                      class="full-width"
                    />
                    <span
                      (click)="exhibitionHistory.removeAt(i, 1)"
                      class="cross-icon"
                      >X</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="field-value doted" [dragula]='"task-group-2"' [(dragulaModel)]='exhibitionHistory.controls'>
            <div formArrayName="exhibitionHistory" id="selected" *ngFor="let item of exhibitionHistory.controls; let i = index"
              class="wrap-collabsible">
              <div formGroupName="{{ i }}">
                <input formControlName="isOpen" class="toggle" type="checkbox" />
                <div (click)="
                    exhibitionHistory.controls[i]
                      .get('isOpen')
                      .setValue(
                        !exhibitionHistory.controls[i].get('isOpen').value
                      )
                  " class="lbl-toggle">
                  {{ i + 1 }} ) year: &nbsp;{{
                  exhibitionHistory.controls[i].get("year").value
                  }}
                </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="year" type="text" placeholder="Year" />
                      <div class="placeholder">Year</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="description" type="text" placeholder="Description" />
                      <div class="placeholder">Description</div>
                    </div>
                    <img class="cross-icon" (click)="exhibitionHistory.removeAt(i,1)"
										src="assets/images/cross.png" *ngIf="i != 0">
                  </div>
              </div>
            </div>
            <div class="input-info">
              Provide details of exhibition history for the artwork
            </div>
          </div> -->

          <div
            style="margin-top: 3.47vw; font-size: 1.25vw"
            [hidden]="!showObj?.publicationHistory"
            [ngClass]="{ 'Disabled-Color': disableObj.publicationHistory }"
          >
            <div class="sub-head">
              <p>Publication History</p>
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [(ngModel)]="publicationHistoryObj.year"
                    [disabled]="disableObj.publicationHistory"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Year"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.publicationHistory && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Year</div>
                </div>
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    [disabled]="disableObj.publicationHistory"
                    [(ngModel)]="publicationHistoryObj.description"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Description"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.publicationHistory && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Description</div>
                </div>
              </div>
              <div class="input-info">
                Provide publication history for the work (giving artist's
                details as current owner for unsold works)
              </div>
              <p (click)="!isArtReadOnly && addRepeater3()">
                <span
                  ><img class="plus-icon" src="assets/images/load-more.png"
                /></span>
                <span class="sub-text">Add to publication history</span>
              </p>
            </div>
            <div
              class="outer-box"
              *ngIf="publicationHistory.controls.length > 0"
            >
              <div class="row">
                <div class="col-md-6 col-sm-6 col-lg-6 text-left">
                  <label class="tb-head">Year</label>
                </div>
                <div class="col-md-6 col-sm-6 col-lg-6 text-left">
                  <label class="tb-head">Description</label>
                </div>
              </div>
              <div
                formArrayName="publicationHistory"
                [dragula]="'task-group-3'"
                [(dragulaModel)]="publicationHistory.controls"
              >
                <div
                  class="row mb-15 inner-box"
                  formGroupName="{{ i }}"
                  *ngFor="
                    let item of publicationHistory.controls;
                    let i = index
                  "
                >
                  <div class="col-md-6 col-sm-6 col-lg-6 text-left d-flex">
                    <span class="cross-icon" style="cursor: grab !important">
                      <i class="fa fa-ellipsis-v mr2"></i>
                      <i class="fa fa-ellipsis-v"></i>
                    </span>
                    <input
                      formControlName="year"
                      type="text"
                      class="full-width"
                    />
                  </div>

                  <div class="col-md-6 col-sm-6 col-lg-6 text-left d-flex">
                    <input
                      formControlName="description"
                      type="text"
                      class="full-width"
                    />
                    <span
                      (click)="publicationHistory.removeAt(i, 1)"
                      class="cross-icon"
                      >X</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="field-value doted" [dragula]='"task-group-3"' [(dragulaModel)]='publicationHistory.controls'>
            <div formArrayName="publicationHistory" id="selected" *ngFor="let item of publicationHistory.controls; let i = index"
              class="wrap-collabsible">
              <div formGroupName="{{ i }}">
                <input formControlName="isOpen" class="toggle" type="checkbox" />
                <div (click)="
                    publicationHistory.controls[i]
                      .get('isOpen')
                      .setValue(
                        !publicationHistory.controls[i].get('isOpen').value
                      )
                  " class="lbl-toggle">
                  {{ i + 1 }} ) year: &nbsp;{{
                  publicationHistory.controls[i].get("year").value
                  }}
                </div>
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="year" type="text" placeholder="Year" />
                      <div class="placeholder">Year</div>
                    </div>
                    <div class="field-value" style="padding-right: 0.5vw">
                      <input formControlName="description" type="text" placeholder="Description" />
                      <div class="placeholder">Description</div>
                    </div>
                    <img class="cross-icon" (click)="publicationHistory.removeAt(i,1)"
										src="assets/images/cross.png" *ngIf="i != 0">
                  </div>
              </div>
            </div>
            <div class="input-info">
              Provide details of publication history for the artwork
            </div>
          </div> -->
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div style="margin-right: 2vw">
              Page
              {{ listCurrentPage }}
              Of {{ listTotalPage }}
            </div>
            <div (click)="onSubmit()" class="next">Save</div>
            <div (click)="getValueWithAsync()" class="next">Save & Close</div>
            <div [hidden]="true" class="next" style="margin-right: 1.5vw">
              Duplicate
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
