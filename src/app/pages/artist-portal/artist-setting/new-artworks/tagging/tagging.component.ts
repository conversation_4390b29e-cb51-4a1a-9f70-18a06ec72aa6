import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { EditNotifyService } from './../edit-notify.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
@Component({
  selector: 'app-artworks-tagging',
  templateUrl: './tagging.component.html',
  styleUrls: ['./tagging.component.scss'],
})
export class TaggingArtworksComponent implements OnInit {
  form: FormGroup;
  selectedKeywords;
  dropDownValue = Array(10).fill(null);
  htmlContent = Array(10).fill(null);
  isSubmited = false;
  isDropDownOpen = Array(10).fill(false);
  moveArr: any = [];
  showMovement: boolean = false;
  selectedMovements: any = [];
  selectedFiles: File[] = [];
  movement;
  objDropDown: any = {
    artworkFilterArr: [],
    roomsArr: [],
    tagsArr: [],
    colorArr: [],
    colorPrimaryArr: [],
    subjectKeywordsArr: [],
    mediumKeywordArr: [],
    AITags: [],
  };
  selectedObj: any = {
    artworkFilterArr: [],
    roomsArr: [],
    tagsArr: [],
    colorArr: [],
    colorPrimaryArr: [],
    subjectKeywordsArr: [],
    mediumKeywordArr: [],
    AITags: [],
  };
  filterObj: any = {
    artworkFilter: false,
    showMovement: false,
    mediumKeyword: false,
    subjectKeywords: false,
    color: false,
    colorPrimary: false,
    tags: false,
    rooms: false,
    AITags: false,
  };
  permissionsObj: any = {};
  showObj: any = {};
  disableObj: any = {};
  Menuinit: any;
  iterableDiffer;

  // AiTagsTest: any =[
  // "aninmal",
  // "Bird",
  // "Blue",
  // "Fruit",
  // "Table",
  // "Horse"]
  AiTagsTest: any = {
    // "bird": {
    //   "confidence": 0.9059681296348572,
    //   "size": 0.0892754658962423,
    //   "colors": [
    //     "red"
    //   ]
    // },
    // "kite": {
    //   "confidence": 0.5764411687850952,
    //   "size": 0.07781378950815404,
    //   "colors": [
    //     "red"
    //   ]
    // },
    // "person": {
    //   "confidence": 0.5589200854301453,
    //   "size": 0.15242808841236233,
    //   "colors": [
    //     "red"
    //   ]
    // }
  };
  AiTagKeys = [];
  listCurrentPage;
  listTotalPage;

  constructor(
    private router: Router,
    private server: CollectorService,
    private editNotifyService: EditNotifyService,
    private formBuilder: FormBuilder
  ) {
    // this.selectedObj.artworkFilterArr = [];
    // this.selectedObj.roomsArr = []
    // this.selectedObj.tagsArr = []
    // this.selectedObj.colorArr = []
    // this.selectedObj.colorPrimaryArr = []
    // this.selectedObj.subjectKeywordsArr = []
    // this.selectedObj.mediumKeywordArr = []
  }

  getSelectedItem(items: Array<string>, index) {
    switch (index) {
      case 0:
        this.selectedObj.artworkFilterArr = items;
        break;
      case 1:
        this.selectedObj.mediumKeyword = items;
        break;
      case 2:
        this.selectedMovements = items;
        break;
      case 3:
        this.selectedObj.subjectKeywordsArr = items;
        break;
      case 4:
        this.selectedObj.colorPrimaryArr = items;
        break;
      case 5:
        this.selectedObj.colorArr = items;
        break;
      case 6:
        this.selectedObj.tagsArr = items;
        break;
      case 7:
        this.selectedObj.roomsArr = items;
        break;
      case 8:
        this.selectedObj.AITags = items;
        console.log(items);
        break;

      default:
        break;
    }
    this.selectedKeywords = items;
  }

  ngOnInit(): void {
    this.listCurrentPage = Number(localStorage.getItem('artworkCMSoffset'));
    this.listTotalPage = Number(localStorage.getItem('artworkCMStotalPage'));
    this.form = this.formBuilder.group({});

    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );

    this.permissionsObj = JSON.parse(decode)
      ['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Tagging');
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;

      // if(ele.formControl && ele.show && ele.mandatory) {
      // 	this.form.controls[ele.name].setValidators([Validators.required]);
      // }
    });
    this.getMediumKeywords();
    this.getAllMovements();
    this.getArtKeywords();
    this.getSubjectKeywords();
    this.getTags();
    this.getRoom();
    this.getFilter();
    this.getColour();
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }

    // console.log(this.disableObj);
    // this.AiTagKeys = Object.keys(this.AiTagsTest);
    // console.log(this.AiTagKeys);
  }
  addToItems45(obj) {
    let index = this.selectedMovements.findIndex((x) => x['_id'] == obj['_id']);
    console.log(index);
    if (index == -1) {
      this.selectedMovements.push(obj);
    }
    this.showMovement = false;
    this.editNotifyService.setOption('Tagging', true);
  }

  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.selectedObj = {
          artworkFilterArr: [],
          roomsArr: res.data.by_room || [],
          colorPrimaryArr: [],
          tagsArr: res.data.tags || [],
          subjectKeywordsArr: res.data.subjects_keywords || [],
          mediumKeywordArr: res.data.medium_keywords || [],
          colorArr: res.data.colours || [],
          AITags: res.data.ai_tags || [],
        };
        if (res.data.ai_metadata) {
          this.AiTagsTest = res.data.ai_metadata;
          this.AiTagKeys = Object.keys(res.data.ai_metadata);
        } else {
          if (res.data.primary_image.length > 0) {
            this.getAITags(res.data.primary_image[0].url);
          } else {
            alert('Please upload primary image !');
          }
        }

        // this.selectedObj.colorPrimaryArr = [];
        // this.selectedObj.colorArr = []
        if (res.data.artwork_filter) {
          this.selectedObj.artworkFilterArr.push(res.data.artwork_filter);
        }
        console.log(this.selectedObj.artworkFilterArr);
        if (res.data.colour) {
          this.selectedObj.colorPrimaryArr.push(res.data.colour);
        }
        if (res.data.movement.length) {
          this.selectedMovements = res.data.movement;
        }
      }
    });
  }

  getAITags(img_url) {
    let url = apiUrl.artworks.aiTags + `/${img_url}`;
    this.server.showSpinner();
    this.server.getThirdParty(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.AiTagsTest = res.ai_metadata;
        this.AiTagKeys = Object.keys(this.AiTagsTest);
      }
    });
  }

  // to get tags
  getRoom() {
    let url = apiUrl.getArtwork + `/filter?search=by_room`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown.roomsArr = res.data;
      }
    });
  }
  // to get tags
  getFilter() {
    let url = apiUrl.getArtwork + `/filter?search=artwork_filter`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown.artworkFilterArr = res.data;
      }
    });
  }
  // to get tags
  getTags() {
    let url = apiUrl.getArtwork + `/filter?search=tags`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown.tagsArr = res.data;
      }
    });
  }
  // to get tags
  getColour() {
    let url = apiUrl.getArtwork + `/filter?search=colour`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown.colorArr = res.data;
        this.objDropDown.colorPrimaryArr = res.data;
      }
    });
  }

  // to get medium
  getArtKeywords() {
    this.server.showSpinner();
    let url = apiUrl.getArtwork + `/filter?search=artwork_filter`;
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown['artworkFilterArr'] = res.data.sort();
      }
    });
  }

  // to get medium
  getMediumKeywords() {
    let url = apiUrl.getMedium + `?limit=999&offset=1`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown['mediumKeywordArr'] = res.data.sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) {
            return -1;
          }
          if (a.name.toLowerCase() > b.name.toLowerCase()) {
            return 1;
          }
          return 0;
        });
      }
    });
  }
  getAllMovements() {
    let url = apiUrl.getMovement + '?limit=999&offset=1';
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.moveArr = res.data.sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) {
            return -1;
          }
          if (a.name.toLowerCase() > b.name.toLowerCase()) {
            return 1;
          }
          return 0;
        });
      }
    });
  }

  // to get subject
  getSubjectKeywords() {
    let url = apiUrl.getSubject + `?limit=999&offset=1`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.objDropDown['subjectKeywordsArr'] = res.data.sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) {
            return -1;
          }
          if (a.name.toLowerCase() > b.name.toLowerCase()) {
            return 1;
          }
          return 0;
        });
      }
    });
  }

  // to add to items
  addToItems(obj, arr_key, boolean_key) {
    let index = this.selectedObj[arr_key].findIndex(
      (x) => x['_id'] == obj['_id']
    );

    console.log(index);

    if (index == -1) {
      this.selectedObj[arr_key].push(obj);
    }
    this.filterObj[boolean_key] = false;
    this.editNotifyService.setOption('Tagging', true);
  }
  // to add to items
  addToItems2(obj, arr_key, boolean_key) {
    if (arr_key == 'artworkFilterArr' || arr_key == 'colorPrimaryArr') {
      this.selectedObj[arr_key] = [];
    }
    let index = this.selectedObj[String(arr_key)].findIndex((x) => x == obj);
    console.log(index);
    if (index == -1) {
      this.selectedObj[arr_key].push(obj);
    }
    console.log(this.selectedObj[arr_key]);
    this.filterObj[boolean_key] = false;
    this.editNotifyService.setOption('Tagging', true);
  }

  // to remove items
  removeItems(index, arr_key) {
    this.selectedObj[arr_key].splice(index, 1);
    this.editNotifyService.setOption('Tagging', true);
  }

  onSubmit() {
    if (
      this.permissionsObj.fields.find((x) => x.name == 'artworkFilterArr')
        .mandatory &&
      this.selectedObj.artworkFilterArr.length == 0
    ) {
      alert('Please select artwork filter!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'mediumKeywordArr')
        .mandatory &&
      this.selectedObj.mediumKeywordArr.length == 0
    ) {
      alert('Please select medium keywords!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'movements').mandatory &&
      this.selectedMovements.length == 0
    ) {
      alert('Please select movements!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'subjectKeywordsArr')
        .mandatory &&
      this.selectedObj.subjectKeywordsArr.length == 0
    ) {
      alert('Please select subject keywords!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'colorArr').mandatory &&
      this.selectedObj.colorArr.length == 0
    ) {
      alert('Please select colour!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'colorPrimaryArr')
        .mandatory &&
      this.selectedObj.colorPrimaryArr.length == 0
    ) {
      alert('Please select primary colour!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'tagsArr').mandatory &&
      this.selectedObj.tagsArr.length == 0
    ) {
      alert('Please select other tags!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'roomsArr').mandatory &&
      this.selectedObj.roomsArr.length == 0
    ) {
      alert('Please select by room!');
      return;
    }
    // if ((this.permissionsObj.fields.find(x => x.name == 'selectedMovements')).mandatory && this.selectedMovements.length == 0) {
    // 	alert('Please select movements!')
    // 	return;
    // }
    // if(this.form.invalid){
    // 	alert('Form not valid.Please fill form correctly !')
    // 	return;
    // }
    // console.log(this.form.value.priceHistory)

    let url = apiUrl.addArtwork;
    let req = {
      artwork_filter: this.selectedObj.artworkFilterArr[0],
      medium_keywords: this.selectedObj.mediumKeywordArr,
      subjects_keywords: this.selectedObj.subjectKeywordsArr,
      colour: this.selectedObj.colorPrimaryArr[0],
      colours: this.selectedObj.colorArr,
      tags: this.selectedObj.tagsArr,
      by_room: this.selectedObj.roomsArr,
      movement: this.selectedMovements,
      ai_metadata: this.selectedObj.AITags,
      tab: 'Tagging',
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }

    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        this.editNotifyService.reset();
        this.isSubmited = true;
        alert(res.message);
      } else {
        this.isSubmited = false;
      }
    });
  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }
  removeMovItems(k) {
    this.selectedMovements.splice(k, 1);
    this.editNotifyService.setOption('Tagging', true);
  }

  changeFocus(index) {
    if (index == 1) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.mediumKeyword = false;
      }, 200);
    }
    if (index == 5) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.showMovement = false;
      }, 200);
    }
    if (index == 2) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.subjectKeywords = false;
      }, 200);
    }
    if (index == 3) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.color = false;
      }, 200);
    }
    if (index == 4) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.tags = false;
      }, 200);
    }
    if (index == 5) {
      setTimeout(() => {
        // this.isDropDownOpen[index] = false;
        this.filterObj.rooms = false;
      }, 200);
    } else {
      setTimeout(() => {
        this.isDropDownOpen[index] = false;
      }, 200);
    }
  }
}
