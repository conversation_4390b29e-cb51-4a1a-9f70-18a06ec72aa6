<form [formGroup]="form" class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <!-- <div class="field-value" [hidden]="!showObj?.artworkFilterArr">
          <div class="input-container" (focusout)="changeFocus(0)">
            <input
              type="text"
              class="selection"
              placeholder="Select an option"
              (focus)="isDropDownOpen[0] = true"
              readonly="readonly"
            />
            <div class="placeholder">Artwork Filter</div>
            <button
              (click)="filterObj.artworkFilter = !filterObj.artworkFilter"
            >
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>
            <div
              [ngClass]="{
                'dropdown-hidden': !isDropDownOpen[0],
                'dropdown-visible': isDropDownOpen[0]
              }"
            >
              <ul>
                <li
                  *ngFor="let item_mov of objDropDown.artworkFilterArr"
                  (click)="
                    addToItems2(item_mov, 'artworkFilterArr', 'artworkFilter')
                  "
                >
                  <div class="country-name">{{ item_mov }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Choose how to categorise the work on Buy page
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                *ngFor="
                  let item_mov of selectedObj?.artworkFilterArr;
                  let k = index
                "
                class="chip"
              >
                {{ item_mov }}
                <button
                  class="buttons"
                  (click)="removeItems(k, 'artworkFilterArr')"
                >
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>

            </div>
          </div>
        </div> -->

        <div class="field-value" [hidden]="!showObj?.artworkFilterArr">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.artworkFilterArr"
            [previosItems]="selectedObj.artworkFilterArr"
            (selectedItem)="getSelectedItem($event, 0)"
            [Disable]="disableObj.artworkFilterArr"
          ></multi-input>
          <div class="placeholder">Artwork Filter</div>
          <div class="input-info">
            Choose all applicable keywords describing the Artwork.
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.mediumKeywordArr">
                    <div class="input-container" (focusout)="changeFocus(1)">
                        <input type="text" class="selection" placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list" (focus)="filterObj.mediumKeyword = true" readonly="readonly" />
                        <div class="placeholder">Medium Keywords</div>

                        <button (click)="filterObj.mediumKeyword = !filterObj.mediumKeyword">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

                        <div [ngClass]="
                filterObj.mediumKeyword ? 'dropdown-visible' : 'dropdown-hidden'
              ">
                            <ul>
                                <li (click)="
                    addToItems(item_mov, 'mediumKeywordArr', 'mediumKeyword')
                  " *ngFor="let item_mov of objDropDown?.mediumKeywordArr">
                                    <div class="country-name">{{ item_mov?.name }}</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="input-info">
                        Choose all applicable keywords describing the artwork medium/materials used
                    </div>
                    <div class="input-info">
                        <div class="roww">
                            <div *ngFor="
                  let item_mov of selectedObj?.mediumKeywordArr;
                  let k = index
                " class="chip">
                                {{ item_mov?.name }}
                                <button class="buttons" (click)="removeItems(k, 'mediumKeywordArr')">
                  <img src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>" />
                </button>
                            </div>


                        </div>
                    </div>
                </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.mediumKeywordArr"
            [previosItems]="selectedObj.mediumKeywordArr"
            (selectedItem)="getSelectedItem($event, 1)"
            [Disable]="disableObj.mediumKeywordArr"
          ></multi-input>
          <div class="placeholder">Medium Keywords</div>
          <div class="input-info">
            Choose all applicable keywords describing the artwork
            medium/materials used
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.movements">
          <div class="input-container" (focusout)="changeFocus(5)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
              (focus)="filterObj.showMovement = true"
              readonly="readonly"
            />
            <div class="placeholder">Movement Keywords</div>
            <button (click)="filterObj.showMovement = !filterObj.showMovement">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

            <div
              [ngClass]="
                filterObj.showMovement ? 'dropdown-visible' : 'dropdown-hidden'
              "
            >
              <ul>
                <li
                  (click)="addToItems45(item_mov)"
                  *ngFor="let item_mov of moveArr"
                >
                  <div class="country-name">{{ item_mov?.name }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Choose the movement keywords associated with the artwork
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="let item_mov of selectedMovements; let k = index"
              >
                {{ item_mov?.name }}
                <button class="buttons" (click)="removeMovItems(k)">
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="moveArr"
            [previosItems]="selectedMovements"
            (selectedItem)="getSelectedItem($event, 2)"
            [Disable]="disableObj.movements"
          ></multi-input>
          <div class="placeholder">Movement Keywords</div>
          <div class="input-info">
            Choose all applicable keywords describing the artwork
            medium/materials used
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.subjectKeywordsArr">
          <div class="input-container" (focusout)="changeFocus(2)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
              (focus)="filterObj.subjectKeywords = true"
              readonly="readonly"
            />
            <div class="placeholder">Subjects Keywords</div>

            <button
              (click)="filterObj.subjectKeywords = !filterObj.subjectKeywords"
            >
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

            <div
              [ngClass]="
                filterObj.subjectKeywords
                  ? 'dropdown-visible'
                  : 'dropdown-hidden'
              "
            >
              <ul>
                <li
                  (click)="
                    addToItems(
                      item_mov,
                      'subjectKeywordsArr',
                      'subjectKeywords'
                    )
                  "
                  *ngFor="let item_mov of objDropDown.subjectKeywordsArr"
                >
                  <div class="country-name">{{ item_mov?.name }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Provide keywords about the subject of the work. e.g.: Irises, Still
            Life, Sky
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="
                  let item_mov of selectedObj.subjectKeywordsArr;
                  let k = index
                "
              >
                {{ item_mov?.name }}
                <button
                  class="buttons"
                  (click)="removeItems(k, 'subjectKeywordsArr')"
                >
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.subjectKeywordsArr"
            [previosItems]="selectedObj.subjectKeywordsArr"
            (selectedItem)="getSelectedItem($event, 3)"
            [Disable]="disableObj.subjectKeywordsArr"
          ></multi-input>
          <div class="placeholder">Subjects Keywords</div>
          <div class="input-info">
            Provide keywords about the subject of the work. e.g.: Irises, Still
            Life, Sky
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.colorPrimaryArr">
          <div class="input-container" (focusout)="changeFocus(6)">
            <input
              type="text"
              class="selection"
              placeholder="Primary Colour"
              (focus)="isDropDownOpen[6] = true"
              readonly="readonly"
            />
            <div class="placeholder">Primary Colour</div>
            <button (click)="isDropDownOpen[6] = !isDropDownOpen[6]">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>
            <div
              [ngClass]="{
                'dropdown-hidden': !isDropDownOpen[6],
                'dropdown-visible': isDropDownOpen[6]
              }"
            >
              <ul>
                <li
                  *ngFor="let item_mov of objDropDown.colorArr"
                  (click)="
                    addToItems2(item_mov, 'colorPrimaryArr', 'colorPrimary');
                    isDropDownOpen[6] = false
                  "
                >
                  <div class="country-name">{{ item_mov }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Select the primary colour of the artwork (for tagging purposes)
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="
                  let item_mov of selectedObj.colorPrimaryArr;
                  let k = index
                "
              >
                {{ item_mov }}
                <button
                  class="buttons"
                  (click)="removeItems(k, 'colorPrimaryArr')"
                >
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add Primary Colour"
            [itemsArray]="objDropDown.colorPrimaryArr"
            [previosItems]="selectedObj.colorPrimaryArr"
            (selectedItem)="getSelectedItem($event, 4)"
            [Disable]="disableObj.colorPrimaryArr"
          ></multi-input>
          <div class="placeholder">Primary Colour</div>
          <div class="input-info">
            Select the primary colour of the artwork (for tagging purposes)
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.colorArr">
          <div class="input-container" (focusout)="changeFocus(3)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
              (focus)="filterObj.color = true"
              readonly="readonly"
            />
            <div class="placeholder">Colours</div>

            <button (click)="filterObj.color = !filterObj.color">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

            <div
              [ngClass]="
                filterObj.color ? 'dropdown-visible' : 'dropdown-hidden'
              "
            >
              <ul>
                <li
                  (click)="addToItems2(item_mov, 'colorArr', 'color')"
                  *ngFor="let item_mov of objDropDown.colorArr"
                >
                  <div class="country-name">{{ item_mov }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Select all relevant colours from the artwork
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="let item_mov of selectedObj.colorArr; let k = index"
              >
                {{ item_mov }}
                <button class="buttons" (click)="removeItems(k, 'colorArr')">
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.colorArr"
            [previosItems]="selectedObj.colorArr"
            (selectedItem)="getSelectedItem($event, 5)"
            [Disable]="disableObj.colorArr"
          ></multi-input>
          <div class="placeholder">Colours</div>
          <div class="input-info">
            Select all relevant colours from the artwork
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.tagsArr">
          <div class="input-container" (focusout)="changeFocus(4)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
              (focus)="filterObj.tags = true"
              readonly="readonly"
            />
            <div class="placeholder">Other Tags</div>

            <button (click)="filterObj.tags = !filterObj.tags">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

            <div
              [ngClass]="
                filterObj.tags ? 'dropdown-visible' : 'dropdown-hidden'
              "
            >
              <ul>
                <li
                  (click)="addToItems2(item_mov, 'tagsArr', 'tags')"
                  *ngFor="let item_mov of objDropDown.tagsArr"
                >
                  <div class="country-name">{{ item_mov }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Provide relevant tags for the artworks that are not covered under
            Movement, Medium, Subject keywords
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="let item_mov of selectedObj.tagsArr; let k = index"
              >
                {{ item_mov }}
                <button class="buttons" (click)="removeItems(k, 'tagsArr')">
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.tagsArr"
            [previosItems]="selectedObj.tagsArr"
            (selectedItem)="getSelectedItem($event, 6)"
            [Disable]="disableObj.tagsArr"
          ></multi-input>
          <div class="placeholder">Other Tags</div>
          <div class="input-info">
            Provide relevant tags for the artworks that are not covered under
            Movement, Medium, Subject keywords
          </div>
        </div>

        <!-- <div class="field-value" [hidden]="!showObj?.roomsArr">
          <div class="input-container" (focusout)="changeFocus(5)">
            <input
              type="text"
              class="selection"
              placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
              (focus)="filterObj.rooms = true"
              readonly="readonly"
            />
            <div class="placeholder">By Room</div>

            <button (click)="filterObj.rooms = !filterObj.rooms">
              <img src="assets/icons/arrow-down.png" class="flag-arrow" />
            </button>

            <div
              [ngClass]="
                filterObj.rooms ? 'dropdown-visible' : 'dropdown-hidden'
              "
            >
              <ul>
                <li
                  (click)="addToItems2(item_mov, 'roomsArr', 'rooms')"
                  *ngFor="let item_mov of objDropDown.roomsArr"
                >
                  <div class="country-name">{{ item_mov }}</div>
                </li>
              </ul>
            </div>
          </div>
          <div class="input-info">
            Provide recommendations for which rooms the artwork would be most
            suited for (based on size, colour, etc.).
          </div>
          <div class="input-info">
            <div class="roww">
              <div
                class="chip"
                *ngFor="let item_mov of selectedObj.roomsArr; let k = index"
              >
                {{ item_mov }}
                <button class="buttons" (click)="removeItems(k, 'roomsArr')">
                  <img
                    src="../../../../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
                  />
                </button>
              </div>
            </div>
          </div>
        </div> -->

        <div class="field-value">
          <multi-input
            placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
            [itemsArray]="objDropDown.roomsArr"
            [previosItems]="selectedObj.roomsArr"
            (selectedItem)="getSelectedItem($event, 7)"
            [Disable]="disableObj.roomsArr"
          ></multi-input>
          <div class="placeholder">By Room</div>
          <div class="input-info">
            Provide recommendations for which rooms the artwork would be most
            suited for (based on size, colour, etc.)
          </div>
        </div>

        <div class="field-value">
          <multi-input
            placeholder="Type a new tag/keyword and press enter to add as confidence level 1.
            "
            [AIsuggestedTags]="AiTagsTest"
            (selectedItemAI)="getSelectedItem($event, 8)"
            [Disable]="disableObj.AITags"
            [Type]="2"
          ></multi-input>
          <div class="placeholder">AI Suggested Tags</div>
          <div class="input-info">
            Verify the AI tags by using the tick mark, edit the tag if details
            are incorrect. Any tags edited or verified will be treated as
            confidence level 1, with previous size and colour values maintained.
            Remove the tag if the keyword is incorrectly assigned.
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<div class="footer-nav">
  <div class="button-group">
    <div style="margin-right: 2vw">
      Page
      {{ listCurrentPage }}
      Of {{ listTotalPage }}
    </div>
    <div (click)="onSubmit()" class="next">Save</div>
    <div (click)="getValueWithAsync()" class="next">Save & Close</div>
    <div [hidden]="true" class="next" style="margin-right: 1.5vw">
      Duplicate
    </div>
  </div>
</div>
