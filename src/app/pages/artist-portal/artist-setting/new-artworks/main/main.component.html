<div class="container__main" #scrollTop>
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div
              class="field-value Disabled-Color"
              style="padding-right: 0.5vw"
              *ngIf="artworksObj?.artist_id?.display_name"
            >
              <input
                type="text"
                [value]="artworksObj?.artist_id?.display_name"
                placeholder="Artist Name"
                [disabled]="true"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              >
              </fa-icon>
              <div class="placeholder">Artist Name</div>
              <!-- <div class="input-info">
              Provide the complete title of the artwork
            </div> -->
            </div>
            <div
              class="field-value Disabled-Color"
              style="padding-right: 0.5vw"
              *ngIf="
                !artworksObj?.artist_id?.display_name &&
                userDetails.role == 'GALLERY'
              "
            >
              <div class="input-container" (focusout)="changeFocus(6)">
                <input
                  type="text"
                  class="selection"
                  [(ngModel)]="selectedArtist.display_name"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Artist Name"
                  (focus)="isDropDownOpen[6] = true"
                  (focusout)="changeFocus(6)"
                />
                <div class="placeholder">Artist Name</div>

                <button (click)="isDropDownOpen[6] = !isDropDownOpen[6]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[6]">
                  <ul>
                    <li
                      *ngFor="let item of artistList"
                      (click)="isDropDownOpen[6] = false; selectedArtist = item"
                    >
                      <div class="country-name">{{ item?.display_name }}</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork is Physical, Digital, or Both.
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.createdAs"
              [ngClass]="{ 'Disabled-Color': disableObj.createdAs }"
            >
              <div class="input-container" (focusout)="changeFocus(0)">
                <input
                  formControlName="createdAs"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.createdAs ? null : (isDropDownOpen[0] = true)
                  "
                  readonly
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.createdAs && !isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Created</div>
                <button
                  (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                  [attr.disabled]="isArtReadOnly ? true : null"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[0]">
                  <!-- [ngClass]="{'dropdown-hidden': isDropDownOpen[0]==false,'dropdown-visible': isDropDownOpen[0]==true}" -->
                  <!-- [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[0],
                    'dropdown-visible': isDropDownOpen[0]
                  }" -->
                  <ul>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form.get('createdAs').setValue('Individually');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">individually</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form.get('createdAs').setValue('Collaboratively');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">collaboratively</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork was created individually or in
                collaboration with another artist
              </div>
            </div>
            <div
              *ngIf="form.get('createdAs').value == 'Collaboratively'"
              class="field-value"
            >
              <input
                formControlName="createdBy"
                type="text"
                placeholder="Only if applicable"
                [readonly]="isArtReadOnly"
              />
              <div class="placeholder">Created By</div>
              <div class="input-info">
                Provide the creator details if work created collaboratively.
                e.g.: Created in collaboration with Artist XYZ
              </div>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.title"
              [ngClass]="{ 'Disabled-Color': disableObj.title }"
            >
              <input
                formControlName="title"
                type="text"
                placeholder="Artwork Title"
                [disabled]="disableObj.title"
                [attr.disabled]="isArtReadOnly ? true : null"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.title && !isArtReadOnly"
              >
              </fa-icon>
              <div class="placeholder">Artwork Title</div>
              <div class="input-info">
                Provide the complete title of the artwork
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.dimensions"
              [ngClass]="{ 'Disabled-Color': disableObj.dimensions }"
            >
              <input
                formControlName="dimensions"
                type="text"
                placeholder="H x W cm | H x W in"
                [disabled]="disableObj.dimensions"
                [attr.disabled]="isArtReadOnly ? true : null"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.dimensions && !isArtReadOnly"
              >
              </fa-icon>

              <div class="placeholder">Dimensions</div>
              <div
                class="input-info"
                title=" Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
              each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x
              3.3d in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm
              (variable) | 7 x 5 x 1 in (variable); 1080x1080 px"
              >
                Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
                each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x
                3.3d in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm
                (variable) | 7 x 5 x 1 in (variable); 1080x1080 px
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.year"
              [ngClass]="{ 'Disabled-Color': disableObj.year }"
            >
              <input
                formControlName="year"
                type="text"
                placeholder="Year"
                [disabled]="disableObj.year"
                [attr.disabled]="isArtReadOnly ? true : null"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.year && !isArtReadOnly"
              >
              </fa-icon>

              <div class="placeholder">Year</div>
              <div class="input-info">
                Provide year (e.g.: 2018) or range (e.g.: 2016-2018)
              </div>
            </div>
          </div>
          <div class="splitter">
            <!-- <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.dimensions"
              [ngClass]="{ 'Disabled-Color': disableObj.dimensions }"
            >
              <input
                formControlName="dimensions"
                type="text"
                placeholder="Dimensions"
                [disabled]="disableObj.dimensions"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.dimensions"
              >
              </fa-icon>

              <div class="placeholder">Dimensions</div>
              <div class="input-info">
                Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
                each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x
                3.3d in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm
                (variable) | 7 x 5 x 1 in (variable); 1080x1080 px
              </div>
            </div> -->
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.medium"
              [ngClass]="{ 'Disabled-Color': disableObj.medium }"
            >
              <input
                formControlName="medium"
                type="text"
                placeholder="Medium"
                [disabled]="disableObj.medium"
                [attr.disabled]="isArtReadOnly ? true : null"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.medium && !isArtReadOnly"
              >
              </fa-icon>

              <div class="placeholder">Medium of work</div>
              <div
                class="input-info"
                title=" Provide complete details of the artwork medium (as detailed as
                possible). e.g.: Digital Image, JPEG; Painting on canvas;
                Acrylic paint on paper"
              >
                Provide complete details of the artwork medium (as detailed as
                possible). e.g.: Digital Image, JPEG; Painting on canvas;
                Acrylic paint on paper
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.type"
              [ngClass]="{ 'Disabled-Color': disableObj.type }"
            >
              <div class="input-container" (focusout)="changeFocus(2)">
                <input
                  type="text"
                  class="selection"
                  formControlName="type"
                  placeholder="Choose an option"
                  (focus)="disableObj.type ? null : (isDropDownOpen[2] = true)"
                  readonly="readonly"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.type && !isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Artwork Type</div>
                <button
                  (click)="isDropDownOpen[2] = !isDropDownOpen[2]"
                  [attr.disabled]="isArtReadOnly ? true : null"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[2]">
                  <ul>
                    <li
                      (click)="
                        form.get('type').setValue('textile arts');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">textile arts</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('painting');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">painting</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('drawing');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">drawing</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('photography');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">photography</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('digital');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">digital</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('print');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">print</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('mixed media');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">mixed media</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('video');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">video</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('other');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">other</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sculpture');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sculpture</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('installation');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">installation</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sound');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sound</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the primary artwork type from the drop-down list
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.workType"
              [ngClass]="{ 'Disabled-Color': disableObj.workType }"
            >
              <div class="input-container" (focusout)="changeFocus(3)">
                <input
                  formControlName="workType"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.workType ? null : (isDropDownOpen[3] = true)
                  "
                  readonly="readonly"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.workType && !isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Work Type</div>
                <button
                  (click)="isDropDownOpen[3] = !isDropDownOpen[3]"
                  [attr.disabled]="isArtReadOnly ? true : null"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[3]">
                  <ul>
                    <li
                      (click)="
                        form.get('workType').setValue('unique');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">unique</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('Unique - Diptych');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">Unique - Diptych</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('Unique - Triptych');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">Unique - Triptych</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('Unique - Set of works');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">Unique - Set of works</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('edition');
                        showEdition = true;
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">edition</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the type of artwork (unique or edition)
              </div>
            </div>
          </div>
          <!-- <div class="splitter"> -->
          <!-- <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.type"
              [ngClass]="{ 'Disabled-Color': disableObj.type }"
            >
              <div class="input-container" (focusout)="changeFocus(2)">
                <input
                  type="text"
                  class="selection"
                  formControlName="type"
                  placeholder="Choose an option"
                  (focus)="disableObj.type ? null : (isDropDownOpen[2] = true)"
                  readonly="readonly"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.type"
                >
                </fa-icon>

                <div class="placeholder">Artwork Type</div>
                <button (click)="isDropDownOpen[2] = !isDropDownOpen[2]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[2]">
                  <ul>
                    <li
                      (click)="
                        form.get('type').setValue('textile arts');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">textile arts</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('painting');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">painting</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('drawing');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">drawing</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('photography');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">photography</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('digital');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">digital</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('print');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">print</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('mixed media');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">mixed media</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('video');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">video</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('other');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">other</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sculpture');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sculpture</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('installation');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">installation</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sound');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sound</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the primary artwork type from the drop-down list
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.workType"
              [ngClass]="{ 'Disabled-Color': disableObj.workType }"
            >
              <div class="input-container" (focusout)="changeFocus(3)">
                <input
                  formControlName="workType"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.workType ? null : (isDropDownOpen[3] = true)
                  "
                  readonly="readonly"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.workType"
                >
                </fa-icon>

                <div class="placeholder">Work Type</div>
                <button (click)="isDropDownOpen[3] = !isDropDownOpen[3]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[3]">
                  <ul>
                    <li
                      (click)="
                        form.get('workType').setValue('edition');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">edition</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('unique');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">unique</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the type of artwork (unique or edition)
              </div>
            </div> -->
          <!-- </div> -->
          <!-- <div class="field-value" [hidden]="!showObj?.selectedMovements">
            <div class="input-container">
              <input type="text" class="selection"
                placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
                (focus)="showMovement = true"
                (focusout)="showMovement = false"
              />
              <div class="placeholder">Movement Keywords</div>
              <button (click)="showMovement = !showMovement">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>

              <div [ngClass]="
                  showMovement ? 'dropdown-visible' : 'dropdown-hidden'
                ">
                <ul>
                  <li (click)="addToItems(item_mov)" *ngFor="let item_mov of moveArr">
                    <div class="country-name">{{ item_mov?.name }}</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="input-info">
              Choose the movement keywords associated with the artwork
            </div>
            <div class="input-info">
              <div class="row">
                <div class="col-md-3 col-sm-3 col-lg-3 multi text-center"
                  *ngFor="let item_mov of selectedMovements; let k = index">
                  <span>{{ item_mov?.name }}
                    <span (click)="removeItems(k)" class="cross">X</span></span>
                </div>
              </div>
            </div>
          </div> -->

          <div class="splitter" *ngIf="form.get('workType').value == 'edition'">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                [(ngModel)]="editionDetails.editionDetails"
                [ngModelOptions]="{ standalone: true }"
                type="text"
                placeholder="Edition Details"
                [attr.disabled]="true"
              />
              <div class="placeholder">Edition Details</div>
              <div class="input-info">
                Edition details (if applicable). e.g.: 'Edition of 5 plus AP'
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.showEdition">
              <button
                class="button-blue"
                style=""
                (click)="isEditionDetailPopUp = true"
              >
                Edition Details
              </button>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.physicalOrDigital"
            >
              <div class="input-container" (focusout)="changeFocus(4)">
                <input
                  type="text"
                  class="selection"
                  formControlName="physicalOrDigital"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.physicalOrDigital
                      ? null
                      : (isDropDownOpen[4] = true)
                  "
                  (focusout)="changeFocus(4)"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <div class="placeholder">Physical or Digital</div>
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>
                <button
                  (click)="isDropDownOpen[4] = !isDropDownOpen[4]"
                  [attr.disabled]="isArtReadOnly ? true : null"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[4]">
                  <ul>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('physical')
                      "
                    >
                      <div class="country-name">Physical</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('digital')
                      "
                    >
                      <div class="country-name">Digital</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('both')
                      "
                    >
                      <div class="country-name">Both</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork is Physical, Digital, or Both.
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container" (focusout)="changeFocus(5)">
                <input
                  type="text"
                  class="selection"
                  formControlName="file_type"
                  placeholder="Choose an option"
                  (focus)="isDropDownOpen[5] = true"
                  (focusout)="changeFocus(5)"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <div class="placeholder">File Type</div>
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>
                <button
                  (click)="isDropDownOpen[5] = !isDropDownOpen[5]"
                  [attr.disabled]="isArtReadOnly ? true : null"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[5]">
                  <ul>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('image');
                        accept = 'image/*'
                      "
                    >
                      <div class="country-name">Image</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('video');
                        accept = 'video/*'
                      "
                    >
                      <div class="country-name">Video</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('3d_asset');
                        accept = 'glb/*'
                      "
                    >
                      <div class="country-name">3D asset</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('html_code');
                        accept = 'html/*'
                      "
                    >
                      <div class="country-name">HTML code</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the file type being uploaded as the artwork file.
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="placeholder">Artwork Thumbnail</div>
              <div class="flex-block">
                <image-upload
                  [accept]="'image/*'"
                  [selectedData]="selectedFilesObj?.profileArr"
                  (onFileChange)="onFileSelect($event, 'profileArr')"
                  [fileSize]="8388608"
                  [placeholder]="placeholder"
                  [Disable]="disableObj.profileImage || isArtReadOnly"
                  [hideAltText]="true"
                ></image-upload>

                <div class="input-info">
                  Upload an image to be used for the Artwork page on the
                  website. Image needs to be cropped to content, with no
                  shadows/background space. Image will be optimised for website.
                  Please name the image files in the following
                  format:&lt;artist_name&gt;_&lt;artwork_title&gt;.
                </div>
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="placeholder">Original Artwork File</div>
              <div class="flex-block">
                <image-upload
                  [accept]="'all'"
                  [selectedData]="selectedFilesObj?.highResArr"
                  (onFileChange)="onFileSelect($event, 'highResArr')"
                  [fileSize]="2147483648"
                  [placeholder]="highResPlaceholder"
                  [Disable]="disableObj.highResFile || isArtReadOnly"
                  [hideAltText]="true"
                >
                  <div
                    *ngIf="
                      (!selectedFilesObj?.highResArr ||
                        selectedFilesObj?.highResArr?.length == 0) &&
                      selectedFilesObj?.profileArr?.length > 0
                    "
                    class="field-value"
                    style="padding-right: 0.5vw"
                  >
                    <input
                      type="checkbox"
                      placeholder="Show Medium"
                      style="margin-right: 0.4vw"
                      (change)="copyToOriginal($event.currentTarget.checked)"
                    />Copy from Thumbnail
                  </div></image-upload
                >

                <div class="input-info">
                  Upload a high res file or provide link to the high res file.
                  This will be sent to the buyer upon confirmation of payment.
                </div>
              </div>
            </div>
          </div>

          <div
            class="editor-head"
            style="margin-top: 2vw"
            *ngIf="form.value.file_type != 'image'"
          >
            Embed Code
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              margin-top: 2.08vw;
            "
            *ngIf="form.value.file_type != 'image'"
          >
            <div style="width: 50%">
              <div class="field-value" style="margin-top: unset">
                <textarea
                  [placeholder]="'Embed Code'"
                  formControlName="artistNote"
                  rows="80"
                  cols="115"
                  style="padding: 1vw; height: 18vw"
                  (change)="onchangesText()"
                  [(ngModel)]="videoLinkText"
                  [ngModelOptions]="{ standalone: true }"
                  [attr.disabled]="isArtReadOnly ? true : null"
                ></textarea>

                <!-- <angular-editor [placeholder]="'Artist Note'"  formControlName="artistNote"></angular-editor> -->
                <div class="input-info">
                  Provide the correct formatted responsive embed code, to
                  display the artwork on the site. For videos, provide the
                  responsive embed code from Vimeo or Youtube. Display of videos
                  will depend on settings provided in Vimeo or Youtube.
                </div>
              </div>
            </div>
            <div style="width: 48%">
              <div style="padding: 56.25% 0 0 0; position: relative">
                <iframe
                  [src]="videoLinkTextSanitized"
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                  "
                  frameborder="0"
                  allow="autoplay; fullscreen; picture-in-picture"
                  allowfullscreen
                ></iframe>
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!isEditionModalOpen">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Create edition records</p>
    <label for="checkid" style="word-wrap: break-word">
      <input id="checkid" type="checkbox" value="test" />Detach edition records
      from edition data record after creating
    </label>
    <span>
      <span class="forLink" style="margin-right: 1vw" (click)="SelectAll()"
        >Select all</span
      >
      <span class="forLink" style="margin-right: 1vw" (click)="SelectNone()"
        >Select none</span
      >
    </span>
    <div
      class="Edition-list"
      style="max-height: 16vw; overflow-y: scroll; margin-top: 1vw"
    >
      <p
        style="margin-bottom: unset"
        *ngFor="let item of editionArray; let i = index"
      >
        <label for="{{ 'wave' + i }}" style="word-wrap: break-word">
          <input
            id="{{ 'wave' + i }}"
            style="margin-right: 0.5vw"
            type="checkbox"
            [(ngModel)]="item.value"
          />
          Edition record #{{ item.index }}
        </label>
      </p>
      <p
        style="margin-bottom: unset"
        *ngFor="let item of artistProofArray; let i = index"
      >
        <label for="{{ 'waved' + i }}" style="word-wrap: break-word">
          <input
            id="{{ 'waved' + i }}"
            style="margin-right: 0.5vw"
            type="checkbox"
            [(ngModel)]="item.value"
          />
          Artist proof record #{{ item.index }}
        </label>
      </p>
    </div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="isEditionModalOpen = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="createER()"
      >
        Create Edition records
      </button>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!EditionChange">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Update edition details?</p>

    <span
      >The number of editions has changed. Would you like to update the 'Edition
      details' field to the text below?</span
    >

    <span style="margin-top: 2vw"
      ><b
        >Edition of {{ form.get("editionCount").value }}
        <span *ngIf="form.get('artistProofCount').value.length != 0"
          >plus {{ form.get("artistProofCount").value }}artist's proofs</span
        >
      </b></span
    >
    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="EditionChange = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="
          EditionChange = false;
          generateEdition(
            form.get('editionCount').value,
            form.get('artistProofCount').value
          )
        "
      >
        Yes
      </button>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" *ngIf="isEditionDetailPopUp" style="overflow: scroll;">
  <div class="popup-card" style="width: 80vw; margin-top: 3vw">
    <p style="font-size: 1.666vw">Edition Details</p>
    <div class="container__main" style="padding-bottom: 0vw">
      <div class="profile-field">
        <div class="field-contents">
          <div class="field-value">
            <div class="splitter">
              <div class="field-value" style="display: block; width: 100%">
                Edition<input
                  type="number"
                  [(ngModel)]="editionDetails.editions"
                  [ngModelOptions]="{ standalone: true }"
                  [attr.disabled]="
                    editionDetails?.freezeEdition || isEditionDisabled
                      ? true
                      : null
                  "
                  style="
                    width: 3vw;
                    padding: 0.2vw;
                    margin-left: 0.5vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  (keyup)="onEditionChange()"
                />
                plus
                <input
                  [(ngModel)]="editionDetails.artistProofs"
                  [ngModelOptions]="{ standalone: true }"
                  [attr.disabled]="
                    editionDetails?.freezeEdition || isEditionDisabled
                      ? true
                      : null
                  "
                  type="number"
                  style="
                    width: 3vw;
                    padding: 0.2vw;
                    margin-left: 0.5vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  (keyup)="onEditionChange()"
                />
                Artist Proofs
                <input
                  [(ngModel)]="editionDetails.limitedEdition"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 2vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Limited Edition</span>
                <input
                  [(ngModel)]="editionDetails.unlimitedEdition"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [disabled]="true"
                />
                <span><del>Unlimited Edition</del></span>
                <input
                  [(ngModel)]="editionDetails.freezeEdition"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Freeze Edition Count</span>
                <button
                  class="button-blue"
                  style="margin-left: 2vw"
                  (click)="clearField()"
                  [attr.disabled]="isEditionDisabled ? true : null"
                >
                  Clear Fields
                </button>
              </div>
              <div
                class="field-value"
                style="width: 100%; font-size: 0.9vw; margin-top: 0.4vw"
              >
                Provide the edition details including total count and number of
                artist proofs if the artwork is a Limited Edition run. Choose
                Freeze Edition Count if the number of editions will not change.
                If there are unlimited editions of the work (printed on demand,
                for example), choose the Unlimited Editions option.
              </div>
              <div
                *ngIf="editionDetails.editions"
                class="field-value"
                style="width: 100%; display: block"
              >
                <b>Content to copy</b
                ><input
                  type="checkbox"
                  [(ngModel)]="editionDetails.images"
                  [ngModelOptions]="{ standalone: true }"
                  style="
                    margin-left: 2vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Images</span>
                <input
                  [(ngModel)]="editionDetails.sale_price"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Price History</span>
                <input
                  [(ngModel)]="editionDetails.shipping"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Shipping</span>
                <div
                  class="field-value"
                  style="width: 100%; font-size: 0.9vw; margin-top: 0.4vw"
                >
                  Choose which details to replicate for all editions. If
                  unchecked, Price, Images and Shipping tab details will need to
                  be provided individually for each edition available.
                </div>
              </div>
              <div
                *ngIf="editionDetails.editions"
                class="field-value"
                style="width: 100%; display: block"
              >
                <b>Available for sale:</b
                ><input
                  type="checkbox"
                  [(ngModel)]="editionDetails.allEdition"
                  [ngModelOptions]="{ standalone: true }"
                  style="
                    margin-left: 2vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  (change)="onEditionChange3()"
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>All Editions Available</span>
                <input
                  [(ngModel)]="editionDetails.selectedEdition"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  (change)="onEditionChange2()"
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Selected Edition</span>
                <input
                  [(ngModel)]="editionDetails.artistProofAvailable"
                  [ngModelOptions]="{ standalone: true }"
                  type="checkbox"
                  style="
                    margin-left: 1vw;
                    margin-right: 0.5vw;
                    display: inline-block;
                  "
                  (change)="onEditionChange3()"
                  [attr.disabled]="isEditionDisabled ? true : null"
                />
                <span>Artist Proofs available</span>
              </div>
              <div
                *ngIf="editionDetails.editions"
                class="field-value"
                style="width: 100%"
              >
                Choose the number of consigned Editions available for sale
                (choose All Editions Available if applicable). Also confirm
                whether Artist Proofs are available for sale.
              </div>
              <div
                class="field-value"
                style="display: flex; width: 100%; flex-wrap: wrap"
              >
                <div style="display: flex; width: 75%; flex-wrap: wrap">
                  <ng-container
                    *ngIf="
                      editionDetails.allEdition ||
                      editionDetails.selectedEdition
                    "
                  >
                    <div
                      *ngFor="
                        let item of editionDetails.editionSaleData2;
                        let i = index
                      "
                    >
                      <input
                        [(ngModel)]="editionDetails.editionSaleData[i]"
                        type="checkbox"
                        style="
                          margin-left: 2vw;
                          margin-right: 0.5vw;
                          display: inline-block;
                        "
                        (change)="onEditionDetailChange()"
                        [attr.disabled]="isEditionDisabled ? true : null"
                      />
                      {{ i + 1 }}
                    </div>
                  </ng-container>
                  <ng-container *ngIf="editionDetails.artistProofAvailable">
                    <div
                      *ngFor="
                        let item of editionDetails.artistProofsSaleData2;
                        let i = index
                      "
                    >
                      <input
                        [(ngModel)]="editionDetails.artistProofsSaleData[i]"
                        [ngModelOptions]="{ standalone: true }"
                        type="checkbox"
                        style="
                          margin-left: 2vw;
                          margin-right: 0.5vw;
                          display: inline-block;
                        "
                        (change)="onEditionDetailChange()"
                        [attr.disabled]="isEditionDisabled ? true : null"
                      />
                      AP {{ i + 1 }}
                    </div>
                  </ng-container>
                </div>
                <div style="display: flex; width: 25%; flex-wrap: wrap">
                  <button
                    class="button-blue"
                    (click)="onSelectAll()"
                    style="margin-left: 2vw"
                    [attr.disabled]="isEditionDisabled ? true : null"
                  >
                    Select All
                  </button>
                  <button
                    class="button-blue"
                    (click)="onUnSelectAll()"
                    style="margin-left: 2vw"
                    [attr.disabled]="isEditionDisabled ? true : null"
                  >
                    UnSelect All
                  </button>
                </div>
              </div>
              <div
                *ngIf="editionDetails.editions"
                class="field-value"
                style="
                  display: flex;
                  width: 100%;
                  flex-wrap: wrap;
                  justify-content: space-between;
                "
              >
                <div
                  style="
                    display: flex;
                    width: 35%;
                    flex-wrap: wrap;
                    flex-direction: column;
                  "
                >
                  <div class="field-value" style="display: block; width: 100%">
                    Edition Details:
                    <input
                      [(ngModel)]="editionDetails.editionDetails"
                      [ngModelOptions]="{ standalone: true }"
                      type="text"
                      style="
                        width: 17vw;
                        padding: 0.2vw;
                        height: 2.5vw;
                        margin-left: 0.5vw;
                        margin-right: 0.5vw;
                        display: inline-block;
                      "
                      disabled
                    />
                  </div>
                  <span style="font-size: 0.9vw; margin-top: 0.3vw">
                    Details to show on the website. This field is auto-generated
                    based on the information provided above.</span
                  >
                </div>
                <div
                  style="
                    display: flex;
                    width: 30%;
                    flex-wrap: wrap;
                    flex-direction: column;
                  "
                >
                  <div class="field-value" style="display: block; width: 100%">
                    <input
                      [(ngModel)]="editionDetails.showInMarketPlace"
                      type="checkbox"
                      style="margin-right: 0.5vw; display: inline-block"
                      [attr.disabled]="isEditionDisabled ? true : null"
                    />Show Editions separately in Marketplace
                  </div>
                  <span style="font-size: 0.9vw; margin-top: 1.5vw"
                    >Choose whether to list the available editions separately in
                    the Marketplace, or whether to show as a single listing
                  </span>
                </div>
                <div
                  style="
                    display: flex;
                    width: 25%;
                    flex-wrap: wrap;
                    flex-direction: column;
                  "
                >
                  <div class="field-value" style="display: block; width: 100%">
                    <input
                      [(ngModel)]="editionDetails.uniqueVariations"
                      type="checkbox"
                      style="margin-right: 0.5vw; display: inline-block"
                      [attr.disabled]="isEditionDisabled ? true : null"
                    />Unique Variations
                  </div>
                  <span style="font-size: 0.9vw; margin-top: 1.5vw"
                    >Choose whether the editions are Unique Variations or not.
                    Artworks that are Unique Variations can have different
                    images/videos for each edition.
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="Edition-list"
      style="max-height: 16vw; overflow-y: scroll; margin-top: 1vw"
    ></div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-blue"
        style="width: 15%; padding: 0.53333vw 1.08333vw"
        (click)="onCancellEdition()"
      >
        Cancel
      </button>
      <button
        class="button-blue"
        style="width: 15%; padding: 0.53333vw 1.08333vw"
        (click)="onSubmit2()"
      >
        Save
      </button>
    </div>
  </div>
</div>
<script src="https://player.vimeo.com/api/player.js"></script>
