import { element } from "protractor";

import {
	Component,
	ElementRef,
	EventEmitter,
	NgModule,
	OnInit,
	Output,
	ViewChild,
	Pipe,
	PipeTransform,
} from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
// import {  } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";
import { NavigationEnd, Router } from "@angular/router";
import { faLock } from "@fortawesome/free-solid-svg-icons";
import { ImageUploadComponent } from "src/app/core/image-upload/image-upload.component";
import { CollectorService } from "src/app/services/collector.service";
import { apiUrl } from "src/environments/environment.prod";
import { EditNotifyService } from "../edit-notify.service";

@Component({
	selector: "app-artworks-main",
	templateUrl: "./main.component.html",
	styleUrls: ["./main.component.scss"],
})
export class MainArtworksComponent implements OnInit {
	faLock = faLock;
	editionArray = [];
	private url: SafeResourceUrl;
	artistProofArray = [];
	isEditionModalOpen = false;
	@ViewChild("scrollTop") scrollTop: ElementRef;
	// @Output() newItemEvent = new EventEmitter<string>();

	Menuinit;
	videoLinkText = "";
	// https://player.vimeo.com/video/699376537?h=17fb6286c9&title=0&byline=0&portrait=0
	videoLinkTextSanitized: SafeResourceUrl;
	EditionChange = false;
	isSubmited = false;
	isDropDown = false;
	isDropDown2 = false;
	dropDownValue = Array(10).fill(null);
	onChangeArray = Array(20).fill(false);
	isDropDownOpen;
	selectedFilesObj: any = {
		profileArr: [],
		highResArr: [],
		profileImage: "",
		highResImage: "",
	};
	selectedFiles: File[] = [];
	form: FormGroup;
	highResPlaceholder = "High Res File to be sent to buyer";
	id;
	showEdition: boolean = false;
	artworksObj: any = {};
	heyy() {
		console.log("asjhdb");
	}
	showMovement: boolean = false;
	accept: any = "image/*";
	artwork_type = {
		"textile arts": 1,
		painting: 2,
		drawing: 3,
		photography: 4,
		digital: 5,
		print: 6,
		"mixed media": 7,
		video: 8,
		other: 9,
		sculpture: 10,
		installation: 11,
	};
	userType;

	listCurrentPage;
	listTotalPage;

	@ViewChild(ImageUploadComponent)
	private imageUploadComponent: ImageUploadComponent;
	primaryImage: any;
	moveArr: any = [];
	selectedMovements: any = [];
	profileImage: any;
	permissionsObj: any = {};
	showObj: any = {};
	disableObj: any = {};
	placeholder: any = "";
	artist_type;
	userDetails;

	createdAt;
	updatedAt;

	selectedArtist = {
		_id: null,
		display_name: null,
	};
	artistList = [];
	isArtReadOnly = false;
	isEditionDetailPopUp = false;
	editionDetails: any = {
		limitedEdition: true,
		allEdition: true,
		images: true,
		sale_price: true,
		shipping: true,
	};

	isEditionDisabled = false;

	extras;
	constructor(
		private router: Router,
		private formBuilder: FormBuilder,
		private server: CollectorService,
		private editNotifyService: EditNotifyService,
		private domSanitizer: DomSanitizer,
	) {}
	onchangesText() {
		this.videoLinkTextSanitized =
			this.domSanitizer.bypassSecurityTrustResourceUrl(this.videoLinkText);
	}
	ngOnInit(): void {
		this.artistList = [];
		if (localStorage.getItem("artworkArtistID")) {
			this.selectedArtist = JSON.parse(localStorage.getItem("artworkArtistID"));
			this.getArtistList();
			// this.artistList.push(this.selectedArtist);
		} else {
			this.getArtistList();
		}
		this.listCurrentPage = Number(localStorage.getItem("artworkCMSoffset"));
		this.listTotalPage = Number(localStorage.getItem("artworkCMStotalPage"));
		this.videoLinkTextSanitized =
			this.domSanitizer.bypassSecurityTrustResourceUrl(this.videoLinkText);
		this.router.events.subscribe((evt) => {
			if (!(evt instanceof NavigationEnd)) {
				return;
			}
			window.scrollTo(0, 0);
		});
		this.isDropDownOpen = Array(10).fill(false);
		this.getAllMovements();
		let decode = decodeURIComponent(
			escape(window.atob(localStorage.getItem("userDetails"))),
		);
		this.userDetails = JSON.parse(decode);
		if (localStorage.getItem("artworkID")) {
			this.showEdition = true;
			this.getArtWork();
		} else {
			this.artist_type = JSON.parse(decode)["role_id"]["artist_type"];
			if (localStorage.getItem("artworkArtistID")) {
				this.artist_type = "Gallery";
			}

			if (this.userDetails.role == "GALLERY") {
				this.artist_type = "Gallery";
			}
		}

		this.permissionsObj = JSON.parse(decode)
			["role_id"]["permissions"].find((x) => x.name == "Artworks")
			.tabsArr.find((x) => x.name == "Main");
		console.log(this.permissionsObj);
		this.form = this.formBuilder.group({
			title: new FormControl(""),
			artistNote: new FormControl(""),
			year: new FormControl(""),
			createdAs: new FormControl(""),
			createdBy: new FormControl(""),
			dimensions: new FormControl(""),
			medium: new FormControl(""),
			showEdition: new FormControl(""),
			editionDataRecord: new FormControl(false),
			editionOFF: new FormControl(false),
			editionCount: new FormControl(""),
			artistProofCount: new FormControl(""),
			pluss: new FormControl(true),
			edition: new FormControl(""),
			freeze: new FormControl(false),
			type: new FormControl(""),
			physicalOrDigital: new FormControl(""),
			file_type: new FormControl(""),
			published: new FormControl(""),
			workType: new FormControl(""),
		});
		this.form.get("workType").valueChanges.subscribe((a) => {
			if (
				!this.form.get("workType").untouched &&
				a == "edition" &&
				localStorage.getItem("artworkID")
			) {
				this.isEditionDetailPopUp = true;
			} else {
				this.isEditionDetailPopUp = false;
			}
		});
		console.log(this.permissionsObj.fields);
		this.permissionsObj.fields.forEach((ele) => {
			this.showObj[ele.name] = ele.show;
			this.disableObj[ele.name] = ele.disabled;
			if (ele.formControl && ele.show) {
				if (ele.mandatory) {
					this.form.controls[ele.name].setValidators([Validators.required]);
				}
				ele["disabled"] =
					ele.disabled == "true" || ele.disabled == true ? true : false;
				if (ele.disabled) {
					this.form.controls[ele.name].disable();
				}
				let obj = {};
				obj[ele.name] = ele.defaultValue;
				// to patch default value
				if (!localStorage.getItem("artworkID")) {
					this.form.patchValue(obj);
				}
			}
		});

		// this.route.paramMap.subscribe((params) => {
		// 	if (params.get('id')) {
		// 		this.id = params.get('id');
		// 		this.artistInfoService.getMainData(this.id).subscribe(async (data) => {
		// 			const art = data;
		// 			this.form.patchValue(art);
		// 			// this.selectedFiles.push(
		// 			// 	new File([''], art.primaryImage.originalname)
		// 			// );
		// 			let response = await fetch(
		// 				'https://api.artist.terrain.art/' + art.primaryImage.path
		// 			);
		// 			let da = await response.blob();
		// 			let metadata = {
		// 				type: 'image/jpeg',
		// 			};
		// 			let file = new File([da], art.primaryImage.originalname, metadata);
		// 			this.imageUploadComponent.onFileSelect([file]);
		// 		});
		// 	} else {
		// 		this.id = null;
		// 	}
		// });

		this.form.valueChanges.subscribe((x) => {
			// ('firstname value changed')
			//   console.log(x)
			// this.newItemEvent.emit("true");
			// Menuinit;
			if (this.Menuinit == null) {
				this.Menuinit = x;
			} else {
				this.editNotifyService.setOption("Main", true);
			}
		});
		console.log(this.disableObj);
	}

	// to get artwork
	getArtWork() {
		let url = apiUrl.getArtwork + `/${localStorage.getItem("artworkID")}`;
		this.server.showSpinner();
		this.server.getApi(url).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				this.extras = res.data.extras;
				if (!this.extras) {
					this.extras = {};
				}
				if (this.extras?.editionDetails) {
					this.editionDetails = this.extras.editionDetails;
					if (this.editionDetails.editions) {
						this.isEditionDisabled = true;
					}
				}
				const artistRoleId = res.data.artist_id.role_id;
				let url = apiUrl.addRole + `/${artistRoleId}`;
				this.server.getApi(url).subscribe((res) => {
					if (res.statusCode == 200) {
						this.artist_type = res.data.artist_type;
					}
				});
				this.artist_type = res.data.artist_id;
				this.artworksObj = res.data;
				this.createdAt = new Date(res.data?.createdAt).toDateString();
				this.updatedAt = new Date(res.data?.updatedAt).toDateString();
				this.form.patchValue({
					title: res.data.artwork_title,
					year: res.data.year,
					createdAs: res.data.created,
					// createdBy: ,
					dimensions: res.data.dimensions,
					// movement: res.data.,
					//physicalOrDigital:res.data.type_of_work,
					medium: res.data.medium,
					showEdition: res.data.show_edition,
					edition: res.data.edition_details,
					type: res.data.artwork_type,
					file_type: res.data.file_type,
					published: res.data.publish_artworks,
					workType: res.data.work_type,
					artistNote: res.data.artwork_embedded_code,
					physicalOrDigital: res.data.physicalOrDigital,
				});
				console.log(this.userDetails.role);

				if (
					res.data.publish_artworks &&
					(this.userDetails.role == "REPRESENTED ARTIST" ||
						this.userDetails.role == "TERRAIN CONSIGNED")
				) {
					this.isArtReadOnly = true;
				}
				// this.selectedMovements = res.data.movement;
				// this.selectedMovements = res.data.movement;
				if (res.data.primary_image.length > 0) {
					this.selectedFilesObj.profileArr = [...res.data.primary_image];
				}
				if (res.data.original_artwork_file.length > 0) {
					this.selectedFilesObj.highResArr = [
						...res.data.original_artwork_file,
					];
				}
				this.videoLinkTextSanitized =
					this.domSanitizer.bypassSecurityTrustResourceUrl(
						res.data.artwork_embedded_code,
					);
			}
		});
	}

	// to get all moments
	getAllMovements() {
		this.server.showSpinner();
		let url = apiUrl.getMovement + "?limit=999&offset=1";
		this.server.getApi(url).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				this.moveArr = res.data;
			}
		});
	}

	onFileSelect(files, key) {
		this.selectedFilesObj[key] = files;
		console.log(this.selectedFilesObj);
		if (files.length == 0) {
			this.selectedFilesObj[key] = [];
		} else {
			this.uploadFile(key, files);
		}
	}

	// to upload file
	uploadFile(key, files: any) {
		let formdata = new FormData();
		formdata.append("image", files[0].file_event);
		let url = apiUrl.upload;
		this.server.showSpinner();
		this.server.postApi(url, formdata).subscribe(
			(res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					this.selectedFilesObj[key][0]["url"] = res.data;
					alert("File uploaded successfully!");
				}
			},
			(err) => {
				alert(err.error.message);
			},
		);
	}
	// onSubmit() {
	// 	const data = this.form.value;
	// 	data.published = data.published === true ? 1 : 0;
	// 	data.showEdition = data.showEdition === true ? 1 : 0;
	// 	console.log(data.type);

	// 	data.type = this.artwork_type[data.type];

	// 	if (this.id) {
	// 		this.artistInfoService
	// 			.addMainData(this.form.value, this.selectedFiles, this.id)
	// 			.subscribe((data) => {
	// 				this.router.navigate([
	// 					'/artist-portal/settings/artwork/edit/' + this.id + '/details',
	// 				]);
	// 			});
	// 	} else {
	// 		this.artistInfoService
	// 			.addMainData(this.form.value, this.selectedFiles)
	// 			.subscribe((data) => {
	// 				localStorage.setItem('artID', data?.id);
	// 				this.router.navigate(['/artist-portal/settings/artwork/add/details']);
	// 			});
	// 	}
	// }

	onSubmit() {
		// if (this.form.invalid) {
		//   alert('Form not valid.Please fill required fields correctly !');
		//   return;
		// }

		if (
			this.permissionsObj.fields.find((x) => x.name == "profileImage")
				.mandatory &&
			this.selectedFilesObj.profileArr.length == 0
		) {
			alert("Please upload primary image!");
			return;
		}
		if (
			this.permissionsObj.fields.find((x) => x.name == "highResFile")
				.mandatory &&
			this.selectedFilesObj.highResArr.length == 0
		) {
			alert("Please upload original artwork!");
			return;
		}
		if (this.selectedFilesObj.profileArr.length > 0) {
			this.selectedFilesObj.profileArr[0].filedata = "";
			this.selectedFilesObj.profileArr[0].file_event = "";
		}
		if (this.selectedFilesObj.highResArr.length > 0) {
			this.selectedFilesObj.highResArr[0].filedata = "";
			this.selectedFilesObj.highResArr[0].file_event = "";
		}
		let url = apiUrl.addArtwork;
		if (!this.extras) {
			this.extras = {};
		}
		this.extras["editionDetails"] = this.editionDetails;
		let req = {
			artwork_title: this.form.getRawValue().title?.trim(),
			artwork_type: this.form.getRawValue().type,
			artwork_embedded_code: this.form.getRawValue().artistNote,
			created: this.form.getRawValue().createdAs,
			dimensions: this.form.getRawValue().dimensions?.trim(),
			edition_details: this.form.getRawValue().edition?.trim(),
			medium: this.form.getRawValue().medium?.trim(),
			primary_image: this.selectedFilesObj.profileArr,
			thumbnail_of_primary:
				this.selectedFilesObj.profileArr.length > 0
					? this.selectedFilesObj.profileArr[0].url
					: "",
			hover_second_image:
				this.selectedFilesObj.profileArr.length > 0
					? this.selectedFilesObj.profileArr[0].url
					: "",
			original_artwork_file: this.selectedFilesObj.highResArr,
			publish_artworks:
				this.form.getRawValue().published == true ? true : false,
			show_edition: this.form.getRawValue().showEdition == true ? true : false,
			work_type: this.form.getRawValue().workType,
			file_type: this.form.getRawValue().file_type,
			year: this.form.getRawValue().year?.trim(),
			tab: "Main",
			artwork_group:
				this.artist_type == "Terrain" ||
				this.artist_type == "Gallery" ||
				this.artist_type == "Consigned"
					? "curated"
					: "open",
			physicalOrDigital: this.form.getRawValue().physicalOrDigital,
			extras: this.extras,
		};

		if (localStorage.getItem("artworkID")) {
			req["artworkId"] = localStorage.getItem("artworkID");
		}
		if (this.selectedArtist?._id) {
			req["artist_id"] = this.selectedArtist._id;
		}

		console.log(req);

		this.server.showSpinner();
		this.server.postApi(url, req).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				localStorage.setItem("artworkID", res.data["_id"]);
				// alert(res.message);
				this.editNotifyService.reset();
				this.isSubmited = true;
				alert(res.message);
			} else {
				alert(res.message);
				this.isSubmited = false;
			}
		});
	}
	async getValueWithAsync() {
		await this.onSubmit();
		if (this.isSubmited) {
			this.router.navigate(["/artist-portal/settings/artworks"]);
		}
	}

	changeFocus(index) {
		// console.log('in in ');
		setTimeout(() => {
			this.isDropDownOpen[index] = false;
		}, 500);
	}

	generateEdition(editionCount, ApCount) {
		this.editionArray = [];
		this.artistProofArray = [];

		// console.log(editionCount, ApCount)
		if (editionCount != 0 && ApCount.length != 0) {
			this.editionArray;
			for (let index = 0; index < editionCount; index++) {
				this.editionArray.push({ index: index + 1, value: false });
			}
			for (let index = 0; index < ApCount; index++) {
				this.artistProofArray.push({ index: index + 1, value: false });
			}
			this.isEditionModalOpen = true;
		}
	}

	SelectAll() {
		for (let index = 0; index < this.editionArray.length; index++) {
			const element = this.editionArray[index];
			element.value = true;
		}
		for (let index = 0; index < this.artistProofArray.length; index++) {
			const element = this.artistProofArray[index];
			element.value = true;
		}
	}
	SelectNone() {
		for (let index = 0; index < this.editionArray.length; index++) {
			const element = this.editionArray[index];
			element.value = false;
		}
		for (let index = 0; index < this.artistProofArray.length; index++) {
			const element = this.artistProofArray[index];
			element.value = false;
		}
	}

	freeze(values) {
		if (values.currentTarget.checked) {
			this.form.get("editionOFF").setValue(false);
			this.form.get("editionOFF").disable();
			this.form.get("editionCount").disable();
			this.form.get("artistProofCount").disable();
		} else {
			this.form.get("editionOFF").enable();
			this.form.get("editionCount").enable();
			this.form.get("artistProofCount").enable();
		}
	}

	createER() {
		console.log(this.artworksObj);
		if (
			this.artworksObj["sale_options"] == "Sold" ||
			this.artworksObj["sale_options"] == "Reserved"
		) {
			alert(
				`Editions can not be created for ${this.artworksObj["sale_options"]} Artwork!`,
			);
			return;
		}
		// for (let index = 0; index < this.editionArray.length; index++) {
		//   if (this.editionArray[index].value == true) {
		//     let removed = this.editionArray.splice(index, 1);
		//   }

		// }
		// for (let index = 0; index < this.artistProofArray.length; index++) {
		//   if (this.artistProofArray[index].value == true) {
		//     let removed = this.artistProofArray.splice(index, 1);
		//   }

		// }
		this.createEditions();
	}

	showEditionPop() {
		if (localStorage.getItem("artworkID")) {
			this.showEdition = true;
		}
	}

	// to create editions
	createEditions() {
		if (!localStorage.getItem("artworkID")) {
			alert("Please save artwork first to edit edition!");
			return;
		}
		let url =
			apiUrl.artworks.createEdition + `/${localStorage.getItem("artworkID")}`;
		let records = [];
		let length = 0;
		this.editionArray.forEach((ele) => {
			if (ele.value) {
				length++;
			}
		});
		this.editionArray.forEach((ele, index) => {
			if (ele.value) {
				records.push({
					edition: true,
					editionRecords: `${index}/${length}`,
					sale_options: this.artworksObj["sale_options"],
				});
			}
		});

		let length2 = 0;
		this.artistProofArray.forEach((ele) => {
			if (ele.value) {
				length2++;
			}
		});

		this.artistProofArray.forEach((ele, index) => {
			if (ele.value) {
				records.push({
					plus: true,
					editionRecords: `${index}/${length2}`,
					sale_options: this.artworksObj["sale_options"],
				});
			}
		});

		let req = {
			records: [...records],
		};
		this.server.postApi(url, req).subscribe(
			(res) => {
				if (res.statusCode == 200) {
					// this.isEditionDisabled = true;
					this.isEditionModalOpen = false;
					alert(res.message);
				} else {
					alert(res.message);
				}
			},
			(err) => {
				alert(err.error.message);
			},
		);
	}
	copyToOriginal(copyToOrigin) {
		if (copyToOrigin) {
			this.selectedFilesObj.highResArr = JSON.parse(
				JSON.stringify(this.selectedFilesObj.profileArr),
			);
		}
	}

	checkIsAddArtwork(item) {
		let partner = item?.artist?.partners?.find((a) => {
			return a?.partner == this.userDetails?._id;
		});
		if (partner?.addArtwork === "true") {
			return true;
		} else {
			return false;
		}
	}

	getArtistList() {
		let url = `partners-artist/all?limit=20&offset=0&status=ACCEPTED`;
		//this.server.showSpinner();
		this.server.getApi(url).subscribe((res) => {
			//this.server.hideSpinner();
			if (res.statusCode == 200) {
				this.artistList = res.data
					?.filter((a) => {
						return this.checkIsAddArtwork(a);
					})
					.map((a) => {
						return a?.artist;
					});
			}
		});
	}
	onEditionChange() {
		if (this.editionDetails?.editions) {
			this.editionDetails["editionSaleData"] = Array(
				this.editionDetails?.editions,
			).fill(false);
			this.editionDetails["editionSaleData2"] = Array(
				this.editionDetails?.editions,
			).fill(false);
		}
		if (this.editionDetails?.artistProofs) {
			this.editionDetails["artistProofsSaleData"] = Array(
				this.editionDetails?.artistProofs,
			).fill(false);
			this.editionDetails["artistProofsSaleData2"] = Array(
				this.editionDetails?.artistProofs,
			).fill(false);
		}
		this.onEditionChange3();
	}
	onEditionChange2() {
		if (this.editionDetails.selectedEdition) {
			this.editionDetails["allEdition"] = false;
			this.editionDetails["artistProofAvailable"] = false;
			this.editionDetails["editionSaleData"] = Array(
				this.editionDetails?.editions,
			).fill(false);
			this.editionDetails["editionSaleData2"] = Array(
				this.editionDetails?.editions,
			).fill(false);
		}
	}
	onEditionChange3() {
		if (this.editionDetails.allEdition) {
			this.editionDetails["editionSaleData"] = Array(
				this.editionDetails?.editions,
			).fill(true);
			this.editionDetails["selectedEdition"] = false;
		}
		if (this.editionDetails.artistProofAvailable) {
			// this.editionDetails['artistProofsSaleData'] = Array(this.editionDetails?.artistProofs).fill(true);
			// this.editionDetails['selectedEdition'] = false;
		}
		this.onEditionDetailChange();
	}
	onSelectAll() {
		if (this.editionDetails.selectedEdition) {
			this.editionDetails["editionSaleData"] = Array(
				this.editionDetails?.editions,
			).fill(true);
		}
		if (this.editionDetails.artistProofAvailable) {
			this.editionDetails["artistProofsSaleData"] = Array(
				this.editionDetails?.artistProofs,
			).fill(true);
		}

		this.onEditionDetailChange();
	}
	onUnSelectAll() {
		this.editionDetails["editionSaleData"] = Array(
			this.editionDetails?.editions,
		).fill(false);
		this.editionDetails["artistProofsSaleData"] = Array(
			this.editionDetails?.artistProofs,
		).fill(false);
		this.onEditionDetailChange();
	}
	onEditionDetailChange() {
		let text = "Edition of ";
		if (this.editionDetails?.editions) {
			text = text + this.editionDetails?.editions;
		}
		if (this.editionDetails?.artistProofs) {
			if (this.editionDetails?.editions) {
				text = text + " plus ";
			}
			text = text + this.editionDetails?.artistProofs + " APs, ";
		} else {
			text = text + ", ";
		}
		let available = 0;
		for (
			let index = 0;
			index < this.editionDetails?.["editionSaleData"]?.length;
			index++
		) {
			const element = this.editionDetails["editionSaleData"][index];
			if (element) {
				available++;
			}
		}
		if (available == this.editionDetails?.["editionSaleData"]?.length) {
			this.editionDetails["allEdition"] = true;
			this.editionDetails["selectedEdition"] = false;
		}
		let apSelected = 0;
		for (
			let index = 0;
			index < this.editionDetails?.["artistProofsSaleData"]?.length;
			index++
		) {
			const element = this.editionDetails["artistProofsSaleData"][index];
			if (element) {
				available++;
				apSelected++;
			}
		}
		text =
			text +
			`${available} out of ${
				this.editionDetails["editionSaleData"].length + apSelected
			} available`;
		this.editionDetails["editionDetails"] = text;
	}
	clearField() {
		this.editionDetails["editions"] = null;
		this.editionDetails["artistProofs"] = null;
		this.editionDetails["editionSaleData"] = null;
		this.editionDetails["artistProofsSaleData"] = null;
		this.editionDetails["editionSaleData2"] = null;
		this.editionDetails["artistProofsSaleData2"] = null;
		this.editionDetails["limitedEdition"] = true;
		this.editionDetails["freezeEdition"] = false;
		this.editionDetails["allEdition"] = true;
		this.editionDetails["selectedEdition"] = false;
		this.editionDetails["artistProofAvailable"] = false;
		this.editionDetails["editionDetails"] = null;
		this.editionDetails["showInMarketPlace"] = false;
		this.editionDetails["uniqueVariations"] = false;
	}
	onSubmit2() {
		if (!localStorage.getItem("artworkID")) {
			alert("Save Artwork First");
		}
		// let req = {
		//   data: this.editionDetails
		// };
		this.server.showSpinner();

		this.server
			.postApi(
				"artwork/addEdition/" + localStorage.getItem("artworkID"),
				this.editionDetails,
			)
			.subscribe((res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					alert("Edition Saved");
					this.isEditionDisabled = true;
					this.isEditionDetailPopUp = false;
				}
			});
	}
	onCancellEdition() {
		if (confirm("Are you sure you want to cancel?")) {
			this.isEditionDetailPopUp = false;
		}
	}
}
