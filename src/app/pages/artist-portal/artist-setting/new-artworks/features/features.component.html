<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div *ngIf="false" class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.useScaleWall"
              [ngClass]="{ 'Disabled-Color': disableObj.useScaleWall }"
            >
              <div class="input-with-text">
                <div class="text-before">Use Scale Wall:</div>
                <label class="switch">
                  <input
                    formControlName="useScaleWall"
                    type="checkbox"
                    [disabled]="disableObj.useScaleWall"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.useScaleWall"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Switch off the feature for artworks that are small (less than 15
                in), or if they are videos, gifs, etc
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.hide3dFeature"
              [ngClass]="{ 'Disabled-Color': disableObj.hide3dFeature }"
            >
              <div class="input-with-text">
                <div class="text-before">Hide 3D Feature:</div>
                <label class="switch">
                  <input formControlName="hide3dFeature" type="checkbox" />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.hide3dFeature"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether to enable/hide 3D feature. Hide if the artwork is
                video, gifs, composite or diptych, etc
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.disableViewInRoom"
              [ngClass]="{ 'Disabled-Color': disableObj.disableViewInRoom }"
            >
              <div class="input-container">
                <div class="text-before">Disable View in Room:</div>
                <label class="switch">
                  <input
                    formControlName="disableViewInRoom"
                    type="checkbox"
                    [disabled]="disableObj.disableViewInRoom"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.disableViewInRoom"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Disable View in Room if the artwork is composite or diptych, or
                if they are videos, gifs, etc
              </div>
            </div>
          </div>
          <div *ngIf="false" class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.isFeatured"
              [ngClass]="{ 'Disabled-Color': disableObj.isFeatured }"
            >
              <div class="input-with-text">
                <div class="text-before">Featured:</div>
                <label class="switch">
                  <input formControlName="isFeatured" type="checkbox" />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.isFeatured"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether to show these artworks first in the Marketplace
                page
              </div>
            </div>
            <!-- <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.height"
              [ngClass]="{ 'Disabled-Color': disableObj.height }"
            >
              <input
                formControlName="height"
                type="number"
                placeholder="Height"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.height"
              >
              </fa-icon>

              <div class="placeholder">Height</div>
              <div class="input-info">
                Height in cm, rounded to two decimal points
              </div>
            </div> -->
            <div
              class="field-value"
              [hidden]="!showObj?.width"
              [ngClass]="{ 'Disabled-Color': disableObj.width }"
            >
              <input
                formControlName="width"
                type="number"
                placeholder="Width"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.width"
              >
              </fa-icon>

              <div class="placeholder">Width</div>
              <div class="input-info">
                Width in cm, rounded to two decimal points
              </div>
            </div>
          </div>
          <!-- <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.height"
              [ngClass]="{ 'Disabled-Color': disableObj.height }"

            >
              <input
                formControlName="height"
                type="number"
                placeholder="Height"
              />
              <fa-icon  class="Lock-position" [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions" [hidden]="!disableObj.height" > </fa-icon>

              <div class="placeholder">Height</div>
              <div class="input-info">
                Height in cm, rounded to two decimal points
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.width"
            [ngClass]="{ 'Disabled-Color': disableObj.width }"
            >
              <input
                formControlName="width"
                type="number"
                placeholder="Width"
              />
              <fa-icon  class="Lock-position" [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions" [hidden]="!disableObj.width" > </fa-icon>

              <div class="placeholder">Width</div>
              <div class="input-info">
                Width in cm, rounded to two decimal points
              </div>
            </div>
          </div> -->
          <div *ngIf="false" class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.depth"
              [ngClass]="{ 'Disabled-Color': disableObj.depth }"
            >
              <input
                formControlName="depth"
                type="number"
                placeholder="Only if applicable"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.depth"
              >
              </fa-icon>

              <div class="placeholder">Depth</div>
              <div class="input-info">
                Depth in cm (if applicable), rounded to two decimal points
              </div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.arId"
              [ngClass]="{ 'Disabled-Color': disableObj.arId }"
            >
              <input formControlName="arId" type="text" placeholder="AR ID" />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.arId"
              >
              </fa-icon>

              <div class="placeholder">AR ID</div>
              <div class="input-info">
                Provide the AR ID from the HelloAR portal
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.isCompositeImage"
              [ngClass]="{ 'Disabled-Color': disableObj.isCompositeImage }"
            >
              <div class="input-with-text">
                <div class="text-before">Composite Image (Flag):</div>
                <label class="switch">
                  <input
                    formControlName="isCompositeImage"
                    type="checkbox"
                    [disabled]="disableObj.isCompositeImage"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.isCompositeImage"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether the artwork is a composite work, diptych,
                triptych
              </div>
            </div>
          </div>
          <!-- <div class="splitter">
          </div> -->
          <div
            *ngIf="false && form.get('isCompositeImage').value"
            class="splitter"
          >
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="compositeHeight"
                type="text"
                placeholder="Composite Height"
              />
              <div class="placeholder">Composite Height</div>
              <div class="input-info">
                Provide the total height of the composite work
              </div>
            </div>
            <div class="field-value">
              <input
                formControlName="compositeWidth"
                type="text"
                placeholder="Composite Width"
              />
              <div class="placeholder">Composite Width</div>
              <div class="input-info">
                Provide the total width of the composite work
              </div>
            </div>
          </div>
          <div style="margin-top: 3.47vw; font-size: 1.25vw">Scale Wall</div>
          <div class="d-flex">
            <div style="padding-right: 1.5vw; margin-top: 1vw; width: 40%">
              <!-- <div class="button-group2" style="flex-direction: column;margin-left: 2vw;">
                <div (click)="downloadImage()" class="next" style="margin-top: 1.5vw;">Download</div>
                <div (click)="saveToMedia()" class="next" style="margin-left: 0.5vw;margin-top: 1.5vw;">Save to Media</div>
                <div (click)="showPreView=false;" class="next" style="margin-left: 0.5vw;margin-top: 1.5vw;">Clear</div>
              </div> -->
              <div class="field-value">
                <input
                  formControlName="width"
                  type="number"
                  placeholder="Width"
                />

                <div class="placeholder">Artwork Width</div>
                <div class="input-info">
                  Artwork width in cm, rounded to two decimal points
                </div>
              </div>
              <div
                *ngIf="
                  this.form.getRawValue().width &&
                  this.form.getRawValue().width > 0
                "
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <div class="input-container" (focusout)="changeFocus(8)">
                  <input
                    [(ngModel)]="scaleWall"
                    [ngModelOptions]="{ standalone: true }"
                    type="text"
                    class="selection"
                    placeholder="Choose an option"
                    (focus)="isDropDownOpen[8] = true"
                    readonly
                  />

                  <div class="placeholder">Background image</div>
                  <div (click)="isDropDownOpen[8] = !isDropDownOpen[8]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </div>
                  <div class="dropdown-visible" [hidden]="!isDropDownOpen[8]">
                    <ul>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair with plants - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair with plants - Posts -
                          1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair with plants - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair with plants - Posts -
                          1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Corner, grey arm chair with plants - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Corner, grey arm chair with plants - Stories -
                          1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('Grey sofa with lamp - Posts - 1080x1080px')
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('Grey sofa with lamp - Posts - 1350x1080px')
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey sofa with lamp - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey sofa with lamp and shelf - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp and shelf - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey sofa with lamp and shelf - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp and shelf - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey sofa with lamp and shelf - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey sofa with lamp and shelf - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey wall, chair with shelves - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey wall, chair with shelves - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey wall, chair with shelves - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey wall, chair with shelves - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Grey wall, chair with shelves - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Grey wall, chair with shelves - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('High arched wall - Posts - 1080x1080px')
                        "
                      >
                        <div class="country-name">
                          High arched wall - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('High arched wall - Posts - 1350x1080px')
                        "
                      >
                        <div class="country-name">
                          High arched wall - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'High arched wall, plant - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          High arched wall, plant - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'High arched wall, plant - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          High arched wall, plant - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Living room with blue sofa - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Living room with blue sofa - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Living room with blue sofa - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Living room with blue sofa - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Living room with blue sofa, plant - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Living room with blue sofa, plant - Posts -
                          1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Living room with blue sofa, plant - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Living room with blue sofa, plant - Posts -
                          1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Plain wall, gallery setting - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Plain wall, gallery setting - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Plain wall, gallery setting - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Plain wall, gallery setting - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Plain wall, gallery setting - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Plain wall, gallery setting - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Reading nook, green arm chair - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Reading nook, green arm chair - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Reading nook, green arm chair - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Reading nook, green arm chair - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'Reading nook, green arm chair - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          Reading nook, green arm chair - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, arm chair - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, arm chair - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, arm chair - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, arm chair - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, arm chair - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, arm chair - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('White wall, bench - Posts - 1080x1080px')
                        "
                      >
                        <div class="country-name">
                          White wall, bench - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('White wall, bench - Posts - 1350x1080px')
                        "
                      >
                        <div class="country-name">
                          White wall, bench - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg('White wall, bench - Stories - 1920x1080px')
                        "
                      >
                        <div class="country-name">
                          White wall, bench - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, bench, plant - Posts - 1080x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, bench, plant - Posts - 1080x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, bench, plant - Posts - 1350x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, bench, plant - Posts - 1350x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, bench, plant - Stories - 1920x1080px'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, bench, plant - Stories - 1920x1080px
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, blue arm chair - Posts - 1080x1080p'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, blue arm chair - Posts - 1080x1080p
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          chooseBg(
                            'White wall, blue arm chair - Posts - 1350x1080p'
                          )
                        "
                      >
                        <div class="country-name">
                          White wall, blue arm chair - Posts - 1350x1080p
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the background image for the scale wall render. Images
                  are in Instagram post and story aspect ratios
                </div>
              </div>
              <div
                *ngIf="step > 0"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <div class="input-container" (focusout)="changeFocus(10)">
                  <input
                    [(ngModel)]="imageType"
                    [ngModelOptions]="{ standalone: true }"
                    type="text"
                    class="selection"
                    placeholder="Choose an option"
                    (focus)="isDropDownOpen[10] = true"
                    readonly
                  />

                  <div class="placeholder">Artwork Image</div>
                  <div (click)="isDropDownOpen[10] = !isDropDownOpen[10]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </div>
                  <div class="dropdown-visible" [hidden]="!isDropDownOpen[10]">
                    <ul>
                      <li
                        style="cursor: pointer"
                        (click)="
                          imageType = 'Primary Image';
                          isDropDownOpen[10] = false;
                          chooseImage()
                        "
                      >
                        <div class="country-name">Primary Image</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          imageType = 'Thumbnail of Primary';
                          isDropDownOpen[10] = false;
                          chooseImage()
                        "
                      >
                        <div class="country-name">Thumbnail of Primary</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        *ngFor="
                          let breadcrumb of data?.additional_images;
                          let i = index
                        "
                        (click)="
                          imageType = 'Additional Media image ' + i;
                          selected_additional_image = i;
                          isDropDownOpen[10] = false;
                          chooseImage()
                        "
                      >
                        <div class="country-name">
                          Additional Media image {{ i }}
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          imageType = 'Upload new image';
                          isDropDownOpen[10] = false
                        "
                      >
                        <div class="country-name">Upload new image</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          imageType = 'Provide new image URL';
                          isDropDownOpen[10] = false
                        "
                      >
                        <div class="country-name">Provide new image URL</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose which image of the artwork to use in the render, or
                  upload a new image. Ensure the dimensions of the image
                  uploaded match the artwork dimensions provided, and the image
                  is cropped to content & does not have extra background
                </div>
              </div>
              <div
                *ngIf="step > 0 && imageType == 'Provide new image URL'"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <input
                  [(ngModel)]="image"
                  [ngModelOptions]="{ standalone: true }"
                  type="text"
                  placeholder="Image URL"
                  (change)="chooseImage()"
                />
                <div class="placeholder">Image URL</div>
                <div class="input-info">URL of the image</div>
              </div>
              <div
                *ngIf="step > 0 && imageType == 'Upload new image'"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <form-image-upload
                  [control]="form2.get('url')"
                  [fileSize]="8388608"
                  [hideAltText]="true"
                  (onFileUpload)="chooseImage()"
                ></form-image-upload>
              </div>
              <div
                *ngIf="step > 1"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <div class="input-container" (focusout)="changeFocus(9)">
                  <input
                    [(ngModel)]="frameType"
                    [ngModelOptions]="{ standalone: true }"
                    type="text"
                    class="selection"
                    placeholder="Choose an option"
                    (focus)="isDropDownOpen[9] = true"
                    readonly
                  />

                  <div class="placeholder">Frame Options</div>
                  <button (click)="isDropDownOpen[9] = !isDropDownOpen[9]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div class="dropdown-visible" [hidden]="!isDropDownOpen[9]">
                    <ul>
                      <li
                        style="cursor: pointer"
                        (click)="
                          frameType = 'Without frame';
                          isDropDownOpen[9] = false;
                          chooseFrame()
                        "
                      >
                        <div class="country-name">Without frame</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          frameType = 'Brown frame';
                          isDropDownOpen[9] = false;
                          chooseFrame()
                        "
                      >
                        <div class="country-name">Brown frame</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          frameType = 'Light brown frame';
                          isDropDownOpen[9] = false;
                          chooseFrame()
                        "
                      >
                        <div class="country-name">Light brown frame</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          frameType = 'Black frame';
                          isDropDownOpen[9] = false;
                          chooseFrame()
                        "
                      >
                        <div class="country-name">Black frame</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          frameType = 'White frame';
                          isDropDownOpen[9] = false;
                          chooseFrame()
                        "
                      >
                        <div class="country-name">White frame</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose whether the artwork needs to be rendered with frame or
                  without
                </div>
              </div>
              <div
                *ngIf="step > 2 && frameType && frameType != 'Without frame'"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <input
                  [(ngModel)]="frameBorder"
                  [ngModelOptions]="{ standalone: true }"
                  type="number"
                  placeholder="Interior Space"
                  (change)="runCalculations()"
                />
                <div class="placeholder">Interior Space</div>
                <div class="input-info">
                  Provide the distance between the interior of the frame & the
                  artwork in cm. Default is 0 cm
                </div>
              </div>
              <div
                *ngIf="step > 2 && frameType && frameType != 'Without frame'"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <input
                  [(ngModel)]="frameWidth"
                  [ngModelOptions]="{ standalone: true }"
                  type="number"
                  placeholder="Frame Width"
                  (change)="runCalculations()"
                />
                <div class="placeholder">Frame Width</div>
                <div class="input-info">
                  Provide the width of the frame in cm, default is 2 cm
                </div>
              </div>
              <div
                *ngIf="
                  step > 2 &&
                  frameType &&
                  frameType != 'Without frame' &&
                  frameBorder > 0
                "
                class="field-value"
                style="position: relative"
              >
                <input
                  type="color"
                  [(ngModel)]="bgColor"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="HEX code"
                  style="inline-size: 25vw"
                  (change)="runCalculations()"
                />
                <div class="placeholder">Interior Colour</div>
                <div class="input-info">
                  Choose the colour for the interior of the frame. Default is
                  white
                </div>
              </div>
            </div>
            <canvas
              id="canvas"
              width="600"
              height="600"
              style="margin-top: 3.08vw"
            ></canvas>
          </div>
          <div *ngIf="false" class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container" (focusout)="changeFocus(10)">
                <input
                  [(ngModel)]="imageType"
                  [ngModelOptions]="{ standalone: true }"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="isDropDownOpen[10] = true"
                  readonly
                />

                <div class="placeholder">Artwork Image</div>
                <button (click)="isDropDownOpen[10] = !isDropDownOpen[10]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[10]">
                  <ul>
                    <li
                      style="cursor: pointer"
                      (click)="
                        imageType = 'Primary Image'; isDropDownOpen[10] = false
                      "
                    >
                      <div class="country-name">Primary Image</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        imageType = 'Thumbnail of Primary';
                        isDropDownOpen[10] = false
                      "
                    >
                      <div class="country-name">Thumbnail of Primary</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      *ngFor="
                        let breadcrumb of data?.additional_images;
                        let i = index
                      "
                      (click)="
                        imageType = 'Additional Media image ' + i;
                        selected_additional_image = i;
                        isDropDownOpen[10] = false
                      "
                    >
                      <div class="country-name">
                        Additional Media image {{ i }}
                      </div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        imageType = 'Upload new image';
                        isDropDownOpen[10] = false
                      "
                    >
                      <div class="country-name">Upload new image</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        imageType = 'Provide new image URL';
                        isDropDownOpen[10] = false
                      "
                    >
                      <div class="country-name">Provide new image URL</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info"></div>
            </div>
            <div
              *ngIf="imageType == 'Provide new image URL'"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <input
                [(ngModel)]="image"
                [ngModelOptions]="{ standalone: true }"
                type="text"
                placeholder="Image URL"
              />
              <div class="placeholder">Image URL</div>
              <div class="input-info">URL of the image</div>
            </div>
            <div
              *ngIf="imageType == 'Upload new image'"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <form-image-upload
                [control]="form2.get('url')"
                [fileSize]="8388608"
                [hideAltText]="true"
              ></form-image-upload>
            </div>
          </div>
          <div *ngIf="false" class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container" (focusout)="changeFocus(9)">
                <input
                  [(ngModel)]="frameType"
                  [ngModelOptions]="{ standalone: true }"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="isDropDownOpen[9] = true"
                  readonly
                />

                <div class="placeholder">Frame Details</div>
                <button (click)="isDropDownOpen[9] = !isDropDownOpen[9]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[9]">
                  <ul>
                    <li
                      style="cursor: pointer"
                      (click)="
                        frameType = 'Without frame'; isDropDownOpen[9] = false
                      "
                    >
                      <div class="country-name">Without frame</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        frameType = 'Brown frame'; isDropDownOpen[9] = false
                      "
                    >
                      <div class="country-name">Brown frame</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        frameType = 'Light brown frame';
                        isDropDownOpen[9] = false
                      "
                    >
                      <div class="country-name">Light brown frame</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        frameType = 'Black frame'; isDropDownOpen[9] = false
                      "
                    >
                      <div class="country-name">Black frame</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        frameType = 'White frame'; isDropDownOpen[9] = false
                      "
                    >
                      <div class="country-name">White frame</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info"></div>
            </div>
            <div
              *ngIf="frameType && frameType != 'Without frame'"
              class="field-value"
              style="padding-right: 0.5vw"
            >
              <input
                [(ngModel)]="frameBorder"
                [ngModelOptions]="{ standalone: true }"
                type="number"
                placeholder="Frame border in CM"
              />
              <div class="placeholder">Frame border</div>
              <div class="input-info">Frame border in CM</div>
            </div>
            <div
              *ngIf="
                frameType && frameType != 'Without frame' && frameBorder > 0
              "
              class="field-value"
              style="position: relative"
            >
              <input
                type="color"
                [(ngModel)]="bgColor"
                [ngModelOptions]="{ standalone: true }"
                placeholder="HEX code"
                style="inline-size: 25vw"
              />
              <div class="placeholder">Frame background colour</div>
              <div class="input-info"></div>
            </div>
          </div>
          <div
            *ngIf="step > 2 || frameType == 'Without frame'"
            class="field-value"
            style="padding-right: 0.5vw; margin-bottom: 1vw"
          >
            <div class="button-group2" style="margin-top: -1vw">
              <div (click)="downloadImage()" class="next">Download</div>
              <div (click)="downloadImage(true)" class="next">
                Download Frame
              </div>
            </div>
            <div class="button-group2" style="margin-top: 1vw">
              <div (click)="downloadImage(false, 0.6)" class="next">
                Download 5mb
              </div>
              <div
                (click)="saveToMedia()"
                class="next"
                style="margin-left: 0.5vw"
              >
                Save to Media
              </div>
            </div>
          </div>

          <!-- <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.arId"
              [ngClass]="{ 'Disabled-Color': disableObj.arId }"

            >
              <input formControlName="arId" type="text" placeholder="AR ID" />
              <fa-icon  class="Lock-position" [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions" [hidden]="!disableObj.arId" > </fa-icon>

              <div class="placeholder">AR ID</div>
              <div class="input-info">
                Provide the AR ID from the HelloAR portal
              </div>
            </div>
          </div> -->
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" *ngIf="showConfirmation">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Update edition details?</p>

    <span
      >The dimensions provided are {{ width1 }} x {{ height1 }} cm but the image
      aspect ratio {{ width2 }} x {{ height2 }} px with a variation of
      {{ percentage }}%. Please confirm whether to proceed with the Preview, or
      click cancel to amend the dimensions or image and try again.</span
    >

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <div class="button-group2">
        <div (click)="showConfirmation = false; runCalculations()" class="next">
          Continue
        </div>
        <div
          (click)="showConfirmation = false; showPreView = false"
          class="next"
          style="margin-left: 0.5vw"
        >
          Cancel
        </div>
      </div>
    </div>
  </div>
</div>
<canvas id="canvas2" width="3000" height="600" style="display: none"></canvas>
