import {
	Component,
	ElementRef,
	NgModule,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { faLock } from "@fortawesome/free-solid-svg-icons";
import { flagData } from "src/app/pages/collector/profile/personal/flags";
import { ArtistInfoService } from "src/app/services/artist-info.service";
import { CollectorService } from "src/app/services/collector.service";
import { apiUrl } from "src/environments/environment.prod";
import { ArtistService } from "../../../services/artist.service";
import { ArtworkService } from "../../../services/artwork.service";
import { EditNotifyService } from "../edit-notify.service";

declare let fabric: any;
@Component({
	selector: "app-artworks-features",
	templateUrl: "./features.component.html",
	styleUrls: ["./features.component.scss"],
})
export class FeaturesArtworksComponent implements OnInit {
	dropDownValue = Array(10).fill(null);
	htmlContent = Array(10).fill(null);
	faLock = faLock;
	isDropDownOpen = Array(10).fill(false);

	selectedFiles: File[] = [];
	form: FormGroup;
	id;
	permissionsObj: any = {};
	showObj: any = {};
	isSubmited = false;
	Menuinit: any;
	disableObj: any = {};

	listCurrentPage;
	listTotalPage;
	scaleWall;
	image;
	showPreView = false;
	imageType;
	data;
	canvas;
	frameType = "Without frame";
	frameBorder;
	bgColor = "#ffffff";
	showConfirmation = false;
	img2;
	frameColor;
	width1;
	width2;
	height1;
	height2;
	percentage;
	selected_additional_image;
	form2;
	frameWidth = 2;
	strokePattern1;
	strokePattern2;

	step = 0;
	canvasWidth = 600;
	wallWidth = 300;

	createdAt;
	updatedAt;

	constructor(
		private router: Router,
		private formBuilder: FormBuilder,
		private server: CollectorService,
		private editNotifyService: EditNotifyService,
	) {}

	ngOnInit(): void {
		this.listCurrentPage = Number(localStorage.getItem("artworkCMSoffset"));
		this.listTotalPage = Number(localStorage.getItem("artworkCMStotalPage"));
		if (localStorage.getItem("artworkID")) {
			this.getArtWork();
		}
		// this.artworkService.nextButton$.subscribe((data) => {
		// 	if (
		// 		data === '/artist-portal/settings/artwork/add/features' ||
		// 		data === '/artist-portal/settings/artwork/edit/' + this.id + '/features'
		// 	) {
		// 		this.onSubmit();
		// 	}
		// });
		let decode = decodeURIComponent(
			escape(window.atob(localStorage.getItem("userDetails"))),
		);
		this.permissionsObj = JSON.parse(decode)
			["role_id"]["permissions"].find((x) => x.name == "Artworks")
			.tabsArr.find((x) => x.name == "Features");
		this.form = this.formBuilder.group({
			useScaleWall: new FormControl(false),
			height: new FormControl(null),
			width: new FormControl(null),
			depth: new FormControl(null),
			disableViewInRoom: new FormControl(false),
			compositeWidth: new FormControl(null),
			compositeHeight: new FormControl(null),
			isCompositeImage: new FormControl(false),
			hide3dFeature: new FormControl(false),
			arId: new FormControl(""),
			isFeatured: new FormControl(false),
		});
		this.form2 = this.formBuilder.group({
			url: new FormControl(null),
		});

		this.permissionsObj.fields.forEach((ele) => {
			this.showObj[ele.name] = ele.show;
			this.disableObj[ele.name] = ele.disabled;
			if (ele.formControl && ele.show) {
				if (ele.mandatory) {
					this.form.controls[ele.name].setValidators([Validators.required]);
				}
				ele["disabled"] =
					ele.disabled == "true" || ele.disabled == true ? true : false;
				if (ele.disabled) {
					this.form.controls[ele.name].disable();
				}
				let obj = {};
				obj[ele.name] = ele.defaultValue;
				// to patch default value
				if (!localStorage.getItem("artworkID")) {
					this.form.patchValue(obj);
				}
			}
		});
		// this.route.parent.paramMap.subscribe((params) => {
		// 	console.log(params.get('id'));

		// 	if (params.get('id')) {
		// 		this.id = params.get('id');
		// 		this.artistInfoService.getFeaturesData(this.id).subscribe((data) => {
		// 			const art = data;
		// 			this.form.patchValue(art);
		// 		});
		// 	} else {
		// 		this.id = null;
		// 	}
		// });

		this.form.valueChanges.subscribe((x) => {
			if (this.Menuinit == null) {
				this.Menuinit = x;
			} else {
				this.editNotifyService.setOption("Features", true);
			}
		});
		console.log(this.disableObj);
		fabric.Image.fromURL(
			"https://www.terrain.art/cdn-cgi/image/width=100,quality=72/https://ta-python.s3.us-east-2.amazonaws.com/1690264077946_oak-wood-textured-design-background.jpg",
			(img) => {
				this.strokePattern1 = new fabric.Pattern({
					source: img.getElement(),
					repeat: "repeat",
				});
			},
			{ crossOrigin: "anonymous" },
		);
		fabric.Image.fromURL(
			"https://www.terrain.art/cdn-cgi/image/width=100,quality=72/https://ta-python.s3.us-east-2.amazonaws.com/1690264137118_dark-brown-wood-texture-background-with-design-space.jpg",
			(img) => {
				this.strokePattern2 = new fabric.Pattern({
					source: img.getElement(),
					repeat: "repeat",
				});
			},
			{ crossOrigin: "anonymous" },
		);
	}

	async onFileSelect(files: FileList) {
		if (files[0].size < 2100000) {
			this.selectedFiles.push(files[0]);
		}
	}

	// to get artwork
	getArtWork() {
		let url = apiUrl.getArtwork + `/${localStorage.getItem("artworkID")}`;
		this.server.showSpinner();
		this.server.getApi(url).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				this.data = res.data;
				this.createdAt = new Date(res.data?.createdAt).toDateString();
				this.updatedAt = new Date(res.data?.updatedAt).toDateString();
				this.form.patchValue({
					useScaleWall: res.data.use_scale_wall,
					disableViewInRoom: res.data.disable_view_in_room,
					hide3dFeature: res.data.hide_3d_feature,
					isFeatured: res.data.featured,
					height: res.data.height,
					width: res.data.width,
					depth: res.data.depth,
					isCompositeImage: res.data.composite_image,
					arId: res.data.ar_id,
					compositeHeight: res.data.composite_height,
					compositeWidth: res.data.composite_width,
				});
			}
		});
	}
	removeItem(index) {
		this.selectedFiles.splice(index, 1);
	}
	onSubmit() {
		if (this.form.invalid) {
			alert("Form not valid.Please fill required fields correctly !");
			return;
		}
		let req = {
			use_scale_wall: this.form.getRawValue().useScaleWall,
			disable_view_in_room: this.form.getRawValue().disableViewInRoom,
			hide_3d_feature: this.form.getRawValue().hide3dFeature,
			featured: this.form.getRawValue().isFeatured,
			height: this.form.getRawValue().height,
			width: this.form.getRawValue().width,
			depth: this.form.getRawValue().depth,
			composite_image: this.form.getRawValue().isCompositeImage,
			ar_id: this.form.getRawValue().arId,
			composite_height: this.form.getRawValue().compositeHeight,
			composite_width: this.form.getRawValue().compositeWidth,
			tab: "Features",
		};
		if (localStorage.getItem("artworkID")) {
			req["artworkId"] = localStorage.getItem("artworkID");
		}

		let url = apiUrl.addArtwork;
		this.server.showSpinner();
		this.server.postApi(url, req).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				localStorage.setItem("artworkID", res.data["_id"]);
				this.editNotifyService.reset();
				alert(res.message);
				this.isSubmited = true;
			} else {
				this.isSubmited = false;
			}
		});

		// if (this.id) {
		// 	this.artistInfoService
		// 		.addFeaturesData(this.id, this.form.value)
		// 		.subscribe((data) => {
		// 			this.router.navigate([
		// 				'/artist-portal/settings/artwork/edit/' + this.id + '/financial',
		// 			]);
		// 		});
		// } else {
		// 	this.artistInfoService
		// 		.addFeaturesData(id, this.form.value)
		// 		.subscribe((data) => {
		// 			this.router.navigate([
		// 				'/artist-portal/settings/artwork/add/financial',
		// 			]);
		// 		});
		// }
	}
	async getValueWithAsync() {
		await this.onSubmit();
		if (this.isSubmited) {
			this.router.navigate(["/artist-portal/settings/artworks"]);
		}
	}

	chooseBg(selcted) {
		let canvas2 = new fabric.Canvas("canvas2");
		canvas2.clear();
		canvas2.dispose();
		let studioImg;
		this.scaleWall = selcted;
		this.isDropDownOpen[8] = false;
		this.step = 1;
		this.imageType = null;
		switch (this.scaleWall) {
			case "Corner, grey arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915436026_Corner%2C+grey+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 263;
				break;
			case "Corner, grey arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915464538_Corner%2C+grey+arm+chair+-+Posts+-+1350x1080px.png";
				this.wallWidth = 211;
				break;
			case "Corner, grey arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915494695_Corner%2C+grey+arm+chair+-+Stories+-+1920x1080px.png";
				this.wallWidth = 128;
				break;
			case "Corner, grey arm chair with plants - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688980613106_Corner%2C+grey+arm+chair+with+plants+-+Posts+-+1080x1080px.png";
				this.wallWidth = 263;
				break;
			case "Corner, grey arm chair with plants - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982027700_Corner%2C+grey+arm+chair+with+plants+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 211;
				break;
			case "Corner, grey arm chair with plants - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982066894_Corner%2C+grey+arm+chair+with+plants+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 128;
				break;
			case "Grey sofa with lamp - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692914977357_Grey+sofa+with+lamp+-+Posts+-+1080x1080px.png";
				this.wallWidth = 274;
				break;
			case "Grey sofa with lamp - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915012314_Grey+sofa+with+lamp+-+Posts+-+1350x1080px.png";
				this.wallWidth = 220;
				break;
			case "Grey sofa with lamp - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915050532_Grey+sofa+with+lamp+-+Stories+-+1920x1080px.png";
				this.wallWidth = 154;
				break;
			case "Grey sofa with lamp and shelf - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692914236950_Grey+sofa+with+lamp+and+shelf+-+Posts+-+1080x1080px.png";
				this.wallWidth = 274;
				break;
			case "Grey sofa with lamp and shelf - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982110356_Grey+sofa+with+lamp+and+shelf+-+Posts+-+1350x1080px.png";
				this.wallWidth = 220;
				break;
			case "Grey sofa with lamp and shelf - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013007441_Grey+sofa+with+lamp+and+shelf+-+Stories+-+1920x1080px.png";
				this.wallWidth = 154;
				break;
			case "Grey wall, chair with shelves - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013081293_Grey+wall%2C+chair+with+shelves+-+Posts+-+1080x1080px.jpg";
				this.wallWidth = 258;
				break;
			case "Grey wall, chair with shelves - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013143268_Grey+wall%2C+chair+with+shelves+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 258;
				break;
			case "Grey wall, chair with shelves - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013179847_Grey+wall%2C+chair+with+shelves+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 182;
				break;
			case "High arched wall - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915658639_High+arched+wall+-+Posts+-+1080x1080px.png";
				this.wallWidth = 359;
				break;
			case "High arched wall - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915692559_High%20arched%20wall%20-%20Posts%20-%201350x1080px.png";
				this.wallWidth = 286;
				break;
			case "High arched wall, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013205560_High%20arched%20wall%2C%20plant%20-%20Posts%20-%201080x1080px.jpg";
				this.wallWidth = 359;
				break;
			case "High arched wall, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013259624_High+arched+wall%2C+plant+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 286;
				break;
			case "Living room with blue sofa - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915912521_Living+room+with+blue+sofa+-+Posts+-+1080x1080px.png";
				this.wallWidth = 342;
				break;
			case "Living room with blue sofa - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915945966_Living+room+with+blue+sofa+-+Posts+-+1350x1080px.png";
				this.wallWidth = 275;
				break;
			case "Living room with blue sofa, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013313074_Living+room+with+blue+sofa+-+Posts+-+1080x1080px.jpeg";
				this.wallWidth = 342;
				break;
			case "Living room with blue sofa, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013395955_Living+room+with+blue+sofa+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 275;
				break;
			case "Plain wall, gallery setting - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013425626_Plain%20wall%2C%20gallery%20setting%20-%20Posts%20-%201080x1080px.jpeg";
				this.wallWidth = 414;
				break;
			case "Plain wall, gallery setting - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013464538_Plain+wall%2C+gallery+setting+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 331;
				break;
			case "Plain wall, gallery setting - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013608769_Plain+wall%2C+gallery+setting+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 413;
				break;
			case "Reading nook, green arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013752668_Reading+nook%2C+green+arm+chair+-+Posts+-+1080x1080px.jpg";
				this.wallWidth = 199;
				break;
			case "Reading nook, green arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013791089_Reading%20nook%2C%20green%20arm%20chair%20-%20Posts%20-%201350x1080px.jpg";
				this.wallWidth = 169;
				break;
			case "Reading nook, green arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013836050_Reading+nook%2C+green+arm+chair+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 111;
				break;
			case "White wall, arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013874164_White+wall%2C+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 160;
				break;
			case "White wall, arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013957402_White+wall%2C+arm+chair+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 123;
				break;
			case "White wall, arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015072310_White+wall%2C+arm+chair+-+Stories+-+1920x1080px.png";
				this.wallWidth = 107;
				break;
			case "White wall, bench - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916179972_White%20wall%2C%20bench%20-%20Posts%20-%201080x1080px.png";
				this.wallWidth = 216;
				break;
			case "White wall, bench - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916212680_White%20wall%2C%20bench%20-%20Posts%20-%201350x1080px.png";
				this.wallWidth = 170;
				break;
			case "White wall, bench - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916258998_White%20wall%2C%20bench%20-%20Stories%20-%201920x1080px.png";
				this.wallWidth = 157;
				break;
			case "White wall, bench, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015107117_White%20wall%2C%20bench%20-%20Posts%20-%201080x1080px.png";
				this.wallWidth = 216;
				break;
			case "White wall, bench, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015130632_White+wall%2C+bench+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 170;
				break;
			case "White wall, bench, plant - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015173565_White+wall%2C+bench+-+Stories+-+1920x1080px.png";
				this.wallWidth = 157;
				break;
			case "White wall, blue arm chair - Posts - 1350x1080p":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692913715150_White+wall%2C+blue+arm+chair+-+Posts+-+1350x1080px.png";
				this.wallWidth = 110;
				break;
			case "White wall, blue arm chair - Posts - 1080x1080p":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692913946590_White+wall%2C+blue+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 143;
				break;

			default:
				break;
		}
		if (this.canvas) {
			this.canvas.dispose();
		}
		this.canvas = new fabric.Canvas("canvas");
		this.canvas.clear();
		this.canvas.setWidth(500);
		fabric.Image.fromURL(
			`https://www.terrain.art/cdn-cgi/image/width=600,quality=52/${studioImg}`,
			(img1) => {
				this.canvas.setHeight((this.canvas.width * img1.height) / img1.width);
				img1.scaleToWidth(this.canvas.width);
				this.canvas.setBackgroundImage(
					img1,
					this.canvas.renderAll.bind(this.canvas),
				);
			},
			{ crossOrigin: "anonymous" },
		);
	}
	chooseImage() {
		this.step = 2;
		let image;
		//this.frameType = null;
		switch (this.imageType) {
			case "Primary Image":
				image = this.data?.primary_image[0]?.url;
				break;
			case "Thumbnail of Primary":
				image = this.data?.thumbnail_of_primary;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Upload new image":
				image = this.form2.value.url;
				break;
			default:
				image =
					this.data?.additional_images[this.selected_additional_image]?.url;
				break;
		}
		fabric.Image.fromURL(
			`https://www.terrain.art/cdn-cgi/image/width=300,quality=52/${image}`,
			(img2) => {
				let ratio =
					this.form.getRawValue().width / this.form.getRawValue().height;
				let ratio2 = img2.width / img2.height;
				this.img2 = img2;
				this.runCalculations();
			},
			{ crossOrigin: "anonymous" },
		);
	}
	async chooseFrame() {
		this.step = 3;
		let frameColor;
		if (this.frameType == "Without frame") {
			frameColor = null;
		}

		switch (this.frameType) {
			case "Without frame":
				frameColor = null;
				this.frameBorder = null;
				await this.chooseImage();

				break;
			case "Brown frame":
				//frameColor = '#503f3f';
				frameColor = this.strokePattern2;
				break;
			case "Light brown frame":
				// frameColor = '#b19870';
				frameColor = this.strokePattern1;
				break;
			case "Black frame":
				frameColor = "#000000";
				break;
			case "White frame":
				frameColor = "#ffffff";
				break;
			default:
				break;
		}
		this.frameColor = frameColor;
		this.runCalculations();
	}
	showPreviewFun() {
		// if (!this.form.getRawValue().height) {
		//   alert('please provide height');
		//   return;
		// }
		if (!this.form.getRawValue().width) {
			alert("please provide width");
			return;
		}

		if (
			this.imageType == "Primary Image" &&
			!this.data?.primary_image[0]?.url
		) {
			alert("Primary Image not found");
			return;
		}
		if (
			this.imageType == "Thumbnail of Primary" &&
			!this.data?.thumbnail_of_primary
		) {
			alert("Thumbnail of Primary not found");
			return;
		}
		if (this.imageType == "Provide new image URL" && !this.image) {
			alert("please provide image");
			return;
		}

		if (!this.scaleWall) {
			alert("please choose Background image");
			return;
		}

		if (!this.frameType) {
			alert("please provide frame deatails");
			return;
		}

		let studioImg;

		switch (this.scaleWall) {
			case "Studio 1":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1684710854633_Arched%20wall%2C%20large%20works%20and%20sculptures_AdobeStock_397283119.jpeg";
				break;
			case "Studio 2":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688640702665_Empty+grey+wall%2C+blue+sofa_AdobeStock_383993129.jpeg";
				break;

			default:
				break;
		}

		let frameColor;
		if (this.frameType == "Without frame") {
			frameColor = null;
		}

		switch (this.frameType) {
			case "Without frame":
				frameColor = null;
				break;
			case "Brown frame":
				frameColor = "#503f3f";
				break;
			case "Light brown frame":
				frameColor = "#b19870";
				break;
			case "Black frame":
				frameColor = "#000000";
				break;
			case "White frame":
				frameColor = "#ffffff";
				break;
			default:
				break;
		}

		let image;

		if (this.imageType == "Primary Image") {
			image = this.data?.primary_image[0]?.url;
		} else if (this.imageType == "Thumbnail of Primary") {
			image = this.data?.thumbnail_of_primary;
		} else {
			image = this.image;
		}

		switch (this.imageType) {
			case "Primary Image":
				image = this.data?.primary_image[0]?.url;
				break;
			case "Thumbnail of Primary":
				image = this.data?.thumbnail_of_primary;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Upload new image":
				image = this.form2.value.url;
				break;
			default:
				image =
					this.data?.additional_images[this.selected_additional_image]?.url;
				break;
		}

		this.canvas = new fabric.Canvas("canvas");
		this.canvas.clear();
		this.canvas.setWidth(600);
		var totalImages = 2;
		var loadedImages = 0;

		fabric.Image.fromURL(
			`https://www.terrain.art/cdn-cgi/image/width=800,quality=52/${studioImg}`,
			(img1) => {
				this.canvas.setHeight((this.canvas.width * img1.height) / img1.width);
				img1.scaleToWidth(this.canvas.width);

				this.canvas.setBackgroundImage(
					img1,
					this.canvas.renderAll.bind(this.canvas),
				);
				loadedImages++;

				if (loadedImages === totalImages) {
					this.allImagesLoaded();
				}
			},
			{ crossOrigin: "anonymous" },
		);

		fabric.Image.fromURL(
			`https://www.terrain.art/cdn-cgi/image/width=300,quality=52/${image}`,
			(img2) => {
				let ratio =
					this.form.getRawValue().width / this.form.getRawValue().height;
				let ratio2 = img2.width / img2.height;
				this.img2 = img2;
				this.frameColor = frameColor;

				// if (ratio != ratio2) {
				//   let percentage;
				//   if(ratio > ratio2){
				//     percentage = Math.floor(((ratio - ratio2)/ratio)*100)

				//   }else{
				//     percentage = Math.floor(((ratio2 - ratio)/ratio2)*100)
				//   }

				//   this.percentage = percentage;
				//   this.width1 = this.form.getRawValue().width;
				//   this.height1 = this.form.getRawValue().height;
				//   this.width2 =  img2.width;
				//   this.height2 =  img2.height;

				//   this.showConfirmation = true;

				// }else{
				this.runCalculations();
				//}

				loadedImages++;
				if (loadedImages === totalImages) {
					this.allImagesLoaded();
				}
			},
			{ crossOrigin: "anonymous" },
		);
	}

	runCalculations() {
		var img2Can = this.canvas
			.getObjects()
			.find((obj) => obj.id === "secondImage");
		if (img2Can) {
			this.canvas.remove(img2Can);
			this.canvas.renderAll();
		}
		var img2CanSet = this.canvas
			.getObjects()
			.find((obj) => obj.id === "secondImageSet");
		if (img2CanSet) {
			this.canvas.remove(img2CanSet);
			this.canvas.renderAll();
		}
		var img2CanScale = this.canvas
			.getObjects()
			.find((obj) => obj.id === "secondImageScale");
		if (img2CanScale) {
			this.canvas.remove(img2CanScale);
			this.canvas.renderAll();
		}
		var frameImage = this.canvas
			.getObjects()
			.find((obj) => obj.id === "frameImage");
		if (frameImage) {
			this.canvas.remove(frameImage);
			this.canvas.renderAll();
		}

		var borderShadow = this.canvas
			.getObjects()
			.find((obj) => obj.id === "borderShadow");
		if (borderShadow) {
			this.canvas.remove(borderShadow);
			this.canvas.renderAll();
		}

		var imageShadow = this.canvas
			.getObjects()
			.find((obj) => obj.id === "imageShadow");
		if (imageShadow) {
			this.canvas.remove(imageShadow);
			this.canvas.renderAll();
		}
		let img2 = this.img2;
		let frameColor = this.frameColor;
		img2.scaleToWidth(
			this.canvas.width / (this.wallWidth / this.form.getRawValue().width),
		);
		if (frameColor) {
			if (this.frameBorder && this.frameBorder > 0) {
				img2.set({
					left: 0,
					top: 0,
					hasBorders: false,
					hasControls: false,
					lockScalingX: true,
					lockScalingY: true,
					selectable: false,
					stroke: frameColor,
					strokeWidth: 0,
					id: "secondImageSet",
					shadow: "rgba(0,0,0,0.3) 0px 0px 0px",
				});

				var border = new fabric.Rect({
					left: -(this.canvas.width * this.frameBorder) / this.wallWidth,
					top: -(this.canvas.width * this.frameBorder) / this.wallWidth,
					width:
						img2.width * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth,
					height:
						img2.height * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth,
					fill: this.bgColor,
					selectable: false,
					id: "secondImageScale",
				});

				var frame = new fabric.Rect({
					left:
						-(this.canvas.width * this.frameWidth) / this.wallWidth -
						(this.canvas.width * this.frameBorder) / this.wallWidth,
					top:
						-(this.canvas.width * this.frameWidth) / this.wallWidth -
						(this.canvas.width * this.frameBorder) / this.wallWidth,
					width:
						img2.width * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth +
						(this.canvas.width * this.frameWidth * 2) / this.wallWidth,
					height:
						img2.height * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth +
						(this.canvas.width * this.frameWidth * 2) / this.wallWidth,
					fill: frameColor,
					selectable: false,
					id: "frameImage",
				});

				var borderShadow = new fabric.Rect({
					left: -(this.canvas.width * this.frameBorder) / this.wallWidth,
					top: -(this.canvas.width * this.frameBorder) / this.wallWidth,
					width:
						img2.width * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth,
					height:
						img2.height * img2.scaleX +
						(this.canvas.width * this.frameBorder * 2) / this.wallWidth,
					fill: "rgba(0,0,0,0)",
					selectable: false,
					stroke: "rgba(0,0,0,0.15)",
					strokeWidth: (this.canvas.width * 0.3) / this.wallWidth,
					id: "borderShadow",
				});
				var imageShadow = new fabric.Rect({
					left: 0,
					top: 0,
					width: img2.width * img2.scaleX,
					height: img2.height * img2.scaleX,
					fill: "rgba(0,0,0,0)",
					selectable: false,
					stroke: "rgba(0,0,0,0.15)",
					strokeWidth: (this.canvas.width * 0.3) / this.wallWidth,
					id: "imageShadow",
				});

				var group = new fabric.Group(
					[frame, border, img2, borderShadow, imageShadow],
					{
						left: 0,
						top: 0,
						hasBorders: true,
						hasControls: true,
						lockScalingX: true,
						lockScalingY: true,
						lockRotation: true,
						id: "secondImage",
						shadow: "rgba(0,0,0,0.3) 2px 2px 2px",
					},
				);

				this.canvas.add(group);
				group.centerH();
				group.centerV();

				// var group2 = new fabric.Group([borderShadow], {
				//   left: 0,
				//   top: 0,
				//   id: 'secondImagesdasd',
				//   selectable: false,
				//   shadow: 'rgba(0,0,0,0.3) 2px 2px 2px',
				// });

				// this.canvas.add(group2);
				// group2.centerH();
				// group2.centerV();
			} else {
				img2.set({
					left: 0,
					top: 0,
					hasBorders: false,
					hasControls: false,
					lockScalingX: true,
					lockScalingY: true,
					selectable: false,
					stroke: frameColor,
					strokeWidth: 0,
					id: "secondImageSet",
					shadow: "rgba(0,0,0,0.3) 0px 0px 0px",
				});
				var frame = new fabric.Rect({
					left: -(this.canvas.width * this.frameWidth) / this.wallWidth,
					top: -(this.canvas.width * this.frameWidth) / this.wallWidth,
					width:
						img2.width * img2.scaleX +
						(this.canvas.width * this.frameWidth * 2) / this.wallWidth,
					height:
						img2.height * img2.scaleX +
						(this.canvas.width * this.frameWidth * 2) / this.wallWidth,
					fill: frameColor,
					selectable: false,
					id: "frameImage",
				});

				var group = new fabric.Group([frame, img2], {
					left: 0,
					top: 0,
					hasBorders: true,
					hasControls: true,
					lockScalingX: true,
					lockScalingY: true,
					lockRotation: true,
					id: "secondImage",
					shadow: "rgba(0,0,0,0.3) 2px 2px 2px",
				});
				this.canvas.add(group);
				group.centerH();
				group.centerV();
			}
		} else {
			let pxValue = Math.ceil(2 / img2.scaleX);
			let shadow = `rgba(0,0,0,0.3) ${pxValue}px ${pxValue}px ${pxValue}px`;

			img2.set({
				left:
					this.canvas.width / 2 -
					img2.width / (2 * (this.wallWidth / this.form.getRawValue().width)),
				top:
					this.canvas.height / 2 -
					img2.height / (2 * (this.wallWidth / this.form.getRawValue().width)),
				hasBorders: true,
				hasControls: true,
				lockScalingX: true,
				lockScalingY: true,
				lockRotation: true,
				selectable: true,
				strokeWidth: 0,
				id: "secondImage",
				shadow: shadow,
			});
			this.canvas.add(img2);
			img2.centerH();
			img2.centerV();
		}
	}
	allImagesLoaded() {
		this.showPreView = true;
	}
	downloadImage(frameOnly = false, scale = 1) {
		this.server.showSpinner();
		var img2Can = this.canvas
			.getObjects()
			.find((obj) => obj.id === "secondImage");

		let studioImg;
		let image;
		switch (this.imageType) {
			case "Primary Image":
				image = this.data?.primary_image[0]?.url;
				break;
			case "Thumbnail of Primary":
				image = this.data?.thumbnail_of_primary;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Provide new image URL":
				image = this.image;
				break;
			case "Upload new image":
				image = this.form2.value.url;
				break;
			default:
				image =
					this.data?.additional_images[this.selected_additional_image]?.url;
				break;
		}
		switch (this.scaleWall) {
			case "Corner, grey arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915436026_Corner%2C+grey+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 263;
				break;
			case "Corner, grey arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915464538_Corner%2C+grey+arm+chair+-+Posts+-+1350x1080px.png";
				this.wallWidth = 211;
				break;
			case "Corner, grey arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915494695_Corner%2C+grey+arm+chair+-+Stories+-+1920x1080px.png";
				this.wallWidth = 128;
				break;
			case "Corner, grey arm chair with plants - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688980613106_Corner%2C+grey+arm+chair+with+plants+-+Posts+-+1080x1080px.png";
				this.wallWidth = 263;
				break;
			case "Corner, grey arm chair with plants - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982027700_Corner%2C+grey+arm+chair+with+plants+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 211;
				break;
			case "Corner, grey arm chair with plants - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982066894_Corner%2C+grey+arm+chair+with+plants+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 128;
				break;
			case "Grey sofa with lamp - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692914977357_Grey+sofa+with+lamp+-+Posts+-+1080x1080px.png";
				this.wallWidth = 274;
				break;
			case "Grey sofa with lamp - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915012314_Grey+sofa+with+lamp+-+Posts+-+1350x1080px.png";
				this.wallWidth = 220;
				break;
			case "Grey sofa with lamp - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915050532_Grey+sofa+with+lamp+-+Stories+-+1920x1080px.png";
				this.wallWidth = 154;
				break;
			case "Grey sofa with lamp and shelf - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692914236950_Grey+sofa+with+lamp+and+shelf+-+Posts+-+1080x1080px.png";
				this.wallWidth = 274;
				break;
			case "Grey sofa with lamp and shelf - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1688982110356_Grey+sofa+with+lamp+and+shelf+-+Posts+-+1350x1080px.png";
				this.wallWidth = 220;
				break;
			case "Grey sofa with lamp and shelf - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013007441_Grey+sofa+with+lamp+and+shelf+-+Stories+-+1920x1080px.png";
				this.wallWidth = 154;
				break;
			case "Grey wall, chair with shelves - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013081293_Grey+wall%2C+chair+with+shelves+-+Posts+-+1080x1080px.jpg";
				this.wallWidth = 258;
				break;
			case "Grey wall, chair with shelves - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013143268_Grey+wall%2C+chair+with+shelves+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 258;
				break;
			case "Grey wall, chair with shelves - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013179847_Grey+wall%2C+chair+with+shelves+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 182;
				break;
			case "High arched wall - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915658639_High+arched+wall+-+Posts+-+1080x1080px.png";
				this.wallWidth = 359;
				break;
			case "High arched wall - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915692559_High%20arched%20wall%20-%20Posts%20-%201350x1080px.png";
				this.wallWidth = 286;
				break;
			case "High arched wall, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013205560_High%20arched%20wall%2C%20plant%20-%20Posts%20-%201080x1080px.jpg";
				this.wallWidth = 359;
				break;
			case "High arched wall, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013259624_High+arched+wall%2C+plant+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 286;
				break;
			case "Living room with blue sofa - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915912521_Living+room+with+blue+sofa+-+Posts+-+1080x1080px.png";
				this.wallWidth = 342;
				break;
			case "Living room with blue sofa - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692915945966_Living+room+with+blue+sofa+-+Posts+-+1350x1080px.png";
				this.wallWidth = 275;
				break;
			case "Living room with blue sofa, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013313074_Living+room+with+blue+sofa+-+Posts+-+1080x1080px.jpeg";
				this.wallWidth = 342;
				break;
			case "Living room with blue sofa, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013395955_Living+room+with+blue+sofa+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 275;
				break;
			case "Plain wall, gallery setting - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013425626_Plain%20wall%2C%20gallery%20setting%20-%20Posts%20-%201080x1080px.jpeg";
				this.wallWidth = 414;
				break;
			case "Plain wall, gallery setting - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013464538_Plain+wall%2C+gallery+setting+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 331;
				break;
			case "Plain wall, gallery setting - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013608769_Plain+wall%2C+gallery+setting+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 413;
				break;
			case "Reading nook, green arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013752668_Reading+nook%2C+green+arm+chair+-+Posts+-+1080x1080px.jpg";
				this.wallWidth = 199;
				break;
			case "Reading nook, green arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013791089_Reading%20nook%2C%20green%20arm%20chair%20-%20Posts%20-%201350x1080px.jpg";
				this.wallWidth = 169;
				break;
			case "Reading nook, green arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013836050_Reading+nook%2C+green+arm+chair+-+Stories+-+1920x1080px.jpg";
				this.wallWidth = 111;
				break;
			case "White wall, arm chair - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013874164_White+wall%2C+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 160;
				break;
			case "White wall, arm chair - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689013957402_White+wall%2C+arm+chair+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 123;
				break;
			case "White wall, arm chair - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015072310_White+wall%2C+arm+chair+-+Stories+-+1920x1080px.png";
				this.wallWidth = 107;
				break;
			case "White wall, bench - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916179972_White%20wall%2C%20bench%20-%20Posts%20-%201080x1080px.png";
				this.wallWidth = 216;
				break;
			case "White wall, bench - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916212680_White%20wall%2C%20bench%20-%20Posts%20-%201350x1080px.png";
				this.wallWidth = 170;
				break;
			case "White wall, bench - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692916258998_White%20wall%2C%20bench%20-%20Stories%20-%201920x1080px.png";
				this.wallWidth = 157;
				break;
			case "White wall, bench, plant - Posts - 1080x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015107117_White%20wall%2C%20bench%20-%20Posts%20-%201080x1080px.png";
				this.wallWidth = 216;
				break;
			case "White wall, bench, plant - Posts - 1350x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015130632_White+wall%2C+bench+-+Posts+-+1350x1080px.jpg";
				this.wallWidth = 170;
				break;
			case "White wall, bench, plant - Stories - 1920x1080px":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1689015173565_White+wall%2C+bench+-+Stories+-+1920x1080px.png";
				this.wallWidth = 157;
				break;
			case "White wall, blue arm chair - Posts - 1350x1080p":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692913715150_White+wall%2C+blue+arm+chair+-+Posts+-+1350x1080px.png";
				this.wallWidth = 110;
				break;
			case "White wall, blue arm chair - Posts - 1080x1080p":
				studioImg =
					"https://ta-python.s3.us-east-2.amazonaws.com/1692913946590_White+wall%2C+blue+arm+chair+-+Posts+-+1080x1080px.png";
				this.wallWidth = 143;
				break;

			default:
				break;
		}
		let canvas = new fabric.Canvas("canvas2");
		canvas.clear();
		canvas.setWidth(3000);
		let group;
		fabric.Image.fromURL(
			`https://www.terrain.art/cdn-cgi/image/width=3000,quality=82/${studioImg}`,
			(img1) => {
				canvas.setHeight((canvas.width * img1.height) / img1.width);
				img1.scaleToWidth(canvas.width);
				canvas.setBackgroundImage(img1, canvas.renderAll.bind(canvas));
				fabric.Image.fromURL(
					`https://www.terrain.art/cdn-cgi/image/width=1500,quality=82/${image}`,
					(img2) => {
						let frameColor = this.frameColor;
						img2.scaleToWidth(
							canvas.width / (this.wallWidth / this.form.getRawValue().width),
						);
						if (frameColor) {
							let boarderWidth =
								(canvas.width * this.frameWidth) / this.wallWidth;
							if (this.frameBorder && this.frameBorder > 0) {
								img2.set({
									left: 0,
									top: 0,
									hasBorders: false,
									hasControls: false,
									lockScalingX: true,
									lockScalingY: true,
									selectable: false,
									shadow: "rgba(0,0,0,0.3) 0px 0px 0px",
								});

								var border = new fabric.Rect({
									left: -(canvas.width * this.frameBorder) / this.wallWidth,
									top: -(canvas.width * this.frameBorder) / this.wallWidth,
									width:
										img2.width * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth,
									height:
										img2.height * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth,
									fill: this.bgColor,
									selectable: false,
								});

								var frame = new fabric.Rect({
									left:
										-(canvas.width * this.frameWidth) / this.wallWidth -
										(canvas.width * this.frameBorder) / this.wallWidth,
									top:
										-(canvas.width * this.frameWidth) / this.wallWidth -
										(canvas.width * this.frameBorder) / this.wallWidth,
									width:
										img2.width * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth +
										(canvas.width * this.frameWidth * 2) / this.wallWidth,
									height:
										img2.height * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth +
										(canvas.width * this.frameWidth * 2) / this.wallWidth,
									fill: frameColor,
									selectable: false,
								});
								var borderShadow = new fabric.Rect({
									left: -(canvas.width * this.frameBorder) / this.wallWidth,
									top: -(canvas.width * this.frameBorder) / this.wallWidth,
									width:
										img2.width * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth,
									height:
										img2.height * img2.scaleX +
										(canvas.width * this.frameBorder * 2) / this.wallWidth,
									fill: "rgba(0,0,0,0)",
									selectable: false,
									stroke: "rgba(0,0,0,0.15)",
									strokeWidth: (canvas.width * 0.3) / this.wallWidth,
									id: "borderShadow",
								});
								var imageShadow = new fabric.Rect({
									left: 0,
									top: 0,
									width: img2.width * img2.scaleX,
									height: img2.height * img2.scaleX,
									fill: "rgba(0,0,0,0)",
									selectable: false,
									stroke: "rgba(0,0,0,0.15)",
									strokeWidth: (canvas.width * 0.3) / this.wallWidth,
									id: "imageShadow",
								});
								let grpArray = [frame, border, img2];

								group = new fabric.Group(
									[...grpArray, borderShadow, imageShadow],
									{
										left: img2Can.left * 6,
										top: img2Can.top * 6,
										hasBorders: true,
										hasControls: true,
										lockScalingX: true,
										lockScalingY: true,
										lockRotation: true,
										id: "secondImage",
										shadow: "rgba(0,0,0,0.3) 12px 12px 12px",
									},
								);
								canvas.add(group);
							} else {
								img2.set({
									left: 0,
									top: 0,
									hasBorders: false,
									hasControls: false,
									lockScalingX: true,
									lockScalingY: true,
									selectable: false,
									stroke: frameColor,
									strokeWidth: 0,
									shadow: "rgba(0,0,0,0.3) 0px 0px 0px",
								});
								var frame = new fabric.Rect({
									left: -(canvas.width * this.frameWidth) / this.wallWidth,
									top: -(canvas.width * this.frameWidth) / this.wallWidth,
									width:
										img2.width * img2.scaleX +
										(canvas.width * this.frameWidth * 2) / this.wallWidth,
									height:
										img2.height * img2.scaleX +
										(canvas.width * this.frameWidth * 2) / this.wallWidth,
									fill: frameColor,
									selectable: false,
									id: "frameImage",
								});
								let grpArray = [frame, img2];

								group = new fabric.Group(grpArray, {
									left: img2Can.left * 6,
									top: img2Can.top * 6,
									hasBorders: true,
									hasControls: true,
									lockScalingX: true,
									lockScalingY: true,
									lockRotation: true,
									id: "secondImage",
									shadow: "rgba(0,0,0,0.3) 12px 12px 12px",
								});
								canvas.add(group);
							}
						} else {
							let pxValue = Math.ceil(12 / img2.scaleX);
							let shadow = `rgba(0,0,0,0.3) ${pxValue}px ${pxValue}px ${pxValue}px`;
							img2.set({
								left: img2Can.left * 6,
								top: img2Can.top * 6,
								hasBorders: true,
								hasControls: true,
								lockScalingX: true,
								lockScalingY: true,
								lockRotation: true,
								id: "secondImage2",
								shadow: shadow,
							});

							canvas.add(img2);
							group = img2;
						}
						setTimeout(() => {
							canvas.setDimensions({
								width: canvas.getWidth() * scale,
								height: canvas.getHeight() * scale,
							});
							canvas.setZoom(scale);
							var link = document.createElement("a");
							link.download =
								this.data.artwork_title + " + " + this.scaleWall + ".png";
							if (!frameOnly) {
								link.href = canvas.toDataURL();
							} else {
								console.log(group);

								link.href = canvas.toDataURL({
									format: "png",
									quality: 1,
									left: group.left,
									top: group.top,
									width: group.width * group.scaleX,
									height: group.height * group.scaleY,
								});
							}

							link.click();
							this.server.hideSpinner();
						}, 500);
					},
					{ crossOrigin: "anonymous" },
				);
			},
			{ crossOrigin: "anonymous" },
		);
	}

	saveToMedia() {
		// this.canvas.forEachObject(function (object) {
		//   object.set('selectable', false);
		//   object.set('evented', false);
		// });
		// this.canvas.selection = false;
		// this.canvas.renderAll();
		this.canvas.getActiveObject().selectable = false;
		this.canvas.getActiveObject().hasControls = false;
		this.canvas.getActiveObject().hasBorders = false;
		console.log(this.canvas.getActiveObject());

		this.canvas.renderAll();

		this.canvas.getElement().toBlob((blob) => {
			var file = new File(
				[blob],
				this.data.artwork_title + " + " + this.scaleWall + ".png",
				{
					type: "image/png",
					lastModified: Date.now(),
				},
			);
			var formData = new FormData();
			formData.append("image", file);
			let url = apiUrl.upload;
			this.server.showSpinner();
			this.server.postApi(url, formData).subscribe(
				(res) => {
					if (res.statusCode == 200) {
						this.canvas.getActiveObject().selectable = true;
						this.canvas.getActiveObject().hasControls = true;
						this.canvas.getActiveObject().hasBorders = true;
						this.canvas.renderAll();
						let imageURL = res.data;
						let additional_images = this.data.additional_images;
						if (!additional_images) {
							additional_images = [];
						}
						additional_images.push({ mediaType: "image", url: imageURL });
						let url2 = apiUrl.addArtwork;
						// let req;
						// if (localStorage.getItem('artworkID')) {
						//   req['artworkId'] = localStorage.getItem('artworkID');
						// }
						// req['additional_images']=additional_images
						this.server
							.postApi(url2, {
								additional_images,
								artworkId: localStorage.getItem("artworkID"),
							})
							.subscribe((res) => {
								this.server.hideSpinner();
								if (res.statusCode == 200) {
									alert(res.message);
								}
							});
					}
				},
				(err) => {
					alert(err.error.message);
				},
			);
		});
	}
	changeFocus(index) {
		// console.log('in in ');
		setTimeout(() => {
			this.isDropDownOpen[index] = false;
		}, 200);
	}
}
