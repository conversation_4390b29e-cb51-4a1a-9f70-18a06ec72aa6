<div>
  <div class="d-flex">
    <div class="section left-container" style="width: 100%">
      <div class="section-inner">
        <div
          class="d-flex justify-content-start slider-header"
          style="width: 100%"
        >
          <a routerLink="/artist-portal/settings/artworks">
            <h3 style="color: var(--tertiary-font-color)">
              <fa-icon [icon]="faArrowAltCircleLeft"></fa-icon>
            </h3>
          </a>
          <ng-container *ngFor="let item of permissionsObj?.tabsArr">
            <a (click)="PromptSave(item.name)" *ngIf="item.show">
              <h3
                [ngClass]="{
                  active: selectedTab == item.name
                }"
              >
                {{ item?.name }}
                <img
                  [hidden]="!editedMenu[item?.name]"
                  src="../../../../../assets/icons/edit-line-item.png"
                  class="edited-tooltip"
                  alt="edited"
                  title="This field was recently edited by you !"
                />
              </h3>
            </a>
          </ng-container>
        </div>
        <div class="content-inside" #scrollTop>
          <router-outlet></router-outlet>
        </div>

        <!-- <div class="footer-nav">
          <div class="button-group">
            <div  class="next">Save</div>
            <div  class="next">Save & Close</div>
            <div  class="next" style="margin-right: 1.5vw;">Duplicate</div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
