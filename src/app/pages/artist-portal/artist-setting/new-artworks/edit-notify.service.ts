import { Subject, BehaviorSubject } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class EditNotifyService {
  //  private data = {};  
  public data: BehaviorSubject<any> = new BehaviorSubject<any>({
    Details: false,
    Features: false,
    Financial: false,
    Main: false,
    Media: false,
    Publish: false,
    Shipping: false,
    Tagging: false
  });

  constructor() { }

  setOption(option, value) {
    // this.data[option] = value;
    // console.log(this.data)
    let DataValue=this.data.getValue();
    DataValue[option]=value;
    this.data.next(DataValue);
  }

  getOption() {
    return this.data;
  }
  reset() {
    this.data.next({
      Details: false,
      Features: false,
      Financial: false,
      Main: false,
      Media: false,
      Publish: false,
      Shipping: false,
      Tagging: false
    });
  }
}
