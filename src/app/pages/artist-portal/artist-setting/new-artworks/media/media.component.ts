import { Component, OnInit, ViewChild } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ImageUploadComponent } from 'src/app/core/image-upload/image-upload.component';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { ArtworkService } from '../../../services/artwork.service';
import { faGripVertical, faLock } from '@fortawesome/free-solid-svg-icons';
import { EditNotifyService } from '../edit-notify.service';
@Component({
  selector: 'app-artworks-media',
  templateUrl: './media.component.html',
  styleUrls: ['./media.component.scss'],
})
export class MediaArtworksComponent implements OnInit {
  tableColumns = [

    // {
    //   name: 'Media Field',
    //   type: 'select',
    //   control: 'mediaField',
    //   data: [
    //     {
    //       label: 'Artwork Thumbnail',
    //       value: 'artworkThumbnail',
    //       unique: true,
    //     },
    //     {
    //       label: 'Original Artwork File',
    //       value: 'originalArtworkFile',
    //       unique: true,
    //     },
    //     {
    //       label: 'Hover Image',
    //       value: 'hover_second_image',
    //       unique: true,
    //     },
    //     {
    //       label: 'Primary Thumbnail',
    //       value: 'thumbnail_of_primary',
    //       unique: true,
    //     },
    //     { label: 'Additional Media', value: 'additionalMedia' },
    //   ],
    // },
    {
      name: 'File Type',
      type: 'select',
      control: 'mediaType',
      data: [
        {
          label: 'Image',
          value: 'image',
        },
        { label: 'Video', value: 'video' },
        { label: '3D asset', value: 'glb' },
        { label: 'HTML code', value: 'html' },
      ],

    },

    {
      name: 'Media',
      type: 'media',
      control: 'url',
      depencyControl: 'mediaType',
    },
    {
      name: 'Publish',
      type: 'checkbox',
      control: 'publish',
    },
  ];
  tableColumns2 = [
    {
      name: 'Field Name',
      type: 'read',
      control: 'fieldName',
    },
    {
      name: 'File Type',
      type: 'select',
      control: 'mediaType',
      data: [
        {
          label: 'Image',
          value: 'image',
        },
        { label: 'Video', value: 'video' },
        { label: '3D asset', value: 'glb' },
        { label: 'HTML code', value: 'html' },
      ],
    },

    {
      name: 'Media',
      type: 'media',
      control: 'url',
      depencyControl: 'mediaType',
    },
  ];
  additionalMediaErrors = [];

  isSubmited = false;
  dropDownValue = Array(10).fill(null);
  htmlContent = Array(10).fill(null);
  tempOrder = 1;
  faGripVertical = faGripVertical;
  faLock = faLock;
  isDropDownOpen = Array(10).fill(false);
  selectedFilesObj: any = {
    thumbnailOfPrimaryArr: [],
    hoverSecondImageArr: [],
    additionalMediaImageArr: [],
    additionalMediaImage: '',
    thumbnailOfPrimary: '',
    hoverSecondImage: '',
  };
  placeholder: any = '';
  showType: boolean = false;
  mediaObj: any = {
    type: '',
    order: '',
  };
  accept: any = 'image/*';
  repeaters = [
    {
      isOpen: true,
      Media_Type: null,
      Order: null,
    },
  ];
  form: FormGroup;
  id;
  @ViewChild('thumbnailOfPrimary')
  private thumbnailOfPrimary: ImageUploadComponent;
  @ViewChild('hoverSecondImage')
  private hoverSecondImage: ImageUploadComponent;
  permissionsObj: any = {};
  showObj: any = {};
  Menuinit: any;
  disableObj: any = {};
  listCurrentPage;
  listTotalPage;

  extras;

  createdAt;
  updatedAt;

  isArtReadOnly = false;
  userDetails;
  artData: any;
  choosedEdition;
  editions: any;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private artworkService: ArtworkService,
    private artistInfoService: ArtistInfoService,
    private server: CollectorService,
    private editNotifyService: EditNotifyService
  ) { }

  ngOnInit(): void {
    this.listCurrentPage = Number(localStorage.getItem('artworkCMSoffset'));
    this.listTotalPage = Number(localStorage.getItem('artworkCMStotalPage'));
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }
    // this.artworkService.nextButton$.subscribe((data) => {
    // 	if (
    // 		data === '/artist-portal/settings/artwork/add/media' ||
    // 		data === '/artist-portal/settings/artwork/edit/' + this.id + '/media'
    // 	) {
    // 		this.onSubmit();
    // 	}
    // });
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetails = JSON.parse(decode);

    this.permissionsObj = JSON.parse(decode)
    ['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Media');
    this.form = this.formBuilder.group({
      showMedia: new FormControl(''),
      additionalMedia: this.formBuilder.array([]),
      mobileView: new FormControl(''),
      mobileViewName: new FormControl('Artwork Thumbnail'),
      originalMedia: this.formBuilder.array([this.formBuilder.group({

        mediaType: new FormControl(''),
        fieldName: new FormControl('Artwork Thumbnail'),
        mediaField: new FormControl('artworkThumbnail'),
        url: new FormControl(''),
      }), this.formBuilder.group({
        mediaType: new FormControl(''),
        fieldName: new FormControl('Original Artwork File'),
        mediaField: new FormControl('originalArtworkFile'),
        url: new FormControl(''),
      }), this.formBuilder.group({
        mediaType: new FormControl(''),
        fieldName: new FormControl('Hover Second Image'),
        mediaField: new FormControl('hover_second_image'),
        url: new FormControl(''),
      }), this.formBuilder.group({
        mediaType: new FormControl(''),
        fieldName: new FormControl('Thumbnail of Primary'),
        mediaField: new FormControl('thumbnail_of_primary'),
        url: new FormControl(''),
      }),]),
      editionMedia: this.formBuilder.array([this.formBuilder.group({

        mediaType: new FormControl(''),
        fieldName: new FormControl('Artwork Thumbnail'),
        mediaField: new FormControl('primary_image'),
        url: new FormControl(''),
      }), this.formBuilder.group({
        mediaType: new FormControl(''),
        fieldName: new FormControl('Original Artwork File'),
        mediaField: new FormControl('original_artwork_file'),
        url: new FormControl(''),
      })]),
      editionNumber: new FormControl(''),
    });
    console.log(this.permissionsObj.fields);
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled'] =
          ele.disabled == 'true' || ele.disabled == true ? true : false;
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
        let obj = {};
        obj[ele.name] = ele.defaultValue;
        // to patch default value
        if (!localStorage.getItem('artworkID')) {
          this.form.patchValue(obj);
        }
      }
    });
    console.log(this.disableObj);
    // this.route.parent.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getMediaData(this.id).subscribe(async (data) => {
    // 			const art = data;
    // 			art?.additionalMedia?.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater();
    // 				}
    // 			});
    // 			if (art.thumbnailOfPrimary) {
    // 				let response = await fetch(
    // 					'https://api.artist.terrain.art/' + art.thumbnailOfPrimary.path
    // 				);
    // 				let da = await response.blob();
    // 				let metadata = {
    // 					type: 'image/jpeg',
    // 				};
    // 				let file = new File(
    // 					[da],
    // 					art.thumbnailOfPrimary.originalname,
    // 					metadata
    // 				);
    // 				this.thumbnailOfPrimary.onFileSelect([file]);
    // 			}
    // 			if (art.hoverOfSecondImage) {
    // 				this.selectedFiles[1].push(
    // 					new File([''], art.hoverOfSecondImage.originalname)
    // 				);
    // 				let response = await fetch(
    // 					'https://api.artist.terrain.art/' + art.hoverOfSecondImage.path
    // 				);
    // 				let da = await response.blob();
    // 				let metadata = {
    // 					type: 'image/jpeg',
    // 				};
    // 				let file = new File(
    // 					[da],
    // 					art.hoverOfSecondImage.originalname,
    // 					metadata
    // 				);
    // 				this.hoverSecondImage.onFileSelect([file]);
    // 			}
    // 			art.mediaFiles.forEach((element, i) => {
    // 				this.selectedFiles[2 + i].push(
    // 					new File([''], element.originalname)
    // 				);
    // 			});
    // 			this.form.patchValue(art);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });

    this.form.valueChanges.subscribe((x) => {
      if (this.Menuinit == null) {
        this.Menuinit = x;
      } else {
        this.editNotifyService.setOption('Media', true);
      }
    });

    console.log(this.disableObj);
  }

  get additionalMedia(): FormArray {
    return <FormArray>this.form.get('additionalMedia');
  }

  // order: new FormControl(this.mediaObj.order),
  addRepeater() {
    // const order=this.tempOrder;
    // this.tempOrder++;
    this.additionalMedia.push(
      this.formBuilder.group({
        // isOpen: new FormControl(false),
        // order: new FormControl(order),
        mediaType: new FormControl(this.mediaObj.type),
        mediaField: new FormControl('additionalMedia'),
        publish: new FormControl(false),
        url: new FormControl(
          this.selectedFilesObj.additionalMediaImageArr.length > 0
            ? this.selectedFilesObj.additionalMediaImageArr[0].url
            : ''
        ),

      })
    );
  }
  // get originalMedia(): FormArray {
  //   return <FormArray>this.form.get('originalMedia');
  // }

  // addRepeater2() {
  //   this.originalMedia.push(
  //     this.formBuilder.group({
  //       mediaType: new FormControl(this.mediaObj.type),
  //       mediaField: new FormControl(this.mediaObj.type),
  //       url: new FormControl(
  //         ''
  //       ),
  //     })
  //   );
  // }
  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }

    this.additionalMediaErrors = Array(
      this.form.getRawValue().additionalMedia.length
    ).fill(null);
    let isAdditionalMediaError = false;
    this.form.getRawValue().additionalMedia.forEach((element, i) => {
      this.additionalMediaErrors[i] = {};
      if (!element.mediaType) {
        this.additionalMediaErrors[i]['mediaType'] = { showError: true };

        isAdditionalMediaError = true;
      }
      if (!element.url) {
        this.additionalMediaErrors[i]['url'] = { showError: true };

        isAdditionalMediaError = true;
      }
    });

    if (isAdditionalMediaError) {
      alert(
        'Form not valid.Please fill price additional media field correctly !'
      );
      return;
    }
    // if((this.permissionsObj.fields.find(x=>x.name == 'thumbnailOfPrimary')).mandatory && !this.selectedFilesObj.thumbnailOfPrimary) {
    // 	alert('Please upload thumbnail of primary!')
    // 	return;
    // }
    // if((this.permissionsObj.fields.find(x=>x.name == 'hoverSecondImage')).mandatory && !this.selectedFilesObj.hoverSecondImage) {
    // 	alert('Please upload hover of second image!')
    // 	return;
    // }
    // if (
    //   this.permissionsObj.fields.find((x) => x.name == 'additionalMedia')
    //     .mandatory &&
    //   this.form.value.additionalMedia.length == 0
    // ) {
    //   alert('Please upload additional media!');
    //   return;
    // }
    console.log(this.form.value.priceHistory);
    let url = apiUrl.addArtwork;
    let additional_images = this.form.getRawValue().additionalMedia;
    let primary_image_data = this.form.getRawValue().originalMedia.filter((a) => {
      return a.mediaField === 'artworkThumbnail'
    })
    let primary_image = [{
      file_event: "",
      filedata: "",
      name: "",
      size: 0,
      type: primary_image_data?.[0].mediaType,
      url: primary_image_data?.[0].url,
    }]
    let original_artwork_file_data = this.form.getRawValue().originalMedia.filter((a) => {
      return a.mediaField === 'originalArtworkFile'
    })
    let hover_second_image_data = this.form.getRawValue().originalMedia.filter((a) => {
      return a.mediaField === 'hover_second_image'
    })
    let thumbnail_of_primary_data = this.form.getRawValue().originalMedia.filter((a) => {
      return a.mediaField === 'thumbnail_of_primary'
    })
    let original_artwork_file = [{
      file_event: "",
      filedata: "",
      name: "",
      size: 0,
      type: original_artwork_file_data?.[0].mediaType,
      url: original_artwork_file_data?.[0].url,
    }]
    if (!this.extras) {
      this.extras = {}
    }
    this.extras['mobileView'] = this.form.getRawValue().mobileView;
    this.extras['mobileViewName'] = this.form.getRawValue().mobileViewName;
    let req = {
      thumbnail_of_primary: thumbnail_of_primary_data?.[0].url,
      hover_second_image: hover_second_image_data?.[0].url,
      show_media: this.form.getRawValue().showMedia,
      additional_images,
      tab: 'Media',
      primary_image,
      original_artwork_file,
      extras: this.extras
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }
    if (this.artData?.work_type == 'edition') {
      let original_artwork_file_data2 = this.form.getRawValue().editionMedia.filter((a) => {
        return a.mediaField === 'original_artwork_file'
      })

      let thumbnail_of_primary_data2 = this.form.getRawValue().editionMedia.filter((a) => {
        return a.mediaField === 'primary_image'
      })
      let primary_image2 = [{
        file_event: "",
        filedata: "",
        name: "",
        size: 0,
        type: thumbnail_of_primary_data2?.[0].mediaType,
        url: thumbnail_of_primary_data2?.[0].url,
      }]
      let original_artwork_file2 = [{
        file_event: "",
        filedata: "",
        name: "",
        size: 0,
        type: original_artwork_file_data2?.[0].mediaType,
        url: original_artwork_file_data2?.[0].url,
      }]
      this.server.postApi('artwork/updateEdition/' + localStorage.getItem('artworkID') + '/' + this.choosedEdition, { original_artwork_file: original_artwork_file2, primary_image: primary_image2 }).subscribe((res2) => {
        if (res2.statusCode == 200) {
          this.server.postApi(url, req).subscribe((res) => {
            if (res.statusCode == 200) {
              localStorage.setItem('artworkID', res.data['_id']);
              this.editNotifyService.reset();
              this.isSubmited = true;
              alert(res.message);
            } else {
              this.isSubmited = false;
            }
          });
        } else {
          this.isSubmited = false;
        }
      })

    } else {
      this.server.postApi(url, req).subscribe((res) => {
        if (res.statusCode == 200) {
          localStorage.setItem('artworkID', res.data['_id']);
          this.editNotifyService.reset();
          this.isSubmited = true;
          alert(res.message);
        } else {
          this.isSubmited = false;
        }
      });
    }

  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }
  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner();
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.artData = res.data;
        this.editions = this.artData?.editionDetails?.filter((a) => {
          return a.sale_options !== 'NotForSale'
        })
        this.extras = res.data.extras;
        this.createdAt = new Date(res.data?.createdAt).toDateString();
        this.updatedAt = new Date(res.data?.updatedAt).toDateString();
        this.form.patchValue({
          showMedia: res.data.show_media,
          mobileView: res.data.extras?.mobileView,
          mobileViewName: res.data.extras?.mobileViewName ? res.data.extras?.mobileViewName : 'Artwork Thumbnail',
        });
        if (res.data.publish_artworks && (this.userDetails.role == 'REPRESENTED ARTIST' || this.userDetails.role == 'TERRAIN CONSIGNED')) {
          this.isArtReadOnly = true;
        }
        if (res.data.primary_image.length > 0) {
          //this.addRepeater();
          this.form['controls'].originalMedia['controls'][0]
            .get('mediaField')
            .setValue('artworkThumbnail');
          this.form['controls'].originalMedia['controls'][0]
            .get('mediaType')
            .setValue(res.data.primary_image[0].type);
          this.form['controls'].originalMedia['controls'][0]
            .get('url')
            .setValue(res.data.primary_image[0].url);
        }
        if (res.data.original_artwork_file.length > 0) {

          this.form['controls'].originalMedia['controls'][1]
            .get('mediaField')
            .setValue('originalArtworkFile');
          this.form['controls'].originalMedia['controls'][1]
            .get('mediaType')
            .setValue(res.data.file_type);
          this.form['controls'].originalMedia['controls'][1]
            .get('url')
            .setValue(res.data.original_artwork_file[0].url);
        }
        if (res.data.thumbnail_of_primary) {
          this.form['controls'].originalMedia['controls'][2]
            .get('mediaField')
            .setValue('thumbnail_of_primary');
          this.form['controls'].originalMedia['controls'][2]
            .get('mediaType')
            .setValue('image');
          this.form['controls'].originalMedia['controls'][2]
            .get('url')
            .setValue(res.data.thumbnail_of_primary);
        }
        if (res.data.hover_second_image) {
          this.form['controls'].originalMedia['controls'][3]
            .get('mediaField')
            .setValue('hover_second_image');
          this.form['controls'].originalMedia['controls'][3]
            .get('mediaType')
            .setValue('image');
          this.form['controls'].originalMedia['controls'][3]
            .get('url')
            .setValue(res.data.hover_second_image);
        }
        this.form.valueChanges.subscribe((a) => {
          if (a?.originalMedia?.[0]?.url && !a?.originalMedia?.[2]?.url) {
            this.form['controls'].originalMedia['controls'][2]
              .get('url')
              .setValue(a?.originalMedia?.[0]?.url);
          }
          if (a?.originalMedia?.[0]?.url && !a?.originalMedia?.[3]?.url) {
            this.form['controls'].originalMedia['controls'][3]
              .get('url')
              .setValue(a?.originalMedia?.[0]?.url);
          }


        })


        res.data.additional_images.forEach((ele, index) => {
          this.addRepeater();
          // this.form['controls'].additionalMedia['controls'][index].get('order').setValue(index);
          this.form['controls'].additionalMedia['controls'][this.additionalMedia.length - 1]
            .get('mediaType')
            .setValue(ele.mediaType);
          this.form['controls'].additionalMedia['controls'][this.additionalMedia.length - 1]
            .get('url')
            .setValue(ele.url);
          this.form['controls'].additionalMedia['controls'][this.additionalMedia.length - 1]
            .get('mediaField')
            .setValue('additionalMedia');
          if (typeof ele?.publish === 'undefined') {
            this.form['controls'].additionalMedia['controls'][this.additionalMedia.length - 1]
              .get('publish')
              .setValue(true);
          } else {
            this.form['controls'].additionalMedia['controls'][this.additionalMedia.length - 1]
              .get('publish')
              .setValue(ele?.publish);
          }

        });
        // this.selectedFilesObj.thumbnailOfPrimary = res.data.thumbnail_of_primary
        // this.selectedFilesObj.hoverSecondImage = res.data.hover_second_image
      }
    });
  }

  changeFocus(index) {
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }
  onEditionSelect(index, name) {
    this.choosedEdition = index;
    console.log(name);

    this.form.patchValue({ editionNumber: name, })
    if (this.editions?.[index]?.original_artwork_file.length > 0) {

      this.form['controls'].editionMedia['controls'][1]
        .get('mediaField')
        .setValue('original_artwork_file');
      this.form['controls'].editionMedia['controls'][1]
        .get('mediaType')
        .setValue(this.editions?.[index]?.original_artwork_file?.[0]?.type);
      this.form['controls'].editionMedia['controls'][1]
        .get('url')
        .setValue(this.editions?.[index]?.original_artwork_file?.[0]?.url);
    }
    if (this.editions?.[index]?.primary_image) {
      this.form['controls'].editionMedia['controls'][0]
        .get('mediaField')
        .setValue('primary_image');
      this.form['controls'].editionMedia['controls'][0]
        .get('mediaType')
        .setValue(this.editions?.[index]?.primary_image?.[0]?.type);
      this.form['controls'].editionMedia['controls'][0]
        .get('url')
        .setValue(this.editions?.[index]?.primary_image?.[0]?.url);
    }

  }
}
