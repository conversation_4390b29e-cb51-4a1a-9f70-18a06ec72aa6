<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div
              class="field-value"
              [hidden]="!showObj?.showMedia"
              [ngClass]="{ 'Disabled-Color': disableObj.showMedia }"
            >
              <div class="input-container">
                <div class="text-before">Show Media:</div>
                <label class="switch">
                  <input
                    formControlName="showMedia"
                    type="checkbox"
                    [disabled]="disableObj.showMedia"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.showMedia && !isArtReadOnly"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Enable the flag to show additional images and videos on the
                artwork page
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container" (focusout)="changeFocus(0)">
                <input
                  formControlName="mobileViewName"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="isDropDownOpen[0] = true"
                  readonly
                />

                <div class="placeholder">Mobile View</div>
                <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[0]">
                  <ul>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form
                          .get('mobileViewName')
                          .setValue('Artwork Thumbnail');
                        form.get('mobileView').setValue(null);
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Artwork Thumbnail</div>
                    </li>
                    <!-- <li
                      style="cursor: pointer"
                      (click)="
                        form
                          .get('mobileViewName')
                          .setValue('Original Artwork File');
                        form
                          .get('mobileView')
                          .setValue(
                            this.form['controls'].originalMedia[
                              'controls'
                            ][1].get('url').value
                          );
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Original Artwork File</div>
                    </li> -->
                    <li
                      style="cursor: pointer"
                      (click)="
                        form
                          .get('mobileViewName')
                          .setValue('Hover Second Image');
                        form
                          .get('mobileView')
                          .setValue(
                            this.form['controls'].originalMedia[
                              'controls'
                            ][2].get('url').value
                          );
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Hover Second Image</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form
                          .get('mobileViewName')
                          .setValue('Thumbnail of Primary');
                        form
                          .get('mobileView')
                          .setValue(
                            this.form['controls'].originalMedia[
                              'controls'
                            ][3].get('url').value
                          );
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Thumbnail of Primary</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      *ngFor="
                        let row of additionalMedia.controls;
                        let i = index
                      "
                      (click)="
                        form
                          .get('mobileViewName')
                          .setValue('Additional Media ' + i + 1);
                        form.get('mobileView').setValue(row.get('url').value);
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">
                        Additional Media {{ i + 1 }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork was created individually or in
                collaboration with another artist
              </div>
            </div>
            <div *ngIf="form.get('mobileView').value" class="field-value">
              <img
                [src]="
                  'https://www.terrain.art/cdn-cgi/image/width=200,quality=52/' +
                  form.get('mobileView').value
                "
                style="
                  width: 8vw;
                  height: 8vw;
                  object-fit: cover;
                  margin-left: 0.8vw;
                "
              />
            </div>
          </div>
          <div *ngIf="artData?.work_type == 'edition'" class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container">
                <input
                  formControlName="editionNumber"
                  type="text"
                  class="selection"
                  placeholder="Choose Edition"
                  readonly="readonly"
                  (focus)="isDropDownOpen[3] = true"
                />
                <div class="placeholder">Choose Edition</div>
                <button
                  (click)="
                    isSaleOptionChangable &&
                      (isDropDownOpen[3] = !isDropDownOpen[3])
                  "
                  type="button"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[3],
                    'dropdown-visible': isDropDownOpen[3]
                  }"
                >
                  <ul>
                    <li
                      *ngFor="let item of editions; let i = index"
                      (click)="
                        isDropDownOpen[3] = false;
                        onEditionSelect(i, item?.edition)
                      "
                    >
                      <div class="country-name">{{ item?.edition }}</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <ng-container
            *ngIf="artData?.work_type == 'edition' && choosedEdition != null"
          >
            <p>
              Edition Media Files
              <fa-icon
                class="Lock-position-1"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!isArtReadOnly"
              >
              </fa-icon>
            </p>
            <forms-dynamic-table
              [enableDrag]="false"
              [columns]="tableColumns2"
              [formArray]="form.get('editionMedia')"
              [readOnly]="true"
              [disable]="isArtReadOnly"
            ></forms-dynamic-table>
          </ng-container>

          <div
            class="sub-head"
            [hidden]="!showObj?.additionalMedia"
            [ngClass]="{ 'Disabled-Color': disableObj.additionalMedia }"
            style="margin-top: 0.5vw"
          >
            <p>Additional Media</p>
            <forms-dynamic-table
              [enableDrag]="true"
              [columns]="tableColumns"
              [formArray]="form.get('additionalMedia')"
              [errors]="additionalMediaErrors"
            ></forms-dynamic-table>

            <p>
              Original Media Files
              <fa-icon
                class="Lock-position-1"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!isArtReadOnly"
              >
              </fa-icon>
            </p>
            <forms-dynamic-table
              [enableDrag]="false"
              [columns]="tableColumns2"
              [formArray]="form.get('originalMedia')"
              [errors]="additionalMediaErrors"
              [readOnly]="true"
              [disable]="isArtReadOnly"
            ></forms-dynamic-table>

            <!-- <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <div class="input-container" (focusout)="changeFocus(0)">
                  <input
                    type="text"
                    class="selection"
                    [(ngModel)]="mediaObj.type"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Choose an option"
                    (focus)="showType = true"
                    (focus)="isDropDownOpen[0] = true"
                    [readonly]="true"
                    [disabled]="!disableObj.additionalMedia"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.additionalMedia"
                  >
                  </fa-icon>

                  <div class="placeholder">File Type</div>
                  <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[0],
                      'dropdown-visible': isDropDownOpen[0]
                    }"
                    *ngIf="!disableObj.additionalMedia"
                  >
                    <ul>
                      <li
                        (click)="
                          showType = false;
                          mediaObj.type = 'image';
                          accept = 'image/*'
                        "
                      >
                        <div class="country-name">Image</div>
                      </li>
                      <li
                        (click)="
                          showType = false;
                          mediaObj.type = 'video';
                          accept = 'video/*'
                        "
                      >
                        <div class="country-name">Video</div>
                      </li>
                      <li
                        (click)="
                          showType = false;
                          mediaObj.type = 'glb';
                          accept = 'glb/*'
                        "
                      >
                        <div class="country-name">3D asset</div>
                      </li>
                      <li
                        (click)="
                          showType = false;
                          mediaObj.type = 'html';
                          accept = 'html/*'
                        "
                      >
                        <div class="country-name">HTML code</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the file type being uploaded.
                </div>
              </div>

            </div> -->
            <!-- <div class="">
              <div class="field-value" style="padding-right: 0.5vw">
                <image-upload
                  [accept]="accept"
                  [selectedData]="selectedFilesObj?.additionalMediaImageArr"
                  (onFileChange)="
                    onFileSelect($event, 'additionalMediaImageArr')
                  "
                  [Disable]="disableObj.additionalMedia"
                  [fileSize]="8388608"
                  [placeholder]="placeholder"
                ></image-upload>
              </div>
            </div> -->
            <!-- <div class="input-info">
              Upload additional images (installation/detail shots), videos (pan,
              instalation videos, in process videos) of the artwork
            </div>
            <p (click)="addRepeater()">
              <span
                ><img class="plus-icon" src="assets/images/load-more.png"
              /></span>
              <span class="sub-text">Add to additional media</span>
            </p> -->
          </div>
          <!-- <div class="outer-box" *ngIf="additionalMedia.controls.length > 0">
            <div class="labelforRepeater">
              <div class="width15vw">
                <span>Order</span>
              </div>
              <div class="width30vw">
                <span>Type</span>
              </div>
              <div class="width30vw">Image Url</div>
              <div class="width15vw">Actions</div>
            </div>
            <div
              class="reapeterContent"
              formArrayName="additionalMedia"
              [dragula]="'task-group'"
              [(dragulaModel)]="additionalMedia.controls"
            >
              <div
                class="inner-div-repeater"
                formGroupName="{{ i }}"
                *ngFor="let item of additionalMedia.controls; let i = index"
                style="cursor: grab !important"
              >
                <div style="display: flex; width: 100%">
                  <div class="width15vw">
                    <fa-icon
                      [icon]="faGripVertical"
                      style="width: 50%; padding-left: 2vw"
                    ></fa-icon>
                    <span style="width: 50%">{{ i + 1 }}</span>
                  </div>
                  <div class="width30vw">
                    <input formControlName="mediaType" type="text" disabled />
                  </div>
                  <div class="width30vw">
                    <input formControlName="url" type="text" disabled />
                  </div>
                  <div class="width15vw" style="justify-content: end">
                    <button
                      class="rm-btn"
                      (click)="additionalMedia.removeAt(i, 1)"
                    >
                      <img src="assets/icons/edit.png" alt="Close" disabled />
                    </button>
                    <button
                      (click)="additionalMedia.removeAt(i, 1)"
                      class="rm-btn"
                      style="padding-right: 2vw"
                    >
                      <img
                        src="assets/icons/grey-close.png"
                        alt="Close"
                        disabled
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>

           </div> -->
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
