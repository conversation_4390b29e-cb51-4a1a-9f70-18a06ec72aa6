import { EditNotifyService } from "./../edit-notify.service";
import {
	Component,
	ElementRef,
	NgModule,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormArray,
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { flagData } from "src/app/pages/collector/profile/personal/flags";
import { ArtistInfoService } from "src/app/services/artist-info.service";
import { CollectorService } from "src/app/services/collector.service";
import { apiUrl } from "src/environments/environment.prod";
import { ArtistService } from "../../../services/artist.service";
import { ArtworkService } from "../../../services/artwork.service";
import { faLock, faTrashAlt } from "@fortawesome/free-solid-svg-icons";
@Component({
	selector: "app-artworks-shipping",
	templateUrl: "./shipping.component.html",
	styleUrls: ["./shipping.component.scss"],
})
export class ShippingArtworksComponent implements OnInit {
	faLock = faLock;
	faTrash = faTrashAlt;
	dropDownValue = Array(30).fill(null);
	htmlContent = Array(10).fill(null);
	isSubmited = false;
	isDropDownOpen = Array(30).fill(false);
	htmlcheckBox = Array(10).fill(false);

	selectedFiles: File[] = [];

	form: FormGroup;
	id;
	permissionsObj: any = {};
	showObj: any = {};
	Menuinit: any;
	disableObj: any = {};
	locationDetailsObj = {
		currentLocation: "",
		locationDetails: "",
		date: "",
	};
	listCurrentPage;
	listTotalPage;
	userDetails;

	addressData = [];
	storageAddress = [];
	locationCode = [];
	showAddDocument = false;
	manageDocPop = false;
	isGallerWearhouse = 0;
	artistAddress;

	extras;
	documentUrl;
	createdAt;
	updatedAt;
	isArtReadOnly = false;
	artData;
	editions;

	choosedEdition;

	constructor(
		private router: Router,
		private server: CollectorService,
		private formBuilder: FormBuilder,
		private editNotifyService: EditNotifyService,
	) {}

	ngOnInit(): void {
		this.listCurrentPage = Number(localStorage.getItem("artworkCMSoffset"));
		this.listTotalPage = Number(localStorage.getItem("artworkCMStotalPage"));
		if (localStorage.getItem("artworkID")) {
			this.getArtWork();
		}
		let decode = decodeURIComponent(
			escape(window.atob(localStorage.getItem("userDetails"))),
		);
		this.userDetails = JSON.parse(decode);

		this.permissionsObj = JSON.parse(decode)
			["role_id"]["permissions"].find((x) => x.name == "Artworks")
			.tabsArr.find((x) => x.name == "Shipping");

		this.form = this.formBuilder.group({
			weight: new FormControl(null),
			createdLocation: new FormControl(""),
			currentLocation: new FormControl(""),
			consignmentStatus: new FormControl(false),
			consignmentDate: new FormControl(""),
			size: new FormControl(null),
			installReady: new FormControl(false),
			currentState: new FormControl(null),
			locationDetails: this.formBuilder.array([]),
			storageAddress: new FormControl(""),
			packaging: new FormControl(""),
			installState: new FormControl(""),
			objectWeight: new FormControl(""),
			packedWeight: new FormControl(""),
			volumetricWeight: new FormControl(""),
			packedHeight: new FormControl(""),
			packedWidth: new FormControl(""),
			packedDepth: new FormControl(""),
			packingNotes: new FormControl(""),
			generalNotes: new FormControl(""),
			shippingDocuments: this.formBuilder.array([]),
			editionNumber: new FormControl(""),
		});

		this.permissionsObj.fields.forEach((ele) => {
			this.showObj[ele.name] = ele.show;
			this.disableObj[ele.name] = ele.disabled;
			if (ele.formControl && ele.show) {
				if (ele.mandatory) {
					this.form.controls[ele.name].setValidators([Validators.required]);
				}
				ele["disabled"] =
					ele.disabled == "true" || ele.disabled == true ? true : false;
				if (ele.disabled) {
					this.form.controls[ele.name].disable();
				}
				let obj = {};
				obj[ele.name] = ele.defaultValue;
				// to patch default value
				if (!localStorage.getItem("artworkID")) {
					this.form.patchValue(obj);
				}
			}
		});
		// this.artworkService.nextButton$.subscribe((data) => {
		// 	if (
		// 		data === '/artist-portal/settings/artwork/add/shipping' ||
		// 		data === '/artist-portal/settings/artwork/edit/' + this.id + '/shipping'
		// 	) {
		// 		this.onSubmit();
		// 	}
		// 	this.form = this.formBuilder.group({
		// 		weight: new FormControl(null),
		// 		createdLocation: new FormControl(''),
		// 		currentLocation: new FormControl(''),
		// 		consignmentStatus: new FormControl(false),
		// 		consignmentDate: new FormControl(''),
		// 		size: new FormControl(null),
		// 		installReady: new FormControl(false),
		// 		currentState: new FormControl(null),
		// 	});
		// 	this.route.parent.paramMap.subscribe((params) => {
		// 		if (params.get('id')) {
		// 			this.id = params.get('id');
		// 			this.artistInfoService.getShippingData(this.id).subscribe((data) => {
		// 				const art = data;
		// 				this.form.patchValue(art);
		// 			});
		// 		} else {
		// 			this.id = null;
		// 		}
		// 	});
		// });

		this.form.valueChanges.subscribe((x) => {
			if (this.Menuinit == null) {
				this.Menuinit = x;
			} else {
				this.editNotifyService.setOption("Shipping", true);
			}
		});

		console.log(this.disableObj);
	}

	// async onFileSelect(files: FileList) {
	// 	if (files[0].size < 2100000) {
	// 		this.selectedFiles.push(files[0]);
	// 	}
	// }
	// removeItem(index) {
	// 	this.selectedFiles.splice(index, 1);
	// }

	// to get artwork
	getArtWork() {
		let url2 = `storage-address/all?offset=0&limit=100`;
		this.server.getApi(url2).subscribe((res) => {
			if (res.statusCode == 200) {
				this.storageAddress = res.data;
			}
		});
		let url = apiUrl.getArtwork + `/${localStorage.getItem("artworkID")}`;
		this.server.showSpinner();
		this.server.getApi(url).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				if (
					res.data.publish_artworks &&
					(this.userDetails.role == "REPRESENTED ARTIST" ||
						this.userDetails.role == "TERRAIN CONSIGNED")
				) {
					this.isArtReadOnly = true;
				}
				if (res.data?.extras) {
					this.extras = res.data?.extras;
				} else {
					this.extras = {};
				}
				this.addressData = res.data?.artist_id?.addresses;
				this.createdAt = new Date(res.data?.createdAt).toDateString();
				this.updatedAt = new Date(res.data?.updatedAt).toDateString();
				this.artData = res.data;
				this.editions = this.artData?.editionDetails?.filter((a) => {
					return a.sale_options !== "NotForSale";
				});
				this.form.patchValue({
					weight: res.data.weight,
					createdLocation: res.data.created_location,
					currentLocation: res.data.location,
					consignmentStatus: res.data.consignment_status,
					consignmentDate: res.data.consignment_date,
					size: res.data.size,
					installReady: res.data.install_ready,
					currentState: res.data.current_state,
					storageAddress: res.data.extras?.storageAddress,
					packaging: res.data.extras?.packaging,
					installState: res.data.extras?.installState,
					objectWeight: res.data.extras?.objectWeight,
					packedWeight: res.data.extras?.packedWeight,
					volumetricWeight: res.data.extras?.volumetricWeight,
					packedHeight: res.data.extras?.packedHeight,
					packedWidth: res.data.extras?.packedWidth,
					packedDepth: res.data.extras?.packedDepth,
					packingNotes: res.data.extras?.packingNotes,
					generalNotes: res.data.extras?.generalNotes,
				});
				this.isGallerWearhouse = res.data.extras?.isGallerWearhouse;
				this.artistAddress = res.data.extras?.artistAddress;
				res.data.extras?.shippingDocuments?.forEach((ele, index) => {
					(this.form.get("shippingDocuments") as FormArray).push(
						new FormGroup({
							url: new FormControl(ele.url),
						}),
					);
				});
			}
		});
	}
	get locationDetails(): FormArray {
		return <FormArray>this.form.get("locationDetails");
	}

	addRepeater2() {
		this.locationDetails.push(
			this.formBuilder.group({
				// isOpen: new FormControl(true),
				currentLocation: new FormControl(
					this.locationDetailsObj.currentLocation,
				),
				locationDetails: new FormControl(
					this.locationDetailsObj.locationDetails,
				),
				date: new FormControl(this.locationDetailsObj.date),
			}),
		);
	}

	onSubmit() {
		// if (this.form.invalid) {
		//   alert('Form not valid.Please fill form correctly !');
		//   return;
		// }

		this.extras["storageAddress"] = this.form.getRawValue().storageAddress;
		this.extras["packaging"] = this.form.getRawValue().packaging;
		this.extras["installState"] = this.form.getRawValue().installState;
		this.extras["objectWeight"] = this.form.getRawValue().objectWeight;
		this.extras["packedWeight"] = this.form.getRawValue().packedWeight;
		this.extras["volumetricWeight"] = this.form.getRawValue().volumetricWeight;
		this.extras["packedHeight"] = this.form.getRawValue().packedHeight;
		this.extras["packedWidth"] = this.form.getRawValue().packedWidth;
		this.extras["packedDepth"] = this.form.getRawValue().packedDepth;
		this.extras["packingNotes"] = this.form.getRawValue().packingNotes;
		this.extras["generalNotes"] = this.form.getRawValue().generalNotes;
		this.extras["shippingDocuments"] =
			this.form.getRawValue().shippingDocuments;
		this.extras["isGallerWearhouse"] = this.isGallerWearhouse;
		this.extras["artistAddress"] = this.artistAddress;

		let req = {
			weight: this.form.getRawValue().weight,
			created_location: this.form.getRawValue().createdLocation?.trim(),
			location: this.form.getRawValue().currentLocation?.trim(),
			consignment_status: this.form.getRawValue().consignmentStatus,
			consignment_date: this.form.getRawValue().consignmentDate,
			size: this.form.getRawValue().size,
			install_ready: this.form.getRawValue().installReady,
			current_state: this.form.getRawValue().currentState,
			tab: "Shipping",
			extras: this.extras,
		};
		if (localStorage.getItem("artworkID")) {
			req["artworkId"] = localStorage.getItem("artworkID");
		}
		if (this.artData?.work_type == "edition") {
			this.extras["weight"] = this.form.getRawValue().weight;
			this.extras["createdLocation"] = this.form.getRawValue().createdLocation;
			this.extras["currentLocation"] = this.form.getRawValue().currentLocation;
			this.extras["consignmentStatus"] =
				this.form.getRawValue().consignmentStatus;
			this.extras["size"] = this.form.getRawValue().size;
			this.extras["installReady"] = this.form.getRawValue().installReady;
			this.extras["currentState"] = this.form.getRawValue().currentState;
			if (localStorage.getItem("artworkID")) {
				this.server
					.postApi(apiUrl.addArtwork, {
						location: this.form.getRawValue().currentLocation?.trim(),
						artworkId: localStorage.getItem("artworkID"),
					})
					.subscribe((res) => {});
			}
			this.server
				.postApi(
					"artwork/updateEdition/" +
						localStorage.getItem("artworkID") +
						"/" +
						this.choosedEdition,
					{ extras: this.extras },
				)
				.subscribe((res2) => {
					if (res2.statusCode == 200) {
						this.isSubmited = true;
						alert("Updated");
					} else {
						this.isSubmited = false;
					}
				});
		} else {
			let url = apiUrl.addArtwork;
			this.server.showSpinner();
			this.server.postApi(url, req).subscribe((res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					localStorage.setItem("artworkID", res.data["_id"]);
					this.editNotifyService.reset();
					this.isSubmited = true;
					alert(res.message);
				} else {
					this.isSubmited = false;
				}
			});
		}
	}
	async getValueWithAsync() {
		await this.onSubmit();
		if (this.isSubmited) {
			this.router.navigate(["/artist-portal/settings/artworks"]);
		}
	}

	// const id = localStorage.getItem('artID');
	// const data = this.form.value;

	// if (this.id) {
	// 	this.artistInfoService
	// 		.addDetailsData(this.id, this.form.value)
	// 		.subscribe((data) => {
	// 			this.router.navigate([
	// 				'/artist-portal/settings/artwork/edit/' + this.id + '/features',
	// 			]);
	// 		});
	// } else {
	// 	this.artistInfoService
	// 		.addDetailsData(id, this.form.value)
	// 		.subscribe((data) => {
	// 			this.router.navigate([
	// 				'/artist-portal/settings/artwork/add/features',
	// 			]);
	// 		});
	// }
	// }
	changeFocus(index) {
		console.log("in in ");
		setTimeout(() => {
			this.isDropDownOpen[index] = false;
		}, 500);
	}

	onDocumentFileSelect(files) {
		if (files.length == 0) {
			this.documentUrl = null;
		} else {
			this.uploadFile(files);
		}
	}
	uploadFile(files: any) {
		let formdata = new FormData();
		formdata.append("image", files[0].file_event);
		let url = apiUrl.upload;
		this.server.showSpinner();
		this.server.postApi(url, formdata).subscribe(
			(res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					this.documentUrl = res.data;
					alert("File uploaded successfully!");
				}
			},
			(err) => {
				alert(err.error.message);
			},
		);
	}
	deleteDocumentEntry(index) {
		(this.form.get("shippingDocuments") as FormArray).removeAt(index);
	}
	addDocument() {
		this.showAddDocument = false;
		(this.form.get("shippingDocuments") as FormArray).push(
			new FormGroup({
				url: new FormControl(this.documentUrl),
			}),
		);
		this.documentUrl = null;
	}
	onEditionSelect(index, name) {
		this.choosedEdition = index;
		console.log(name);
		if (this.editions?.[index]?.extras) {
			this.extras = this.editions?.[index]?.extras;
		} else {
			this.extras = {};
		}

		this.form.patchValue({ editionNumber: name });
		this.form.patchValue({
			weight: this.editions?.[index]?.extras?.weight,
			createdLocation: this.editions?.[index]?.extras?.createdLocation,
			currentLocation: this.editions?.[index]?.extras?.currentLocation,
			consignmentStatus: this.editions?.[index]?.extras?.consignment_status,
			consignmentDate: this.editions?.[index]?.extras?.consignment_date,
			size: this.editions?.[index]?.extras?.size,
			installReady: this.editions?.[index]?.extras?.install_ready,
			currentState: this.editions?.[index]?.extras?.currentState,
			storageAddress: this.editions?.[index]?.extras?.storageAddress,
			packaging: this.editions?.[index]?.extras?.packaging,
			installState: this.editions?.[index]?.extras?.installState,
			objectWeight: this.editions?.[index]?.extras?.objectWeight,
			packedWeight: this.editions?.[index]?.extras?.packedWeight,
			volumetricWeight: this.editions?.[index]?.extras?.volumetricWeight,
			packedHeight: this.editions?.[index]?.extras?.packedHeight,
			packedWidth: this.editions?.[index]?.extras?.packedWidth,
			packedDepth: this.editions?.[index]?.extras?.packedDepth,
			packingNotes: this.editions?.[index]?.extras?.packingNotes,
			generalNotes: this.editions?.[index]?.extras?.generalNotes,
		});
		this.isGallerWearhouse = this.editions?.[index]?.extras?.isGallerWearhouse;
		this.artistAddress = this.editions?.[index]?.extras?.artistAddress;
		this.editions?.[index]?.extras?.shippingDocuments?.forEach((ele, index) => {
			(this.form.get("shippingDocuments") as FormArray).push(
				new FormGroup({
					url: new FormControl(ele.url),
				}),
			);
		});
	}
}
