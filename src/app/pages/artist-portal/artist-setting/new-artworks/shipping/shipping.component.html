<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form *ngIf="form" [formGroup]="form">
          <div *ngIf="artData?.work_type == 'edition'" class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container">
                <input
                  formControlName="editionNumber"
                  type="text"
                  class="selection"
                  placeholder="Choose Edition"
                  readonly="readonly"
                  (focus)="isDropDownOpen[30] = true"
                />
                <div class="placeholder">Choose Edition</div>
                <button
                  (click)="
                    isSaleOptionChangable &&
                      (isDropDownOpen[30] = !isDropDownOpen[30])
                  "
                  type="button"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[30],
                    'dropdown-visible': isDropDownOpen[30]
                  }"
                >
                  <ul>
                    <li
                      *ngFor="let item of editions; let i = index"
                      (click)="
                        isDropDownOpen[30] = false;
                        onEditionSelect(i, item?.edition)
                      "
                    >
                      <div class="country-name">{{ item?.edition }}</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <ng-container
            *ngIf="artData?.work_type != 'edition' || choosedEdition != null"
          >
            <div class="splitter">
              <div
                class="field-value"
                style="padding-right: 0.5vw"
                [hidden]="!showObj?.createdLocation"
                [ngClass]="{ 'Disabled-Color': disableObj.createdLocation }"
              >
                <input
                  formControlName="createdLocation"
                  type="text"
                  placeholder="Created Location"
                  [disabled]="disableObj.createdLocation"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.createdLocation && !isArtReadOnly"
                >
                </fa-icon>
                <div class="placeholder">Created Location</div>
                <div class="input-info">
                  Choose from the drop-down where the artwork is currently
                  located. If the address is not listed in the drop-down, please
                  add in the Address Book section.
                </div>
              </div>

              <div class="field-value" style="padding-right: 0.5vw">
                <div class="input-container" (focusout)="changeFocus(20)">
                  <input
                    formControlName="currentLocation"
                    type="text"
                    class="selection"
                    placeholder="Choose an option"
                    (focus)="isDropDownOpen[20] = true"
                    readonly
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">Current location</div>

                  <button (click)="isDropDownOpen[20] = !isDropDownOpen[20]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div class="dropdown-visible" [hidden]="!isDropDownOpen[20]">
                    <ul>
                      <ng-container
                        *ngIf="
                          userDetails.role == 'SUPERADMIN' ||
                          userDetails.role == 'TERRAIN CURATOR'
                        "
                      >
                        <li
                          style="cursor: pointer"
                          (click)="
                            form
                              .get('currentLocation')
                              .setValue(item?.addressLabel);
                            isDropDownOpen[20] = false;
                            isGallerWearhouse = 1;
                            locationCode = item?.locationCode
                          "
                          *ngFor="let item of storageAddress; let i = index"
                        >
                          <div class="country-name">
                            {{ item?.addressLabel }}
                          </div>
                        </li>
                      </ng-container>

                      <li
                        style="cursor: pointer"
                        (click)="
                          form
                            .get('currentLocation')
                            .setValue('Delivered to Client');
                          isDropDownOpen[20] = false;
                          isGallerWearhouse = 0
                        "
                        *ngIf="
                          userDetails.role == 'SUPERADMIN' ||
                          userDetails.role == 'TERRAIN CURATOR'
                        "
                      >
                        <div class="country-name">Delivered to Client</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          form.get('currentLocation').setValue('On Viewing');
                          isDropDownOpen[20] = false;
                          isGallerWearhouse = 0
                        "
                        *ngIf="
                          userDetails.role == 'SUPERADMIN' ||
                          userDetails.role == 'TERRAIN CURATOR'
                        "
                      >
                        <div class="country-name">On Viewing</div>
                      </li>
                      <li
                        style="cursor: pointer"
                        (click)="
                          form
                            .get('currentLocation')
                            .setValue(item?.title || item?.address_line_1);
                          isDropDownOpen[20] = false;
                          isGallerWearhouse = 2;
                          artistAddress = item
                        "
                        *ngFor="let item of addressData; let i = index"
                      >
                        <div class="country-name">
                          {{ item?.title || item?.address_line_1 }}
                        </div>
                      </li>
                      <li
                        style="cursor: pointer"
                        *ngIf="
                          userDetails.role != 'SUPERADMIN' &&
                          userDetails.role != 'TERRAIN CURATOR' &&
                          addressData?.length < 1
                        "
                      >
                        <a routerLink="/artist-portal/settings/address-book"
                          >+ Add Address</a
                        >
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose from the drop-down where the artwork is currently
                  located. If the address is not listed in the drop-down, please
                  add in the Address Book section.
                </div>
              </div>
              <div
                *ngIf="isGallerWearhouse == 1"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                <div class="input-container" (focusout)="changeFocus(21)">
                  <input
                    formControlName="storageAddress"
                    type="text"
                    class="selection"
                    placeholder="Choose an option"
                    (focus)="isDropDownOpen[21] = true"
                    readonly
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Storage Address</div>
                  <button (click)="isDropDownOpen[21] = !isDropDownOpen[21]">
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div class="dropdown-visible" [hidden]="!isDropDownOpen[21]">
                    <ul>
                      <li
                        style="cursor: pointer"
                        (click)="
                          form.get('storageAddress').setValue(item?.name);
                          isDropDownOpen[21] = false
                        "
                        *ngFor="let item of locationCode; let i = index"
                      >
                        <div class="country-name">
                          {{ item?.name }}
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info"></div>
              </div>
              <div
                *ngIf="isGallerWearhouse == 2"
                class="field-value"
                style="padding-right: 0.5vw"
              >
                Name: {{ artistAddress?.name }}<br />
                {{ artistAddress?.address_line_1 }}<br />
                {{ artistAddress?.address_line_2 }}<br />
                City: {{ artistAddress?.city }}<br />
                State: {{ artistAddress?.state }}<br />
                <div class="input-info"></div>
              </div>
            </div>
            <div class="splitter">
              <div
                class="field-value"
                [hidden]="!showObj?.currentState"
                [ngClass]="{ 'Disabled-Color': disableObj.currentState }"
              >
                <div class="input-container" (focusout)="changeFocus(1)">
                  <input
                    formControlName="currentState"
                    [disabled]="disableObj.currentState"
                    type="text"
                    class="selection"
                    placeholder="Current State"
                    (focus)="
                      disableObj.size ? null : (isDropDownOpen[1] = true)
                    "
                    readonly="readonly"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.currentState && !isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Current State</div>
                  <button
                    (click)="isDropDownOpen[1] = !isDropDownOpen[1]"
                    type="button"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[1],
                      'dropdown-visible': isDropDownOpen[1]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          form.get('currentState').setValue('Unstretched');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Unstretched</div>
                      </li>
                      <li
                        (click)="
                          form.get('currentState').setValue('Stretched');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Stretched</div>
                      </li>
                      <li
                        (click)="
                          form
                            .get('currentState')
                            .setValue('Stretched & Framed');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Stretched & Framed</div>
                      </li>
                      <li
                        (click)="
                          form.get('currentState').setValue('Mounted');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Mounted</div>
                      </li>
                      <li
                        (click)="
                          form.get('currentState').setValue('Framed');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Framed</div>
                      </li>
                      <li
                        (click)="
                          form.get('currentState').setValue('Unframed');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">Unframed</div>
                      </li>

                      <li
                        (click)="
                          form.get('currentState').setValue('To Be Printed');
                          isDropDownOpen[1] = false
                        "
                      >
                        <div class="country-name">To Be Printed</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the current state of the artwork from the list
                </div>
              </div>
              <div class="field-value">
                <div class="input-container" (focusout)="changeFocus(2)">
                  <input
                    formControlName="packaging"
                    type="text"
                    class="selection"
                    placeholder="Packaging"
                    (focus)="isDropDownOpen[2] = true"
                    readonly="readonly"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Packaging</div>
                  <button
                    (click)="isDropDownOpen[2] = !isDropDownOpen[2]"
                    type="button"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[2],
                      'dropdown-visible': isDropDownOpen[2]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          form.get('packaging').setValue('In Tube');
                          isDropDownOpen[2] = false
                        "
                      >
                        <div class="country-name">In Tube</div>
                      </li>
                      <li
                        (click)="
                          form.get('packaging').setValue('Wrapped in Paper');
                          isDropDownOpen[2] = false
                        "
                      >
                        <div class="country-name">Wrapped in Paper</div>
                      </li>
                      <li
                        (click)="
                          form.get('packaging').setValue('Crated');
                          isDropDownOpen[2] = false
                        "
                      >
                        <div class="country-name">Crated</div>
                      </li>
                      <li
                        (click)="
                          form.get('packaging').setValue('Soft/Flat Packed');
                          isDropDownOpen[2] = false
                        "
                      >
                        <div class="country-name">Soft/Flat Packed</div>
                      </li>
                      <li
                        (click)="
                          form.get('packaging').setValue('Not Packed');
                          isDropDownOpen[2] = false
                        "
                      >
                        <div class="country-name">Not Packed</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the current packed state of the artwork from the list
                </div>
              </div>
              <div class="field-value">
                <div class="input-container" (focusout)="changeFocus(3)">
                  <input
                    formControlName="installState"
                    type="text"
                    class="selection"
                    placeholder="Install State"
                    (focus)="isDropDownOpen[3] = true"
                    readonly="readonly"
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!isArtReadOnly"
                  >
                  </fa-icon>

                  <div class="placeholder">Install State</div>
                  <button
                    (click)="isDropDownOpen[3] = !isDropDownOpen[3]"
                    type="button"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[3],
                      'dropdown-visible': isDropDownOpen[3]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          form.get('installState').setValue('Ready');
                          isDropDownOpen[3] = false
                        "
                      >
                        <div class="country-name">Ready</div>
                      </li>
                      <li
                        (click)="
                          form.get('installState').setValue('Not Ready');
                          isDropDownOpen[3] = false
                        "
                      >
                        <div class="country-name">Not Ready</div>
                      </li>
                      <li
                        (click)="
                          form
                            .get('installState')
                            .setValue('Assembly Required');
                          isDropDownOpen[3] = false
                        "
                      >
                        <div class="country-name">Assembly Required</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the current packed state of the artwork from the list
                </div>
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="objectWeight"
                  type="number"
                  placeholder="Object Weight"
                />

                <div class="placeholder">Object Weight</div>
                <div class="input-info">
                  Provide weight (in kg) of the unpacked artwork
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="packedWeight"
                  type="number"
                  placeholder="Packed Weight"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Packed Weight</div>
                <div class="input-info">
                  Provide the weight (in kg) of the packed artwork
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="volumetricWeight"
                  type="number"
                  placeholder="Volumetric Weight"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Volumetric Weight</div>
                <div class="input-info">
                  Calculated volumetric weight (in kg) of packed work (w x h x
                  d) / 5000
                </div>
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="packedHeight"
                  type="number"
                  placeholder="Packed Height"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Packed Height</div>
                <div class="input-info">
                  Packed height of the work in centimetres
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="packedWidth"
                  type="number"
                  placeholder="Packed Width"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>

                <div class="placeholder">Packed Width</div>
                <div class="input-info">
                  Packed width of the work in centimetres
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  formControlName="packedDepth"
                  type="number"
                  placeholder="Packed Depth"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>
                <div class="placeholder">Packed Depth</div>
                <div class="input-info">
                  Packed depth of the work in centimetres
                </div>
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <div>
                  <angular-editor
                    *ngIf="htmlcheckBox[0]"
                    id="editor20"
                    formControlName="packingNotes"
                  ></angular-editor>
                  <textarea
                    *ngIf="!htmlcheckBox[0]"
                    [placeholder]="'Packing Notes'"
                    formControlName="packingNotes"
                    rows="6"
                    style="padding: 1vw; width: 100%"
                  ></textarea>
                  <div class="placeholder">
                    Packing Notes
                    <fa-icon
                      class="Lock-position-1"
                      [icon]="faLock"
                      title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                      [hidden]="!isArtReadOnly"
                    >
                    </fa-icon>
                  </div>

                  <div class="input-info">
                    Provide complete details (for ex. if works are packed in
                    separate packages or crates).
                    <div
                      style="
                        display: inline-block;
                        text-align: right;
                        width: 100%;
                      "
                    >
                      <input
                        type="checkbox"
                        [(ngModel)]="htmlcheckBox[0]"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      HTML Editor
                    </div>
                  </div>
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <div>
                  <angular-editor
                    *ngIf="htmlcheckBox[1]"
                    id="editor20"
                    formControlName="generalNotes"
                  ></angular-editor>
                  <textarea
                    *ngIf="!htmlcheckBox[1]"
                    [placeholder]="'General Notes'"
                    formControlName="generalNotes"
                    rows="6"
                    style="padding: 1vw; width: 100%"
                  ></textarea>
                  <div class="placeholder">
                    General Notes
                    <fa-icon
                      class="Lock-position-1"
                      [icon]="faLock"
                      title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                      [hidden]="!isArtReadOnly"
                    >
                    </fa-icon>
                  </div>
                  <div class="input-info">
                    Provide general notes regarding installation or assembly of
                    the works.
                    <div
                      style="
                        display: inline-block;
                        text-align: right;
                        width: 100%;
                      "
                    >
                      <input
                        type="checkbox"
                        [(ngModel)]="htmlcheckBox[1]"
                        [ngModelOptions]="{ standalone: true }"
                      />
                      HTML Editor
                    </div>
                  </div>
                </div>
              </div>
              <div class="field-value" style="padding-right: 0.5vw">
                <div style="padding-bottom: 0.5vw">Assembly/Install manual</div>
                <div class="add-document" style="padding-bottom: 2vw">
                  <br />
                  <div
                    *ngFor="let item of form.get('shippingDocuments').value"
                    style="display: inline-block"
                  >
                    <a [href]="item.url" target="_blank" style="color: #004ddd">
                      ...{{ item?.url?.slice(-10) }}</a
                    ><span style="margin-right: 0.5vw"> ;</span>
                  </div>
                  <br />

                  <span (click)="showAddDocument = true"
                    ><img
                      class="plus-icon"
                      style="margin-right: 0.5vw"
                      src="assets/images/load-more.png"
                  /></span>
                  <span (click)="showAddDocument = true" class="sub-text"
                    >Add documents</span
                  >
                  <span
                    *ngIf="form.get('shippingDocuments').value?.length > 0"
                    class="sub-text"
                    style="margin-left: 1vw"
                    (click)="manageDocPop = true"
                    >Manage documents</span
                  >
                </div>
              </div>
            </div>
          </ng-container>
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" *ngIf="showAddDocument">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Add Document</p>
    <div class="container__main" style="padding-bottom: 0vw">
      <div class="profile-field">
        <div class="field-contents">
          <div class="field-value">
            <p>Document</p>
            <image-upload
              [accept]="'all'"
              [fileSize]="2147483648"
              [hideAltText]="true"
              (onFileChange)="onDocumentFileSelect($event)"
            >
            </image-upload>
          </div>
        </div>
      </div>
    </div>

    <div
      class="Edition-list"
      style="max-height: 16vw; overflow-y: scroll; margin-top: 1vw"
    ></div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="showAddDocument = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="documentUrl && addDocument()"
      >
        Add
      </button>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!manageDocPop">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Manage Documents</p>

    <div
      *ngFor="let item of form.get('shippingDocuments').value; let i = index"
    >
      <a
        [href]="item.url"
        target="_blank"
        style="margin-right: 0.5vw; color: #004ddd"
      >
        ...{{ item?.url?.slice(-10) }}
      </a>
      <fa-icon (click)="deleteDocumentEntry(i)" [icon]="faTrash"> </fa-icon>
    </div>

    <div
      class="buttonList"
      style="
        margin-top: 1.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="manageDocPop = false"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
