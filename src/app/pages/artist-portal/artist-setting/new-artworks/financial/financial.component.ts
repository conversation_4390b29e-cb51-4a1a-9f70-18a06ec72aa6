import { EditNotifyService } from "./../edit-notify.service";
import {
	Component,
	ElementRef,
	NgModule,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	FormArray,
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { faLock, faTrash } from "@fortawesome/free-solid-svg-icons";
import { ActivatedRoute, Router } from "@angular/router";
import { DragulaService } from "ng2-dragula";
import { flagData } from "src/app/pages/collector/profile/personal/flags";
import { ArtistInfoService } from "src/app/services/artist-info.service";
import { CollectorService } from "src/app/services/collector.service";
import { apiUrl } from "src/environments/environment.prod";
import { ArtistService } from "../../../services/artist.service";
import { ArtworkService } from "../../../services/artwork.service";
@Component({
	selector: "app-artworks-financial",
	templateUrl: "./financial.component.html",
	styleUrls: ["./financial.component.scss"],
})
export class FinancialArtworksComponent implements OnInit {
	movetoSaleHisPop = false;
	updateAllInput = false;
	manageDocPop = false;
	tableColumns = [
		{ name: "Artist Price", type: "number", control: "artist_price" },
		{ name: "Sale Price", type: "number", control: "sale_price" },
		{ name: "Pricing Date", type: "date", control: "pricing_date" },
	];
	addOnly = false;
	faTrash = faTrash;
	priceHistoryErrors = [];
	saleHistoryColumns = [
		{
			name: "Sale Options",
			type: "select",
			control: "sale_options",
			readOnly: true,
			data: [
				{
					label: "For Sale",
					value: "ForSale",
				},
				{
					label: "Not For Sale",
					value: "NotForSale",
					expanded: [
						{
							name: "Not For Sale Date",
							type: "read",
							control: "notForSaleDate",
						},
						{
							name: "Not For Sale Reason",
							type: "read",
							control: "notForSaleReason",
						},
					],
				},

				{
					label: "Sold",
					value: "Sold",
					expanded: [
						{ name: "Sold To", type: "read", control: "soldTo" },
						{ name: "Sold Date", type: "read", control: "soldDate" },
						{
							name: "Billing Address",
							type: "read",
							control: "billingAddress",
						},
						{
							name: "Shipping Address",
							type: "read",
							control: "shippingAddress",
						},

						{ name: "Sold By", type: "read", control: "soldBy" },
						{ name: "Discount", type: "read", control: "discount" },
						{ name: "Tax Percentage", type: "read", control: "taxPercentage" },
						{
							name: "Final Sale Price",
							type: "read",
							control: "finalSalePrice",
						},
						{ name: "PI #", type: "read", control: "pi" },
						{
							name: "Amount Received",
							type: "read",
							control: "amountReceived",
						},
						{ name: "Payment Date", type: "read", control: "paymentDate" },
						{ name: "Tax Invoice #", type: "read", control: "taxInvoice" },
						{
							name: "Artist Paid Date",
							type: "read",
							control: "artistPaidDate",
						},
						{
							name: "Documents",
							type: "array",
							control: "documents",
						},
					],
				},
				{
					label: "Reserved",
					value: "Reserved",
					expanded: [
						{ name: "Reserved Date", type: "read", control: "reservedDate" },
						{ name: "Reserved For", type: "read", control: "reservedFor" },
						{ name: "Reserved By", type: "read", control: "reservedBy" },
					],
				},
				{
					label: "Returned To Artist",
					value: "ReturnedToArtist",
					expanded: [
						{ name: "Returned Date", type: "read", control: "returnedDate" },
						{
							name: "Reason for returning",
							type: "read",
							control: "returnedToArtistReason",
						},
					],
				},
				{
					label: "On Loan",
					value: "OnLoan",
					expanded: [
						{ name: "Start Date", type: "read", control: "startDate" },
						{ name: "End Date", type: "read", control: "endDate" },
						{ name: "Loan Details", type: "read", control: "loanDetails" },
					],
				},
			],
		},
		{ name: "Entry Date", type: "read", control: "createdAt" },
		{ name: "Entered by", type: "read", control: "created" },
	];
	saleHistoryExpandedColumns = [];
	saleDetailsErrors = [];
	faLock = faLock;
	dropDownValue = Array(10).fill(null);
	htmlContent = Array(10).fill(null);
	isSubmited = false;
	isDropDownOpen = Array(10).fill(false);
	priceHistoryObj: any = {
		artist_price: "",
		sale_price: "",
		pricing_date: "",
	};

	selectedFiles: File[] = [];

	soldByUser = [];
	soldByLimit = 20;
	soldByOffset = 1;
	soldBySearch = "";

	// repeaters = [
	// 	{
	// 		isOpen: true,
	// 		Artist_Price: null,
	// 		Sale_Price: null,
	// 		Discount: null,
	// 		Pricing_Date: null,
	// 	},
	// ];
	// repeaters2 = [
	// 	{
	// 		isOpen: true,
	// 		Final_Sale_Price: null,
	// 		Discount: null,
	// 		Taxable: null,
	// 		Tax_Percent: null,
	// 	},
	// ];
	form: FormGroup;
	id;
	saleDetailsObj = {
		final_sale_price: "",
		discount: "",
		taxable: "",
		tax_percent: "",
	};
	permissionsObj: any = {};
	showObj: any = {};
	Menuinit: any;
	disableObj: any = {};
	userDetails;

	listCurrentPage;
	listTotalPage;
	showAddDocument = false;
	showAddDocumenttype = false;
	documentValue = "";
	documentUrl = null;
	inputErros = Array(4).fill(false);

	isArtist = false;
	isSaleOptionChangable = false;

	createdAt;
	updatedAt;

	isArtReadOnly = false;

	artData;

	editions;

	choosedEdition = null;
	extras: any;
	extras2: any;

	isSaleOptionSelection = false;
	selectedSaleOption;

	constructor(
		private router: Router,
		private route: ActivatedRoute,
		private formBuilder: FormBuilder,
		private artworkService: ArtworkService,
		private artistInfoService: ArtistInfoService,
		private server: CollectorService,
		private editNotifyService: EditNotifyService,
	) {
		// dragulaService.createGroup("task-group", {
		// 	removeOnSpill: true
		//   });
	}

	ngOnInit(): void {
		this.listCurrentPage = Number(localStorage.getItem("artworkCMSoffset"));
		this.listTotalPage = Number(localStorage.getItem("artworkCMStotalPage"));
		if (localStorage.getItem("artworkID")) {
			this.getArtWork();
		}

		let decode = decodeURIComponent(
			escape(window.atob(localStorage.getItem("userDetails"))),
		);
		this.userDetails = JSON.parse(decode);
		if (
			this.userDetails?.role === "SUPERADMIN" ||
			this.userDetails?.role === "ARCHIVIST" ||
			this.userDetails?.role === "TERRAIN CURATOR" ||
			this.userDetails?.role === "TERRAIN SALES"
		) {
		} else {
		}
		if (
			this.userDetails?.role === "GALLERY ARTIST" ||
			this.userDetails?.role === "REPRESENTED ARTIST" ||
			this.userDetails?.role === "OPEN CREATOR" ||
			this.userDetails?.role === "TERRAIN CONSIGNED"
		) {
			this.addOnly = true;
			this.tableColumns = [
				{ name: "Artist Price", type: "number", control: "artist_price" },
				{ name: "Pricing Date", type: "date", control: "pricing_date" },
			];
			this.isArtist = true;
			this.saleHistoryColumns = [
				{
					name: "Sale Options",
					type: "select",
					control: "sale_options",
					readOnly: true,
					data: [
						{
							label: "For Sale",
							value: "ForSale",
						},
						{
							label: "Not For Sale",
							value: "NotForSale",
						},

						{
							label: "Sold",
							value: "Sold",
						},
						{
							label: "Reserved",
							value: "Reserved",
						},
						{
							label: "Returned To Artist",
							value: "ReturnedToArtist",
						},
						{
							label: "On Loan",
							value: "OnLoan",
						},
					],
				},
				{ name: "Entry Date", type: "read", control: "createdAt" },
			] as any;
		}
		this.permissionsObj = JSON.parse(decode)
			["role_id"]["permissions"].find((x) => x.name == "Artworks")
			.tabsArr.find((x) => x.name == "Financial");
		this.form = this.formBuilder.group({
			catalogNumber: new FormControl(""),
			priceHistory: this.formBuilder.array([]),
			saleOption: new FormControl(""),
			saleInfo: new FormControl(""),
			saleDetails: this.formBuilder.array([]),
			reservedDate: new FormControl(""),
			reservedBy: new FormControl(""),
			notForSaleDate: new FormControl(""),
			soldTo: new FormControl(""),
			soldDate: new FormControl(""),
			returnedDate: new FormControl(""),
			startDate: new FormControl(""),
			endDate: new FormControl(""),
			loanDetails: new FormControl(""),
			returnedToArtistReason: new FormControl(""),
			notForSaleReason: new FormControl(""),
			reservedFor: new FormControl(""),
			billingAddress: new FormControl(""),
			shippingAddress: new FormControl(""),
			soldBy: new FormControl(""),
			discount: new FormControl(""),
			taxPercentage: new FormControl(""),
			finalSalePrice: new FormControl(""),
			pi: new FormControl(""),
			amountReceived: new FormControl(""),
			paymentDate: new FormControl(""),
			taxInvoice: new FormControl(""),
			artistInvoice: new FormControl(""),
			artistPaidDate: new FormControl(""),
			documents: this.formBuilder.array([]),
			consignmentStartDate: new FormControl(""),
			consignmentEndDate: new FormControl(""),
			editionNumber: new FormControl(""),
		});
		console.log(this.permissionsObj.fields);
		this.permissionsObj.fields.forEach((ele) => {
			this.showObj[ele.name] = ele.show;
			this.disableObj[ele.name] = ele.disabled;
			if (ele.parent) {
				ele.child.forEach((ele_child) => {
					this.priceHistoryObj[ele_child.name] = ele_child.defaultValue;
					this.showObj[ele_child.name] = ele_child.show;
				});
			}
			if (ele.formControl && ele.show) {
				if (ele.mandatory) {
					this.form.controls[ele.name].setValidators([Validators.required]);
				}
				ele["disabled"] =
					ele.disabled == "true" || ele.disabled == true ? true : false;
				if (ele.disabled) {
					this.form.controls[ele.name].disable();
				}
				let obj = {};
				obj[ele.name] = ele.defaultValue;
				// console.log(obj)
				// to patch default value
				if (!localStorage.getItem("artworkID")) {
					this.form.patchValue(obj);
					this.checkSaleOptions();
				}
			}
		});

		// this.route.parent.paramMap.subscribe((params) => {
		// 	if (params.get('id')) {
		// 		this.id = params.get('id');
		// 		this.artistInfoService.getFinancialData(this.id).subscribe((data) => {
		// 			const art = data;
		// 			art.priceHistory.forEach((a, i) => {
		// 				if (i !== 0) {
		// 					this.addRepeater();
		// 				}
		// 			});
		// 			art.saleDetails.forEach((a, i) => {
		// 				if (i !== 0) {
		// 					this.addRepeater2();
		// 				}
		// 			});
		// 			this.form.patchValue(art);
		// 		});
		// 	} else {
		// 		this.id = null;
		// 	}
		// });
		this.form.valueChanges.subscribe((x) => {
			if (this.Menuinit == null) {
				this.Menuinit = x;
			} else {
				this.editNotifyService.setOption("Financial", true);
			}
		});
		console.log(this.disableObj);
		this.checkSaleOptions();
		this.getTerrainSaleUsers();
	}

	// to check and add required as per sale options conditions
	checkSaleOptions() {
		if (
			this.form.value.saleOption == "Sold" ||
			this.form.value.saleOption == "Reserved"
		) {
			this.form.controls["saleInfo"].setValidators([Validators.required]);
			this.form.controls["soldDate"].setValidators([Validators.required]);
			console.log("added");
		} else {
			this.form.controls["saleInfo"].setValidators([]);
			this.form.controls["soldDate"].setValidators([]);
			console.log("removed");
		}
		this.form.controls["saleInfo"].updateValueAndValidity();
		this.form.controls["soldDate"].updateValueAndValidity();
	}
	checkSaleOptions2() {
		this.form.patchValue({
			reservedDate: null,
			reservedBy: null,
			notForSaleDate: null,
			soldTo: null,
			soldDate: null,
			returnedDate: null,
			startDate: null,
			endDate: null,
			loanDetails: null,
			returnedToArtistReason: null,
			notForSaleReason: null,
			reservedFor: null,
			billingAddress: null,
			shippingAddress: null,
			soldBy: null,
			discount: null,
			taxPercentage: null,
			finalSalePrice: null,
			pi: null,
			amountReceived: null,
			paymentDate: null,
			taxInvoice: null,
			artistInvoice: null,
			artistPaidDate: null,
		});
		(this.form.get("documents") as FormArray).clear();
		this.updateAllInput = false;
	}

	// to get artwork
	getArtWork() {
		this.form = this.formBuilder.group({
			catalogNumber: new FormControl(""),
			priceHistory: this.formBuilder.array([]),
			saleOption: new FormControl(""),
			saleInfo: new FormControl(""),
			saleDetails: this.formBuilder.array([]),
			reservedDate: new FormControl(""),
			reservedBy: new FormControl(""),
			notForSaleDate: new FormControl(""),
			soldTo: new FormControl(""),
			soldDate: new FormControl(""),
			returnedDate: new FormControl(""),
			startDate: new FormControl(""),
			endDate: new FormControl(""),
			loanDetails: new FormControl(""),
			returnedToArtistReason: new FormControl(""),
			notForSaleReason: new FormControl(""),
			reservedFor: new FormControl(""),
			billingAddress: new FormControl(""),
			shippingAddress: new FormControl(""),
			soldBy: new FormControl(""),
			discount: new FormControl(""),
			taxPercentage: new FormControl(""),
			finalSalePrice: new FormControl(""),
			pi: new FormControl(""),
			amountReceived: new FormControl(""),
			paymentDate: new FormControl(""),
			taxInvoice: new FormControl(""),
			artistInvoice: new FormControl(""),
			artistPaidDate: new FormControl(""),
			documents: this.formBuilder.array([]),
			editionNumber: new FormControl(""),
		});
		let url = apiUrl.getArtwork + `/${localStorage.getItem("artworkID")}`;
		this.server.showSpinner();
		this.server.getApi(url).subscribe((res) => {
			this.server.hideSpinner();
			if (res.statusCode == 200) {
				this.artData = res.data;
				this.editions = this.artData?.editionDetails;
				this.extras = res.data.extras;
				this.createdAt = new Date(res.data?.createdAt).toDateString();
				this.updatedAt = new Date(res.data?.updatedAt).toDateString();
				this.form.patchValue({
					catalogNumber: res.data.catalog_number,
					saleOption: res.data.sale_options,
					saleInfo: res.data.sale_information,
					reservedDate: res.data.extras?.reservedDate,
					reservedBy: res.data.extras?.reservedBy,
					notForSaleDate: res.data.extras?.notForSaleDate,
					soldTo: res.data.extras?.soldTo,
					soldDate: res.data.extras?.soldDate,
					returnedDate: res.data.extras?.returnedDate,
					startDate: res.data.extras?.startDate,
					endDate: res.data.extras?.endDate,
					loanDetails: res.data.extras?.loanDetails,
					returnedToArtistReason: res.data.extras?.returnedToArtistReason,
					notForSaleReason: res.data.extras?.notForSaleReason,
					reservedFor: res.data.extras?.reservedFor,
					billingAddress: res.data.extras?.billingAddress,
					shippingAddress: res.data.extras?.shippingAddress,
					soldBy: res.data.extras?.soldBy,
					discount: res.data.extras?.discount,
					taxPercentage: res.data.extras?.taxPercentage,
					finalSalePrice: res.data.extras?.finalSalePrice,
					pi: res.data.extras?.pi,
					amountReceived: res.data.extras?.amountReceived,
					paymentDate: res.data.extras?.paymentDate,
					taxInvoice: res.data.extras?.taxInvoice,
					artistInvoice: res.data.extras?.artistInvoice,
					artistPaidDate: res.data.extras?.artistPaidDate,
					consignmentStartDate: res.data.extras?.consignmentStartDate,
					consignmentEndDate: res.data.extras?.consignmentEndDate,
					// priceHistory: res.data.price_history,
					// saleDetails: res.data.sale_details,
				});
				if (
					res.data.publish_artworks &&
					(this.userDetails.role == "REPRESENTED ARTIST" ||
						this.userDetails.role == "TERRAIN CONSIGNED")
				) {
					this.isArtReadOnly = true;
				}
				if (!this.form.getRawValue().catalog_number) {
					// this.form.patchValue({ catalogNumber: 'RAPL-' + res.data.artist_id?.catalogue_code + '-001' })
				}
				if (!this.form.getRawValue().consignmentStartDate) {
					this.form.patchValue({
						consignmentStartDate:
							res.data.artist_id?.extras?.consignmentStartDate,
					});
				}
				if (!this.form.getRawValue().consignmentEndDate) {
					this.form.patchValue({
						consignmentEndDate: res.data.artist_id?.extras?.consignmentEndDate,
					});
				}
				res.data.extras?.documents?.forEach((ele, index) => {
					(this.form.get("documents") as FormArray).push(
						new FormGroup({
							type: new FormControl(ele.type),
							url: new FormControl(ele.url),
						}),
					);
				});
				res.data.price_history.forEach((ele, index) => {
					console.log("hey");

					this.addRepeater();
					this.form["controls"].priceHistory["controls"][index]
						.get("artist_price")
						?.setValue(ele.artist_price);
					this.form["controls"].priceHistory["controls"][index]
						.get("sale_price")
						?.setValue(ele.sale_price);
					this.form["controls"].priceHistory["controls"][index]
						.get("pricing_date")
						?.setValue(ele.pricing_date);
				});
				res.data.saleHistory.forEach((ele, index) => {
					this.addRepeater2();
					this.form["controls"].saleDetails["controls"][index]
						.get("sale_options")
						.setValue(ele.sale_options);
					this.form["controls"].saleDetails["controls"][index]
						.get("created")
						.setValue(ele.created.fullName);
					this.form["controls"].saleDetails["controls"][index]
						.get("createdAt")
						.setValue(ele.createdAt);
					this.form["controls"].saleDetails["controls"][index]
						.get("contents")
						.setValue(ele.contents);
				});
				this.isSaleOptionChangable =
					// this.form.get('saleDetails').value?.length > 0 &&
					this.form.get("saleOption").value
						? this.form.get("saleDetails").value?.at(-1)?.sale_options ===
							this.form.get("saleOption").value
						: true;
			}
		});
	}

	async onFileSelect(files: FileList) {
		if (files[0].size < 2100000) {
			this.selectedFiles.push(files[0]);
		}
	}
	removeItem(index) {
		this.selectedFiles.splice(index, 1);
	}

	get priceHistory(): FormArray {
		return <FormArray>this.form.get("priceHistory");
	}

	get saleDetails(): FormArray {
		return <FormArray>this.form.get("saleDetails");
	}
	// this.priceHistoryObj.pricing_date.split('-').reverse().join('/')
	addRepeater() {
		let obj = {};

		// if (this.showObj.artist_price) {
		obj["artist_price"] = new FormControl(this.priceHistoryObj.artist_price);
		//}
		//if (this.showObj.sale_price) {
		obj["sale_price"] = new FormControl(this.priceHistoryObj.sale_price);
		//}
		//if (this.showObj.pricing_date) {
		obj["pricing_date"] = new FormControl(this.priceHistoryObj.pricing_date);
		// }
		this.priceHistory.push(this.formBuilder.group(obj));

		this.priceHistoryObj = {
			artist_price: "",
			sale_price: "",
			pricing_date: "",
		};

		// this.priceHistoryObj={
		// 	artist_price: '',
		// 	sale_price: '',
		// 	pricing_date:''
		// }
	}
	addRepeater2() {
		this.saleDetails.push(
			this.formBuilder.group({
				// isOpen: new FormControl(true),
				sale_options: new FormControl(""),
				created: new FormControl(""),
				createdAt: new FormControl(""),
				contents: new FormControl(""),
			}),
		);
		// this.saleDetailsObj={
		// 	final_sale_price: '',
		// 	discount: '',
		// 	taxable:'',
		// 	tax_percent:''
		// }
	}
	onSubmit() {
		// console.log(this.form);
		// this.priceHistoryErrors = Array(
		//   this.form.getRawValue().priceHistory.length
		// ).fill(null);
		// let isPriceError = false;
		// this.form.getRawValue().priceHistory.forEach((element, i) => {
		//   if (
		//     !element.artist_price &&
		//     !element.sale_price &&
		//     !element.pricing_date
		//   ) {
		//     this.priceHistoryErrors[i] = {};
		//     this.priceHistoryErrors[i]['artist_price'] = { showError: true };
		//     this.priceHistoryErrors[i]['sale_price'] = { showError: true };
		//     this.priceHistoryErrors[i]['pricing_date'] = { showError: true };
		//     isPriceError = true;
		//   }
		// });

		// this.saleDetailsErrors = Array(
		//   this.form.getRawValue().priceHistory.length
		// ).fill(null);
		// let isSaleDetailsError = false;
		// this.form.getRawValue().saleDetails.forEach((element, i) => {
		//   if (
		//     !element.finalSalePrice &&
		//     !element.discount &&
		//     !element.taxable &&
		//     !element.taxPercent
		//   ) {
		//     this.saleDetailsErrors[i] = {};
		//     this.saleDetailsErrors[i]['finalSalePrice'] = { showError: true };
		//     this.saleDetailsErrors[i]['discount'] = { showError: true };
		//     this.saleDetailsErrors[i]['taxable'] = { showError: true };
		//     this.saleDetailsErrors[i]['taxPercent'] = { showError: true };
		//     isSaleDetailsError = true;
		//   }
		// });

		// if (isPriceError) {
		//   alert('Form not valid.Please fill price history fields correctly !');
		//   return;
		// }
		// if (isSaleDetailsError) {
		//   alert('Form not valid.Please fill sale details fields correctly !');
		//   return;
		// }
		// if (this.form.invalid) {
		//   alert('Form not valid.Please fill required fields correctly !');
		//   return;
		// }
		// if (
		//   this.form.value.priceHistory.length == 0 &&
		//   this.permissionsObj.fields.find((x) => x.name == 'priceHistory').mandatory
		// ) {
		//   alert('Please add price history !');
		//   return;
		// }
		// if (
		//   this.form.value.saleDetails.length == 0 &&
		//   this.permissionsObj.fields.find((x) => x.name == 'saleDetails').mandatory
		// ) {
		//   alert('Please add sale details !');
		//   return;
		// }
		// let obj = {};

		// if (this.showObj.artist_price) {
		//   obj['artist_price'] = new FormControl(this.priceHistoryObj.artist_price);
		// }
		// if (this.showObj.sale_price) {
		//   obj['sale_price'] = new FormControl(this.priceHistoryObj.sale_price);
		// }
		// if (this.showObj.pricing_date) {
		//   obj['pricing_date'] = new FormControl(this.priceHistoryObj.pricing_date);
		// }
		// if (
		//   this.priceHistoryObj.artist_price != '' ||
		//   this.priceHistoryObj.sale_price != '' ||
		//   this.priceHistoryObj.pricing_date != ''
		// ) {
		//   this.priceHistory.push(this.formBuilder.group(obj));
		// }

		if (
			this.form.getRawValue().discount &&
			(!this.isValidNumber(this.form.getRawValue().discount) ||
				Number(this.form.getRawValue().discount > 100) ||
				Number(this.form.getRawValue().discount < 0))
		) {
			alert("Please enter valid discount value!");
			return;
		}
		if (
			this.form.getRawValue().taxPercentage &&
			(!this.isValidNumber(this.form.getRawValue().taxPercentage) ||
				Number(this.form.getRawValue().taxPercentage > 100) ||
				Number(this.form.getRawValue().taxPercentage < 0))
		) {
			alert("Please enter valid Tax Percentage value!");
			return;
		}
		if (
			this.form.getRawValue().finalSalePrice &&
			!this.isValidNumber(this.form.getRawValue().finalSalePrice)
		) {
			alert("Please enter valid Final Sale Price value!");
			return;
		}
		if (
			this.form.getRawValue().amountReceived &&
			!this.isValidNumber(this.form.getRawValue().amountReceived)
		) {
			alert("Please enter valid Amount Received value!");
			return;
		}
		if (!this.form.getRawValue().saleOption) {
			alert(
				"You need to provide the current Sale Options before saving the artwork",
			);
			return;
		}

		let availableEditions = this.artData?.editionDetails?.filter((a) => {
			return a.sale_options == "ForSale";
		});

		if (
			availableEditions.length == 1 &&
			this.artData?.editionDetails[this.choosedEdition].edition ==
				availableEditions[0].edition &&
			this.form.getRawValue().saleOption.replace(/ /g, "") != "ForSale" &&
			!this.selectedSaleOption
		) {
			this.isSaleOptionSelection = true;
			this.selectedSaleOption = "NotForSale";
			return;
		} else if (
			availableEditions.length < 1 &&
			this.form.getRawValue().saleOption.replace(/ /g, "") !== "ForSale" &&
			!this.selectedSaleOption
		) {
			this.isSaleOptionSelection = true;
			this.selectedSaleOption = "NotForSale";
			return;
		}
		this.priceHistoryObj = {
			artist_price: "",
			sale_price: "",
			pricing_date: "",
		};

		let priceHistory = this.form.getRawValue().priceHistory.sort((a, b) => {
			return (
				new Date(a.pricing_date).getTime() - new Date(b.pricing_date).getTime()
			);
		});
		priceHistory = priceHistory.map((a) => {
			if (!a?.pricing_date) {
				let todayDate = new Date();
				a.pricing_date = todayDate.toISOString().split("T")[0];
			}
			return a;
		});

		if (this.userDetails.role == "OPEN CREATOR") {
			priceHistory = priceHistory.map((a) => {
				return {
					artist_price: a?.artist_price,
					sale_price: a?.artist_price,
					pricing_date: a?.pricing_date,
				};
			});
		}

		if (!this.extras) {
			this.extras = {};
		}
		if (!this.extras2) {
			this.extras2 = {};
		}
		this.extras["reservedDate"] = this.form.getRawValue().reservedDate;
		this.extras["reservedBy"] = this.form.getRawValue().reservedBy;
		this.extras["notForSaleDate"] = this.form.getRawValue().notForSaleDate;
		this.extras["soldTo"] = this.form.getRawValue().soldTo;
		this.extras["soldDate"] = this.form.getRawValue().soldDate;
		this.extras["returnedDate"] = this.form.getRawValue().returnedDate;
		this.extras["startDate"] = this.form.getRawValue().startDate;
		this.extras["endDate"] = this.form.getRawValue().endDate;
		this.extras["loanDetails"] = this.form.getRawValue().loanDetails;
		this.extras["returnedToArtistReason"] =
			this.form.getRawValue().returnedToArtistReason;
		this.extras["notForSaleReason"] = this.form.getRawValue().notForSaleReason;
		this.extras["reservedFor"] = this.form.getRawValue().reservedFor;
		this.extras["billingAddress"] = this.form.getRawValue().billingAddress;
		this.extras["shippingAddress"] = this.form.getRawValue().shippingAddress;
		this.extras["soldBy"] = this.form.getRawValue().soldBy;
		this.extras["discount"] = this.form.getRawValue().discount;
		this.extras["taxPercentage"] = this.form.getRawValue().taxPercentage;
		this.extras["finalSalePrice"] = this.form.getRawValue().finalSalePrice;
		this.extras["pi"] = this.form.getRawValue().pi;
		this.extras["amountReceived"] = this.form.getRawValue().amountReceived;
		this.extras["paymentDate"] = this.form.getRawValue().paymentDate;
		this.extras["taxInvoice"] = this.form.getRawValue().taxInvoice;
		this.extras["artistInvoice"] = this.form.getRawValue().artistInvoice;
		this.extras["artistPaidDate"] = this.form.getRawValue().artistPaidDate;
		this.extras["documents"] = this.form.getRawValue().documents;
		this.extras["consignmentStartDate"] =
			this.form.getRawValue().consignmentStartDate;
		this.extras["consignmentEndDate"] =
			this.form.getRawValue().consignmentEndDate;

		this.extras2["reservedDate"] = this.form.getRawValue().reservedDate;
		this.extras2["reservedBy"] = this.form.getRawValue().reservedBy;
		this.extras2["notForSaleDate"] = this.form.getRawValue().notForSaleDate;
		this.extras2["soldTo"] = this.form.getRawValue().soldTo;
		this.extras2["soldDate"] = this.form.getRawValue().soldDate;
		this.extras2["returnedDate"] = this.form.getRawValue().returnedDate;
		this.extras2["startDate"] = this.form.getRawValue().startDate;
		this.extras2["endDate"] = this.form.getRawValue().endDate;
		this.extras2["loanDetails"] = this.form.getRawValue().loanDetails;
		this.extras2["returnedToArtistReason"] =
			this.form.getRawValue().returnedToArtistReason;
		this.extras2["notForSaleReason"] = this.form.getRawValue().notForSaleReason;
		this.extras2["reservedFor"] = this.form.getRawValue().reservedFor;
		this.extras2["billingAddress"] = this.form.getRawValue().billingAddress;
		this.extras2["shippingAddress"] = this.form.getRawValue().shippingAddress;
		this.extras2["soldBy"] = this.form.getRawValue().soldBy;
		this.extras2["discount"] = this.form.getRawValue().discount;
		this.extras2["taxPercentage"] = this.form.getRawValue().taxPercentage;
		this.extras2["finalSalePrice"] = this.form.getRawValue().finalSalePrice;
		this.extras2["pi"] = this.form.getRawValue().pi;
		this.extras2["amountReceived"] = this.form.getRawValue().amountReceived;
		this.extras2["paymentDate"] = this.form.getRawValue().paymentDate;
		this.extras2["taxInvoice"] = this.form.getRawValue().taxInvoice;
		this.extras2["artistInvoice"] = this.form.getRawValue().artistInvoice;
		this.extras2["artistPaidDate"] = this.form.getRawValue().artistPaidDate;
		this.extras2["documents"] = this.form.getRawValue().documents;
		this.extras2["consignmentStartDate"] =
			this.form.getRawValue().consignmentStartDate;
		this.extras2["consignmentEndDate"] =
			this.form.getRawValue().consignmentEndDate;
		let url = apiUrl.addArtwork;
		let req = {
			catalog_number: this.form.getRawValue().catalogNumber,
			price_history: priceHistory,
			sale_options: this.form.getRawValue().saleOption.replace(/ /g, ""),
			sale_details: this.form.getRawValue().saleDetails,
			sold_date: this.form.getRawValue().soldDate,
			sale_information: this.form.getRawValue().saleInfo,
			extras: this.extras,
			tab: "Financial",
		};

		if (localStorage.getItem("artworkID")) {
			req["artworkId"] = localStorage.getItem("artworkID");
		}
		if (this.artData?.work_type == "edition") {
			this.server.showSpinner();
			this.server
				.postApi(
					"artwork/updateEdition/" +
						localStorage.getItem("artworkID") +
						"/" +
						this.choosedEdition,
					{
						extras: this.extras2,
						price_history: req.price_history,
						sale_options: req.sale_options,
						sale_information: req.sale_information,
						updateAll: this.updateAllInput,
					},
				)
				.subscribe((res2) => {
					//this.server.hideSpinner();
					if (res2.statusCode == 200) {
						delete req["extras"];
						req["sale_options"] = this.selectedSaleOption;
						this.server.postApi(url, req).subscribe((res) => {
							this.server.hideSpinner();
							if (res.statusCode == 200) {
								localStorage.setItem("artworkID", res.data["_id"]);
								this.editNotifyService.reset();
								this.getArtWork();
								//alert(res.message);
								this.isSubmited = true;
							} else {
								this.isSubmited = false;
							}
						});
					} else {
						this.isSubmited = false;
					}
				});
		} else {
			this.server.showSpinner();
			this.server.postApi(url, req).subscribe((res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					localStorage.setItem("artworkID", res.data["_id"]);
					this.editNotifyService.reset();
					this.getArtWork();
					alert(res.message);
					this.isSubmited = true;
				} else {
					this.isSubmited = false;
				}
			});
		}
	}
	changeFocus(index) {
		console.log("in in ");
		setTimeout(() => {
			this.isDropDownOpen[index] = false;
		}, 500);
	}
	async getValueWithAsync() {
		await this.onSubmit();
		if (this.isSubmited) {
			this.router.navigate(["/artist-portal/settings/artworks"]);
		}
	}
	testSubmit() {
		console.log(this.form.getRawValue().priceHistory);
	}

	addToSaleHistory() {
		if (
			this.form.getRawValue().discount &&
			(!this.isValidNumber(this.form.getRawValue().discount) ||
				Number(this.form.getRawValue().discount > 100) ||
				Number(this.form.getRawValue().discount < 0))
		) {
			alert("Please enter valid discount value!");
			return;
		}
		if (
			this.form.getRawValue().taxPercentage &&
			(!this.isValidNumber(this.form.getRawValue().taxPercentage) ||
				Number(this.form.getRawValue().taxPercentage > 100) ||
				Number(this.form.getRawValue().taxPercentage < 0))
		) {
			alert("Please enter valid Tax Percentage value!");
			return;
		}
		if (
			this.form.getRawValue().finalSalePrice &&
			!this.isValidNumber(this.form.getRawValue().finalSalePrice)
		) {
			alert("Please enter valid Final Sale Price value!");
			return;
		}
		if (
			this.form.getRawValue().amountReceived &&
			!this.isValidNumber(this.form.getRawValue().amountReceived)
		) {
			alert("Please enter valid Amount Received value!");
			return;
		}
		let req = {
			sale_options: this.form.getRawValue().saleOption.replace(/ /g, ""),
			contents: {
				reservedDate: this.form.getRawValue().reservedDate,
				reservedBy: this.form.getRawValue().reservedBy,
				notForSaleDate: this.form.getRawValue().notForSaleDate,
				soldTo: this.form.getRawValue().soldTo,
				soldDate: this.form.getRawValue().soldDate,
				returnedDate: this.form.getRawValue().returnedDate,
				startDate: this.form.getRawValue().startDate,
				endDate: this.form.getRawValue().endDate,
				loanDetails: this.form.getRawValue().loanDetails,
				returnedToArtistReason: this.form.getRawValue().returnedToArtistReason,
				notForSaleReason: this.form.getRawValue().notForSaleReason,
				reservedFor: this.form.getRawValue().reservedFor,
				billingAddress: this.form.getRawValue().billingAddress,
				shippingAddress: this.form.getRawValue().shippingAddress,
				soldBy: this.form.getRawValue().soldBy,
				discount: this.form.getRawValue().discount,
				taxPercentage: this.form.getRawValue().taxPercentage,
				finalSalePrice: this.form.getRawValue().finalSalePrice,
				pi: this.form.getRawValue().pi,
				amountReceived: this.form.getRawValue().amountReceived,
				paymentDate: this.form.getRawValue().paymentDate,
				taxInvoice: this.form.getRawValue().taxInvoice,
				artistInvoice: this.form.getRawValue().artistInvoice,
				artistPaidDate: this.form.getRawValue().artistPaidDate,
				documents: this.form.getRawValue().documents,
			},
		};
		if (this.artData?.work_type == "edition") {
			this.server.showSpinner();
			this.server
				.postApi(
					"artwork/addEditionSaleHistory/" +
						localStorage.getItem("artworkID") +
						"/" +
						this.choosedEdition,
					req,
				)
				.subscribe((res) => {
					this.server.hideSpinner();
					if (res.statusCode == 200) {
						this.isSaleOptionChangable = true;
						this.form.patchValue({
							reservedDate: null,
							reservedBy: null,
							notForSaleDate: null,
							soldTo: null,
							soldDate: null,
							returnedDate: null,
							startDate: null,
							endDate: null,
							loanDetails: null,
							returnedToArtistReason: null,
							notForSaleReason: null,
							reservedFor: null,
							billingAddress: null,
							shippingAddress: null,
							soldBy: null,
							discount: null,
							taxPercentage: null,
							finalSalePrice: null,
							pi: null,
							amountReceived: null,
							paymentDate: null,
							taxInvoice: null,
							artistInvoice: null,
							saleOption: null,
							artistPaidDate: null,
						});

						(this.form.get("documents") as FormArray).clear();

						let data = res.data.saleHistory[res.data.saleHistory.length - 1];
						this.addRepeater2();
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("sale_options")
							.setValue(data.sale_options);

						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("contents")
							.setValue(data.contents);
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("created")
							.setValue(data.created.fullName);
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("createdAt")
							.setValue(data.createdAt);
						//alert('Added');
					} else {
						// this.isSubmited = false;
					}
				});
		} else {
			this.server.showSpinner();
			this.server
				.postApi(
					"artwork/addSaleHistory/" + localStorage.getItem("artworkID"),
					req,
				)
				.subscribe((res) => {
					this.server.hideSpinner();
					if (res.statusCode == 200) {
						this.isSaleOptionChangable = true;
						this.form.patchValue({
							reservedDate: null,
							reservedBy: null,
							notForSaleDate: null,
							soldTo: null,
							soldDate: null,
							returnedDate: null,
							startDate: null,
							endDate: null,
							loanDetails: null,
							returnedToArtistReason: null,
							notForSaleReason: null,
							reservedFor: null,
							billingAddress: null,
							shippingAddress: null,
							soldBy: null,
							discount: null,
							taxPercentage: null,
							finalSalePrice: null,
							pi: null,
							amountReceived: null,
							paymentDate: null,
							taxInvoice: null,
							artistInvoice: null,
							saleOption: null,
							artistPaidDate: null,
						});

						(this.form.get("documents") as FormArray).clear();

						let data = res.data.saleHistory[res.data.saleHistory.length - 1];
						this.addRepeater2();
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("sale_options")
							.setValue(data.sale_options);

						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("contents")
							.setValue(data.contents);
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("created")
							.setValue(data.created.fullName);
						this.form["controls"].saleDetails["controls"][
							this.form["controls"].saleDetails["controls"].length - 1
						]
							.get("createdAt")
							.setValue(data.createdAt);
						//alert('Added');
					} else {
						// this.isSubmited = false;
					}
				});
		}
	}
	getTerrainSaleUsers() {
		let url = `api/admin/user3?limit=${this.soldByLimit}&offset=${this.soldByOffset}`;
		if (this.soldBySearch) {
			url = url + `&search=${this.soldBySearch}`;
		}
		this.server.getApi(url).subscribe((res) => {
			this.soldByUser = res.data;
		});
	}
	isValidNumber(str) {
		var regex = /^[0-9]*\.?[0-9]{0,2}$/;
		return regex.test(str);
	}
	onDocumentFileSelect(files) {
		if (files.length == 0) {
			this.documentUrl = null;
		} else {
			this.uploadFile(files);
		}
	}
	uploadFile(files: any) {
		let formdata = new FormData();
		formdata.append("image", files[0].file_event);
		let url = apiUrl.upload;
		this.server.showSpinner();
		this.server.postApi(url, formdata).subscribe(
			(res) => {
				this.server.hideSpinner();
				if (res.statusCode == 200) {
					this.documentUrl = res.data;
					alert("File uploaded successfully!");
				}
			},
			(err) => {
				alert(err.error.message);
			},
		);
	}
	addDocument() {
		this.showAddDocument = false;
		(this.form.get("documents") as FormArray).push(
			new FormGroup({
				type: new FormControl(this.documentValue),
				url: new FormControl(this.documentUrl),
			}),
		);
		this.documentValue = null;
		this.documentUrl = null;
	}
	deleteDocumentEntry(index) {
		(this.form.get("documents") as FormArray).removeAt(index);
	}
	onInput(index): void {
		switch (index) {
			case 0:
				if (
					this.form.getRawValue().discount &&
					(!this.isValidNumber(this.form.getRawValue().discount) ||
						Number(this.form.getRawValue().discount > 100) ||
						Number(this.form.getRawValue().discount < 0))
				) {
					this.inputErros[0] = true;
				} else {
					this.inputErros[0] = false;
				}
				break;
			case 1:
				if (
					this.form.getRawValue().taxPercentage &&
					(!this.isValidNumber(this.form.getRawValue().taxPercentage) ||
						Number(this.form.getRawValue().taxPercentage > 100) ||
						Number(this.form.getRawValue().taxPercentage < 0))
				) {
					this.inputErros[1] = true;
				} else {
					this.inputErros[1] = false;
				}
				break;
			case 2:
				if (
					this.form.getRawValue().finalSalePrice &&
					!this.isValidNumber(this.form.getRawValue().finalSalePrice)
				) {
					this.inputErros[2] = true;
				} else {
					this.inputErros[2] = false;
				}
				break;
			case 3:
				if (
					this.form.getRawValue().amountReceived &&
					!this.isValidNumber(this.form.getRawValue().amountReceived)
				) {
					this.inputErros[3] = true;
				} else {
					this.inputErros[3] = false;
				}
				break;

			default:
				break;
		}
	}
	onEditionSelect(index, name) {
		this.choosedEdition = this.artData?.editionDetails?.findIndex((a) => {
			return a.edition == this.editions?.[index]?.edition;
		});

		this.form = this.formBuilder.group({
			catalogNumber: new FormControl(""),
			priceHistory: this.formBuilder.array([]),
			saleOption: new FormControl(""),
			saleInfo: new FormControl(""),
			saleDetails: this.formBuilder.array([]),
			reservedDate: new FormControl(""),
			reservedBy: new FormControl(""),
			notForSaleDate: new FormControl(""),
			soldTo: new FormControl(""),
			soldDate: new FormControl(""),
			returnedDate: new FormControl(""),
			startDate: new FormControl(""),
			endDate: new FormControl(""),
			loanDetails: new FormControl(""),
			returnedToArtistReason: new FormControl(""),
			notForSaleReason: new FormControl(""),
			reservedFor: new FormControl(""),
			billingAddress: new FormControl(""),
			shippingAddress: new FormControl(""),
			soldBy: new FormControl(""),
			discount: new FormControl(""),
			taxPercentage: new FormControl(""),
			finalSalePrice: new FormControl(""),
			pi: new FormControl(""),
			amountReceived: new FormControl(""),
			paymentDate: new FormControl(""),
			taxInvoice: new FormControl(""),
			artistInvoice: new FormControl(""),
			artistPaidDate: new FormControl(""),
			documents: this.formBuilder.array([]),
			editionNumber: new FormControl(name),
		});
		this.extras2 = this.editions?.[index]?.extras;
		this.form.patchValue({
			catalogNumber: this.artData.catalog_number,
			saleOption: this.editions?.[index]?.sale_options,
			saleInfo: this.editions?.[index]?.sale_information,
			reservedDate: this.editions?.[index]?.extras?.reservedDate,
			reservedBy: this.editions?.[index]?.extras?.reservedBy,
			notForSaleDate: this.editions?.[index]?.extras?.notForSaleDate,
			soldTo: this.editions?.[index]?.extras?.soldTo,
			soldDate: this.editions?.[index]?.extras?.soldDate,
			returnedDate: this.editions?.[index]?.extras?.returnedDate,
			startDate: this.editions?.[index]?.extras?.startDate,
			endDate: this.editions?.[index]?.extras?.endDate,
			loanDetails: this.editions?.[index]?.extras?.loanDetails,
			returnedToArtistReason:
				this.editions?.[index]?.extras?.returnedToArtistReason,
			notForSaleReason: this.editions?.[index]?.extras?.notForSaleReason,
			reservedFor: this.editions?.[index]?.extras?.reservedFor,
			billingAddress: this.editions?.[index]?.extras?.billingAddress,
			shippingAddress: this.editions?.[index]?.extras?.shippingAddress,
			soldBy: this.editions?.[index]?.extras?.soldBy,
			discount: this.editions?.[index]?.extras?.discount,
			taxPercentage: this.editions?.[index]?.extras?.taxPercentage,
			finalSalePrice: this.editions?.[index]?.extras?.finalSalePrice,
			pi: this.editions?.[index]?.extras?.pi,
			amountReceived: this.editions?.[index]?.extras?.amountReceived,
			paymentDate: this.editions?.[index]?.extras?.paymentDate,
			taxInvoice: this.editions?.[index]?.extras?.taxInvoice,
			artistInvoice: this.editions?.[index]?.extras?.artistInvoice,
			artistPaidDate: this.editions?.[index]?.extras?.artistPaidDate,
			consignmentStartDate: this.artData.extras?.consignmentStartDate,
			consignmentEndDate: this.artData.extras?.consignmentEndDate,
		});

		this.editions?.[index]?.extras?.documents?.forEach((ele, index) => {
			(this.form.get("documents") as FormArray).push(
				new FormGroup({
					type: new FormControl(ele.type),
					url: new FormControl(ele.url),
				}),
			);
		});
		this.editions?.[index]?.price_history.forEach((ele, index) => {
			console.log("hey");

			this.addRepeater();
			this.form["controls"].priceHistory["controls"][index]
				.get("artist_price")
				?.setValue(ele.artist_price);
			this.form["controls"].priceHistory["controls"][index]
				.get("sale_price")
				?.setValue(ele.sale_price);
			this.form["controls"].priceHistory["controls"][index]
				.get("pricing_date")
				?.setValue(ele.pricing_date);
		});
		this.editions?.[index]?.saleHistory.forEach((ele, index) => {
			this.addRepeater2();
			this.form["controls"].saleDetails["controls"][index]
				.get("sale_options")
				.setValue(ele.sale_options);
			this.form["controls"].saleDetails["controls"][index]
				.get("created")
				.setValue(ele.created.fullName);
			this.form["controls"].saleDetails["controls"][index]
				.get("createdAt")
				.setValue(ele.createdAt);
			this.form["controls"].saleDetails["controls"][index]
				.get("contents")
				.setValue(ele.contents);
		});
		this.isSaleOptionChangable =
			// this.form.get('saleDetails').value?.length > 0 &&
			this.form.get("saleOption").value
				? this.form.get("saleDetails").value?.at(-1)?.sale_options ===
					this.form.get("saleOption").value
				: true;
	}
}
