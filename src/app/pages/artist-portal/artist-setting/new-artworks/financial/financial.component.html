<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form *ngIf="form" [formGroup]="form">
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.catalogNumber"
              [ngClass]="{ 'Disabled-Color': disableObj.catalogNumber }"
            >
              <input
                formControlName="catalogNumber"
                type="text"
                placeholder="Catalog Number"
                [attr.disabled]="
                  userDetails?.role === 'SUPERADMIN' ||
                  userDetails?.role === 'ARCHIVIST' ||
                  userDetails?.role === 'TERRAIN CURATOR' ||
                  userDetails?.role === 'TERRAIN SALES'
                    ? null
                    : true
                "
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.catalogNumber && !isArtReadOnly"
              >
              </fa-icon>
              <div class="placeholder">Catalog Number</div>
              <div class="input-info">
                Provide the Catalog Number for the artwork. e.g.:
                RAPL-2021-JSMITH-47-02.
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="consignmentStartDate"
                type="date"
                placeholder="Consignment Start"
                [attr.disabled]="
                  userDetails?.role === 'SUPERADMIN' ||
                  userDetails?.role === 'ARCHIVIST' ||
                  userDetails?.role === 'TERRAIN CURATOR' ||
                  userDetails?.role === 'TERRAIN SALES'
                    ? null
                    : true
                "
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!isArtReadOnly"
              >
              </fa-icon>
              <div class="placeholder">Consignment Start</div>
              <div class="input-info"></div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="consignmentEndDate"
                type="date"
                placeholder="Consignment End"
                [attr.disabled]="
                  userDetails?.role === 'SUPERADMIN' ||
                  userDetails?.role === 'ARCHIVIST' ||
                  userDetails?.role === 'TERRAIN CURATOR' ||
                  userDetails?.role === 'TERRAIN SALES'
                    ? null
                    : true
                "
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!isArtReadOnly"
              >
              </fa-icon>
              <div class="placeholder">Consignment End</div>
              <div class="input-info"></div>
            </div>
          </div>
          <div *ngIf="artData?.work_type == 'edition'" class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container">
                <input
                  formControlName="editionNumber"
                  type="text"
                  class="selection"
                  placeholder="Choose Edition"
                  readonly="readonly"
                  (focus)="isDropDownOpen[3] = true"
                />
                <div class="placeholder">Choose Edition</div>
                <button
                  (click)="
                    isSaleOptionChangable &&
                      (isDropDownOpen[3] = !isDropDownOpen[3])
                  "
                  type="button"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[3],
                    'dropdown-visible': isDropDownOpen[3]
                  }"
                >
                  <ul>
                    <li
                      *ngFor="let item of editions; let i = index"
                      (click)="
                        isDropDownOpen[3] = false;
                        onEditionSelect(i, item?.edition)
                      "
                    >
                      <div class="country-name">{{ item?.edition }}</div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <ng-container
            *ngIf="artData?.work_type != 'edition' || choosedEdition != null"
          >
            <div class="sub-head" [hidden]="!showObj?.priceHistory">
              <p>
                Price History
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>
              </p>
              <forms-dynamic-table
                [columns]="tableColumns"
                [formArray]="form.get('priceHistory')"
                [errors]="priceHistoryErrors"
                [addOnly]="addOnly"
              ></forms-dynamic-table>

              <!-- <div class="splitter">
              <div
                class="field-value"
                style="padding-right: 0.5vw"
                [hidden]="!showObj?.artist_price"
              >
                <input
                  type="number"
                  [(ngModel)]="priceHistoryObj.artist_price"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Artist Price"
                />
                <div class="placeholder">Artist Price</div>
              </div>
              <div
                class="field-value"
                style="padding-right: 0.5vw"
                [hidden]="!showObj?.sale_price"
              >
                <input
                  type="number"
                  [(ngModel)]="priceHistoryObj.sale_price"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Sale Price"
                />
                <div class="placeholder">Sale Price</div>
              </div>
              <div
                class="field-value"
                style="padding-right: 0.5vw"
                [hidden]="!showObj?.pricing_date"
              >
                <input
                  type="date"
                  [(ngModel)]="priceHistoryObj.pricing_date"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Pricing Date"
                  required
                />
                <div class="placeholder">Pricing Date</div>
              </div>
            </div>
            <p class="input-info">
              Provide the price info and date when the price was set. Click on
              ‘Add to Price History’ before saving the page. The latest entry
              (based on date given) will be used on the website for pricing.
            </p>
            <div (click)="addRepeater()" class="add-next">
              Add to price history
            </div>

          </div>
          <div class="outer-box" *ngIf="priceHistory.controls.length > 0">
            <div class="row">
              <div
                class="col-md-4 col-sm-4 col-lg-4 text-left"
                *ngIf="showObj.artist_price"
              >
                <label class="tb-head ml-35">Artist Price</label>
              </div>
              <div
                class="col-md-4 col-sm-4 col-lg-4 text-left"
                *ngIf="showObj.sale_price"
              >
                <label class="tb-head">Sale Price</label>
              </div>
              <div
                class="col-md-4 col-sm-4 col-lg-4 text-left"
                *ngIf="showObj.pricing_date"
              >
                <label class="tb-head">Pricing Date</label>
              </div>
            </div>
            <div
              formArrayName="priceHistory"
              [dragula]="'task-group'"
              [(dragulaModel)]="priceHistory.controls"
            >
              <div
                class="row mb-15 inner-box"
                formGroupName="{{ i }}"
                *ngFor="let item of priceHistory.controls; let i = index"
              >
                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                  <span class="cross-icon" style="cursor: grab !important">
                    <i class="fa fa-ellipsis-v mr2"></i>
                    <i class="fa fa-ellipsis-v"></i>
                  </span>
                  <input
                    formControlName="artist_price"
                    type="number"
                    *ngIf="showObj.artist_price"
                    [readonly]="
                      userDetails?.role === 'REPRESENTED ARTIST' ||
                      userDetails?.role === 'GALLERY ARTIST'
                    "
                  />
                </div>
                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                  <input
                    formControlName="sale_price"
                    type="number"
                    *ngIf="showObj.sale_price"
                    [readonly]="
                      userDetails?.role === 'REPRESENTED ARTIST' ||
                      userDetails?.role === 'GALLERY ARTIST'
                    "
                  />
                </div>
                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                  <input
                    formControlName="pricing_date"
                    type="date"
                    *ngIf="showObj.pricing_date"
                    [readonly]="
                      userDetails?.role === 'REPRESENTED ARTIST' ||
                      userDetails?.role === 'GALLERY ARTIST'
                    "
                  />
                  <span
                    *ngIf="
                      !(
                        userDetails?.role === 'REPRESENTED ARTIST' ||
                        userDetails?.role === 'GALLERY ARTIST'
                      )
                    "
                    (click)="priceHistory.removeAt(i, 1)"
                    class="cross-icon"
                    >X</span
                  >
                </div>
              </div>
            </div>
           -->
            </div>
            <div class="splitter">
              <div
                class="field-value"
                style="padding-right: 0.5vw"
                [hidden]="!showObj?.saleOption"
                [ngClass]="{ 'Disabled-Color': disableObj.saleOption }"
              >
                <div class="input-container" (focusout)="changeFocus(0)">
                  <input
                    formControlName="saleOption"
                    type="text"
                    class="selection"
                    placeholder="Choose Sale Option"
                    readonly="readonly"
                    (focus)="
                      disableObj.saleOption ? null : (isDropDownOpen[0] = true)
                    "
                    [attr.disabled]="
                      isSaleOptionChangable && !isArtReadOnly ? null : true
                    "
                  />
                  <fa-icon
                    class="Lock-position"
                    [icon]="faLock"
                    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                    [hidden]="!disableObj.saleOption && !isArtReadOnly"
                  >
                  </fa-icon>
                  <div class="placeholder">Sale Options</div>
                  <button
                    (click)="
                      isSaleOptionChangable &&
                        (isDropDownOpen[0] = !isDropDownOpen[0])
                    "
                    type="button"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  >
                    <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                  </button>
                  <div
                    [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[0],
                      'dropdown-visible': isDropDownOpen[0]
                    }"
                  >
                    <ul>
                      <li
                        (click)="
                          form.get('saleOption').setValue('ForSale');
                          isDropDownOpen[0] = false;
                          checkSaleOptions2()
                        "
                      >
                        <div class="country-name">For Sale</div>
                      </li>
                      <!-- <li
                      (click)="
                        form.get('saleOption').setValue('ForSale - Amex');
                        isDropDownOpen[0] = false;
                        checkSaleOptions()
                      "
                    >
                      <div class="country-name">ForSale - Amex</div>
                    </li> -->
                      <li
                        (click)="
                          form.get('saleOption').setValue('NotForSale');
                          isDropDownOpen[0] = false;
                          checkSaleOptions2()
                        "
                      >
                        <div class="country-name">Not For Sale</div>
                      </li>
                      <li
                        (click)="
                          form.get('saleOption').setValue('Sold');
                          isDropDownOpen[0] = false;
                          checkSaleOptions()
                        "
                        *ngIf="!isArtist"
                      >
                        <div class="country-name">Sold</div>
                      </li>
                      <li
                        (click)="
                          form.get('saleOption').setValue('Reserved');
                          isDropDownOpen[0] = false;
                          checkSaleOptions2()
                        "
                        *ngIf="!isArtist"
                      >
                        <div class="country-name">Reserved</div>
                      </li>
                      <li
                        (click)="
                          form.get('saleOption').setValue('ReturnedToArtist');
                          isDropDownOpen[0] = false;
                          checkSaleOptions2()
                        "
                      >
                        <div class="country-name">Returned to Artist</div>
                      </li>
                      <li
                        (click)="
                          form.get('saleOption').setValue('OnLoan');
                          isDropDownOpen[0] = false;
                          checkSaleOptions2()
                        "
                        *ngIf="!isArtist"
                      >
                        <div class="country-name">On Loan</div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="input-info">
                  Choose the current sale status of the artwork
                </div>
              </div>
              <ng-container
                *ngIf="form.get('saleOption').value === 'NotForSale'"
              >
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    formControlName="notForSaleDate"
                    type="date"
                    placeholder=" Not For Sale date"
                    [attr.disabled]="isSaleOptionChangable ? null : true"
                  />
                  <div class="placeholder">Not For Sale date</div>
                </div>
              </ng-container>
              <ng-container
                *ngIf="form.get('saleOption').value === 'ReturnedToArtist'"
              >
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    formControlName="returnedDate"
                    type="date"
                    placeholder=" Returned Date"
                    [attr.disabled]="isSaleOptionChangable ? null : true"
                  />
                  <div class="placeholder">Returned Date</div>
                </div>
              </ng-container>
              <ng-container
                *ngIf="
                  artData?.work_type == 'edition' &&
                  form.get('saleOption').value === 'ReturnedToArtist'
                "
              >
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    [(ngModel)]="updateAllInput"
                    [ngModelOptions]="{ standalone: true }"
                    type="checkbox"
                    placeholder=" Update All"
                  />
                  &nbsp;Update All
                </div>
              </ng-container>
              <ng-container
                *ngIf="
                  userDetails?.role === 'SUPERADMIN' ||
                  userDetails?.role === 'ARCHIVIST' ||
                  userDetails?.role === 'TERRAIN CURATOR' ||
                  userDetails?.role === 'TERRAIN SALES'
                "
              >
                <ng-container
                  *ngIf="form.get('saleOption').value === 'Reserved'"
                >
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="reservedDate"
                      type="date"
                      placeholder=" Reserved Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Reserved Date</div>
                  </div>

                  <div class="field-value" style="padding-right: 0.5vw">
                    <div class="input-container" (focusout)="changeFocus(2)">
                      <input
                        formControlName="reservedBy"
                        type="text"
                        class="selection"
                        placeholder="Reserved By"
                        readonly="readonly"
                        (focus)="
                          disableObj.saleOption
                            ? null
                            : (isDropDownOpen[2] = true)
                        "
                        [attr.disabled]="isSaleOptionChangable ? null : true"
                      />
                      <div class="placeholder">Reserved By</div>
                      <button
                        (click)="
                          isSaleOptionChangable &&
                            (isDropDownOpen[2] = !isDropDownOpen[2])
                        "
                        type="button"
                      >
                        <img
                          src="assets/icons/arrow-down.png"
                          class="flag-arrow"
                        />
                      </button>
                      <div
                        [ngClass]="{
                          'dropdown-hidden': !isDropDownOpen[2],
                          'dropdown-visible': isDropDownOpen[2]
                        }"
                      >
                        <ul>
                          <li
                            *ngFor="let item of soldByUser"
                            (click)="
                              form.get('reservedBy').setValue(item?.fullName);
                              isDropDownOpen[2] = false
                            "
                          >
                            <div class="country-name">{{ item?.fullName }}</div>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <!-- <input
                    formControlName="soldBy"
                    type="text"
                    placeholder=" Sold By"
                  />
                  <div class="placeholder">Sold By</div> -->
                  </div>
                </ng-container>

                <ng-container *ngIf="form.get('saleOption').value === 'Sold'">
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="soldTo"
                      type="text"
                      placeholder=" Sold To"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Sold To</div>
                  </div>
                  <div class="field-value">
                    <input
                      formControlName="soldDate"
                      type="date"
                      placeholder=" Sold Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Sold Date</div>
                  </div>
                </ng-container>

                <ng-container *ngIf="form.get('saleOption').value === 'OnLoan'">
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="startDate"
                      type="date"
                      placeholder=" Start Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Start Date</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="endDate"
                      type="date"
                      placeholder=" End Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">End Date</div>
                  </div>
                </ng-container>
              </ng-container>
            </div>
            <ng-container
              *ngIf="form.get('saleOption').value === 'ReturnedToArtist'"
            >
              <div class="field-value">
                <p>Reason for returning</p>
                <angular-editor
                  formControlName="returnedToArtistReason"
                  id="editor28"
                  [placeholder]="'Reason for returning'"
                  [config]="{ editable: isSaleOptionChangable }"
                ></angular-editor>
              </div>
            </ng-container>
            <ng-container *ngIf="form.get('saleOption').value === 'NotForSale'">
              <div class="field-value">
                <p>Not For Sale Reason</p>
                <angular-editor
                  formControlName="notForSaleReason"
                  id="editor28"
                  [placeholder]="'Not For Sale Reason'"
                  [config]="{ editable: isSaleOptionChangable }"
                ></angular-editor>
              </div>
            </ng-container>
            <ng-container
              *ngIf="
                userDetails?.role === 'SUPERADMIN' ||
                userDetails?.role === 'ARCHIVIST' ||
                userDetails?.role === 'TERRAIN CURATOR' ||
                userDetails?.role === 'TERRAIN SALES'
              "
            >
              <ng-container *ngIf="form.get('saleOption').value === 'OnLoan'">
                <div class="field-value">
                  <p>Loan Details</p>
                  <angular-editor
                    formControlName="loanDetails"
                    id="editor28"
                    [placeholder]="'Loan Details'"
                    [config]="{ editable: isSaleOptionChangable }"
                  ></angular-editor>
                </div>
              </ng-container>

              <ng-container *ngIf="form.get('saleOption').value === 'Reserved'">
                <div class="field-value">
                  <p>Reserved For</p>
                  <angular-editor
                    formControlName="reservedFor"
                    id="editor28"
                    [placeholder]="'Reserved For'"
                    [config]="{ editable: isSaleOptionChangable }"
                  ></angular-editor>
                </div>
              </ng-container>
              <ng-container *ngIf="form.get('saleOption').value === 'Sold'">
                <!-- <div class="field-value">
              <p>Billing Address</p>
              <angular-editor
                id="editor28"
                [placeholder]="'Billing Address'"
              ></angular-editor>
            </div>
            <div class="field-value">
              <p>Shipping Address</p>
              <angular-editor
                id="editor28"
                [placeholder]="'Shipping Address'"
              ></angular-editor>
            </div> -->
                <div class="splitter">
                  <div class="field-value">
                    <!-- <p>Billing Address</p> -->
                    <textarea
                      formControlName="billingAddress"
                      style="width: 95%"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    ></textarea>
                    <div class="placeholder">Billing Address</div>
                  </div>
                  <div class="field-value">
                    <!-- <p>Shipping Address</p> -->
                    <textarea
                      formControlName="shippingAddress"
                      style="width: 95%"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    ></textarea>
                    <div class="placeholder">Shipping Address</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <div class="input-container" (focusout)="changeFocus(0)">
                      <input
                        formControlName="soldBy"
                        type="text"
                        class="selection"
                        placeholder="Sold By"
                        readonly="readonly"
                        (focus)="
                          disableObj.saleOption
                            ? null
                            : (isDropDownOpen[1] = true)
                        "
                        [attr.disabled]="isSaleOptionChangable ? null : true"
                      />
                      <div class="placeholder">Sold By</div>
                      <button
                        (click)="
                          isSaleOptionChangable &&
                            (isDropDownOpen[1] = !isDropDownOpen[1])
                        "
                        type="button"
                      >
                        <img
                          src="assets/icons/arrow-down.png"
                          class="flag-arrow"
                        />
                      </button>
                      <div
                        [ngClass]="{
                          'dropdown-hidden': !isDropDownOpen[1],
                          'dropdown-visible': isDropDownOpen[1]
                        }"
                      >
                        <ul>
                          <li
                            *ngFor="let item of soldByUser"
                            (click)="
                              form.get('soldBy').setValue(item?.fullName);
                              isDropDownOpen[1] = false
                            "
                          >
                            <div class="country-name">{{ item?.fullName }}</div>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <!-- <input
                    formControlName="soldBy"
                    type="text"
                    placeholder=" Sold By"
                  />
                  <div class="placeholder">Sold By</div> -->
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="discount"
                      type="text"
                      placeholder=" Discount"
                      (input)="onInput(0)"
                      [class.invalid]="inputErros[0]"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Discount Percentage</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="taxPercentage"
                      type="text"
                      placeholder=" Tax Percentage"
                      (input)="onInput(1)"
                      [class.invalid]="inputErros[1]"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Tax Percentage</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="finalSalePrice"
                      type="text"
                      placeholder=" Final Sale Price"
                      (input)="onInput(2)"
                      [class.invalid]="inputErros[2]"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Final Sale Price</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="pi"
                      type="text"
                      placeholder=" PI #"
                    />
                    <div class="placeholder">PI #</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="amountReceived"
                      type="text"
                      placeholder="INR Amount Received"
                      (input)="onInput(3)"
                      [class.invalid]="inputErros[3]"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Amount Received</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="paymentDate"
                      type="date"
                      placeholder=" Payment Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Payment Date</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="taxInvoice"
                      type="text"
                      placeholder=" Tax Invoice #"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Tax Invoice #</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="artistInvoice"
                      type="text"
                      placeholder=" Artist Invoice #"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Artist Invoice #</div>
                  </div>
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      formControlName="artistPaidDate"
                      type="date"
                      placeholder="  Artist Paid Date"
                      [attr.disabled]="isSaleOptionChangable ? null : true"
                    />
                    <div class="placeholder">Artist Paid Date</div>
                  </div>
                </div>
              </ng-container>
              <div
                *ngIf="form.get('saleOption').value === 'Sold'"
                class="add-document"
                style="padding-bottom: 2vw"
              >
                <br />
                <div
                  *ngFor="let item of form.get('documents').value"
                  style="display: inline-block"
                >
                  <a [href]="item.url" target="_blank" style="color: #004ddd">
                    {{ item.type }}</a
                  ><span style="margin-right: 0.5vw"> ;</span>
                </div>
                <br />

                <span (click)="showAddDocument = true"
                  ><img
                    class="plus-icon"
                    style="margin-right: 0.5vw"
                    src="assets/images/load-more.png"
                /></span>
                <span
                  *ngIf="isSaleOptionChangable"
                  (click)="showAddDocument = true"
                  class="sub-text"
                  >Add documents</span
                >
                <span
                  *ngIf="
                    isSaleOptionChangable &&
                    form.get('documents').value?.length > 0
                  "
                  class="sub-text"
                  style="margin-left: 1vw"
                  (click)="manageDocPop = true"
                  >Manage documents</span
                >
              </div>
            </ng-container>

            <ng-container
              *ngIf="
                userDetails.role != 'REPRESENTED ARTIST' &&
                userDetails.role != 'GALLERY ARTIST'
              "
            >
              <div
                *ngIf="form.get('saleOption').value"
                class="addbutton-container"
              >
                <div class="button" (click)="movetoSaleHisPop = true">
                  Move to Sale History
                </div>
              </div></ng-container
            >

            <div
              *ngIf="
                userDetails.role != 'REPRESENTED ARTIST' &&
                userDetails.role != 'GALLERY ARTIST'
              "
              class="sub-head"
              style="margin-top: 1vw"
            >
              <p>
                Sale History
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!isArtReadOnly"
                >
                </fa-icon>
              </p>
              <forms-dynamic-table
                [expandedColumns]="saleHistoryExpandedColumns"
                [columns]="saleHistoryColumns"
                [formArray]="form.get('saleDetails')"
                [errors]="saleDetailsErrors"
                [readOnly]="true"
                [expandedMode]="true"
              ></forms-dynamic-table>
              <!-- <div class="splitter" style="margin-bottom: 1vw">
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.final_sale_price"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Final Price"
                />
                <div class="placeholder">Final Sale Price</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.discount"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Discount"
                />
                <div class="placeholder">Discount</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.taxable"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Taxable"
                />
                <div class="placeholder">Taxable</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.tax_percent"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Tax Percent"
                />
                <div class="placeholder">Tax percent</div>
              </div>
            </div>
            <span (click)="addRepeater2()" style="padding-bottom: 2vw">
              <span
                ><img
                  class="plus-icon"
                  style="margin-right: 0.5vw"
                  src="assets/images/load-more.png"
              /></span>
              <span class="sub-text">Add to sale details</span>
            </span> -->
            </div>
            <!-- <div class="outer-box" *ngIf="saleDetails.controls.length > 0">
            <div class="row">
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head ml-35">Final Sale Price</label>
              </div>
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Discount</label>
              </div>
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Taxable</label>
              </div>
              <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                <label class="tb-head">Tax Percent</label>
              </div>
            </div>
            <div
              formArrayName="saleDetails"
              [dragula]="'task-group-2'"
              [(dragulaModel)]="saleDetails.controls"
            >
              <div
                class="row mb-15 inner-box"
                formGroupName="{{ i }}"
                *ngFor="let item of saleDetails.controls; let i = index"
              >
                <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                  <span class="cross-icon" style="cursor: grab !important">
                    <i class="fa fa-ellipsis-v mr2"></i>
                    <i class="fa fa-ellipsis-v"></i>
                  </span>
                  <input
                    formControlName="finalSalePrice"
                    type="number"
                    class="width"
                  />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <input
                    formControlName="discount"
                    type="number"
                    class="width"
                  />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                  <input
                    formControlName="taxable"
                    type="number"
                    class="width"
                  />
                </div>
                <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                  <input
                    formControlName="taxPercent"
                    type="number"
                    class="width"
                  />
                  <span (click)="saleDetails.removeAt(i, 1)" class="cross-icon"
                    >X
                  </span>
                </div>
              </div>
            </div>
          </div> -->
          </ng-container>
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" *ngIf="showAddDocument">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Add Document</p>
    <div class="container__main" style="padding-bottom: 0vw">
      <div class="profile-field">
        <div class="field-contents">
          <div class="field-value" style="padding-right: 0.5vw">
            <div class="input-container" (focusout)="changeFocus(0)">
              <input
                type="text"
                class="selection"
                placeholder="Document type"
                readonly="readonly"
                [value]="documentValue"
                (focus)="showAddDocumenttype = true"
              />
              <div class="placeholder">Document type</div>
              <button
                (click)="showAddDocumenttype = !showAddDocumenttype"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !showAddDocumenttype,
                  'dropdown-visible': showAddDocumenttype
                }"
              >
                <ul>
                  <li
                    (click)="
                      documentValue = 'Proforma Invoice';
                      showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Proforma Invoice</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Tax Invoice'; showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Tax Invoice</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Shipping Invoice';
                      showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Shipping Invoice</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Framing Invoice';
                      showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Framing Invoice</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'E-Way Bill'; showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">E-Way Bill</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Artist Statement';
                      showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Artist Statement</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Artist Invoice';
                      showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Artist Invoice</div>
                  </li>
                  <li
                    (click)="
                      documentValue = 'Other'; showAddDocumenttype = false
                    "
                  >
                    <div class="country-name">Other</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="field-value">
            <p>Document</p>
            <image-upload
              [accept]="'all'"
              [fileSize]="2147483648"
              [hideAltText]="true"
              (onFileChange)="onDocumentFileSelect($event)"
            >
            </image-upload>
          </div>
        </div>
      </div>
    </div>

    <div
      class="Edition-list"
      style="max-height: 16vw; overflow-y: scroll; margin-top: 1vw"
    ></div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="showAddDocument = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="documentValue && documentUrl && addDocument()"
      >
        Add
      </button>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" [hidden]="!movetoSaleHisPop">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Move to Sale History</p>

    <span
      >Are you sure you wish to move this entry to history? Only click when
      making a new entry for this artwork.</span
    >

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="movetoSaleHisPop = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="movetoSaleHisPop = false; addToSaleHistory()"
      >
        Yes
      </button>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!manageDocPop">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Manage Documents</p>

    <div *ngFor="let item of form.get('documents').value; let i = index">
      <a
        [href]="item.url"
        target="_blank"
        style="margin-right: 0.5vw; color: #004ddd"
      >
        {{ item.type }}</a
      >
      <fa-icon (click)="deleteDocumentEntry(i)" [icon]="faTrash"> </fa-icon>
    </div>

    <div
      class="buttonList"
      style="
        margin-top: 1.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="manageDocPop = false"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" *ngIf="isSaleOptionSelection">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Select Sale Option for The Artwork</p>
    <div class="container__main" style="padding-bottom: 0vw">
      <div class="profile-field">
        <div class="field-contents">
          <div class="field-value" style="padding-right: 0.5vw">
            <div class="input-container" (focusout)="changeFocus(8)">
              <input
                [(ngModel)]="selectedSaleOption"
                [ngModelOptions]="{ standalone: true }"
                type="text"
                class="selection"
                placeholder="Choose Sale Option"
                readonly="readonly"
                (focus)="
                  disableObj.saleOption ? null : (isDropDownOpen[8] = true)
                "
                [attr.disabled]="!isArtReadOnly ? null : true"
              />

              <div class="placeholder">Sale Options</div>
              <button
                (click)="isDropDownOpen[8] = !isDropDownOpen[8]"
                type="button"
                [attr.disabled]="isArtReadOnly ? true : null"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDownOpen[8],
                  'dropdown-visible': isDropDownOpen[8]
                }"
              >
                <ul>
                  <!-- <li
                      (click)="
                        form.get('saleOption').setValue('ForSale - Amex');
                        isDropDownOpen[0] = false;
                        checkSaleOptions()
                      "
                    >
                      <div class="country-name">ForSale - Amex</div>
                    </li> -->
                  <li
                    (click)="
                      selectedSaleOption = 'NotForSale';
                      isDropDownOpen[8] = false
                    "
                  >
                    <div class="country-name">Not For Sale</div>
                  </li>
                  <li
                    (click)="
                      selectedSaleOption = 'Sold'; isDropDownOpen[8] = false
                    "
                  >
                    <div class="country-name">Sold</div>
                  </li>
                  <li
                    (click)="
                      selectedSaleOption = 'Reserved'; isDropDownOpen[8] = false
                    "
                  >
                    <div class="country-name">Reserved</div>
                  </li>
                  <li
                    (click)="
                      selectedSaleOption = 'ReturnedToArtist';
                      isDropDownOpen[8] = false
                    "
                  >
                    <div class="country-name">Returned to Artist</div>
                  </li>
                  <li
                    (click)="
                      selectedSaleOption = 'OnLoan'; isDropDownOpen[8] = false
                    "
                  >
                    <div class="country-name">On Loan</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="input-info">Choose the status of the artwork</div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="Edition-list"
      style="max-height: 16vw; overflow-y: scroll; margin-top: 1vw"
    ></div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="selectedSaleOption = null; isSaleOptionSelection = false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 35%; padding: 0.33333vw 1.08333vw"
        (click)="onSubmit() && (isSaleOptionSelection = false)"
      >
        Save
      </button>
    </div>
  </div>
</div>
