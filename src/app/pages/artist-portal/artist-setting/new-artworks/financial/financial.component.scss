input[type="text"][disabled] {
  color: var(--quaternary-font-color) !important;
}
.Lock-position {
  position: absolute;
  top: 1vw;
  right: 1vw;
  // z-index: 1;
  color: var(--quaternary-font-color);
}
.container__main {
  //padding-right: calc(36.111vw - var(--page-default-margin));
  padding-bottom: 6.3vw;
  @media (max-width: 768px) {
    padding-bottom: 24.3vw;
  }
  .main__heading {
    font-size: 2.083vw;
    margin-bottom: 2.083vw;
  }
  .sub__text {
    font-size: 1.111vw;
    font-family: var(--secondary-font);
    margin-bottom: 3.472vw;
  }
  .form__info {
    display: flex;
    // margin-bottom: 2.222vw;
    .icon__history {
      img {
        width: 1.527vw;
        height: 1.388vw;
        margin-right: 0.902vw;
      }
    }
    .info {
      font-size: 0.972vw;
      color: var(--quaternary-font-color);
      //   span {
      // font-size: 0.972vw;
      // color: var(--quaternary-font-color);
      .blue {
        color: var(--tertiary-font-color);
      }
      //   }
    }
  }
  .profile-field {
    .field-contents {
      .field-heading {
        margin-top: 2.78vw;
        margin-bottom: 0;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
      }
      .sub-head {
        margin-top: 3.47vw;
        font-size: 1.25vw;
        @media (max-width: 768px) {
          font-size: 4.34vw;
        }
        .plus-icon {
          width: 1.5vw;
          @media (max-width: 768px) {
            width: 4.9vw;
            margin-left: 2vw;
            margin-top: -1vw;
          }
        }
      }
      .double {
        width: 35vw;
        .input-double {
          width: 17.01vw !important;
        }
      }
      .field-value {
        margin-top: 2.08vw;
        position: relative;
        //display: flex;
        justify-content: start;
        align-items: center;
        &.doted {
          border: 0.069vw dotted var(--timeline-color);
          padding-left: 1vw;
          padding-right: 1vw;
          padding-bottom: 0.5vw;
        }
        input[type="text"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="number"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="date"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          color: var(--quaternary-font-color);
          //   ::placeholder{
          // color: var(--quaternary-font-color);
          // }
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="password"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        .input-container {
          width: 100%;
          position: relative;
          display: inline-flex;
          justify-content: start;
          align-items: center;
          flex-wrap: wrap;
          &.doted {
            border: dotted 1px black;
          }
          .flag-icon {
            position: absolute;
            left: 1.04vw;
          }
          button {
            outline: none;
            background: none;
            border: none;
          }
          .flag-arrow {
            position: absolute;
            max-width: 0.69vw;
            height: auto;
            right: 2.08vw;
            top: 0;
            bottom: 0;
            margin: auto;
            @media (max-width: 768px) {
              max-width: 1.95vw;
            }
          }
          .division {
            position: absolute;
            width: 0.069vw;
            height: 1.04vw;
            background-color: var(--timeline-color);
            left: 3.33vw;
          }
          input[type="text"] {
            // background: transparent;
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 1.04vw 1.11vw 1.04vw 4.2vw;
            border-radius: 0.14vw;
            height: 3.47vw;
            width: 100%;
            z-index: 0;
            @media (max-width: 768px) {
              height: 11.35vw;
              font-size: 3.86vw;
              padding: 1.04vw 2.11vw;
            }
            &.selection {
              padding: 1.04vw 1.11vw 1.04vw 1.04vw;
            }
          }
          .dropdown-visible {
            background-color: var(--primary-background-color);
            visibility: visible;
            position: absolute;
            top: 3.47vw;
            z-index: 100;
            box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
            width: 100%;
            @media (max-width: 768px) {
              top: 10.47vw;
            }
            ul {
              list-style: none;
              padding: 0.69vw 0;
              max-height: 27.97vw;
              margin: 0;
              overflow: hidden;
              overflow-y: scroll;
              li {
                padding: 0.69vw 1.04vw;
                width: 34.9vw;
                display: flex;
                @media (max-width: 768px) {
                  padding: 1.25vw 1.04vw;
                }
                .country-name {
                  margin-left: 0.69vw;
                  @media (max-width: 768px) {
                    font-size: 3.38vw;
                  }
                }
              }
              li:hover {
                background-color: var(--timeline-color);
              }
            }
            ul::-webkit-scrollbar {
              display: none;
            }
          }
          .dropdown-hidden {
            display: none;
          }
        }
        .ph-flag {
          height: 1.25vw;
          // padding-right: 0.62vw;
          // border-right: solid 0.09vw var(--quaternary-font-color);
        }
        .placeholder {
          position: absolute;
          top: -0.4vw;
          left: 1.04vw;
          font-size: 0.8333vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
          // background-color: #ededf1;
          @media (max-width: 768px) {
            top: -1.8vw;
            left: 2.04vw;
            font-size: 3.38vw;
          }
        }
        .send {
          margin-left: 2.08vw;
          color: var(--tertiary-font-color);
        }
      }
      .field-value-4 {
        margin-top: 2.08vw;
        position: relative;
        //display: flex;
        justify-content: start;
        align-items: center;
        &.doted {
          border: 0.069vw dotted var(--timeline-color);
          padding-left: 1vw;
          padding-right: 1vw;
          padding-bottom: 0.5vw;
        }
        input[type="text"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="number"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="date"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 100%;
          height: 3.47vw;
          color: var(--quaternary-font-color);
          //   ::placeholder{
          // color: var(--quaternary-font-color);
          // }
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        input[type="password"] {
          background: transparent;
          font-family: var(--secondary-font);
          border: none;
          border: 0.069vw solid var(--timeline-color);
          border-radius: 0.14vw;
          padding: 1.04vw 1.11vw;
          width: 35vw;
          height: 3.47vw;
          @media (max-width: 768px) {
            height: 11.35vw;
            font-size: 3.86vw;
            padding: 1.04vw 2.11vw;
          }
        }
        .input-container {
          width: 100%;
          position: relative;
          display: inline-flex;
          justify-content: start;
          align-items: center;
          flex-wrap: wrap;
          &.doted {
            border: dotted 1px black;
          }
          .flag-icon {
            position: absolute;
            left: 1.04vw;
          }
          button {
            outline: none;
            background: none;
            border: none;
          }
          .flag-arrow {
            position: absolute;
            max-width: 0.69vw;
            height: auto;
            right: 2.08vw;
            top: 0;
            bottom: 0;
            margin: auto;
            @media (max-width: 768px) {
              max-width: 1.95vw;
            }
          }
          .division {
            position: absolute;
            width: 0.069vw;
            height: 1.04vw;
            background-color: var(--timeline-color);
            left: 3.33vw;
          }
          input[type="text"] {
            // background: transparent;
            font-family: var(--secondary-font);
            border: 0.069vw solid var(--timeline-color);
            padding: 1.04vw 1.11vw 1.04vw 4.2vw;
            border-radius: 0.14vw;
            height: 3.47vw;
            width: 100%;
            z-index: 0;
            @media (max-width: 768px) {
              height: 11.35vw;
              font-size: 3.86vw;
              padding: 1.04vw 2.11vw;
            }
            &.selection {
              padding: 1.04vw 1.11vw 1.04vw 1.04vw;
            }
          }
          .dropdown-visible {
            background-color: var(--primary-background-color);
            visibility: visible;
            position: absolute;
            top: 3.47vw;
            z-index: 100;
            box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
            width: 100%;
            @media (max-width: 768px) {
              top: 10.47vw;
            }
            ul {
              list-style: none;
              padding: 0.69vw 0;
              max-height: 27.97vw;
              margin: 0;
              overflow: hidden;
              overflow-y: scroll;
              li {
                padding: 0.69vw 1.04vw;
                width: 34.9vw;
                display: flex;
                @media (max-width: 768px) {
                  padding: 1.25vw 1.04vw;
                }
                .country-name {
                  margin-left: 0.69vw;
                  @media (max-width: 768px) {
                    font-size: 3.38vw;
                  }
                }
              }
              li:hover {
                background-color: var(--timeline-color);
              }
            }
            ul::-webkit-scrollbar {
              display: none;
            }
          }
          .dropdown-hidden {
            display: none;
          }
        }
        .ph-flag {
          height: 1.25vw;
          // padding-right: 0.62vw;
          // border-right: solid 0.09vw var(--quaternary-font-color);
        }
        .placeholder {
          position: absolute;
          top: -0.4vw;
          left: 1.04vw;
          font-size: 0.8333vw;
          color: var(--quaternary-font-color);
          padding: 0 0.3vw;
          background-color: var(--primary-background-color);
          // background-color: #ededf1;
          @media (max-width: 768px) {
            top: -1.8vw;
            left: 2.04vw;
            font-size: 3.38vw;
          }
        }
        .send {
          margin-left: 2.08vw;
          color: var(--tertiary-font-color);
        }
      }
      .verify {
        margin-top: 2.78vw;
        font-family: var(--secondary-font);
        font-size: 1.25vw;
      }
      .partitioned {
        margin-top: 1.33vw;
        outline: none;
        padding-left: 0.8vw;
        letter-spacing: 0;
        border: 0;
        background-image: linear-gradient(
          to left,
          var(--timeline-color) 70%,
          rgba(255, 255, 255, 0) 0%
        );
        background-position: bottom;
        background-size: 3.2vw 0.069vw;
        width: 3.2vw;
        background-repeat: repeat-x;
        background-position-x: 2.2vw;
        height: 2vw;
        padding-bottom: 0.35vw;
        font-family: var(--secondary-font);
      }
      .last-changed {
        margin-top: 1.04vw;
        font-size: 0.97vw;
        color: var(--quaternary-font-color);
      }
    }
    .buttonList {
      margin-top: 3.47vw;
      margin-bottom: 1.042vw;
      .save {
        // display: block;
        width: 100%;
        outline: none;
        font-size: 1.25vw;
        // width: 20.56vw;
        background-color: transparent;
        color: var(--tertiary-font-color);
        padding: 0.833333vw 12.08333vw;
        border: 0.069vw solid var(--tertiary-font-color);
        border-radius: 1.46vw;
      }
      .save:hover {
        font-weight: 500;
      }
    }
  }
  .splitter {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    .field-value {
      width: 30%;
      @media (max-width: 768px) {
        width: 100%;
      }
    }
    .field-value-4 {
      width: 23%;
      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }
}

label .check {
  width: 1.25vw;
  height: 1.25vw;
  margin-right: 0.7vw;
  // border: 0.0069vw solid var(--checkbox-border-color);
  border-radius: 50%;
  position: relative;
  background-color: transparent;
  transition: all 0.15s cubic-bezier(0, 1.05, 0.72, 1.07);
  background-image: url("../../../../../../assets/icons/checkbox/<EMAIL>");
  background-size: 1.25vw;
  &::after {
    content: "";
    width: 0.7vw;
    height: 0.7vw;
    border-radius: 50%;
    opacity: 0;
    position: absolute;
    // background-color: var(--tertiary-font-color);
    background-image: url("../../../../../../assets/icons/check/<EMAIL>");
    background-size: 0.7vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.check-label {
  margin-bottom: 0;
  .colorRad {
    border: 0.0069vw solid var(--primary-border-color);
    outline: none;
    width: 1.56vw;
    height: 1.56vw;
    border-radius: 50%;
    margin-right: 0.8vw;
  }
  .title {
    margin-right: 2.08vw;
  }
}

input[type="radio"] {
  display: none;
}

input {
  &:checked {
    ~ .check {
      &::after {
        opacity: 1;
      }
    }
  }
}

.file-upload {
  height: 100%;
  border-radius: 0.07vw;
  border: solid 0.07vw #ced4db;
  display: flex;
  justify-content: start;
  flex-direction: column;
  .icon {
    margin-top: 2.08vw;
    img {
      height: 1.38vw;
      width: 1.38vw;
    }
  }
  .text-content {
    text-align: center;
    margin-top: 1.38vw;
    width: 20.83vw;
    position: relative;
    padding-right: 1vw;
    padding-left: 1vw;
    .title {
      font-size: 1.11vw;
    }
    .sub-title {
      color: #808080;
      margin-top: 0.69vw;
      font-size: 0.97vw;
    }
    .close {
      position: absolute;
      right: 0;
      top: 0;
      padding: 0.5vw;
      img {
        cursor: pointer;
        width: 0.69vw;
        height: 0.69vw;
      }
    }
  }
}

.button-container {
  margin-top: 2.08vw;
  margin-bottom: 2.08vw;
  .button {
    width: 13.33vw;
    height: 3.05vw;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 1.49vw;
    border: solid 0.069vw #004ddd;
    color: #004ddd;
  }
}

.upload-input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

/* Hide default HTML checkbox */

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--tertiary-font-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Rounded sliders */

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.text-before {
  margin-right: 0.5vw;
  @media (max-width: 768px) {
    font-size: 4.34vw;
    margin-right: 2.5vw;
  }
}

.input-with-text {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
}

.input-info {
  font-size: 0.95vw;
  margin-top: 0.5vw;
  @media (max-width: 768px) {
    font-size: 3.38vw;
    margin-top: 0.76vw;
    margin-bottom: 4.62vw;
  }
}

.wrap-collabsible {
  margin: 1.2rem 0;
  input[type="checkbox"] {
    display: none;
  }
  .lbl-toggle {
    display: block;
    padding: 1rem;
    border: 0.069vw solid var(--timeline-color);
    cursor: pointer;
    transition: all 0.25s ease-out;
    @media (max-width: 768px) {
      font-size: 3.86vw;
    }
  }
  .collapsible-content {
    max-height: 0px;
    overflow: hidden;
    transition: max-height 0.25s ease-in-out;
  }
  .toggle:checked + .lbl-toggle + .collapsible-content {
    max-height: 350px;
  }
  .toggle:checked + .lbl-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .collapsible-content .content-inner {
    padding: 0.5rem 1rem;
  }
  .collapsible-content p {
    margin-bottom: 0;
  }
}

.sub-text {
  font-size: 15px;
  color: #1259e0;
  cursor: pointer;
}

.cross-icon {
  font-size: large;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 10px;
  margin-top: 2px;
}

.outer-box {
  border: 1px solid gray;
  padding: 40px;
}

.tb-head {
  font-size: 12px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-35 {
  margin-left: 35px;
}

.d-flex {
  display: flex;
}

.inner-box {
  border: 1px solid gray;
  padding: 20px 0px 15px 0px;
  cursor: grab;
}

.width {
  width: 80%;
}

.mr2 {
  margin-right: 2px;
}

.footer-nav {
  height: 6.3vw;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0);
  @media (max-width: 768px) {
    padding-bottom: 11.24vw;
  }
  .button-group {
    //margin-left: 21.66vw;
    height: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 1.25vw;
    @media (max-width: 768px) {
      font-size: 4.34vw;
      margin-left: 0vw;
      align-items: unset;
    }
    .next {
      margin: 0 0.5vw;
      width: 9vw;
      height: 3.05vw;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 1.49vw;
      border: solid 0.069vw #004ddd;
      color: #004ddd;
      @media (max-width: 768px) {
        width: 46.37vw;
        height: 10.62vw;
        border-radius: 6.08vw;
      }
    }
    .back {
      margin-left: 2.77vw;
      color: #808080;
    }
  }
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
.Disabled-Color {
  color: var(--quaternary-font-color) !important;
}
.add-next {
  margin: 0.5vw 0.5vw;
  width: 12vw;
  height: 3.05vw;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1.49vw;
  border: solid 0.069vw #004ddd;
  color: #004ddd;
  @media (max-width: 768px) {
    width: 46.37vw;
    height: 10.62vw;
    border-radius: 6.08vw;
  }
}

.addbutton-container {
  display: flex;
  justify-content: end;
  margin-top: 1vw;
  .button {
    background-color: #004ddd;
    color: white;
    border: none;
    cursor: pointer;
    padding: 8px 16px;
    font-size: 16px;
    border-radius: 10px;
  }
}
.add-document {
  img {
    width: 1vw;
  }
}
.popup-for-confirmation {
  // position: absolute;
  // display: none;
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.433);

  .popup-card {
    width: 50vw;
    // height: 100%;
    margin: auto;
    margin-top: 10vw;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
      rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    border-radius: 1.5vw;
    padding: 3vw 2vw;
    display: flex;
    flex-direction: column;
    background-color: white;
  }
}
.invalid {
  border-color: red !important;
}
