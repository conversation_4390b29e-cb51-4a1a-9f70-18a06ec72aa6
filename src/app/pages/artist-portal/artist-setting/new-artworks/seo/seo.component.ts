import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';

@Component({
  selector: 'app-seo',
  templateUrl: './seo.component.html',
  styleUrls: ['./seo.component.scss']
})
export class SeoComponent implements OnInit {
  form: FormGroup;
  dataforSEO = [{ value: 1, display: 'asdass' }, { value: 2, display: 'dfjusa' }, { value: 3, display: 'zasd' }, { value: 4, display: 'ksadk' }, { value: 5, display: 'asdjd' }];
  selectedKeywords;
  previousData=[{ value: 1, display: 'asdass' }, { value: 2, display: 'dfjusa' }];
  constructor(
    private formBuilder: FormBuilder,
  ) { }
  getSelectedItem(items: Array<string>) {
    this.selectedKeywords = items;
    console.log(this.selectedKeywords);
  }



  ngOnInit(): void {
    this.form = this.formBuilder.group({
    });
  }

  onSubmit() {
  }
}