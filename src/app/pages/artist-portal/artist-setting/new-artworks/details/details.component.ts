import { EditNotifyService } from './../edit-notify.service';
import { ViewportScroller } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
import { faLock } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-artworks-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss'],
})
export class DetailsArtworksComponent implements OnInit {
  isDropDown = false;
  isDropDown2 = false;
  faLock = faLock;
  dropDownValue = Array(10).fill(null);
  htmlContent = Array(10).fill(null);

  isDropDownOpen = Array(10).fill(false);

  selectedFiles: File[] = [];

  form: FormGroup;
  id;
  permissionsObj: any = {};
  showObj: any = {};
  isSubmited = false;
  Menuinit: any;
  disableObj: any = {};

  listCurrentPage;
  listTotalPage;

  createdAt;
  updatedAt;
  isArtReadOnly = false;
  userDetails;
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private scroller: ViewportScroller,
    private editNotifyService: EditNotifyService
  ) { }
  goTop() {
    this.scroller.scrollToAnchor('scrollTop');
  }
  ngOnInit(): void {
    this.listCurrentPage = Number(localStorage.getItem('artworkCMSoffset'));
    this.listTotalPage = Number(localStorage.getItem('artworkCMStotalPage'));
    this.goTop();
    // this.artworkService.nextButton$.subscribe((data) => {
    // 	if (
    // 		data === '/artist-portal/settings/artwork/add/details' ||
    // 		data === '/artist-portal/settings/artwork/edit/' + this.id + '/details'
    // 	) {
    // 		this.onSubmit();
    // 	}
    // });
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }

    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.userDetails = JSON.parse(decode);
    this.permissionsObj = JSON.parse(decode)
    ['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Details');
    this.form = this.formBuilder.group({
      titleVariation: new FormControl(''),
      showArtistNote: new FormControl(false),
      artistNote: new FormControl(''),
      showCuratorNote: new FormControl(false),
      curatorNote: new FormControl(''),
      additionalInfo: new FormControl(''),
      signed: new FormControl(false),
      signatureDetails: new FormControl(''),
      creditLine: new FormControl(''),
      usageRestrictions: new FormControl(''),
      minted: new FormControl(false),
      nftId: new FormControl(''),
    });

    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled;
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled'] =
          ele.disabled == 'true' || ele.disabled == true ? true : false;
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
        let obj = {};
        obj[ele.name] = ele.defaultValue;
        // to patch default value
        if (!localStorage.getItem('artworkID')) {
          this.form.patchValue(obj);
        }
      }
    });
    // this.route.parent.paramMap.subscribe((params) => {
    // 	console.log(params.get('id'));

    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getDetailsData(this.id).subscribe((data) => {
    // 			const art = data;
    // 			this.form.patchValue(art);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });
    this.form.valueChanges.subscribe((x) => {
      if (this.Menuinit == null) {
        this.Menuinit = x;
      } else {
        this.editNotifyService.setOption('Details', true);
      }
    });
    console.log(this.disableObj);
  }

  async onFileSelect(files: FileList) {
    if (files[0].size < 2100000) {
      this.selectedFiles.push(files[0]);
    }
  }
  removeItem(index) {
    this.selectedFiles.splice(index, 1);
  }

  // to get artwork
  getArtWork() {
    this.server.showSpinner();
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        this.createdAt = new Date(res.data?.createdAt).toDateString();
        this.updatedAt = new Date(res.data?.updatedAt).toDateString();
        this.form.patchValue({
          titleVariation: res.data.title_variations?.trim(),
          showArtistNote: res.data.show_artist_note,
          artistNote: res.data.artist_note?.trim(), // add this key
          showCuratorNote: res.data.show_curator_note,
          curatorNote: res.data.curator_note,
          additionalInfo: res.data.additional_information,
          signatureDetails: res.data.signature_details?.trim(),
          signed: res.data.signed,
          creditLine: res.data.credit_line?.trim(),
          minted: res.data.minted,
          nftId: res.data.nft_id,
          usageRestrictions: res.data.usageRestrictions,
        });
        if (res.data.publish_artworks && (this.userDetails.role == 'REPRESENTED ARTIST' || this.userDetails.role == 'TERRAIN CONSIGNED')) {
          this.isArtReadOnly = true;
        }
      }
    });
  }

  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }

  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }

    console.log(this.form.value);
    let req = {
      title_variations: this.form.getRawValue().titleVariation,
      show_artist_note: this.form.getRawValue().showArtistNote,
      artist_note: this.form.getRawValue().artistNote, // add this key
      show_curator_note: this.form.getRawValue().showCuratorNote,
      curator_note: this.form.getRawValue().curatorNote,
      additional_information: this.form.getRawValue().additionalInfo,
      signature_details: this.form.getRawValue().signatureDetails,
      signed: this.form.getRawValue().signed,
      credit_line: this.form.getRawValue().creditLine,
      minted: this.form.getRawValue().minted,
      nft_id: this.form.getRawValue().nftId,
      usageRestrictions: this.form.getRawValue().usageRestrictions,
      tab: 'Details',
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }
    let url = apiUrl.addArtwork;
    this.server.showSpinner();
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner();
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        this.editNotifyService.reset();
        alert(res.message);
        this.isSubmited = true;
      } else {
        this.isSubmited = false;
      }
    });

    // const id = localStorage.getItem('artID');
    // const data = this.form.value;

    // if (this.id) {
    // 	this.artistInfoService
    // 		.addDetailsData(this.id, this.form.value)
    // 		.subscribe((data) => {
    // 			this.router.navigate([
    // 				'/artist-portal/settings/artwork/edit/' + this.id + '/features',
    // 			]);
    // 		});
    // } else {
    // 	this.artistInfoService
    // 		.addDetailsData(id, this.form.value)
    // 		.subscribe((data) => {
    // 			this.router.navigate([
    // 				'/artist-portal/settings/artwork/add/features',
    // 			]);
    // 		});
    // }
  }
}
