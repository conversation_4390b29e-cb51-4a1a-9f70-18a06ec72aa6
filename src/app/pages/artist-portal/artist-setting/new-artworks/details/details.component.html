<div class="container__main" #scrollTop>
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <!-- <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [ngClass]="{ 'Disabled-Color': true }"
            >
              <input
                type="text"
                placeholder="Created Date"
                [readonly]="true"
                [(ngModel)]="createdAt"
                [ngModelOptions]="{ standalone: true }"
              />
              <fa-icon class="Lock-position" [icon]="faLock"> </fa-icon>
              <div class="placeholder">Created Date</div>
            </div>
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [ngClass]="{ 'Disabled-Color': true }"
            >
              <input
                type="text"
                placeholder="Last Modified Date"
                [readonly]="true"
                [(ngModel)]="updatedAt"
                [ngModelOptions]="{ standalone: true }"
              />
              <fa-icon class="Lock-position" [icon]="faLock"> </fa-icon>
              <div class="placeholder">Last Modified Date</div>
            </div>
          </div> -->
          <div
            class="field-value"
            style="padding-right: 0.5vw"
            [hidden]="!showObj?.titleVariation"
            [ngClass]="{ 'Disabled-Color': disableObj.titleVariation }"
          >
            <input
              formControlName="titleVariation"
              type="text"
              placeholder="Only if applicable"
              [attr.disabled]="isArtReadOnly ? true : null"
            />
            <fa-icon
              class="Lock-position"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!disableObj.titleVariation && !isArtReadOnly"
            >
            </fa-icon>

            <div class="placeholder">Title Variations</div>
            <div class="input-info">
              Provide all variations of the artwork title
            </div>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.signed"
            [ngClass]="{ 'Disabled-Color': disableObj.signed }"
          >
            <div class="input-container">
              <div class="text-before">Signed:</div>
              <label class="switch">
                <input
                  formControlName="signed"
                  type="checkbox"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <span class="slider round"></span>
              </label>
              <fa-icon
                class="Lock-position-1"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.signed && !isArtReadOnly"
              >
              </fa-icon>
            </div>
            <div class="input-info">Select if the work is signed or not</div>
          </div>
          <div class="editor-head" *ngIf="form.get('signed').value">
            Signature Details
          </div>

          <div *ngIf="form.get('signed').value" class="field-value">
            <angular-editor
              id="editor27"
              [placeholder]="'Signature Details'"
              formControlName="signatureDetails"
              [config]="{ editable: !isArtReadOnly }"
              [disabled]="isArtReadOnly"
            ></angular-editor>
            <div class="input-info">
              Provide signature/incription details for the work, including
              location of signature. e.g.: Signed dated "Artist 123, 2002" lower
              right
            </div>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.creditLine"
            [ngClass]="{ 'Disabled-Color': disableObj.creditLine }"
          >
            <input
              formControlName="creditLine"
              type="text"
              placeholder="Credit Line"
              [disabled]="disableObj.creditLine"
              [attr.disabled]="isArtReadOnly ? true : null"
            />
            <fa-icon
              class="Lock-position"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!disableObj.creditLine && !isArtReadOnly"
            >
            </fa-icon>

            <div class="placeholder">Credit Line</div>
            <div class="input-info">
              Provide the credit line/ Copyright information for the artwork to
              be displayed when the image is used
            </div>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.usageRestrictions"
            [ngClass]="{ 'Disabled-Color': disableObj.usageRestrictions }"
          >
            <input
              formControlName="usageRestrictions"
              type="text"
              placeholder="Usage Restrictions"
              [disabled]="disableObj.usageRestrictions"
              [attr.disabled]="isArtReadOnly ? true : null"
            />
            <div class="placeholder">Usage Restrictions</div>
            <fa-icon
              class="Lock-position"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!disableObj.creditLine && !isArtReadOnly"
            >
            </fa-icon>
            <div class="input-info">
              Specify if there are any restrictions on how the artwork can be
              used / displayed / represented by the buyer
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              [hidden]="!showObj?.showArtistNote"
              [ngClass]="{ 'Disabled-Color': disableObj.showArtistNote }"
            >
              <div class="input-container">
                <div class="text-before">Show Artist Note:</div>
                <label class="switch">
                  <input
                    formControlName="showArtistNote"
                    type="checkbox"
                    [disabled]="disableObj.showArtistNote"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.showArtistNote && !isArtReadOnly"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether to show the Artist Note on the website
              </div>
            </div>
          </div>
          <div
            class="editor-head"
            [hidden]="!showObj?.artistNote"
            [ngClass]="{ 'Disabled-Color': disableObj.artistNote }"
          >
            Artist Note
            <fa-icon
              class="Lock-position-1"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!isArtReadOnly"
            >
            </fa-icon>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.artistNote"
            [ngClass]="{ 'Disabled-Color': disableObj.artistNote }"
          >
            <angular-editor
              id="editor25"
              [placeholder]="'Artist Note'"
              formControlName="artistNote"
              [config]="{ editable: !isArtReadOnly }"
              [disabled]="isArtReadOnly"
            ></angular-editor>
            <div class="input-info">
              Provide a short description/note about the artwork (50-80 words
              max)
            </div>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.showCuratorNote"
            [ngClass]="{ 'Disabled-Color': disableObj.showCuratorNote }"
          >
            <div class="input-container">
              <div class="text-before">Show Curator Note:</div>
              <label class="switch">
                <input
                  formControlName="showCuratorNote"
                  type="checkbox"
                  [disabled]="disableObj.showCuratorNote"
                  [attr.disabled]="isArtReadOnly ? true : null"
                />
                <span class="slider round"></span>
              </label>
              <fa-icon
                class="Lock-position-1"
                [icon]="faLock"
                title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.showCuratorNote"
              >
              </fa-icon>
            </div>
            <div class="input-info">
              Choose whether to show the Curator Note on the website
            </div>
          </div>
          <div
            class="editor-head"
            [hidden]="!showObj?.curatorNote"
            [ngClass]="{ 'Disabled-Color': disableObj.curatorNote }"
          >
            Curator Note
            <fa-icon
              class="Lock-position-1"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!isArtReadOnly"
            >
            </fa-icon>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.curatorNote"
            [ngClass]="{ 'Disabled-Color': disableObj.curatorNote }"
          >
            <angular-editor
              id="editor25"
              [placeholder]="'Curator Note'"
              formControlName="curatorNote"
              [config]="{ editable: !isArtReadOnly }"
              [disabled]="isArtReadOnly"
            ></angular-editor>
            <div class="input-info">
              Provide a short Curator Note about the work (50-100 words max) by
              a curator/critic/gallerist
            </div>
          </div>
          <div
            class="editor-head"
            [hidden]="!showObj?.additionalInfo"
            [ngClass]="{ 'Disabled-Color': disableObj.additionalInfo }"
          >
            Additional Information
            <fa-icon
              class="Lock-position-1"
              [icon]="faLock"
              title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
              [hidden]="!isArtReadOnly"
            >
            </fa-icon>
          </div>
          <div
            class="field-value"
            [hidden]="!showObj?.additionalInfo"
            [ngClass]="{ 'Disabled-Color': disableObj.additionalInfo }"
          >
            <angular-editor
              id="editor26"
              [placeholder]="'Additional Information'"
              formControlName="additionalInfo"
              [config]="{ editable: !isArtReadOnly }"
              [disabled]="isArtReadOnly"
            ></angular-editor>
            <div class="input-info">
              Provide any additional information about the artwork (not to be
              published on the site)
            </div>
          </div>

          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.minted"
              [ngClass]="{ 'Disabled-Color': disableObj.minted }"
            >
              <div class="input-with-text">
                <div class="text-before">Minted:</div>
                <label class="switch">
                  <input
                    formControlName="minted"
                    type="checkbox"
                    [disabled]="disableObj.minted"
                    [attr.disabled]="isArtReadOnly ? true : null"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock"
                  title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.minted"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether the artwork NFT has been minted or not
              </div>
            </div>
            <div *ngIf="form.get('minted').value" class="field-value">
              <input
                formControlName="nftId"
                type="text"
                placeholder="NFT ID"
                [attr.disabled]="isArtReadOnly ? true : null"
              />
              <div class="placeholder">NFT ID</div>
              <div class="input-info">Provide the NFT ID of the artwork</div>
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div
            style="display: flex; justify-content: space-between; height: 100%"
          >
            <div
              style="
                display: flex;
                justify-content: space-around;
                flex-direction: column;
                margin-top: 1.5vw;
                margin-bottom: 1.5vw;
                margin-left: 2vw;
              "
            >
              <div>
                Created Date: {{ createdAt }} ; Last Modified: {{ updatedAt }}
              </div>
            </div>
            <div class="button-group">
              <div style="margin-right: 2vw">
                Page
                {{ listCurrentPage }}
                Of {{ listTotalPage }}
              </div>
              <div (click)="onSubmit()" class="next">Save</div>
              <div (click)="getValueWithAsync()" class="next">Save & Close</div>
              <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                Duplicate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
