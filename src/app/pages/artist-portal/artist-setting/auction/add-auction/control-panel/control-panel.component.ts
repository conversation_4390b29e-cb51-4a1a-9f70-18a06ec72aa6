import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';

@Component({
  selector: 'app-control-panel',
  templateUrl: './control-panel.component.html',
  styleUrls: ['./control-panel.component.scss'],
})
export class ControlPanelComponent implements OnInit {
  form: FormGroup;
  isArtworkDetailsOpen = false;
  showInfo = true;
  auctionStatus = [
    'Not Started',
    'Express Interest',
    'Bidding',
    'Bid Won',
    'Suspended',
  ];
  PauseArray = [true, false, true, false, false];
  id = 0;
  close = faTimes;
  artworkArray = [
    { artist: '<PERSON><PERSON><PERSON>' },
    { artist: '<PERSON>' },
    { artist: 'Unnitahn' },
    { artist: 'IP<PERSON>' },
    { artist: '<PERSON><PERSON>' },
    { artist: 'CARt OOh' },
    { artist: '<PERSON>' },
    { artist: '<PERSON><PERSON><PERSON>' },
    { artist: '<PERSON>' },
    { artist: '<PERSON> Daday' },
    { artist: '<PERSON>' },
  ];
  data;
  artwork_control = [];
  artworkArrayTime = [];
  artworkStatus = [];
  timer;
  currentArtwork;
  step;
  distance;
  auctionStartTime;
  processDatas = false;
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      auctionLive: new FormControl(false),
      closeLive: new FormControl(false),
      suspendLive: new FormControl(false),
    });
    if (localStorage.getItem('auctionID')) {
      this.getExhibitionById();
    }
  }
  getExhibitionById() {
    let url = 'api/auctions' + `/${localStorage.getItem('auctionID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.data = res.data;
        this.form.patchValue({
          auctionLive: res.data?.control_panel?.auctionLive,
          closeLive: res.data?.control_panel?.closeLive,
          suspendLive: res.data?.control_panel?.suspendLive,
        });
        this.processData();
        setInterval(() => {
          this.server.getApi(url).subscribe((res) => {
            if (res.statusCode == 200) {
              this.data = res.data;

              this.data?.control_panel?.artwork_control.forEach(
                (element, index) => {
                  if (element.suspend) {
                  } else {
                  }
                  if (!element?.start && this.currentArtwork == index) {
                    this.processDatas = false;
                    clearInterval(this.timer);
                  } else if (
                    element?.start &&
                    this.currentArtwork == index &&
                    !this.processData
                  ) {
                    this.processData();
                  }
                }
              );
            }
          });
        }, 2000);
      }
    });
  }
  processData() {
    this.processDatas = true;
    this.auctionStartTime = this.data?.auctionStartTime;
    const startTime = this.data?.auctionStartTime.split(':');
    const uDate = new Date(this.data?.auctionStartDate);
    const uOffset = 60 * 1000 * new Date().getTimezoneOffset();
    const sOffset = 60 * 1000 * -330; //IST

    uDate.setMilliseconds(
      sOffset + startTime[0] * 60 * 60 * 1000 + startTime[1] * 60 * 1000
    );
    var now = new Date().getTime();
    var distance = now - uDate.getTime();
    if (!this.data?.control_panel?.artwork_control || distance < 0) {
      this.artwork_control = [];
      this.artworkStatus = [];
      this.data?.artworkSelect.forEach((element) => {
        this.artwork_control.push({
          start: true,
          pauseTime: null,
          startTime: null,
          suspend: false,
          timeleft: this.data.biddingDuration,
        });
        this.artworkStatus.push('Not Started');
      });
      // this.artwork_control = Array(res.data?.artworkSelect.length).fill({
      //   start: true,
      //   pauseTime: null,
      //   startTime: null,
      //   suspend: false,
      //   timeleft: this.data.biddingDuration,
      // });
    } else {
      this.artwork_control = this.data?.control_panel?.artwork_control;
      this.artworkStatus = [];
      this.data?.artworkSelect.forEach((element) => {
        this.artworkStatus.push('Not Started');
      });
    }
    this.timer = setInterval(() => {
      var now = new Date().getTime();
      var distance = now - uDate.getTime();
      // var days = Math.floor(distance / (1000 * 60 * 60 * 24));
      // var hours = Math.floor(
      //   (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      // );
      // var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      // var seconds = Math.floor((distance % (1000 * 60)) / 1000);

      if (distance < 0) {
        console.log('Auction has not started yet');
        this.artworkArrayTime = [];
        this.artwork_control.forEach((element, index) => {
          let days = Math.floor(this.data?.biddingDuration / (60 * 60 * 24));
          let hours =
            Math.floor(
              (this.data?.biddingDuration % (60 * 60 * 24)) / (60 * 60)
            ) +
            days * 24;
          let minutes = Math.floor(
            (this.data?.biddingDuration % (60 * 60)) / 60
          );
          let seconds = Math.floor(this.data?.biddingDuration % 60);

          this.artworkArrayTime.push(`${hours}h:${minutes}m:${seconds}s`);
        });
        //clearInterval(this.timer);
      } else {
        this.processDistance(distance / 1000);
      }
    }, 1000);
  }
  processDistance(dis) {
    const itemShowTime = 5;
    const itemShowWinner = 5;
    const startIn = 10;
    const biddingDuration = this.data?.biddingDuration;
    const expressInterestDuration = this.data?.expressInterestDuration;
    const noOfArtworks = this.data?.artworkSelect?.length;
    const totalDurationForEach =
      itemShowTime +
      biddingDuration +
      expressInterestDuration +
      itemShowWinner +
      startIn;
    const reminder = dis % totalDurationForEach;
    const quotient = Math.floor(dis / totalDurationForEach);
    if (quotient >= noOfArtworks) {
      console.log('Auction Ended');
      clearInterval(this.timer);
    } else {
      this.currentArtwork = quotient;

      for (let index = quotient - 1; index >= 0; index--) {
        this.artworkStatus[index] = 'Bid Won';
        this.artworkArrayTime[index] = `${0}h:${0}m:${0}s`;
      }

      if (reminder <= itemShowTime) {
        this.step = 0;
        console.log(`showing item ${quotient + 1}`);
      } else if (reminder <= itemShowTime + startIn) {
        this.step = 1;
        console.log(`start in ${quotient + 1}`);
      } else if (reminder <= expressInterestDuration + itemShowTime + startIn) {
        this.step = 2;
        console.log(`express interest ${quotient + 1}`);
        this.artworkStatus[quotient] = 'Express Interest';
      } else if (
        reminder <=
        expressInterestDuration + itemShowTime + biddingDuration + startIn
      ) {
        let remind =
          reminder - expressInterestDuration - itemShowTime - startIn;
        let distance = this?.data?.biddingDuration - remind;
        this.distance = distance;
        let days = Math.floor(distance / (60 * 60 * 24));
        let hours =
          Math.floor((distance % (60 * 60 * 24)) / (60 * 60)) + days * 24;
        let minutes = Math.floor((distance % (60 * 60)) / 60);
        let seconds = Math.floor(distance % 60);

        this.artworkArrayTime[quotient] = `${hours}h:${minutes}m:${seconds}s`;
        this.step = 3;
        console.log(`bidding time ${quotient + 1}`);
        this.artworkStatus[quotient] = 'Bidding';
      } else if (
        reminder <=
        expressInterestDuration +
          itemShowTime +
          biddingDuration +
          itemShowWinner +
          startIn
      ) {
        this.step = 4;
        console.log(`winner show ${quotient + 1}`);
        this.artworkStatus[quotient] = 'Bid Won';
      }
    }
  }
  ArtworkDetails() {
    this.isArtworkDetailsOpen = true;
  }
  onSubmit() {
    let req = {
      control_panel: {
        auctionLive: this.form.value.auctionLive,
        closeLive: this.form.value.closeLive,
        suspendLive: this.form.value.suspendLive,
        artwork_control: this.artwork_control,
      },
      auctionStartTime: this.auctionStartTime,
    };
    let url = localStorage.getItem('auctionID')
      ? 'api/auctions' + `/${localStorage.getItem('auctionID')}`
      : 'api/auctions';

    if (localStorage.getItem('auctionID')) {
      this.updateBlog(req, url);
      return;
    } else {
      this.server.postApi(url, req).subscribe((res) => {
        if (res.statusCode == 200) {
          localStorage.setItem('auctionID', res.data['_id']);
          alert(res.message);
        }
      });
    }
  }
  startPause(index) {
    this.artwork_control[index].start = !this.artwork_control[index].start;
    if (this.artwork_control[index].start) {
      this.artwork_control[index].startTime = new Date().getTime();
      const diff =
        this.artwork_control[index].startTime -
        this.artwork_control[index].pauseTime;
      let days = Math.floor(diff / (1000 * 60 * 60 * 24));
      let hours =
        Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)) +
        days * 24;
      let minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      let seconds = Math.floor((diff % (1000 * 60)) / 1000);
      const startTime = this.data?.auctionStartTime.split(':');
      this.auctionStartTime = `${Number(startTime[0]) + hours}:${
        Number(startTime[1]) + minutes
      }`;
    } else {
      this.artwork_control[index].pauseTime = new Date().getTime();
      this.artwork_control[index].timeleft = this.distance;
    }
    console.log(this.artwork_control);
  }

  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('auctionID', res.data['_id']);
        alert(res.message);
        // this.router.navigate(['artist-portal/settings/exhibitions'])
      }
    });
  }
}
