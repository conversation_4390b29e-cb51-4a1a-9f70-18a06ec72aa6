<div class="content__section">
  <div class="profile-field">
    <div class="w-100 field-contents">
      <form [formGroup]="form">
        <!-- <div class="splitter"> -->

        <div class="field-value" style="margin-bottom: 2vw">
          <div class="input-with-text">
            <div class="text-before">Mark Auction as Live:</div>
            <label class="switch">
              <input formControlName="auctionLive" type="checkbox" />
              <span class="slider round"></span>
            </label>
          </div>
          <div class="input-info">Choose whether to make your auction live</div>
        </div>
        <div class="field-value" style="margin-bottom: 2vw">
          <div class="input-with-text">
            <div class="text-before">Suspend Auction:</div>
            <label class="switch">
              <input
                type="checkbox"
                formControlName="suspendLive"
                [disabled]="form.get('auctionLive').value == false"
              />
              <span class="slider round"></span>
            </label>
          </div>
          <div class="input-info">
            Choose whether to Suspend the live auction
          </div>
        </div>
        <div class="field-value" style="margin-bottom: 2vw">
          <div class="input-with-text">
            <div class="text-before">Close Auction:</div>
            <label class="switch">
              <input
                type="checkbox"
                formControlName="closeLive"
                [disabled]="form.get('auctionLive').value == false"
              />
              <span class="slider round"></span>
            </label>
          </div>
          <div class="input-info">Choose whether to Close the live auction</div>
        </div>

        <div
          class="Marketplace-Table-Content Mui-Table"
          style="margin-bottom: 2vw"
        >
          <div class="filter-section"></div>

          <table style="width: 100%">
            <tr>
              <th class="Mui-thead" style="padding-left: 1vw; width: 10%">
                Lot No.
              </th>
              <th class="Mui-thead" style="width: 15%">Artwork Title</th>
              <th class="Mui-thead" style="text-align: center; width: 15%">
                Artist name
              </th>
              <th
                class="Mui-thead"
                style="text-align: center; padding-right: 1vw; width: 10%"
              >
                Auction Status
              </th>
              <th
                class="Mui-thead"
                style="text-align: center; padding-right: 1vw; width: 10%"
              >
                Start / Pause
              </th>
              <th
                class="Mui-thead"
                style="text-align: center; padding-right: 1vw; width: 10%"
              >
                Time Remaining
              </th>
              <th
                class="Mui-thead"
                style="text-align: right; padding-right: 1vw; width: 10%"
              >
                Suspend from Auction
              </th>
            </tr>
            <tr
              class="Mui-trow"
              *ngFor="let item of data?.artworkSelect; let i = index"
            >
              <td
                class="Mui-td"
                style="
                  padding-left: 1vw;
                  border-top-left-radius: 0.555vw;
                  border-bottom-left-radius: 0.555vw;
                "
              >
                <div style="display: flex; align-items: center">
                  <!-- <div
                    class="img-thumbnaill"
                    style="width: 3.333vw; height: 3vw"
                  >
                    <img
                      src="../../../../../../../assets/5.jpg"
                      style="width: 100%; height: 100%; object-fit: contain"
                      alt="thumbnail"
                    />
                  </div> -->

                  <span> #{{ i + 1 }} / {{ data?.artworkSelect?.length }}</span>
                </div>
              </td>
              <td class="Mui-td" *ngIf="!isMobile">
                {{ item?.artwork_title }}
              </td>
              <td class="Mui-td" style="text-align: center">
                {{ item?.artist_id?.display_name }}
              </td>
              <!-- <td class="Mui-td" style="text-align: center">
                 <p
                  style="
                    margin-bottom: 0;
                    color: var(--tertiary-font-color);
                    cursor: pointer;
                  "
                  (click)="ArtworkDetails()"
                >
                  View
                </p>
              </td> -->

              <td class="Mui-td" style="text-align: center">
                {{ artworkStatus[i] }}
              </td>
              <td class="Mui-td" style="text-align: center">
                <button
                  (click)="startPause(i)"
                  class="startButton"
                  [ngClass]="{
                    disabled: artworkStatus[i] != 'Bidding'
                  }"
                  [disabled]="artworkStatus[i] != 'Bidding'"
                >
                  {{ artwork_control[i].start ? "Pause" : "Start" }}
                </button>
              </td>
              <td class="Mui-td" style="text-align: center">
                {{ artworkArrayTime[i] }}
              </td>

              <td
                class="Mui-td"
                style="
                  text-align: right;
                  padding-right: 1vw;
                  border-top-right-radius: 0.555vw;
                  border-bottom-right-radius: 0.555vw;
                "
              >
                <button
                  (click)="
                    !artwork_control[i].suspend &&
                      (artwork_control[i].suspend = true)
                  "
                  class="startButton"
                  [ngClass]="{
                    disabled:
                      artwork_control[i].suspend ||
                      artworkStatus[i] == 'Bid Won' ||
                      artworkStatus[i] == 'Bidding'
                  }"
                  [disabled]="
                    artwork_control[i].suspend ||
                    artworkStatus[i] == 'Bid Won' ||
                    artworkStatus[i] == 'Bidding'
                  "
                >
                  suspend
                </button>
              </td>
            </tr>
          </table>
        </div>
      </form>
      <div class="section section-margin-top"></div>
      <div class="footer-nav">
        <div class="button-group">
          <div (click)="onSubmit()" class="next">Save</div>
        </div>
      </div>
    </div>
  </div>
</div>
