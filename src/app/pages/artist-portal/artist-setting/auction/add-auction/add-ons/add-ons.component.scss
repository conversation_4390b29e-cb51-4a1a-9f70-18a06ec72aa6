.container__main {
    //padding-right: calc(36.111vw - var(--page-default-margin));
    padding-bottom: 6.3vw;
    .main__heading {
        font-size: 2.083vw;
        margin-bottom: 2.083vw;
    }
    .sub__text {
        font-size: 1.111vw;
        font-family: var(--secondary-font);
        margin-bottom: 3.472vw;
    }
    .form__info {
        display: flex;
        // margin-bottom: 2.222vw;
        .icon__history {
            img {
                width: 1.527vw;
                height: 1.388vw;
                margin-right: 0.902vw;
            }
        }
        .info {
            font-size: 0.972vw;
            color: var(--quaternary-font-color);
            //   span {
            // font-size: 0.972vw;
            // color: var(--quaternary-font-color);
            .blue {
                color: var(--tertiary-font-color);
            }
            //   }
        }
    }
    .profile-field {
        .field-contents {
            .field-heading {
                margin-top: 2.78vw;
                margin-bottom: 0;
                font-family: var(--secondary-font);
                font-size: 1.25vw;
            }
            .double {
                width: 35vw;
                .input-double {
                    width: 17.01vw !important;
                }
            }
            .field-value {
                margin-top: 2.08vw;
                position: relative;
                //display: flex;
                justify-content: start;
                align-items: center;
                &.doted {
                    border: 0.069vw dotted var(--timeline-color);
                    padding-left: 1vw;
                    padding-right: 1vw;
                    padding-bottom: 0.5vw;
                }
                input[type="text"] {
                    background: transparent;
                    font-family: var(--secondary-font);
                    border: none;
                    border: 0.069vw solid var(--timeline-color);
                    border-radius: 0.14vw;
                    padding: 1.04vw 1.11vw;
                    width: 100%;
                    height: 3.47vw;
                }
                input[type="number"] {
                    background: transparent;
                    font-family: var(--secondary-font);
                    border: none;
                    border: 0.069vw solid var(--timeline-color);
                    border-radius: 0.14vw;
                    padding: 1.04vw 1.11vw;
                    width: 100%;
                    height: 3.47vw;
                }
                input[type="date"] {
                    background: transparent;
                    font-family: var(--secondary-font);
                    border: none;
                    border: 0.069vw solid var(--timeline-color);
                    border-radius: 0.14vw;
                    padding: 1.04vw 1.11vw;
                    width: 35vw;
                    height: 3.47vw;
                    color: var(--quaternary-font-color);
                    //   ::placeholder{
                    // color: var(--quaternary-font-color);
                    // }
                }
                input[type="password"] {
                    background: transparent;
                    font-family: var(--secondary-font);
                    border: none;
                    border: 0.069vw solid var(--timeline-color);
                    border-radius: 0.14vw;
                    padding: 1.04vw 1.11vw;
                    width: 35vw;
                    height: 3.47vw;
                }
                .input-container {
                    width: 100%;
                    position: relative;
                    display: inline-flex;
                    justify-content: start;
                    align-items: center;
                    .flag-icon {
                        position: absolute;
                        left: 1.04vw;
                    }
                    button {
                        outline: none;
                        background: none;
                        border: none;
                    }
                    .flag-arrow {
                        position: absolute;
                        max-width: 0.69vw;
                        height: auto;
                        right: 2.08vw;
                    }
                    .division {
                        position: absolute;
                        width: 0.069vw;
                        height: 1.04vw;
                        background-color: var(--timeline-color);
                        left: 3.33vw;
                    }
                    input[type="text"] {
                        // background: transparent;
                        font-family: var(--secondary-font);
                        border: 0.069vw solid var(--timeline-color);
                        padding: 1.04vw 1.11vw 1.04vw 4.2vw;
                        border-radius: 0.14vw;
                        height: 3.47vw;
                        width: 100%;
                        z-index: 0;
                        &.selection {
                            padding: 1.04vw 1.11vw 1.04vw 1.04vw;
                        }
                    }
                    .dropdown-visible {
                        background-color: var(--primary-background-color);
                        visibility: visible;
                        position: absolute;
                        top: 3.47vw;
                        z-index: 1;
                        box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
                        width: 100%;
                        @media (max-width: 768px) {
                            top: 10.47vw;
                        }
                        ul {
                            list-style: none;
                            padding: 0.69vw 0;
                            max-height: 27.97vw;
                            margin: 0;
                            overflow: hidden;
                            overflow-y: scroll;
                            @media (max-width: 768px) {
                                padding: 1.69vw 0;
                            }
                            li {
                                padding: 0.69vw 1.04vw;
                                width: 34.9vw;
                                display: flex;
                                @media (max-width: 768px) {
                                    padding: 1.69vw 1.04vw;
                                }
                                .country-name {
                                    margin-left: 0.69vw;
                                    @media (max-width: 768px) {
                                        font-size: 3.38vw;
                                    }
                                }
                            }
                            li:hover {
                                background-color: var(--timeline-color);
                            }
                        }
                        ul::-webkit-scrollbar {
                            display: none;
                        }
                    }
                    .dropdown-hidden {
                        display: none;
                    }
                }
                .ph-flag {
                    height: 1.25vw;
                    // padding-right: 0.62vw;
                    // border-right: solid 0.09vw var(--quaternary-font-color);
                }
                .placeholder {
                    position: absolute;
                    top: -0.4vw;
                    left: 1.04vw;
                    font-size: 0.8333vw;
                    color: var(--quaternary-font-color);
                    padding: 0 0.3vw;
                    background-color: var(--primary-background-color);
                    // background-color: #ededf1;
                }
                .send {
                    margin-left: 2.08vw;
                    color: var(--tertiary-font-color);
                }
            }
            .verify {
                margin-top: 2.78vw;
                font-family: var(--secondary-font);
                font-size: 1.25vw;
            }
            .partitioned {
                margin-top: 1.33vw;
                outline: none;
                padding-left: 0.8vw;
                letter-spacing: 0;
                border: 0;
                background-image: linear-gradient( to left, var(--timeline-color) 70%, rgba(255, 255, 255, 0) 0%);
                background-position: bottom;
                background-size: 3.2vw 0.069vw;
                width: 3.2vw;
                background-repeat: repeat-x;
                background-position-x: 2.2vw;
                height: 2vw;
                padding-bottom: 0.35vw;
                font-family: var(--secondary-font);
            }
            .last-changed {
                margin-top: 1.04vw;
                font-size: 0.97vw;
                color: var(--quaternary-font-color);
            }
        }
        .buttonList {
            margin-top: 3.47vw;
            margin-bottom: 1.042vw;
            .save {
                // display: block;
                width: 100%;
                outline: none;
                font-size: 1.25vw;
                // width: 20.56vw;
                background-color: transparent;
                color: var(--tertiary-font-color);
                padding: 0.833333vw 12.08333vw;
                border: 0.069vw solid var(--tertiary-font-color);
                border-radius: 1.46vw;
            }
            .save:hover {
                font-weight: 500;
            }
        }
    }
    .splitter {
        display: flex;
        // justify-content: space-between;
        .field-value {
            width: 50%;
        }
    }
}

.upload-head {
    margin-top: 3.47vw;
    font-size: 1.25vw;
    @media (max-width: 768px) {
        font-size: 4.34vw;
    }
}

.splitter {
    display: flex;
    // justify-content: space-between;
    .field-value {
        width: 50%;
    }
}

.save {
    // display: block;
    //width: 4vw;
    outline: none;
    font-size: 1.25vw;
    // width: 20.56vw;
    background-color: transparent;
    color: var(--tertiary-font-color);
    padding: 0.833333vw 3vw;
    border: 0.069vw solid var(--tertiary-font-color);
    border-radius: 1.46vw;
    @media (max-width: 768px) {
        font-size: 3.86vw;
        margin-bottom: 4.83vw;
        border-radius: 10.1vw;
        padding: 3.45vw 6.89vw;
    }
}

label .check {
    width: 1.25vw;
    height: 1.25vw;
    margin-right: 0.7vw;
    // border: 0.0069vw solid var(--checkbox-border-color);
    border-radius: 50%;
    position: relative;
    background-color: transparent;
    transition: all 0.15s cubic-bezier(0, 1.05, 0.72, 1.07);
    background-image: url("../../../../../../../assets/icons/checkbox/<EMAIL>");
    background-size: 1.25vw;
    &::after {
        content: "";
        width: 0.7vw;
        height: 0.7vw;
        border-radius: 50%;
        opacity: 0;
        position: absolute;
        // background-color: var(--tertiary-font-color);
        background-image: url("../../../../../../../assets/icons/check/<EMAIL>");
        background-size: 0.7vw;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

.check-label {
    margin-bottom: 0;
    .colorRad {
        border: 0.0069vw solid var(--primary-border-color);
        outline: none;
        width: 1.56vw;
        height: 1.56vw;
        border-radius: 50%;
        margin-right: 0.8vw;
    }
    .title {
        margin-right: 2.08vw;
    }
}

input[type="radio"] {
    display: none;
}

input {
    &:checked {
        ~.check {
            &::after {
                opacity: 1;
            }
        }
    }
}

.file-upload {
    height: 100%;
    border-radius: 0.07vw;
    border: solid 0.07vw #ced4db;
    display: flex;
    justify-content: start;
    flex-direction: column;
    .icon {
        margin-top: 2.08vw;
        img {
            height: 1.38vw;
            width: 1.38vw;
        }
    }
    .text-content {
        text-align: center;
        margin-top: 1.38vw;
        width: 20.83vw;
        position: relative;
        padding-right: 1vw;
        padding-left: 1vw;
        .title {
            font-size: 1.11vw;
        }
        .sub-title {
            color: #808080;
            margin-top: 0.69vw;
            font-size: 0.97vw;
        }
        .close {
            position: absolute;
            right: 0;
            top: 0;
            padding: 0.5vw;
            img {
                cursor: pointer;
                width: 0.69vw;
                height: 0.69vw;
            }
        }
    }
}

.button-container {
    margin-top: 2.08vw;
    margin-bottom: 2.08vw;
    .button {
        width: 13.33vw;
        height: 3.05vw;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 1.49vw;
        border: solid 0.069vw #004ddd;
        color: #004ddd;
    }
}

.upload-input {
    position: absolute;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    outline: none;
    opacity: 0;
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 2.6vw;
    height: 1.11vw;
    margin-bottom: 0 !important;
}


/* Hide default HTML checkbox */

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}


/* The slider */

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 1vw;
    width: 1vw;
    left: 0.06vw;
    bottom: 0.06vw;
    background-color: white;
    transition: 0.4s;
}

input:checked+.slider {
    background-color: var(--tertiary-font-color);
}

input:focus+.slider {
    box-shadow: 0 0 1px var(--tertiary-font-color);
}

input:checked+.slider:before {
    transform: translateX(1.4vw);
}


/* Rounded sliders */

.slider.round {
    border-radius: 3vw;
}

.slider.round:before {
    border-radius: 50%;
}

.text-before {
    margin-right: 0.5vw;
}

.input-with-text {
    position: relative;
    display: flex;
    justify-content: start;
    align-items: center;
}

.input-info {
    font-size: 0.95vw;
    margin-top: 0.5vw;
}

.wrap-collabsible {
    margin: 1.2rem 0;
    input[type="checkbox"] {
        display: none;
    }
    .lbl-toggle {
        display: block;
        padding: 1rem;
        border: 0.069vw solid var(--timeline-color);
        cursor: pointer;
        transition: all 0.25s ease-out;
    }
    .collapsible-content {
        max-height: 0px;
        overflow: hidden;
        transition: max-height 0.25s ease-in-out;
    }
    .toggle:checked+.lbl-toggle+.collapsible-content {
        max-height: 800px;
    }
    .toggle:checked+.lbl-toggle {
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
    }
    .collapsible-content .content-inner {
        padding: 0.5rem 1rem;
    }
    .collapsible-content p {
        margin-bottom: 0;
    }
}

.modal {
    //   display: none; /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 1;
    /* Sit on top */
    padding-top: 17.36vw;
    /* Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    //   overflow:; /* Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4);
    /* Black w/ opacity */
}

.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 20px;
    border: 1px solid #888;
    height: 15.972vw;
    width: 31.25vw;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    .buttonKeeper {
        margin-top: 3vw;
        .btnn {
            margin: 0 1vw;
            outline: none;
            font-size: 1.111vw;
            background-color: transparent;
            color: var(--tertiary-font-color);
            padding: 0.5vw 1.5vw;
            border: 0.069vw solid var(--tertiary-font-color);
            border-radius: 1.46vw;
        }
        .btnn:hover,
        .btnn:focus {
            font-weight: 500;
        }
    }
    p {
        font-size: 1.25vw;
        font-family: var(--primary-font);
    }
}

.contain_ {
    margin-top: 1vw;
    .selection_container {
        height: 250px;
        width: 100%;
        display: flex;
        // align-items: center;
        // justify-content: center;
        flex-direction: column;
        .selection_headings {
            height: 20%;
            background-color: var(--secondary-background-color);
            border-top-left-radius: 0.14vw;
            border-top-right-radius: 0.14vw;
            display: flex;
            flex-direction: row;
            .property_title {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1 2 auto;
                font-family: var(--secondary-font);
                font-weight: 500;
            }
        }
        .selection_data {
            // background-color: #bbba;
            height: 80%;
            border-bottom-left-radius: 0.14vw;
            border-bottom-right-radius: 0.14vw;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            .artwork_details {
                height: 2.777vw;
                border-bottom: 1px solid var(--primary-border-color);
                display: flex;
                flex-direction: row;
                .properties {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img {
                        width: 2vw;
                        height: 2vw;
                        object-fit: cover;
                        border-radius: 0.14vw;
                        cursor: pointer;
                    }
                    .close_icon {
                        font-size: 2vw;
                        margin: 0;
                        cursor: pointer;
                    }
                }
            }
        }
        .box {
            width: 10%;
        }
        .box2 {
            width: 20%;
        }
    }
    .select {
        margin: 2vw 0 1vw 0;
        outline: none;
        font-size: 1.042vw;
        // width: 20.56vw;
        background-color: transparent;
        color: var(--tertiary-font-color);
        padding: 0.833333vw 1vw;
        border: 0.069vw solid var(--tertiary-font-color);
        border-radius: 0.46vw;
    }
}

.artworkModal {
    // display: block; /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 1;
    /* Sit on top */
    padding-top: 8vw;
    /* Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    //   overflow:; /* Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4);
    /* Black w/ opacity */
}

.artwork-modal-content {
    background-color: #fefefe;
    margin: auto;
    height: 30vw;
    width: 40vw;
    border-radius: 0.14vw;
    .Addselection_container {
        height: 100%;
        display: flex;
        margin: 1vw .7vw;
        flex-direction: column;
        .Addtitle_bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1vw 0;
            .Addclose_icon {
                font-size: 2vw;
                margin: 0;
                cursor: pointer;
                float: right;
            }
        }
        .Addselection_headings {
            background-color: var(--secondary-background-color);
            border-top-left-radius: 0.14vw;
            border-top-right-radius: 0.14vw;
            display: flex;
            flex-direction: row;
            .Addproperty_title {
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1 2 auto;
                font-family: var(--secondary-font);
                font-weight: 500;
                font-size: 1.042vw;
                padding: 1vw 0;
                input[type="checkbox"] {
                    width: 1.2vw;
                    height: 1.2vw;
                }
            }
        }
        .Addfooter_bar {
            .Addselect {
                margin: 1vw 0;
                outline: none;
                font-size: 1.042vw;
                // width: 20.56vw;
                background-color: transparent;
                color: var(--tertiary-font-color);
                padding: 0.5vw 1vw;
                border: 0.05vw solid var(--tertiary-font-color);
                border-radius: 0.46vw;
            }
        }
        .Addsearch_bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: row;
            margin-bottom: 1vw;
            .filter_container {
                display: flex;
                // justify-content: flex-end;
            }
        }
        .Addselection_data {
            // background-color: #bbba;
            flex-grow: 3;
            border-bottom-left-radius: 0.14vw;
            border-bottom-right-radius: 0.14vw;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            // background-color: #888;
            .Addartwork_details {
                height: 2.777vw;
                border-bottom: 1px solid var(--primary-border-color);
                display: flex;
                flex-direction: row;
                font-size: 1.042vw;
                .Addproperties {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                    input[type="checkbox"] {
                        width: 1.2vw;
                        height: 1.2vw;
                    }
                    img {
                        width: 2vw;
                        height: 2vw;
                        object-fit: cover;
                        border-radius: 0.14vw;
                        cursor: pointer;
                    }
                }
            }
        }
        .Addbox {
            width: 12.5%;
        }
        .Addbox2 {
            width: 25%;
        }
    }
    .fieldd-value {
        position: relative;
        display: flex;
        justify-content: start;
        align-items: center;
        input[type="text"] {
            background: transparent;
            font-family: var(--secondary-font);
            border: none;
            border: 0.069vw solid var(--timeline-color);
            border-radius: 0.14vw;
            padding: 1.04vw 1.11vw;
            width: 70%;
            height: 2.47vw;
        }
        .input-container {
            // width: 70%;
            position: relative;
            display: inline-flex;
            justify-content: start;
            align-items: center;
            .flag-icon {
                position: absolute;
                left: 1.04vw;
            }
            button {
                outline: none;
                background: none;
                border: none;
            }
            .flag-arrow {
                position: absolute;
                max-width: 0.69vw;
                height: auto;
                right: 2.08vw;
            }
            .division {
                position: absolute;
                width: 0.069vw;
                height: 1.04vw;
                background-color: var(--timeline-color);
                left: 3.33vw;
            }
            input[type="text"] {
                // background: transparent;
                font-family: var(--secondary-font);
                border: 0.069vw solid var(--timeline-color);
                padding: 1.04vw 1.11vw 1.04vw 4.2vw;
                border-radius: 0.14vw;
                // height: 3.47vw;
                width: 100%;
                // padding: 1.04vw 1.11vw;
                // width: 70%;
                height: 2.47vw;
                z-index: 0;
                &.selection {
                    padding: 1.04vw 1.11vw 1.04vw 1.04vw;
                }
            }
            .dropdown-visible {
                background-color: var(--primary-background-color);
                visibility: visible;
                position: absolute;
                top: 3.47vw;
                z-index: 1;
                box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
                width: 92%;
                @media (max-width: 768px) {
                    top: 10.47vw;
                }
                ul {
                    list-style: none;
                    padding: 0.69vw 0;
                    max-height: 27.97vw;
                    margin: 0;
                    max-width: 100%;
                    overflow: hidden;
                    overflow-y: scroll;
                    @media (max-width: 768px) {
                        padding: 1.69vw 0;
                    }
                    li {
                        padding: 0.69vw 1.04vw;
                        width: 100%;
                        display: flex;
                        @media (max-width: 768px) {
                            padding: 1.69vw 1.04vw;
                        }
                        .country-name {
                            margin-left: 0.69vw;
                            @media (max-width: 768px) {
                                font-size: 3.38vw;
                            }
                        }
                    }
                    li:hover {
                        background-color: var(--timeline-color);
                    }
                }
                ul::-webkit-scrollbar {
                    display: none;
                }
            }
            .dropdown-hidden {
                display: none;
            }
        }
        .ph-flag {
            height: 1.25vw;
            // padding-right: 0.62vw;
            // border-right: solid 0.09vw var(--quaternary-font-color);
        }
        .placeholder {
            position: absolute;
            top: -0.4vw;
            left: 1.04vw;
            font-size: 0.8333vw;
            color: var(--quaternary-font-color);
            padding: 0 0.3vw;
            background-color: var(--primary-background-color);
            // background-color: #ededf1;
        }
        .send {
            margin-left: 2.08vw;
            color: var(--tertiary-font-color);
        }
    }
}

.dropdown-collapse {
    border: 1px solid gray;
    padding: 12px;
    width: 97%;
    overflow-y: scroll;
    max-height: 250px;
}

#option {
    background: gray;
    color: white;
    padding: 10px;
    cursor: pointer
}

.outer-border {
    border: 1px solid gray;
    padding: 15px;
    margin-bottom: 5px;
    margin-top: 5px;
    cursor: grab
}

.eye-icon {
    font-size: 20px;
    // margin-bottom: 15px;
    // margin-left: 600px;
    float: right
}

.cross-icon {
    cursor: pointer
}

.FlexEnd {
    display: flex;
    justify-content: end;
}

.SwitchInBar {
    margin-top: 0.5vw;
    margin-right: 2vw;
    label {
        margin-left: 0.5vw;
        margin-right: 0.5vw;
    }
}

.mr2 {
    margin-right: 2px
}

.input-container-head {
    width: 100%;
    position: relative;
    justify-content: start;
    align-items: center;
}

.ml20 {
    margin-left: 20px
}

.drag-icon {
    margin-top: 4px;
    font-size: 20px;
}

.main-title {
    margin-top: 5px;
    font-size: 15px;
}

.footer-nav {
    height: 6.3vw;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0);
    @media (max-width: 768px) {
        padding-bottom: 11.24vw;
    }
    .button-group {
        //margin-left: 21.66vw;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 1.25vw;
        @media (max-width: 768px) {
            font-size: 4.34vw;
            margin-left: 0vw;
            align-items: unset;
        }
        .next {
            margin: 0 0.5vw;
            width: 9vw;
            height: 3.05vw;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1.49vw;
            border: solid 0.069vw #004ddd;
            color: #004ddd;
            @media (max-width: 768px) {
                width: 46.37vw;
                height: 10.62vw;
                border-radius: 6.08vw;
            }
        }
        .back {
            margin-left: 2.77vw;
            color: #808080;
        }
    }
}