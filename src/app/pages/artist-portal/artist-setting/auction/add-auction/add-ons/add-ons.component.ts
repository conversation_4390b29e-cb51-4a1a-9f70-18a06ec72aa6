import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import {
  faEye,
  faEyeSlash,
  faTrash,
  faGripVertical,
} from '@fortawesome/free-solid-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-add-ons',
  templateUrl: './add-ons.component.html',
  styleUrls: ['./add-ons.component.scss'],
})
export class AddOnsComponent implements OnInit {
  faEllipsisV = faGripVertical;
  faEye = faEye;
  faEyeSlash = faEyeSlash;
  faTrash = faTrash;
  dynamicForm: FormGroup;
  selectedIndex: any;
  dataObj: any = {};
  constructor(
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private router: Router
  ) {}
  isFilterDropdownOpen = false;
  isFilterDropdownOpen1 = false;
  isDropDownOpen;
  isPopupOpen: boolean = false;
  artistMediaType: any = 'all';
  isArtworkPopupOpen;
  isArtworkDropdownOpen1;
  isArtworkDropdownOpen2;
  isArtworkDropdownOpen3;
  isQuoteMediaDropdownOpen1;
  isQuoteMediaDropdownOpen2;
  isQuoteMediaDropdownOpen3;
  isArtistProfile2DropdownOpen;
  isBannerDropdownOpen;
  artistMedia1 = Array(10).fill(false);
  bannerType1 = Array(10).fill(false);
  griddrop1 = Array(10).fill(false);
  imageTypeDrop = Array(10).fill(false);
  textPosition = Array(10).fill(false);
  componentName: string;
  postion = Array(10).fill(false);
  bannerTypeIndexed = [];
  form: FormGroup;
  selectedFiles: File[][] = [
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
    [],
  ];
  ForDeleteComponentName;
  ForDeleteIndex;
  MediaTypeImage = {
    banner: [false, false],
    titleBanner: false,
    quoteMedia: null,
    artistMedia: null,
  };
  placeholder: any = '';
  selectedFilesObj: any = {
    quoteImageArr: [],
    audioArr: [],
    artistMediaTypeArr: [],
    bannerArr: [],
    imageUrls: [],
    gridUrls: [],
    artistProfileImageArr: [],
  };
  accept: any = 'all';

  // new
  componentSelector: any = 'Choose Component';
  showdrop: boolean = false;

  ngOnInit(): void {
    this.initialiseForm();
    if (localStorage.getItem('auctionID')) {
      this.getExhibitionById();
    }
    this.isArtworkPopupOpen = false;
    this.isArtworkDropdownOpen1 = Array(10).fill(false);
    this.isArtworkDropdownOpen2 = Array(10).fill(false);
    this.isArtworkDropdownOpen3 = Array(10).fill(false);

    this.isQuoteMediaDropdownOpen1 = Array(10).fill(false);
    this.isQuoteMediaDropdownOpen2 = Array(10).fill(false);
    this.isQuoteMediaDropdownOpen3 = Array(10).fill(false);
  }
  ComponentSelection = {
    titleBanner: false,
    description: false,
    introVideo: false,
    quote: false,
    audio: false,
    artistProfile1: false,
    artistProfile2: false,
    quoteMedia: false,
    artworkSelect: false,
    bannerDivide: false,
  };
  repeat = ['artworkSelect', 'bannerDivide', 'quote', 'quoteMedia'];
  setDeleteSelection(componentname, index) {
    switch (componentname) {
      case 'artworkSelect':
        this.ForDeleteComponentName = 'artworkSelect';
        this.ForDeleteIndex = index;
        break;
      case 'quote':
        this.ForDeleteComponentName = 'quote';
        this.ForDeleteIndex = index;
        break;
      case 'quoteMedia':
        this.ForDeleteComponentName = 'quoteMedia';
        this.ForDeleteIndex = index;
        break;
      case 'artistProfileMedia':
        this.ForDeleteComponentName = 'artistProfileMedia';
        this.ForDeleteIndex = index;
        break;
      case 'bannerDivide':
        if (this.selectBannerDivide.length < 2) {
          this.ForDeleteComponentName = 'bannerDivide';
          this.ForDeleteIndex = index;
        }
        break;

      default:
        break;
    }
    this.isPopupOpen = true;
  }
  ComponentSelect(componentName, dropdownName) {
    this.form.get('componentSelector').setValue(dropdownName);
    this.componentName = componentName;
  }
  deleteComponent() {
    if (
      this.repeat.includes(this.ForDeleteComponentName) ||
      this.ForDeleteComponentName == 'artistProfileMedia'
    ) {
      switch (this.ForDeleteComponentName) {
        case 'artworkSelect':
          this.selectArtwork.removeAt(this.ForDeleteIndex);
          break;
        case 'quote':
          this.selectQuote.removeAt(this.ForDeleteIndex);
          break;
        case 'quoteMedia':
          this.selectQuoteMedia.removeAt(this.ForDeleteIndex);
          break;
        case 'artistProfileMedia':
          this.selectArtistMedia2.removeAt(this.ForDeleteIndex);
          break;
        case 'bannerDivide':
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.removeAt(this.ForDeleteIndex);
          }
          break;

        default:
          break;
      }

      this.isPopupOpen = false;
    }
  }
  addComponent(componentName) {
    if (
      this.ComponentSelection[componentName] &&
      this.repeat.includes(componentName)
    ) {
      switch (componentName) {
        case 'artworkSelect':
          this.selectArtwork.push(
            this.formBuilder.group({
              showArtworks: true,
              artworkGrid: { value: '', disabled: true },
              artworkInterior: { value: 'Artwork Interior', disabled: true },
              artistSelect: { value: 'Artwork Select', disabled: false },
              showPrice: true,
            })
          );
          break;
        case 'quote':
          this.selectQuote.push(this.formBuilder.group({ showQuote: true }));
          break;
        case 'quoteMedia':
          this.selectQuoteMedia.push(
            this.formBuilder.group({
              showQuoteMedia: true,
              quoteMediaPosition: {
                value: 'Quote Media Position',
                disabled: true,
              },
              quoteMediaBackground: {
                value: 'Quote Media Background',
                disabled: true,
              },
              quoteMedia: { value: 'Quote Media', disabled: true },
            })
          );
          break;
        case 'bannerDivide':
          if (this.selectBannerDivide.length < 2) {
            this.selectBannerDivide.push(
              this.formBuilder.group({
                showBanner: true,
                bannerType: { value: 'Banner Type', disabled: true },
                showTitle: true,
                showButtons: true,
                showButton2: true,
                showButton3: true,
              })
            );
          }
          break;

        default:
          break;
      }
    } else this.ComponentSelection[componentName] = true;
  }
  bannerTypeSelect(type, index) {
    this.selectBannerDivide.controls[index].get('bannerType').setValue(type);
    this.bannerTypeIndexed[index] =
      this.selectBannerDivide.controls[index].get('bannerType').value;
    if ((type = 'Video')) {
      this.MediaTypeImage.banner[index] = false;
    } else {
      this.MediaTypeImage.banner[index] = true;
    }
  }
  artworkSelector(index, name) {
    this.selectArtwork.controls[index].get('artworkGrid').setValue(name);
  }
  artistSelector(index, name) {
    this.selectArtwork.controls[index].get('artistSelect').setValue(name);
  }
  addRepeater1() {
    this.selectArtistMedia2.push(
      this.formBuilder.group({
        showUrl: true,
        position: { value: 'Position', disabled: true },
      })
    );
  }
  get selectArtwork(): FormArray {
    return <FormArray>this.form.get('selectArtwork');
  }
  get selectQuoteMedia(): FormArray {
    return <FormArray>this.form.get('selectQuoteMedia');
  }
  get selectQuote(): FormArray {
    return <FormArray>this.form.get('selectQuote');
  }
  get selectArtistMedia2(): FormArray {
    return <FormArray>this.dynamicForm.get('selectArtistMedia2');
  }
  get selectBannerDivide(): FormArray {
    return <FormArray>this.form.get('selectBannerDivide');
  }

  async onFileSelect(index: number, files: FileList) {
    console.log(index);

    if (files[0].size < 2100000) {
      this.selectedFiles[index].push(files[0]);
    }
    console.log(this.selectedFiles);
  }

  // new code starts from here
  onComponentSelect(componentName, dropdownName) {
    this.componentName = componentName;
    this.componentSelector = dropdownName;
    this.showdrop = true;
  }

  initialiseForm() {
    this.dynamicForm = this.formBuilder.group({
      mainArr: this.formBuilder.array([]),
      filter: new FormControl({ value: 'ID', disabled: true }),
      sort: new FormControl({ value: 'Sort', disabled: true }),
    });
  }

  // to add into profile array
  addIntoProfileArr() {
    this.artistProfileArr.push(
      this.formBuilder.group({
        prifileImageSub: new FormControl(''),
        artistProfileText: new FormControl(''),
        artistName: new FormControl(''),
        showUrl: new FormControl(false),
        showUrlArtistProfile: new FormControl(''),
        positionArtistProfile: new FormControl(''),
      })
    );
  }

  addNewComponent() {
    console.log(this.componentName);
    if (this.componentName == 'Choose Component' || !this.componentName) {
      return;
    }
    this.mainArr.push(
      this.formBuilder.group({
        collapse: new FormControl(false),
        publish: new FormControl(false),
        compType: new FormControl(this.componentName),
        // description

        // text component
        descriptionText: new FormControl(''),
        EnableReadMore: new FormControl(false),

        // video
        exhibitionVideo: new FormControl(''),
        video_description: new FormControl(''),

        //artwork select
        artworkGrid: new FormControl(''),
        artworkInterior: new FormControl(''),
        artistSelect: new FormControl({ value: '', disabled: false }),
        showPrice: new FormControl(true),
        mediaUrl: new FormControl(''),

        // quote
        quoteText: new FormControl(''),

        // quote media
        quoteData: new FormControl(''),
        quoteSrc: new FormControl(''),
        quoteSize: new FormControl(),
        quoteLinkOrText: new FormControl(),
        quoteMediaPosition: new FormControl(''),
        quoteMediaBackground: new FormControl(''),
        quoteMedia: new FormControl(''),

        //audio
        audioLoop: new FormControl(true),
        audioLevel: new FormControl(''),

        //artist profile 1
        artistMedia: new FormControl({ value: '', disabled: true }),
        showProfileLink: new FormControl(true),
        profileText: new FormControl(''),
        profileUrl: new FormControl(''),
        profileMediaUrl: new FormControl(''),

        // banner
        videoUrlBanner: new FormControl(''),
        titleText: new FormControl(''),
        bannerType: new FormControl({ value: '', disabled: true }),
        showTitle: new FormControl(true),
        showButtons: new FormControl(true),
        button1Text: new FormControl(''),
        button1Url: new FormControl(''),
        button2Text: new FormControl(''),
        button2Url: new FormControl(''),
        showButton2: new FormControl(true),
        showButton3: new FormControl(true),
        button3Text: new FormControl(''),
        button3Url: new FormControl(''),
        showArtworkFeature: new FormControl(false),
        imageType: new FormControl(''),
        griddimensions: new FormControl(''),
        showText: new FormControl(true),
        showTextData: new FormControl(true),
        textPosition: new FormControl(''),
        zoomLevel: new FormControl(''),
        xAxis: new FormControl(''),
        yAxis: new FormControl(''),

        //artist profile 2
        selectArtistMedia2: this.formBuilder.array([
          this.formBuilder.group({
            showUrl: true,
            position: { value: 'Position', disabled: true },
          }),
        ]),

        // artist profile 2
        artistProfileText: new FormControl(''),
        artistProfileArr: this.formBuilder.array([]),
      })
    );
    this.componentName = '';
    this.componentSelector = 'Choose Component';
  }

  get mainArr(): FormArray {
    return <FormArray>this.dynamicForm.get('mainArr');
  }
  get artistProfileArr(): FormArray {
    return <FormArray>this.dynamicForm.get('mainArr').get('artistProfileArr');
  }

  collapseForm(value, index) {
    (this.mainArr.at(index) as FormGroup).get('collapse').patchValue(!value);
  }

  // to delete row
  deleteRow(index) {
    this.selectedIndex = index;
    this.isPopupOpen = true;
  }

  confirmDelete() {
    this.mainArr.removeAt(this.selectedIndex);
    this.isPopupOpen = false;
  }

  value(item) {
    console.log(item);
  }

  changeFocus(dropname, index) {
    console.log('change Focus');
    switch (dropname) {
      case 'isArtworkDropdownOpen1':
        {
          console.log(121212);
          setTimeout(() => {
            this.isArtworkDropdownOpen1[index] = false;
          }, 200);
        }
        break;

      default:
        break;
    }
  }

  chooseDrop() {
    setTimeout(() => {
      this.showdrop = false;
    }, 200);
  }

  getExhibitionById() {
    let url = 'api/auctions' + `/${localStorage.getItem('auctionID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.dataObj = res.data;
        res.data.contents.forEach((ele, index) => {
          if (ele.compType == 'textComponent') {
            this.componentName = 'textComponent';
          } else if (ele.compType == 'introVideo') {
            this.componentName = 'introVideo';
          } else if (ele.compType == 'bannerDivide') {
            this.componentName = 'bannerDivide';
          } else if (ele.compType == 'audio') {
            this.componentName = 'audio';
          } else if (ele.compType == 'quoteMedia') {
            this.componentName = 'quoteMedia';
          } else if (ele.compType == 'videoComponent') {
            this.componentName = 'videoComponent';
          } else if (ele.compType == 'quote') {
            this.componentName = 'quote';
          } else if (ele.compType == 'artworkSelect') {
            this.componentName = 'artworkSelect';
          } else if (ele.compType == 'artistProfile1') {
            this.componentName = 'artistProfile1';
          } else if (ele.compType == 'artworkFeature') {
            this.componentName = 'artworkFeature';
          } else {
            alert('Criteria not found!');
            return;
          }
          this.addNewComponent();
          setTimeout(() => {
            this.patchComponent(ele, index);
          }, 2000);
        });
      }
    });
  }

  patchComponent(ele, index) {
    this.dynamicForm['controls'].mainArr['controls'][index]
      .get('publish')
      .setValue(ele.data.publish);
    if (ele.compType == 'textComponent') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('descriptionText')
        .setValue(ele.data.text);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('EnableReadMore')
        .setValue(ele.data.readMore);
    } else if (ele.compType == 'introVideo') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('exhibitionVideo')
        .setValue(ele.data.src);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_description')
        .setValue(ele.data.description);
    } else if (ele.compType == 'artworkSelect') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('artworkGrid')
        .setValue(ele.data.artworkGrid);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('artworkInterior')
        .setValue(ele.data.artworkInterior);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showPrice')
        .setValue(ele.data.showPrice);
    } else if (ele.compType == 'quoteMedia') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteData')
        .setValue(ele.data.quote);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteSrc')
        .setValue(ele.data.image);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteMediaPosition')
        .setValue(ele.data.mediaPosition);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteLinkOrText')
        .setValue(ele.data.mediaUrl);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteMediaBackground')
        .setValue(ele.data.mediaBackground);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteMedia')
        .setValue(ele.data.quoteMedia);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteSize')
        .setValue(ele.data.quoteSize);
    } else if (ele.compType == 'videoComponent') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_url')
        .setValue(ele.data.video_url);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('video_description')
        .setValue(ele.data.video_description);
    } else if (ele.compType == 'quote') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('quoteText')
        .setValue(ele.data.text);
    } else if (ele.compType == 'bannerDivide') {
      if (ele.data.src) {
        this.selectedFilesObj.bannerArr.push({
          url: ele.data.src,
        });
      }
      if (ele.data.bannerType != 'Video') {
        this.selectedFilesObj.bannerArr.push({
          url: ele.data.src,
        });
      } else {
        this.dynamicForm['controls'].mainArr['controls'][index]
          .get('videoUrlBanner')
          .setValue(ele.data.src);
      }
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('bannerType')
        .setValue(ele.data.bannerType);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showTitle')
        .setValue(ele.data.showTitle);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('titleText')
        .setValue(ele.data.titleText);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showButtons')
        .setValue(ele.data.showButtons);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button1Text')
        .setValue(ele.data.button1Text);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button1Url')
        .setValue(ele.data.button1Url);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showButton2')
        .setValue(ele.data.showButton2);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button2Text')
        .setValue(ele.data.button2Text);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button2Url')
        .setValue(ele.data.button2Url);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showButton3')
        .setValue(ele.data.showButton3);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button3Text')
        .setValue(ele.data.button3Text);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('button3Url')
        .setValue(ele.data.button3Url);
    } else if (ele.compType == 'audio') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('audioLevel')
        .setValue(ele.data.audioLevel);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('audioLoop')
        .setValue(ele.data.loop);
      if (ele.data.src) {
        this.selectedFilesObj.audioArr.push({
          url: ele.data.src,
        });
      }
    } else if (ele.compType == 'artistProfile1') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('profileText')
        .setValue(ele.data.text);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('profileUrl')
        .setValue(ele.data.url);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('artistMedia')
        .setValue(ele.data.mediaType);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showProfileLink')
        .setValue(ele.data.showProfileLink);
      if (ele.data.mediaType == 'Video') {
        this.artistMediaType = 'video/*';
        this.dynamicForm['controls'].mainArr['controls'][index]
          .get('mediaUrl')
          .setValue(ele.data.mediaUrl);
      } else {
        this.selectedFilesObj.artistMediaTypeArr.push({
          url: ele.data.mediaUrl,
        });
      }
    } else if (ele.compType == 'artworkFeature') {
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('imageType')
        .setValue(ele.data.imageType);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('griddimensions')
        .setValue(ele.data.gridDimensions);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showText')
        .setValue(ele.data.showText);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('showTextData')
        .setValue(ele.data.showTextData);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('textPosition')
        .setValue(ele.data.textPosition);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('zoomLevel')
        .setValue(ele.data.zoomLevel);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('xAxis')
        .setValue(ele.data.xAxis);
      this.dynamicForm['controls'].mainArr['controls'][index]
        .get('yAxis')
        .setValue(ele.data.yAxis);
      if (ele.data.imageUrls.length > 0) {
        this.selectedFilesObj.imageUrls.push({
          url: ele.data.imageUrls,
        });
      }
      if (ele.data.gridUrls.length > 0) {
        this.selectedFilesObj.gridUrls.push({
          url: ele.data.gridUrls,
        });
      }
    }
  }

  onSubmit() {
    console.log(this.mainArr.controls);
    // console.log(this.mainArr.value)
    let arr = [];
    this.mainArr.controls.forEach((ele) => {
      console.log(ele);
      let obj = {};
      if (ele['value']['compType'] == 'textComponent') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'text-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          text: ele['value']['descriptionText'],
          readMore: ele['value']['EnableReadMore'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'introVideo') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'video-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          src: ele['value']['exhibitionVideo'],
          title: '',
          description: ele['value']['video_description'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'artworkSelect') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'artwork-container';
        obj['model'] = 'model-1';
        obj['data'] = {
          artworkGrid: ele['value']['artworkGrid'],
          artworkInterior: ele['value']['artworkInterior'],
          selectedArtworks: [],
          showPrice: ele['value']['showPrice'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'quote') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'quote-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          text: ele['value']['quoteText'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'quoteMedia') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'quote-cards';
        obj['model'] = 'model-1';
        obj['data'] = {
          quote: ele['value']['quoteData'],
          image: ele['value']['quoteSrc'],
          mediaPosition: ele['value']['quoteMediaPosition'],
          mediaBackground: ele['value']['quoteMediaBackground'],
          mediaUrl: ele['value']['quoteLinkOrText'],
          quoteMedia: ele['value']['quoteMedia'],
          quoteSize: ele['value']['quoteSize'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'audio') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'audio-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          src:
            this.selectedFilesObj.audioArr.length > 0
              ? this.selectedFilesObj.audioArr[0].url
              : '',
          audioLevel: ele['value']['audioLevel'],
          loop: ele['value']['audioLoop'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'artistProfile1') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'profile-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          text: ele['value']['profileText'],
          url: ele['value']['profileUrl'],
          mediaType: ele['value']['artistMedia'],
          mediaUrl:
            this.artistMediaType == 'video/*'
              ? ele['value']['mediaUrl']
              : this.selectedFilesObj.artistMediaTypeArr.length > 0
              ? this.selectedFilesObj.artistMediaTypeArr[0].url
              : '',
          showProfileLink: ele['value']['showProfileLink'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'bannerDivide') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'banner-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          bannerType: ele['value']['bannerType'],
          showTitle: ele['value']['showTitle'],
          src:
            this.accept == 'video/*'
              ? ele['value']['videoUrlBanner']
              : this.selectedFilesObj.bannerArr.length > 0
              ? this.selectedFilesObj.bannerArr[0].url
              : '',
          titleText: ele['value']['titleText'],
          showButtons: ele['value']['showButtons'],
          button1Text: ele['value']['button1Text'],
          button1Url: ele['value']['button1Url'],
          showButton2: ele['value']['showButton2'],
          button2Text: ele['value']['button2Text'],
          button2Url: ele['value']['button2Url'],
          showButton3: ele['value']['showButton3'],
          button3Text: ele['value']['button3Text'],
          button3Url: ele['value']['button3Url'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'artworkFeature') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'banner-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          imageType: ele['value']['imageType'],
          imageUrls:
            this.selectedFilesObj.imageUrls.length > 0
              ? this.selectedFilesObj.imageUrls[0].url
              : '',
          gridDimensions: ele['value']['griddimensions'],
          gridUrls:
            this.selectedFilesObj.gridUrls.length > 0
              ? this.selectedFilesObj.gridUrls[0].url
              : '',
          showText: ele['value']['showText'],
          showTextData: ele['value']['showTextData'],
          textPosition: ele['value']['button1Text'],
          zoomLevel: ele['value']['zoomLevel'],
          xAxis: ele['value']['xAxis'],
          yAxis: ele['value']['yAxis'],
          publish: ele['value']['publish'],
        };
      } else if (ele['value']['compType'] == 'artistProfile2') {
        obj['compType'] = ele['value']['compType'];
        obj['component'] = 'banner-containers';
        obj['model'] = 'model-1';
        obj['data'] = {
          artistProfileText: ele['value']['artistProfileText'],
          imageSrc:
            this.selectedFilesObj.imageUrls.length > 0
              ? this.selectedFilesObj.imageUrls[0].url
              : '',
          artistName: ele['value']['artistName'],
          showUrlArtistProfile: ele['value']['showUrlArtistProfile'],
          positionArtistProfile: ele['value']['positionArtistProfile'],
          publish: ele['value']['publish'],
        };
      }
      arr.push(obj);
    });
    let req = {
      contents: [...arr],
    };
    let url = localStorage.getItem('auctionID')
      ? 'api/auctions' + `/${localStorage.getItem('auctionID')}`
      : 'api/auctions';

    if (localStorage.getItem('auctionID')) {
      this.updateBlog(req, url);
      return;
    } else {
      this.server.postApi(url, req).subscribe((res) => {
        if (res.statusCode == 200) {
          localStorage.setItem('auctionID', res.data['_id']);
          alert(res.message);
        }
      });
    }
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('auctionID', res.data['_id']);
        alert(res.message);
        // this.router.navigate(['artist-portal/settings/exhibitions'])
      }
    });
  }
}
