
.MuiBtn {
    color: #586a6d;
    // height: 2.222vw;
    margin-right: 2vw;
    padding: 0.4vw 0vw;
    font-size: 1.666vw;
    min-width: unset;
    background: transparent;
    min-height: unset;
    // border-radius: 69vw;
    border: 0;
  }
  .MuiBtn-selected {
    color: var(--primary-font-color);
    // background: #192c30;
    border-bottom: 0.18888vw solid #004ddd;
  }
  .Mui-content {
    margin-top: 3vw;
  }
  .Mui-Table {
    padding: 1.5vw;
    border: 0.069444vw solid #ddd;
    border-radius: 1.111vw;
  }
  .Mui-thead {
    padding-bottom: 1.5vw;
  }
  .Mui-td {
    padding: 1vw 0;
  }
  .Mui-trow {
    border-radius: 0.555vw;
    border: 0;
    padding: 1vw;
    // border-top-left-radius: 0.555vw;
    //   border-bottom-left-radius: 8px;
    padding: 1.111vw;
  }
  tr:nth-child(even) {
    background-color: #f8f8f8;
  }
  .Mui-Action-btn {
    color: var(--tertiary-font-color);
    background: transparent;
    padding: 0.2vw 0.69444vw;
    // font-size: 1.666vw;
    min-width: unset;
    min-height: unset;
    border-radius: 69vw;
    border: 0.069444vw solid var(--tertiary-font-color);
    margin-left: 0.4vw;
  }
  
  td {
    overflow: hidden;
    white-space: nowrap;
  }