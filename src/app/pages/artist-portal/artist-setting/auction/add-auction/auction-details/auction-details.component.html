
 <span style="font-size: 1.25vw; display: block; margin-bottom: 2vw;">Auction Artworks</span>

<div class="Marketplace-Table-Content Mui-Table">
   <div class="filter-section">

   </div>
    
    <table style="width: 100%">
      <tr>
        <th
          class="Mui-thead"
          style="padding-left: 1vw;width:15%"
        >
        Lot No.
        </th>
        <th class="Mui-thead" style="width: 15%">Artwork Title</th>
        <th class="Mui-thead" style="text-align: center; width: 15%">
            Artist name
        </th>
         <th class="Mui-thead" style="text-align: center; width: 15%">
          Listing Price
        </th>
        <th class="Mui-thead" style="text-align: right;padding-right: 1vw; width: 10%">
          Sale Status</th>
        <th class="Mui-thead" style="text-align: right;padding-right: 1vw; width: 15%">
          Collector Name</th>
        <th class="Mui-thead" style="text-align: right;padding-right: 1vw; width: 15%">
          Payment Status</th>
      </tr>
      <tr class="Mui-trow" *ngFor="let item of [1, 2, 3]" (click)="navigateTo()">
        <td
          class="Mui-td"
          style="
            padding-left: 1vw;
            border-top-left-radius: 0.555vw;
            border-bottom-left-radius: 0.555vw;
          "
        >
          <div style="display: flex; align-items: center">
            <div class="img-thumbnaill" style="width: 3.333vw; height: 3vw">
              <img
                src="../../../../../../../assets/5.jpg"
                style="width: 100%; height: 100%; object-fit: contain"
                alt="thumbnail"
              />
            </div>

            <span> #18 / 25</span>
          </div>
        </td>
        <td class="Mui-td" *ngIf="!isMobile">Crypto Broker 411</td>
       
              <td class="Mui-td" style="text-align: center">Jhon Doe</td>
        <td class="Mui-td" style="text-align: center">$1,600.00</td>
        <td class="Mui-td" style="text-align: center">Jhon Doe</td>
        <td class="Mui-td" style="text-align: center">Jhon Doe</td>
        <td
          class="Mui-td"
          style="
            text-align: right;
            padding-right: 1vw;
            border-top-right-radius: 0.555vw;
            border-bottom-right-radius: 0.555vw;
          "
        >
        Pending
        </td>
      </tr>
    </table>
  </div>