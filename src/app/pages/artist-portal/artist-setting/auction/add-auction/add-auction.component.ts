import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';
import { Subscription } from 'rxjs';
import { FooterService } from 'src/app/shared/services/footer.service';

@Component({
  selector: 'app-add-auction',
  templateUrl: './add-auction.component.html',
  styleUrls: ['./add-auction.component.scss'],
})
export class AddAuctionComponent implements OnInit, OnDestroy {
  permissionsObj: any = {};
  constructor(private router: Router, private footerService: FooterService) {}
  /** selected tab Index stored variable */
  // selectedMenu;
  faArrowAltCircleLeft = faArrowAltCircleLeft;
  routerObserver: Subscription;
  activeMenu = 'main';

  ngOnDestroy(): void {
    //this.footerService.changeFooterType(FooterType.DEFAULT);
  }
  ngOnInit(): void {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Exhibitions'
    );
    console.log(this.permissionsObj);
    //this.footerService.changeFooterType(FooterType.HIDE);
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/auction/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/auction/add/add-on':
        this.activeMenu = 'add-on';
        break;
      case '/artist-portal/settings/auction/add/seo':
        this.activeMenu = 'seo';
        break;
      case '/artist-portal/settings/auction/add/registrations':
        this.activeMenu = 'registrations';
        break;
      case '/artist-portal/settings/auction/add/control-panel':
        this.activeMenu = 'control-panel';
        break;
      case '/artist-portal/settings/auction/add/auction-details':
        this.activeMenu = 'auction-details';
        break;
      default:
        this.activeMenu = 'main';
        break;
    }
  }

  // onMenuClick(index) {
  //   console.log(index);
  //   switch (index) {
  //     case 0:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/main']);
  //       break;
  //     case 1:
  //       this.router.navigate(['/artist-portal/settings/exhibitions/add/add-on']);
  //       break;
  //     default:
  //       break;
  //   }
  // }

  navigateTo(path, type) {
    if (localStorage.getItem('auctionID')) {
      this.router.navigate([path]);
    } else {
      alert('Please create auction first!');
    }
  }
}
