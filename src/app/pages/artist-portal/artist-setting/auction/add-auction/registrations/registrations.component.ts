import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { faPlusCircle } from '@fortawesome/free-solid-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';

@Component({
  selector: 'app-registrations',
  templateUrl: './registrations.component.html',
  styleUrls: ['./registrations.component.scss'],
})
export class RegistrationsComponent implements OnInit {
  limit = 12;
  Fiter_totalCount = 25;
  page = 1;
  InviteEmails = [{ email: '' }];
  CollectorDetailsModalOpen = false;
  EmailPop = false;
  faPlusCircle = faPlusCircle;
  data;
  form: FormGroup;
  constructor(
    private router: Router,
    private server: CollectorService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      allowUnregisteredUsers: new FormControl(false),
    });
    if (localStorage.getItem('auctionID')) {
      this.getExhibitionById();
    }
  }
  getExhibitionById() {
    let url = 'api/auctions' + `/${localStorage.getItem('auctionID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.data = res.data;
        this.form.patchValue({
          allowUnregisteredUsers: res.data?.allowUnregisteredUsers != 'false',
        });
      }
    });
  }
  addMailTab() {
    this.InviteEmails.push({ email: '' });
  }
  next2(i): void {
    // if (i > 0) {
    //   if (this.index < Math.ceil(this.currentArtists.length / 12)) {
    //     this.index += i;
    //     this.updateCurrent();
    //   }
    // }
    // if (i === 0) {
    //   this.index = this.lastIndex;
    //   this.updateCurrent();
    // }
    // this.scrollBack();
  }

  next(i): void {
    // if ((this.page + i - 1) * this.limit < this.Fiter_totalCount) {
    //   this.offset = this.offset + this.limit * i;
    //   this.getData();
    //   this.scrollBack();
    // }
  }
  CollectorDetails(value) {
    this.CollectorDetailsModalOpen = true;
  }
  navigateTo() {
    // this.router.navigate(["discover/artists/starinfinity/artworks/Yellow_Rhythmic_Warrior_2-2022-62288052981249c2aedd735c"])
    const url = this.router.serializeUrl(
      this.router.createUrlTree([
        'discover/artists/starinfinity/artworks/Yellow_Rhythmic_Warrior_2-2022-62288052981249c2aedd735c',
      ])
    );

    window.open(url, '_blank');
  }
  inviteUsers() {
    this.EmailPop = false;
    let emailID = [];
    this.InviteEmails.forEach((element) => {
      emailID.push(element.email);
    });
    let url = 'api/auctions/inviteEmail';
    this.server
      .postApi(url, {
        emailID: emailID,
        auction_id: localStorage.getItem('auctionID'),
      })
      .subscribe((res) => {
        if (res.statusCode == 200) {
          alert('Invitation sent successfully');
        }
      });
  }
  onSubmit() {
    let req = {
      allowUnregisteredUsers: this.form.value.allowUnregisteredUsers,
    };
    let url = localStorage.getItem('auctionID')
      ? 'api/auctions' + `/${localStorage.getItem('auctionID')}`
      : 'api/auctions';

    if (localStorage.getItem('auctionID')) {
      this.updateBlog(req, url);
      return;
    } else {
      this.server.postApi(url, req).subscribe((res) => {
        if (res.statusCode == 200) {
          localStorage.setItem('auctionID', res.data['_id']);
          alert(res.message);
        }
      });
    }
  }
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('auctionID', res.data['_id']);
        alert(res.message);
        // this.router.navigate(['artist-portal/settings/exhibitions'])
      }
    });
  }
}
