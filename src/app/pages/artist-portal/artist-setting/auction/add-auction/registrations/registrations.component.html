<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <button type="button" (click)="EmailPop = true" class="saveBtn">
            Invite People
          </button>
          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Allow Uninvited Users:</div>
              <label class="switch">
                <input
                  formControlName="allowUnregisteredUsers"
                  type="checkbox"
                />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to allow uninvited users in the Auction.
            </div>
          </div>
          <div class="content-table">
            <table style="width: 100%">
              <thead>
                <tr>
                  <th>Index</th>
                  <th>Email ID</th>
                  <th>User Name</th>
                  <th>Registered User</th>
                  <th></th>
                </tr>
              </thead>
              <tbody style="max-height: 55vh; overflow-y: scroll">
                <tr *ngFor="let item of data?.emailID; let i = index">
                  <td class="table-data">{{ i + 1 }}</td>
                  <td class="table-data">{{ item.email }}</td>
                  <td class="table-data">{{ item.display_name }}</td>
                  <td class="table-data">{{ item.is_register_user }}</td>
                  <td class="table-data">
                    <!-- <span
                      style="color: var(--tertiary-font-color); cursor: pointer"
                      (click)="CollectorDetails(item)"
                      >View</span
                    > -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- <div class="paginate">
            <div class="paginate-content">
              <button class="image-wrapper" [disabled]="page == 1">
                <img
                  src="../../../../../../../assets/icons/back-chevron_2020-11-12/<EMAIL>"
                  alt=""
                />
              </button>
              <button class="nextPrev noHover" *ngIf="this.page - 3 > 0">
                ...
              </button>
              <button class="nextPrev" *ngIf="this.page - 2 > 0">
                {{ page - 2 }}
              </button>
              <button class="nextPrev" *ngIf="this.page - 1 > 0">
                {{ page - 1 }}
              </button>
              <button class="current">{{ page }}</button>
              <button
                class="nextPrev"
                *ngIf="this.page * this.limit < this.Fiter_totalCount"
              >
                {{ page + 1 }}
              </button>
              <button
                class="nextPrev"
                (click)="next(2)"
                *ngIf="(this.page + 1) * this.limit < this.Fiter_totalCount"
              >
                {{ page + 2 }}
              </button>
              <button
                class="nextPrev noHover"
                *ngIf="(this.page + 2) * this.limit < this.Fiter_totalCount"
              >
                ...
              </button>
              <button
                class="image-wrapper"
                (click)="next(1)"
                [disabled]="page * limit >= Fiter_totalCount"
              >
                <img
                  src="../../../../../../../assets/icons/front-chevron_2020-11-12/<EMAIL>"
                  alt=""
                />
              </button>
            </div>
          </div> -->
        </form>
        <div class="section section-margin-top"></div>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="popup-for-confirmation" [hidden]="!CollectorDetailsModalOpen">
  <div class="popup-card">
    <span
      style="
        position: absolute;
        top: 1vw;
        right: 1vw;
        color: var(--tertiary-font-color);
        cursor: pointer;
      "
      (click)="CollectorDetailsModalOpen = false"
      >Close</span
    >
    <p style="font-size: 1.25vw">Interested Artworks</p>
    <div class="Marketplace-Table-Content Mui-Table">
      <table style="width: 100%">
        <tr>
          <th class="Mui-thead" style="padding-left: 1vw; width: 20%">
            Lot No.
          </th>
          <th class="Mui-thead" style="width: 20%">Artwork Title</th>
          <th class="Mui-thead" style="text-align: center; width: 20%">
            Artist
          </th>
          <th class="Mui-thead" style="text-align: center; width: 20%">
            Listing Price
          </th>
          <th
            class="Mui-thead"
            style="text-align: right; padding-right: 1vw; width: 20%"
          >
            Status
          </th>
        </tr>
        <tr
          class="Mui-trow"
          *ngFor="let item of [1, 2, 3]"
          style="cursor: pointer"
          (click)="navigateTo()"
        >
          <td
            class="Mui-td"
            style="
              padding-left: 1vw;
              border-top-left-radius: 0.555vw;
              border-bottom-left-radius: 0.555vw;
            "
          >
            <div style="display: flex; align-items: center">
              <div class="img-thumbnaill" style="width: 3.333vw; height: 3vw">
                <img
                  src="../../../../../assets/5.jpg"
                  style="width: 100%; height: 100%; object-fit: contain"
                  alt="thumbnail"
                />
              </div>

              <span> #18 / 25</span>
            </div>
          </td>
          <td class="Mui-td" *ngIf="!isMobile">Crypto Broker 411</td>
          <!-- <td *ngIf="!isMobile" class="Mui-td">--</td>
                <td *ngIf="!isMobile" class="Mui-td">Mar 15, 2022</td> -->
          <td class="Mui-td" style="text-align: center">Jhon Doe</td>
          <td class="Mui-td" style="text-align: center">$1,600.00</td>
          <td
            class="Mui-td"
            style="
              text-align: right;
              padding-right: 1vw;
              border-top-right-radius: 0.555vw;
              border-bottom-right-radius: 0.555vw;
            "
          >
            Pending
          </td>
        </tr>
      </table>
    </div>
    <div class="paginate">
      <div class="paginate-content">
        <button class="image-wrapper" [disabled]="page == 1">
          <img
            src="../../../../../../../assets/icons/back-chevron_2020-11-12/<EMAIL>"
            alt=""
          />
        </button>
        <button class="nextPrev noHover" *ngIf="this.page - 3 > 0">...</button>
        <button class="nextPrev" *ngIf="this.page - 2 > 0">
          {{ page - 2 }}
        </button>
        <button class="nextPrev" *ngIf="this.page - 1 > 0">
          {{ page - 1 }}
        </button>
        <button class="current">{{ page }}</button>
        <button
          class="nextPrev"
          *ngIf="this.page * this.limit < this.Fiter_totalCount"
        >
          {{ page + 1 }}
        </button>
        <button
          class="nextPrev"
          (click)="next(2)"
          *ngIf="(this.page + 1) * this.limit < this.Fiter_totalCount"
        >
          {{ page + 2 }}
        </button>
        <button
          class="nextPrev noHover"
          *ngIf="(this.page + 2) * this.limit < this.Fiter_totalCount"
        >
          ...
        </button>
        <button
          class="image-wrapper"
          (click)="next(1)"
          [disabled]="page * limit >= Fiter_totalCount"
        >
          <img
            src="../../../../../../../assets/icons/front-chevron_2020-11-12/<EMAIL>"
            alt=""
          />
        </button>
      </div>
    </div>
    <p style="font-size: 1.25vw; margin-top: 2vw">Bidding History</p>
    <div class="Marketplace-Table-Content Mui-Table">
      <table style="width: 100%">
        <tr>
          <th class="Mui-thead" style="padding-left: 1vw; width: 20%">
            Lot No.
          </th>
          <th class="Mui-thead" style="width: 20%">Artwork Title</th>
          <th class="Mui-thead" style="text-align: center; width: 20%">
            Artist
          </th>
          <th class="Mui-thead" style="text-align: center; width: 20%">
            Listing Price
          </th>
          <th
            class="Mui-thead"
            style="text-align: right; padding-right: 1vw; width: 20%"
          >
            Status
          </th>
        </tr>
        <tr class="Mui-trow" *ngFor="let item of [1, 2, 3]">
          <td
            class="Mui-td"
            style="
              padding-left: 1vw;
              border-top-left-radius: 0.555vw;
              border-bottom-left-radius: 0.555vw;
            "
          >
            <div style="display: flex; align-items: center">
              <div class="img-thumbnaill" style="width: 3.333vw; height: 3vw">
                <img
                  src="../../../../../assets/5.jpg"
                  style="width: 100%; height: 100%; object-fit: contain"
                  alt="thumbnail"
                />
              </div>

              <span> #18 / 25</span>
            </div>
          </td>
          <td class="Mui-td">Crypto Broker 411</td>
          <td class="Mui-td" style="text-align: center">Jhon Doe</td>

          <!-- <td *ngIf="!isMobile" class="Mui-td">--</td>
                  <td *ngIf="!isMobile" class="Mui-td">Mar 15, 2022</td> -->
          <td class="Mui-td" style="text-align: center">$1,600.00</td>
          <td
            class="Mui-td"
            style="
              text-align: right;
              padding-right: 1vw;
              border-top-right-radius: 0.555vw;
              border-bottom-right-radius: 0.555vw;
            "
          >
            Won
          </td>
        </tr>
      </table>
    </div>
    <div class="paginate">
      <div class="paginate-content">
        <button class="image-wrapper" [disabled]="page == 1">
          <img
            src="../../../../../../../assets/icons/back-chevron_2020-11-12/<EMAIL>"
            alt=""
          />
        </button>
        <button class="nextPrev noHover" *ngIf="this.page - 3 > 0">...</button>
        <button class="nextPrev" *ngIf="this.page - 2 > 0">
          {{ page - 2 }}
        </button>
        <button class="nextPrev" *ngIf="this.page - 1 > 0">
          {{ page - 1 }}
        </button>
        <button class="current">{{ page }}</button>
        <button
          class="nextPrev"
          *ngIf="this.page * this.limit < this.Fiter_totalCount"
        >
          {{ page + 1 }}
        </button>
        <button
          class="nextPrev"
          (click)="next(2)"
          *ngIf="(this.page + 1) * this.limit < this.Fiter_totalCount"
        >
          {{ page + 2 }}
        </button>
        <button
          class="nextPrev noHover"
          *ngIf="(this.page + 2) * this.limit < this.Fiter_totalCount"
        >
          ...
        </button>
        <button
          class="image-wrapper"
          (click)="next(1)"
          [disabled]="page * limit >= Fiter_totalCount"
        >
          <img
            src="../../../../../../../assets/icons/front-chevron_2020-11-12/<EMAIL>"
            alt=""
          />
        </button>
      </div>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!EmailPop">
  <div class="popup-card-for-email">
    <p style="font-size: 1.25vw">Invite people by Email</p>
    <div style="max-height: 34vh; overflow-y: auto">
      <div
        class="field-value"
        style="margin-top: 1vw"
        *ngFor="let item of InviteEmails; let i = index"
      >
        <input type="text" style="height: 2.47vw" [(ngModel)]="item.email" />
        <div class="placeholder">Email</div>
      </div>
    </div>

    <span class="addMore" (click)="addMailTab()"
      ><fa-icon [icon]="faPlusCircle"></fa-icon> Add More</span
    >
    <div
      style="
        display: flex;
        justify-content: flex-end;
        gap: 1vw;
        align-items: center;
        margin-top: 2vw;
      "
    >
      <button class="saveBtn" (click)="EmailPop = false">Cancel</button>
      <button class="saveBtn" (click)="inviteUsers()">Send</button>
    </div>
  </div>
</div>
