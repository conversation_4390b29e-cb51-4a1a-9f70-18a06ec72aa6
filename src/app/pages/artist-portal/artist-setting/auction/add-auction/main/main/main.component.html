<div class="container__main" #scrollTop>
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.createdAs"
              [ngClass]="{ 'Disabled-Color': disableObj.createdAs }"
            >
              <div class="input-container" (focusout)="changeFocus(0)">
                <input
                  formControlName="createdAs"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.createdAs ? null : (isDropDownOpen[0] = true)
                  "
                  readonly
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.createdAs"
                >
                </fa-icon>

                <div class="placeholder">Created</div>
                <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[0]">
                  <!-- [ngClass]="{'dropdown-hidden': isDropDownOpen[0]==false,'dropdown-visible': isDropDownOpen[0]==true}" -->
                  <!-- [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[0],
                    'dropdown-visible': isDropDownOpen[0]
                  }" -->
                  <ul>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form.get('createdAs').setValue('Individually');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">individually</div>
                    </li>
                    <li
                      style="cursor: pointer"
                      (click)="
                        form.get('createdAs').setValue('Collaboratively');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">collaboratively</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork was created individually or in
                collaboration with another artist
              </div>
            </div>
            <div
              *ngIf="form.get('createdAs').value == 'Collaboratively'"
              class="field-value"
            >
              <input
                formControlName="createdBy"
                type="text"
                placeholder="Only if applicable"
              />
              <div class="placeholder">Created By</div>
              <div class="input-info">
                Provide the creator details if work created collaboratively.
                e.g.: Created in collaboration with Artist XYZ
              </div>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.title"
              [ngClass]="{ 'Disabled-Color': disableObj.title }"
            >
              <input
                formControlName="title"
                type="text"
                placeholder="Artwork Title"
                [disabled]="disableObj.title"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.title"
              >
              </fa-icon>
              <div class="placeholder">Artwork Title</div>
              <div class="input-info">
                Provide the complete title of the artwork
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.year"
              [ngClass]="{ 'Disabled-Color': disableObj.year }"
            >
              <input
                formControlName="year"
                type="text"
                placeholder="Year"
                [disabled]="disableObj.year"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.year"
              >
              </fa-icon>

              <div class="placeholder">Year</div>
              <div class="input-info">
                Provide year (e.g.: 2018) or range (e.g.: 2016-2018)
              </div>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.dimensions"
              [ngClass]="{ 'Disabled-Color': disableObj.dimensions }"
            >
              <input
                formControlName="dimensions"
                type="text"
                placeholder="Dimensions"
                [disabled]="disableObj.dimensions"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.dimensions"
              >
              </fa-icon>

              <div class="placeholder">Dimensions</div>
              <div class="input-info">
                Dimensions as to be displayed on artwork page. E.g: 78.4x78.4 cm
                each | 31x31 in each of 34h x 19w x 8.5d cm | 13.4h x 7.5w x
                3.3d in, 7kg; 4 min 8 sec (loop); 17.5 x 12.5 x 2.5 cm
                (variable) | 7 x 5 x 1 in (variable); 1080x1080 px
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.medium"
              [ngClass]="{ 'Disabled-Color': disableObj.medium }"
            >
              <input
                formControlName="medium"
                type="text"
                placeholder="Medium"
                [disabled]="disableObj.medium"
              />
              <fa-icon
                class="Lock-position"
                [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                [hidden]="!disableObj.medium"
              >
              </fa-icon>

              <div class="placeholder">Medium of work</div>
              <div class="input-info">
                Provide complete details of the artwork medium (as detailed as
                possible). e.g.: Digital Image, JPEG; Painting on canvas;
                Acrylic paint on paper
              </div>
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.type"
              [ngClass]="{ 'Disabled-Color': disableObj.type }"
            >
              <div class="input-container" (focusout)="changeFocus(2)">
                <input
                  type="text"
                  class="selection"
                  formControlName="type"
                  placeholder="Choose an option"
                  (focus)="disableObj.type ? null : (isDropDownOpen[2] = true)"
                  readonly="readonly"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.type"
                >
                </fa-icon>

                <div class="placeholder">Artwork Type</div>
                <button (click)="isDropDownOpen[2] = !isDropDownOpen[2]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[2]">
                  <ul>
                    <li
                      (click)="
                        form.get('type').setValue('textile arts');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">textile arts</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('painting');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">painting</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('drawing');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">drawing</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('photography');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">photography</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('digital');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">digital</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('print');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">print</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('mixed media');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">mixed media</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('video');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">video</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('other');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">other</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sculpture');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sculpture</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('installation');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">installation</div>
                    </li>
                    <li
                      (click)="
                        form.get('type').setValue('sound');
                        isDropDownOpen[2] = false
                      "
                    >
                      <div class="country-name">sound</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the primary artwork type from the drop-down list
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!showObj?.workType"
              [ngClass]="{ 'Disabled-Color': disableObj.workType }"
            >
              <div class="input-container" (focusout)="changeFocus(3)">
                <input
                  formControlName="workType"
                  type="text"
                  class="selection"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.workType ? null : (isDropDownOpen[3] = true)
                  "
                  readonly="readonly"
                />
                <fa-icon
                  class="Lock-position"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.workType"
                >
                </fa-icon>

                <div class="placeholder">Work Type</div>
                <button (click)="isDropDownOpen[3] = !isDropDownOpen[3]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[3]">
                  <ul>
                    <li
                      (click)="
                        form.get('workType').setValue('edition');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">edition</div>
                    </li>
                    <li
                      (click)="
                        form.get('workType').setValue('unique');
                        isDropDownOpen[3] = false
                      "
                    >
                      <div class="country-name">unique</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the type of artwork (unique or edition)
              </div>
            </div>
          </div>
          <!-- <div class="field-value" [hidden]="!showObj?.selectedMovements">
            <div class="input-container">
              <input type="text" class="selection"
                placeholder="Choose an option from the list, or add a new keyword and hit Enter to add to list"
                (focus)="showMovement = true"
                (focusout)="showMovement = false"
              />
              <div class="placeholder">Movement Keywords</div>
              <button (click)="showMovement = !showMovement">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>

              <div [ngClass]="
                  showMovement ? 'dropdown-visible' : 'dropdown-hidden'
                ">
                <ul>
                  <li (click)="addToItems(item_mov)" *ngFor="let item_mov of moveArr">
                    <div class="country-name">{{ item_mov?.name }}</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="input-info">
              Choose the movement keywords associated with the artwork
            </div>
            <div class="input-info">
              <div class="row">
                <div class="col-md-3 col-sm-3 col-lg-3 multi text-center"
                  *ngFor="let item_mov of selectedMovements; let k = index">
                  <span>{{ item_mov?.name }}
                    <span (click)="removeItems(k)" class="cross">X</span></span>
                </div>
              </div>
            </div>
          </div> -->

          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.showEdition"
              [ngClass]="{ 'Disabled-Color': disableObj.showEdition }"
            >
              <div class="input-with-text">
                <div class="text-before">Show Edition:</div>
                <label class="switch">
                  <input
                    formControlName="showEdition"
                    type="checkbox"
                    [disabled]="disableObj.showEdition"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.showEdition"
                >
                </fa-icon>
              </div>
              <div class="input-info">
                Choose whether to show edition details on the site
              </div>
            </div>
            <div
              class="field-value"
              [hidden]="!form.get('showEdition').value"
              [ngClass]="{ 'Disabled-Color': disableObj.showEdition }"
            >
              <div class="input-with-text">
                <div class="text-before">Edition data record:</div>
                <label class="switch">
                  <input
                    formControlName="editionDataRecord"
                    type="checkbox"
                    [disabled]="disableObj.showEdition"
                  />
                  <span class="slider round"></span>
                </label>
                <fa-icon
                  class="Lock-position-1"
                  [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
                  [hidden]="!disableObj.showEdition"
                >
                </fa-icon>
              </div>
              <!-- <div class="input-info">
                Choose whether to show edition details on the site
              </div> -->
            </div>
          </div>
          <div class="field-value" [hidden]="!form.get('editionDataRecord').value" >
            <div class="input-with-text">
              <div
                class="input-with-text"
                [hidden]="form.get('editionOFF').value"
              >
                <div class="text-before">Edition of</div>
                <input
                formControlName="editionCount"
                  type="number"
                  name="editionCount"
                  id="editionCount"
                  min="0"
                  (change)="EditionChange=true"
                  oninput="this.value = Math.abs(this.value)"
                  style="max-width: 3vw; margin-right: 1vw"
                />
                <div class="text-before">plus</div>
                <input
                  type="number"
                  formControlName="artistProofCount"
                  name="artistProofCount"
                  id="artistProofCount"
                  (change)="EditionChange=true"
                  min="0"
                  oninput="this.value = Math.abs(this.value)"
                  style="max-width: 3vw; margin-right: 1vw"
                /><div class="text-before">artist’s proofs’</div>
                <label
                for="editionOFF"
                style="
                  word-wrap: break-word;
                  margin-right: 2vw;
                  margin-left: 1.5vw;
                  margin-bottom: 0;
                  margin-top: 0.1vw;
                "
              >
               <input type="checkbox" name="editionOFF" id="editionOFF"
               style="vertical-align: middle;"
               formControlName="editionOFF">
               
                Unlimited edition
              </label>
              <label
              for="freeze"
              style="
                word-wrap: break-word;
                margin-right: 2vw;
                margin-top: 0.1vw;
                margin-bottom: 0;
              "
            >
              <input
                id="freeze"
                formControlName="freeze"
                type="checkbox"
                (change)="freeze($event)"
                style="vertical-align: middle;"
              />
              Freeze edition count
            </label>
              </div>
              
              <label
                for="pluss"
                style="word-wrap: break-word; margin-top: 0.5vw"
                hidden
              >
                <input
                  id="pluss"
                  formControlName="pluss"
                  type="checkbox"
                  value="test"
                />
                Show more options
              </label>
            </div>
            <!-- <div class="input-info">
            Choose whether to show edition details on the site
          </div> -->
          </div>
          <button  [hidden]="!form.get('editionDataRecord').value" class="button-type" style="margin: 1vw 0 1vw 0" type="button" (click)="generateEdition(form.get('editionCount').value,form.get('artistProofCount').value )">
            Create edition records
          </button>

          <div class="splitter"
          [hidden]="!form.get('showEdition').value" >
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.showEdition"
            >
              <input
                formControlName="edition"
                type="text"
                placeholder="Edition Details"
              />
              <div class="placeholder">Edition Details</div>
              <div class="input-info">
                Edition details (if applicable). e.g.: 'Edition of 5 plus AP'
              </div>
            </div>
            <div class="field-value" [hidden]="!showObj?.showEdition">
              <textarea name="thv"></textarea>
              <div class="placeholder">Edition publisher details</div>
              <!-- <div class="input-info">
              Edition details (if applicable). e.g.: 'Edition of 5 plus AP'
            </div> -->
            </div>
          </div>
          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              [hidden]="!showObj?.physicalOrDigital"
            >
              <div class="input-container" (focusout)="changeFocus(4)">
                <input
                  type="text"
                  class="selection"
                  formControlName="physicalOrDigital"
                  placeholder="Choose an option"
                  (focus)="
                    disableObj.physicalOrDigital
                      ? null
                      : (isDropDownOpen[4] = true)
                  "
                  (focusout)="changeFocus(4)"
                />
                <div class="placeholder">Physical or Digital</div>
                <button (click)="isDropDownOpen[4] = !isDropDownOpen[4]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[4]">
                  <ul>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('physical')
                      "
                    >
                      <div class="country-name">Physical</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('digital')
                      "
                    >
                      <div class="country-name">Digital</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[4] = false;
                        form.get('physicalOrDigital').setValue('both')
                      "
                    >
                      <div class="country-name">Both</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose whether the artwork is Physical, Digital, or Both.
              </div>
            </div>
            <div class="field-value" style="padding-right: 0.5vw">
              <div class="input-container" (focusout)="changeFocus(5)">
                <input
                  type="text"
                  class="selection"
                  formControlName="file_type"
                  placeholder="Choose an option"
                  (focus)="isDropDownOpen[5] = true"
                  (focusout)="changeFocus(5)"
                />
                <div class="placeholder">File Type</div>
                <button (click)="isDropDownOpen[5] = !isDropDownOpen[5]">
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div class="dropdown-visible" [hidden]="!isDropDownOpen[5]">
                  <ul>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('image');
                        accept = 'image/*'
                      "
                    >
                      <div class="country-name">Image</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('video');
                        accept = 'video/*'
                      "
                    >
                      <div class="country-name">Video</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('3d_asset');
                        accept = 'glb/*'
                      "
                    >
                      <div class="country-name">3D asset</div>
                    </li>
                    <li
                      (click)="
                        isDropDownOpen[5] = false;
                        form.get('file_type').setValue('html_code');
                        accept = 'html/*'
                      "
                    >
                      <div class="country-name">HTML code</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the file type being uploaded as the artwork file.
              </div>
            </div>
          </div>

          <div class="upload-head">Artwork Thumbnail</div>
          <div
            class="field-value flex-block"
            [ngClass]="{ 'Disabled-Color': disableObj.profileImage }"
          >
            <image-upload
              [accept]="'image/*'"
              [selectedData]="selectedFilesObj?.profileArr"
              (onFileChange)="onFileSelect($event, 'profileArr')"
              [fileSize]="8388608"
              [placeholder]="placeholder"
              [Disable]="disableObj.profileImage"
            ></image-upload>
            <div class="input-info">
              Upload an image to be used for the Artwork page on the website.
              Image needs to be cropped to content, with no shadows/background
              space. Image will be optimised for website. Please name the image
              files in the following
              format:&lt;artist_name&gt;_&lt;artwork_title&gt;.
            </div>
          </div>

          <div class="upload-head">Original Artwork File</div>
          <!-- <div class="field-value flex-block">
            <img [src]="profileImage" style="width: 100%" />
          </div> -->

          <div
            class="field-value flex-block"
            [ngClass]="{ 'Disabled-Color': disableObj.highResFile }"
          >
            <image-upload
              [accept]="'all'"
              [selectedData]="selectedFilesObj?.highResArr"
              (onFileChange)="onFileSelect($event, 'highResArr')"
              [fileSize]="2147483648"
              [placeholder]="highResPlaceholder"
              [Disable]="disableObj.highResFile"
            ></image-upload>
            <div class="input-info">
              Upload a high res file or provide link to the high res file. This
              will be sent to the buyer upon confirmation of payment.
            </div>
          </div>

          <div
            class="editor-head"
            style="margin-top: 2vw"
            *ngIf="form.value.file_type != 'image'"
          >
            Embed Code
          </div>
          <div class="field-value" *ngIf="form.value.file_type != 'image'">
            <textarea
              [placeholder]="'Embed Code'"
              formControlName="artistNote"
              rows="6"
              cols="115"
              style="padding: 1vw"
            ></textarea>
            <!-- <angular-editor [placeholder]="'Artist Note'"  formControlName="artistNote"></angular-editor> -->
            <div class="input-info">
              Provide the correct formatted responsive embed code, to display
              the artwork on the site. For videos, provide the responsive embed
              code from Vimeo or Youtube. Display of videos will depend on
              settings provided in Vimeo or Youtube.
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
            <div (click)="getValueWithAsync()" class="next">Save & Close</div>
            <div [hidden]="true" class="next" style="margin-right: 1.5vw">
              Duplicate
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="popup-for-confirmation" [hidden]="!isEditionModalOpen">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Create edition records</p>
    <label for="checkid" style="word-wrap: break-word">
      <input id="checkid" type="checkbox" value="test" />Detach edition records
      from edition data record after creating
    </label>
    <span>
      <span class="forLink" style="margin-right: 1vw" (click)="SelectAll()">Select all</span>
      <span class="forLink" style="margin-right: 1vw" (click)="SelectNone()" >Select none</span>
    </span>
<div class="Edition-list" style="max-height: 16vw;overflow-y: scroll; margin-top: 1vw;">
  <p style="margin-bottom: unset;" *ngFor="let item of editionArray; let i=index" >
    <label for="{{ 'wave' + i }}" style="word-wrap: break-word; ">
    <input id="{{ 'wave' + i }}" style="margin-right: .5vw;" type="checkbox" [(ngModel)]="item.value" /> Edition record #{{item.index}}
  </label>
  </p>
  <p  style="margin-bottom: unset;" *ngFor="let item of artistProofArray; let i=index" >
   <label for="{{ 'waved' + i }}" style="word-wrap: break-word" >
    <input id="{{ 'waved' + i }}" style="margin-right: .5vw;" type="checkbox"[(ngModel)]="item.value" /> Artist proof record  #{{item.index}}
  </label> 
  </p>
  
</div>

    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="isEditionModalOpen=false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="createER()"
      >
       Create Edition records
      </button>
    </div>
  </div>
</div>



<div class="popup-for-confirmation" [hidden]="!EditionChange">
  <div class="popup-card">
    <p style="font-size: 1.666vw">Update edition details?</p>
  
<span>The number of editions has changed. Would you like to update the 'Edition details' field to the text below?</span>

<span style="margin-top: 2vw;"><b>Edition of {{form.get('editionCount').value}} <span *ngIf="form.get('artistProofCount').value.length!=0">plus {{form.get('artistProofCount').value}}artist's proofs</span> 

</b></span>
    <div
      class="buttonList"
      style="
        margin-top: 2.042vw;
        margin-bottom: 0;
        display: flex;
        justify-content: space-around;
      "
    >
      <button
        class="button-type"
        style="width: 35%; padding: 0.833333vw 3.08333vw"
        (click)="EditionChange=false"
      >
        Cancel
      </button>
      <button
        class="button-type"
        style="width: 50%; padding: 0.833333vw 3.08333vw"
        (click)="EditionChange=false;generateEdition(form.get('editionCount').value,form.get('artistProofCount').value )"
      >
       Yes
      </button>
    </div>
  </div>
</div>