<div class="container__main">
    <div class="content__section">
        <div class="profile-field">
            <div class="w-100 field-contents">
                <form *ngIf="form" [formGroup]="form">
                    <div class="splitter">
                        <div class="field-value" style="padding-right: 0.5vw"  [ngClass]="{ 'Disabled-Color': disableObj.catalogNumber }">
                            <input formControlName="catalogNumber" type="text" placeholder="Catalog Number" />
                            <fa-icon class="Lock-position" [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions" [hidden]="!disableObj.catalogNumber">
                            </fa-icon>
                            <div class="placeholder">Catalog Number</div>
                            <div class="input-info">
                                Provide the Catalog Number for the artwork. e.g.: RAPL-2021-JSMITH-47-02.
                            </div>
                        </div>
                        <div class="field-value"></div>
                    </div>
                    <div class="sub-head" >
                        <p>Price History</p>
                        <div class="splitter">
                            <div class="field-value" style="padding-right: 0.5vw">
                                <input type="number" [(ngModel)]="priceHistoryObj.artist_price" [ngModelOptions]="{ standalone: true }" placeholder="Artist Price" />
                                <div class="placeholder">Artist Price</div>
                            </div>
                            <div class="field-value" style="padding-right: 0.5vw" >
                                <input type="number" [(ngModel)]="priceHistoryObj.sale_price" [ngModelOptions]="{ standalone: true }" placeholder="Sale Price" />
                                <div class="placeholder">Sale Price</div>
                            </div>
                            <div class="field-value" style="padding-right: 0.5vw" >
                                <input type="date" [(ngModel)]="priceHistoryObj.pricing_date" [ngModelOptions]="{ standalone: true }" placeholder="Pricing Date" required />
                                <div class="placeholder">Pricing Date</div>
                            </div>
                        </div>
                        <p class="input-info">
                            Provide the price info and date when the price was set. When updating the price, move the previous entry to history. The latest entry (based on date given) will be used on the website.
                        </p>
                        <span (click)="addRepeater()">
              <span
                ><img
                  class="plus-icon"
                  style="margin-right: 0.5vw"
                  src="assets/images/load-more.png"
              /></span>
                        <span class="sub-text">Add to price history</span>
                        </span>
                    </div>
                    <div class="outer-box" *ngIf="priceHistory.controls.length > 0">
                        <div class="row">
                            <div class="col-md-4 col-sm-4 col-lg-4 text-left" *ngIf="showObj.artist_price">
                                <label class="tb-head ml-35">Artist Price</label>
                            </div>
                            <div class="col-md-4 col-sm-4 col-lg-4 text-left" *ngIf="showObj.sale_price">
                                <label class="tb-head">Sale Price</label>
                            </div>
                            <div class="col-md-4 col-sm-4 col-lg-4 text-left" *ngIf="showObj.pricing_date">
                                <label class="tb-head">Pricing Date</label>
                            </div>
                        </div>
                        <div formArrayName="priceHistory" [dragula]="'task-group'" [(dragulaModel)]="priceHistory.controls">
                            <div class="row mb-15 inner-box" formGroupName="{{ i }}" *ngFor="let item of priceHistory.controls; let i = index">
                                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                                    <span class="cross-icon" style="cursor: grab !important">
                    <i class="fa fa-ellipsis-v mr2"></i>
                    <i class="fa fa-ellipsis-v"></i>
                  </span>
                                    <input formControlName="artist_price" type="number" *ngIf="showObj.artist_price" />
                                </div>
                                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                                    <input formControlName="sale_price" type="number" *ngIf="showObj.sale_price" />
                                </div>
                                <div class="col-md-4 col-sm-4 col-lg-4 text-left">
                                    <input formControlName="pricing_date" type="date" *ngIf="showObj.pricing_date" />
                                    <span (click)="priceHistory.removeAt(i, 1)" class="cross-icon">X</span
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="splitter">
            <div
              class="field-value"
              style="padding-right: 0.5vw"
              
              [ngClass]="{ 'Disabled-Color': disableObj.saleOption }"
            >
              <div class="input-container" (focusout)="changeFocus(0)">
                <input
                  formControlName="saleOption"
                  type="text"
                  class="selection"
                  placeholder="Sale Options"
                  readonly="readonly"
                  (focus)="
                    disableObj.saleOption ? null : (isDropDownOpen[0] = true)
                  "
                />
                <fa-icon  class="Lock-position" [icon]="faLock" title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions" [hidden]="!disableObj.saleOption" > </fa-icon>
                <div class="placeholder">Sale Options</div>
                <button
                  (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                  type="button"
                >
                  <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                </button>
                <div
                  [ngClass]="{
                    'dropdown-hidden': !isDropDownOpen[0],
                    'dropdown-visible': isDropDownOpen[0]
                  }"
                >
                  <ul>
                    <li
                      (click)="
                        form.get('saleOption').setValue('For Sale');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">For Sale</div>
                    </li>
                    <li
                      (click)="
                        form.get('saleOption').setValue('Not For Sale');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Not For Sale</div>
                    </li>
                    <li
                      (click)="
                        form.get('saleOption').setValue('Sold');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Sold</div>
                    </li>
                    <li
                      (click)="
                        form.get('saleOption').setValue('Reserved');
                        isDropDownOpen[0] = false
                      "
                    >
                      <div class="country-name">Reserved</div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="input-info">
                Choose the current sale status of the artwork
              </div>
            </div>
            <div
              *ngIf="
                form.get('saleOption').value === 'Sold' ||
                form.get('saleOption').value === 'Reserved'
              "
              class="field-value"
            >
              <input
                formControlName="soldDate"
                type="date"
                placeholder="Sold Date"
              />
              <div class="placeholder">Sold Date</div>
              <div class="input-info">Provide the sold date of the artwork</div>
            </div>
          </div>
          <div
            *ngIf="
              form.get('saleOption').value === 'Sold' ||
              form.get('saleOption').value === 'Reserved'
            "
            class="field-value"
          >
            <angular-editor
            id="editor28"
              [placeholder]="'Sale Information'"
              formControlName="saleInfo"
            ></angular-editor>
            <div class="placeholder">Sale Information</div>
          </div>

          <div class="sub-head" >
            <p>Sale Details</p>
            <div class="splitter" style="margin-bottom: 1vw">
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.final_sale_price"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Final Price"
                />
                <div class="placeholder">Final Sale Price</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.discount"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Discount"
                />
                <div class="placeholder">Discount</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.taxable"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Taxable"
                />
                <div class="placeholder">Taxable</div>
              </div>
              <div class="field-value-4" style="padding-right: 0.5vw">
                <input
                  type="number"
                  [(ngModel)]="saleDetailsObj.tax_percent"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Tax Percent"
                />
                <div class="placeholder">Tax percent</div>
              </div>
            </div>
            <span (click)="addRepeater2()" style="padding-bottom: 2vw">
              <span
                ><img
                  class="plus-icon"
                  style="margin-right: 0.5vw"
                  src="assets/images/load-more.png"
              /></span>
                                    <span class="sub-text">Add to sale details</span>
                                    </span>
                                </div>
                                <div class="outer-box" *ngIf="saleDetails.controls.length > 0">
                                    <div class="row">
                                        <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                            <label class="tb-head ml-35">Final Sale Price</label>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                            <label class="tb-head">Discount</label>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                            <label class="tb-head">Taxable</label>
                                        </div>
                                        <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                            <label class="tb-head">Tax Percent</label>
                                        </div>
                                    </div>
                                    <div formArrayName="saleDetails" [dragula]="'task-group-2'" [(dragulaModel)]="saleDetails.controls">
                                        <div class="row mb-15 inner-box" formGroupName="{{ i }}" *ngFor="let item of saleDetails.controls; let i = index">
                                            <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                                                <span class="cross-icon" style="cursor: grab !important">
                    <i class="fa fa-ellipsis-v mr2"></i>
                    <i class="fa fa-ellipsis-v"></i>
                  </span>
                                                <input formControlName="finalSalePrice" type="number" class="width" />
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                                <input formControlName="discount" type="number" class="width" />
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-lg-3 text-left">
                                                <input formControlName="taxable" type="number" class="width" />
                                            </div>
                                            <div class="col-md-3 col-sm-3 col-lg-3 text-left d-flex">
                                                <input formControlName="taxPercent" type="number" class="width" />
                                                <span (click)="saleDetails.removeAt(i, 1)" class="cross-icon">X
                  </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                </form>
                <div class="footer-nav">
                    <div class="button-group">
                        <div (click)="onSubmit()" class="next">Save</div>
                        <div (click)="getValueWithAsync()" class="next">Save & Close</div>
                        <div [hidden]="true" class="next" style="margin-right: 1.5vw">
                            Duplicate
                        </div>
                    </div>
                </div>
                </div>
                </div>
                </div>
            </div>