import { EditNotifyService } from './../../../../new-artworks/edit-notify.service';
import { element } from 'protractor';

import {
  Component,
  ElementRef,
  EventEmitter,
  NgModule,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { faLock } from '@fortawesome/free-solid-svg-icons';
import { ImageUploadComponent } from 'src/app/core/image-upload/image-upload.component';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
// import { EditNotifyService } from '../edit-notify.service';

@Component({
  selector: 'app-add-artworks-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainArtworksComponent implements OnInit {
  faLock = faLock;
  editionArray = []
  artistProofArray = []
  isEditionModalOpen = false;
  @ViewChild('scrollTop') scrollTop: ElementRef;
  // @Output() newItemEvent = new EventEmitter<string>();

  Menuinit;

  EditionChange=false;
  isSubmited = false;
  isDropDown = false;
  isDropDown2 = false;
  dropDownValue = Array(10).fill(null);
  onChangeArray = Array(20).fill(false);
  isDropDownOpen;
  selectedFilesObj: any = {
    profileArr: [],
    highResArr: [],
    profileImage: '',
    highResImage: '',
  };
  selectedFiles: File[] = [];
  form: FormGroup;
  highResPlaceholder = 'High Res File to be sent to buyer';
  id;
  heyy() {
    console.log("asjhdb")
  }
  showMovement: boolean = false;
  accept: any = 'image/*';
  artwork_type = {
    'textile arts': 1,
    painting: 2,
    drawing: 3,
    photography: 4,
    digital: 5,
    print: 6,
    'mixed media': 7,
    video: 8,
    other: 9,
    sculpture: 10,
    installation: 11,
  };
  userType;

  @ViewChild(ImageUploadComponent)
  private imageUploadComponent: ImageUploadComponent;
  primaryImage: any;
  moveArr: any = [];
  selectedMovements: any = [];
  profileImage: any;
  permissionsObj: any = {};
  showObj: any = {};
  disableObj: any = {};
  placeholder: any = '';
  artist_type;
  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private server: CollectorService,
    private editNotifyService: EditNotifyService,

  ) { }
  ngOnInit(): void {
    this.router.events.subscribe((evt) => {
      if (!(evt instanceof NavigationEnd)) {
        return;
      }
      window.scrollTo(0, 0);
    });
    this.isDropDownOpen = Array(10).fill(false);
    this.getAllMovements();
    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    } else {

      this.artist_type = JSON.parse(decode)[
        'role_id'
      ]['artist_type'];
    }
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find((x) => x.name == 'Artworks')
      .tabsArr.find((x) => x.name == 'Main');
    console.log(this.permissionsObj);
    this.form = this.formBuilder.group({
      title: new FormControl(''),
      artistNote: new FormControl(''),
      year: new FormControl(''),
      createdAs: new FormControl(''),
      createdBy: new FormControl(''),
      dimensions: new FormControl(''),
      medium: new FormControl(''),
      showEdition: new FormControl(''),
      editionDataRecord: new FormControl(false),
      editionOFF: new FormControl(false),
      editionCount: new FormControl(''),
      artistProofCount: new FormControl(''),
      pluss: new FormControl(true),
      edition: new FormControl(''),
      freeze: new FormControl(false),
      type: new FormControl(''),
      physicalOrDigital: new FormControl(''),
      file_type: new FormControl(''),
      published: new FormControl(''),
      workType: new FormControl(''),
    });
    console.log(this.permissionsObj.fields);
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled
      if (ele.formControl && ele.show) {
        if (ele.mandatory) {
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled'] = (ele.disabled == 'true' || ele.disabled == true) ? true : false
        if (ele.disabled) {
          this.form.controls[ele.name].disable();
        }
        let obj = {}
        obj[ele.name] = ele.defaultValue
        // to patch default value
        if (!localStorage.getItem('artworkID')) {
          this.form.patchValue(obj)
        }
      }
    });

    // this.route.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getMainData(this.id).subscribe(async (data) => {
    // 			const art = data;
    // 			this.form.patchValue(art);
    // 			// this.selectedFiles.push(
    // 			// 	new File([''], art.primaryImage.originalname)
    // 			// );
    // 			let response = await fetch(
    // 				'https://api.artist.terrain.art/' + art.primaryImage.path
    // 			);
    // 			let da = await response.blob();
    // 			let metadata = {
    // 				type: 'image/jpeg',
    // 			};
    // 			let file = new File([da], art.primaryImage.originalname, metadata);
    // 			this.imageUploadComponent.onFileSelect([file]);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });


    this.form.valueChanges.subscribe(x => {
      // ('firstname value changed')
      //   console.log(x)
      // this.newItemEvent.emit("true");
      // Menuinit;
      if (this.Menuinit == null) {
        this.Menuinit = x;
      }
      else {
        this.editNotifyService.setOption('Main', true);
      }


    })
    console.log(this.disableObj);
  }



  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner()
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode == 200) {
        const artistRoleId = res.data.artist_id.role_id;
        let url = apiUrl.addRole + `/${artistRoleId}`;
        this.server.getApi(url).subscribe((res) => {
          if (res.statusCode == 200) {
            this.artist_type = res.data.artist_type;
          }
        });
        this.artist_type = res.data.artist_id;
        this.form.patchValue({
          title: res.data.artwork_title,
          year: res.data.year,
          createdAs: res.data.created,
          // createdBy: ,
          dimensions: res.data.dimensions,
          // movement: res.data.,
          //physicalOrDigital:res.data.type_of_work,
          medium: res.data.medium,
          showEdition: res.data.show_edition,
          edition: res.data.edition_details,
          type: res.data.artwork_type,
          file_type: res.data.file_type,
          published: res.data.publish_artworks,
          workType: res.data.work_type,
          artistNote: res.data.artwork_embedded_code,
          physicalOrDigital: res.data.physicalOrDigital,
        });
        // this.selectedMovements = res.data.movement;
        // this.selectedMovements = res.data.movement;
        if (res.data.primary_image.length > 0) {
          this.selectedFilesObj.profileArr = [...res.data.primary_image];
        }
        if (res.data.original_artwork_file.length > 0) {
          this.selectedFilesObj.highResArr = [
            ...res.data.original_artwork_file,
          ];
        }
      }
    });
  }

  // to get all moments
  getAllMovements() {
    this.server.showSpinner()
    let url = apiUrl.getMovement + '?limit=999&offset=1';
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode == 200) {
        this.moveArr = res.data;
      }
    });
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner()
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner()
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }
  // onSubmit() {
  // 	const data = this.form.value;
  // 	data.published = data.published === true ? 1 : 0;
  // 	data.showEdition = data.showEdition === true ? 1 : 0;
  // 	console.log(data.type);

  // 	data.type = this.artwork_type[data.type];

  // 	if (this.id) {
  // 		this.artistInfoService
  // 			.addMainData(this.form.value, this.selectedFiles, this.id)
  // 			.subscribe((data) => {
  // 				this.router.navigate([
  // 					'/artist-portal/settings/artwork/edit/' + this.id + '/details',
  // 				]);
  // 			});
  // 	} else {
  // 		this.artistInfoService
  // 			.addMainData(this.form.value, this.selectedFiles)
  // 			.subscribe((data) => {
  // 				localStorage.setItem('artID', data?.id);
  // 				this.router.navigate(['/artist-portal/settings/artwork/add/details']);
  // 			});
  // 	}
  // }

  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }

    if (
      this.permissionsObj.fields.find((x) => x.name == 'profileImage')
        .mandatory &&
      this.selectedFilesObj.profileArr.length == 0
    ) {
      alert('Please upload primary image!');
      return;
    }
    if (
      this.permissionsObj.fields.find((x) => x.name == 'highResFile')
        .mandatory &&
      this.selectedFilesObj.highResArr.length == 0
    ) {
      alert('Please upload original artwork!');
      return;
    }
    if (this.selectedFilesObj.profileArr.length > 0) {
      this.selectedFilesObj.profileArr[0].filedata = '';
      this.selectedFilesObj.profileArr[0].file_event = '';
    }
    if (this.selectedFilesObj.highResArr.length > 0) {
      this.selectedFilesObj.highResArr[0].filedata = '';
      this.selectedFilesObj.highResArr[0].file_event = '';
    }
    let url = apiUrl.addArtwork;
    let req = {
      artwork_title: this.form.getRawValue().title,
      artwork_type: this.form.getRawValue().type,
      artwork_embedded_code: this.form.getRawValue().artistNote,
      created: this.form.getRawValue().createdAs,
      dimensions: this.form.getRawValue().dimensions,
      edition_details: this.form.getRawValue().edition,
      medium: this.form.getRawValue().medium,
      primary_image: this.selectedFilesObj.profileArr,
      thumbnail_of_primary:
        this.selectedFilesObj.profileArr.length > 0
          ? this.selectedFilesObj.profileArr[0].url
          : '',
      hover_second_image:
        this.selectedFilesObj.profileArr.length > 0
          ? this.selectedFilesObj.profileArr[0].url
          : '',
      original_artwork_file: this.selectedFilesObj.highResArr,
      publish_artworks: this.form.getRawValue().published == true ? true : false,
      show_edition: this.form.getRawValue().showEdition == true ? true : false,
      work_type: this.form.getRawValue().workType,
      file_type: this.form.getRawValue().file_type,
      year: this.form.getRawValue().year,
      artwork_group:
        this.artist_type == 'Terrain' || this.artist_type == 'Gallery'
          ? 'curated'
          : 'open',
      physicalOrDigital: this.form.getRawValue().physicalOrDigital,
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }
    this.server.showSpinner()
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        // alert(res.message);
        this.editNotifyService.reset();
        this.isSubmited = true;
        alert(res.message);
      } else {
        this.isSubmited = false;
      }
    });
  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }
  }

  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }


  generateEdition(editionCount,ApCount ) {
    // console.log(editionCount, ApCount)
if(editionCount.length!==0 ||ApCount.length!==0 ){
    for (let index = 0; index < editionCount; index++) {
      this.editionArray.push({ 'index': index + 1, 'value': false })

    }
    for (let index = 0; index < ApCount; index++) {
      this.artistProofArray.push({ 'index': index + 1, 'value': false })
    }
    this.isEditionModalOpen = true;
}
  
  }

  SelectAll() {
    for (let index = 0; index < this.editionArray.length; index++) {
      const element = this.editionArray[index]
      element.value = true;
    }
    for (let index = 0; index < this.artistProofArray.length; index++) {
      const element = this.artistProofArray[index]
      element.value = true;
    }
  }
  SelectNone() {
    for (let index = 0; index < this.editionArray.length; index++) {
      const element = this.editionArray[index]
      element.value = false;
    }
    for (let index = 0; index < this.artistProofArray.length; index++) {
      const element = this.artistProofArray[index]
      element.value = false;
    }
  }

  freeze(values) {
    if (values.currentTarget.checked) {
      this.form.get('editionOFF').setValue(false);
      this.form.get('editionOFF').disable();
      this.form.get('editionCount').disable();
 this.form.get('artistProofCount').disable();
    }
    else {
      this.form.get('editionOFF').enable();
      this.form.get('editionCount').enable();
      this.form.get('artistProofCount').enable();
    }

  }

  createER() {
    this.isEditionModalOpen = false;
    for (let index = 0; index < this.editionArray.length; index++) {
      if (this.editionArray[index].value == true) {
        let removed = this.editionArray.splice(index, 1);
      }

    }
    for (let index = 0; index < this.artistProofArray.length; index++) {
      if (this.artistProofArray[index].value == true) {
        let removed = this.artistProofArray.splice(index, 1);
      }

    }
  }


}
