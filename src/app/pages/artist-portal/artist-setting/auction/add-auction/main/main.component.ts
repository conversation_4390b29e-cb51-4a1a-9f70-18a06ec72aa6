import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { faTimes, faGripVertical } from '@fortawesome/free-solid-svg-icons';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
})
export class MainComponent implements OnInit {
  faTimes = faTimes;
  faGripVertical = faGripVertical;
  form: FormGroup;
  selectedArtworks = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  isDropDownOpen = Array(10).fill(false);
  selectedFilesObj = {
    auctionThumbnail: [],
    auctionBannerImage: [],
    titleBannermedia: [],
    bannerBackgroundImage: [],
    thumbnailArr: [],
    bannerMediaArr: [],
    bannerBackgroundArr: [],
    bannerImage: '',
    blogThumbnail: '',
  };
  placeholder: any = '';
  acceptType: any = 'image/*';
  permissionsObj: any = {};
  isArtworkPopupOpen = false;
  isArtworkDropdownOpen1 = Array(10).fill(false);
  isArtworkDropdownOpen2 = Array(10).fill(false);
  isArtworkDropdownOpen3 = Array(10).fill(false);
  isFilterDropdownOpen = false;
  isFilterDropdownOpen1 = false;
  sort;
  filter;
  activeMenu = 'main';
  isAddNewModalOpen = false;
  routerObserver: Subscription;

  searchKey: any = '';
  offset: number = 1;
  totalPage: number = 0;
  artworksArr: any = [];
  search: Subject<any> = new Subject();
  totalRecords: number = 0;
  totalPages: number = 0;
  selectedIndex: any;
  selectedArtworksArr: any = [];
  constructor(
    private formBuilder: FormBuilder,
    public server: CollectorService,
    private router: Router
  ) {
    let decode = decodeURIComponent(
      escape(window.atob(localStorage.getItem('userDetails')))
    );
    this.permissionsObj = JSON.parse(decode)['role_id']['permissions'].find(
      (x) => x.name == 'Exhibitions'
    );
    console.log(this.permissionsObj);
  }
  changeFocusArt(dropname, index) {
    console.log('change Focus');
    switch (dropname) {
      case 'isArtworkDropdownOpen1':
        {
          console.log(121212);
          setTimeout(() => {
            this.isArtworkDropdownOpen1[index] = false;
          }, 200);
        }
        break;

      default:
        break;
    }
  }
  ngOnInit(): void {
    this.searchByKey();
    this.getArtworks();
    this.changeActiveMenu(this.router.url);
    this.routerObserver = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeActiveMenu(event.urlAfterRedirects);
      }
    });
    this.form = this.formBuilder.group({
      publishAuction: new FormControl(false),
      pageUrl: new FormControl(''),
      auctionThumbnail: new FormControl(''),
      auctionBannerImage: new FormControl(''),
      headerColour: new FormControl(''),
      titleBannerType: new FormControl(''),
      titleBannermedia: new FormControl(''),
      bannerBackgroundImage: new FormControl(''),
      bannerTextColor: new FormControl(''),
      titleSize: new FormControl(false),
      showAboveSubtitle: new FormControl(false),
      aboveSubtitletext: new FormControl(''),
      aboveSubtitlesize: new FormControl(''),
      belowSubtitlesize: new FormControl(''),
      showBelowSubtitle: new FormControl(false),
      // position:new FormControl(''),
      belowSubtitletext: new FormControl(''),
      showArtistName: new FormControl(false),
      artistName: new FormControl(''),
      artistNameSize: new FormControl(''),
      showDate: new FormControl(false),
      auctionStartDate: new FormControl(''),
      auctionStartTime: new FormControl(''),
      auctionEndTime: new FormControl(''),
      bidIncrement: new FormControl(''),
      expressInterestDuration: new FormControl(''),
      biddingDuration: new FormControl(''),
      description: new FormControl(''),
      enableReadMore: new FormControl(false),
      auctionVideo: new FormControl(''),
      videoText: new FormControl(''),
      artworkGrid: new FormControl(''),
      artworkSelect: new FormControl(''),
      showPrice: new FormControl(true),
      artworkInterior: new FormControl(true),
      quote: new FormControl(''),
      quoteSize: new FormControl(''),
      quoteSource: new FormControl(''),
      quoteMediaPosition: new FormControl(''),
      quoteMedia: new FormControl(''),
      quoteMediaURL: new FormControl(''),
      quoteMediaBackground: new FormControl(''),
      playAudio: new FormControl(''),
      audioLevel: new FormControl(''),
      audioFile: new FormControl(''),
      audioLoop: new FormControl(''),
      artistProfile: new FormControl(''),
      artistMediaType: new FormControl(''),
      artistMedia: new FormControl(''),
      showProfileLink: new FormControl(false),
      profileURL: new FormControl(''),
      artistsProfile: new FormControl(''),
      bannertype: new FormControl(''),
      bannermedia: new FormControl(''),
      showTitle: new FormControl(false),
      titleText: new FormControl(''),
      showButtons: new FormControl(false),
      buttonText: new FormControl(''),
      buttonURL: new FormControl(''),
      allowUnregisteredUsers: new FormControl(''),
      emailID: new FormControl(''),
      userName: new FormControl(''),
      KYCStatus: new FormControl(''),
      interestedArtworks: new FormControl(''),
      auctionArtworks: new FormControl(''),
    });
    if (localStorage.getItem('auctionID')) {
      this.getExhibitionById();
    }
  }

  // to get exhibition by id
  getExhibitionById() {
    let url = `api/auctions/${localStorage.getItem('auctionID')}`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.form.patchValue({
          publishAuction: res.data.publishAuction,
          pageUrl: res.data.pageURL,
          headerColour: res.data.headerColour,
          auctionStartDate: res.data.auctionStartDate,
          auctionStartTime: res.data.auctionStartTime,
          auctionEndTime: res.data.auctionEndTime,
          bidIncrement: res.data.bidIncrement,
          expressInterestDuration: res.data.expressInterestDuration,
          biddingDuration: res.data.biddingDuration,
          showPrice: res.data.showPrice,
          titleBannerType: res.data.titleBannerType,
          titleBannermedia:
            res.data.titleBannerType == 'Video'
              ? res.data.titleBannermedia
              : null,
          bannerTextColor: res.data.bannerTextColour,
          titleSize: res.data.titleSize,
          showAboveSubtitle: res.data.showAboveSubtitle,
          aboveSubText: res.data.aboveSubtitletext,
          aboveSubSize: res.data.aboveSubtitlesize,
          showBelowSubtitle: res.data.showbelowsubtitle,
          belowSubtitletext: res.data.belowSubtitletext,
          showArtistName: res.data.showArtistName,
          artistNameSize: res.data.artistNameSize,
          showDate: res.data.showDates,
          titleText: res.data.titleText,
        });
        if (res.data.auctionThumbnail) {
          this.selectedFilesObj.auctionThumbnail.push({
            url: res.data.auctionThumbnail,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.auctionBannerImage) {
          this.selectedFilesObj.auctionBannerImage.push({
            url: res.data.auctionBannerImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.titleBannerType != 'Video' && res.data.titleBannermedia) {
          this.selectedFilesObj.titleBannermedia.push({
            url: res.data.titleBannermedia,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.bannerBackgroundImage) {
          this.selectedFilesObj.bannerBackgroundImage.push({
            url: res.data.bannerBackgroundImage,
            type: 'image',
            name: 'File',
            size: '',
          });
        }
        if (res.data.artworkSelect) {
          this.selectedArtworksArr = res.data.artworkSelect;
        }
      }
    });
  }

  onFileSelect(files, key) {
    this.selectedFilesObj[key] = files;
    console.log(this.selectedFilesObj);
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    } else {
      this.uploadFile(key, files);
    }
  }

  // to upload file
  uploadFile(key, files: any) {
    let formdata = new FormData();
    formdata.append('image', files[0].file_event);
    let url = apiUrl.upload;
    this.server.showSpinner();
    this.server.postApi(url, formdata).subscribe(
      (res) => {
        this.server.hideSpinner();
        if (res.statusCode == 200) {
          this.selectedFilesObj[key][0]['url'] = res.data;
          alert('File uploaded successfully!');
        }
      },
      (err) => {
        alert(err.error.message);
      }
    );
  }

  onMultiAdd(files, key) {
    this.selectedFilesObj[key] = files;
    if (files.length == 0) {
      this.selectedFilesObj[key] = [];
    }
  }

  onSubmit() {
    // if (!this.permissionsObj.update) {
    //   alert('Update permissions denied!');
    //   return;
    // }
    let req = {
      publishAuction: this.form.value.publishAuction,
      pageURL: this.form.value.pageUrl,
      auctionThumbnail: this.selectedFilesObj.auctionThumbnail?.[0]?.url,
      auctionBannerImage: this.selectedFilesObj.auctionBannerImage?.[0]?.url,
      headerColour: this.form.value.headerColour,
      auctionStartDate: this.form.value.auctionStartDate,
      auctionStartTime: this.form.value.auctionStartTime,
      auctionEndTime: this.form.value.auctionEndTime,
      bidIncrement: this.form.value.bidIncrement,
      expressInterestDuration: this.form.value.expressInterestDuration,
      biddingDuration: this.form.value.biddingDuration,
      artworkGrid: '',
      showPrice: this.form.value.showPrice,
      titleBannerType: this.form.value.titleBannerType,
      titleBannermedia:
        this.form.value.titleBannerType == 'Video'
          ? this.form.value.titleBannermedia
          : this.selectedFilesObj.titleBannermedia?.[0]?.url,
      bannerBackgroundImage:
        this.selectedFilesObj.bannerBackgroundImage?.[0]?.url,
      bannerTextColour: this.form.value.bannerTextColor,
      titleSize: this.form.value.titleSize,
      showAboveSubtitle: this.form.value.showAboveSubtitle,
      aboveSubtitletext: this.form.value.aboveSubText,
      aboveSubtitlesize: this.form.value.aboveSubSize,
      showbelowsubtitle: this.form.value.showBelowSubtitle,
      belowSubtitletext: this.form.value.belowSubtitletext,
      showArtistName: this.form.value.showArtistName,
      artistNameSize: this.form.value.artistNameSize,
      showDates: this.form.value.showDate,
      titleText: this.form.value.titleText,
      titleBanner: {
        type: this.form.value.titleBannerType,
        media:
          this.form.value.titleBannerType == 'Video'
            ? this.form.value.titleBannermedia
            : this.selectedFilesObj.titleBannermedia?.[0]?.url,
        backgroundImage: this.selectedFilesObj.bannerBackgroundImage?.[0]?.url,
        textColor: this.form.value.bannerTextColor,
        titleSize: this.form.value.titleSize,
        aboveSubTitle: {
          show: this.form.value.showAboveSubtitle,
          text: this.form.value.aboveSubText,
          size: this.form.value.aboveSubSize,
        },
        belowSubTitle: {
          show: this.form.value.showBelowSubtitle,
          text: this.form.value.belowSubtitletext,
          size: this.form.value.belowSubtitlesize,
        },
        position: 0,
        showArtistName: this.form.value.showArtistName,
        artistNameSize: this.form.value.artistNameSize,
        showDate: this.form.value.showDate,
      },
    };
    let artworks_id = [];
    this.selectedArtworksArr.forEach((ele_arr) => {
      artworks_id.push(ele_arr['_id']);
    });
    req['artworkSelect'] = artworks_id;
    let url = localStorage.getItem('auctionID')
      ? 'api/auctions' + `/${localStorage.getItem('auctionID')}`
      : 'api/auctions';
    if (localStorage.getItem('auctionID')) {
      this.updateBlog(req, url);
      return;
    } else {
      this.server.postApi(url, req).subscribe((res) => {
        if (res.statusCode == 200) {
          localStorage.setItem('auctionID', res.data['_id']);
          alert(res.message);
        }
      });
    }
  }

  // to update blog
  updateBlog(req, url) {
    this.server.patchApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        localStorage.setItem('auctionID', res.data['_id']);
        alert(res.message);
        //this.router.navigate(['artist-portal/settings/exhibitions']);
      }
    });
  }

  changeFocus(index) {
    // console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 500);
  }

  changeActiveMenu(url: string) {
    switch (url) {
      case '/artist-portal/settings/auction/add':
        this.activeMenu = 'main';
        break;

      case '/artist-portal/settings/auction/add/financial':
        this.activeMenu = 'financial';
        break;

      default:
        this.activeMenu = 'main';
        break;
    }
  }

  navigateTo(path, type) {
    // if(localStorage.getItem('exhibitionIdID')){
    this.router.navigate([path]);
    // }else {
    //   alert('Please create auction first!')
    // }
  }

  removeItem(i) {
    if (confirm('Are you sure to remove this artwork form list !')) {
      console.log('true');
    } else {
      console.log('false');
    }
  }
  searchResult(evt) {
    var charCode = evt.which ? evt.which : evt.keyCode;
    let string = evt.target.value;
    if (charCode == 13 || charCode == 8 || charCode == 16) {
      return;
    } else {
      this.search.next(string.trim());
    }
  }
  searchByKey() {
    if (navigator.onLine) {
      this.search.pipe(debounceTime(1500)).subscribe((val) => {
        this.searchKey = val;
        this.offset = 1;
        this.totalPage = 0;
        this.getArtworks();
      });
    }
  }
  getArtworks() {
    let url = apiUrl.getArtwork;
    let req = {
      search: this.searchKey,
      external: false,
      artworkIds: [],
      platform: '',
      userId: '',
      offset: this.offset,
      limit: 5,
      sort: this.sort == 'Desc' ? 0 : 1,
      publish_artworks: true,
      priceMin: 0,
      priceMax: 0,
      sale_options: '',
      artwork_group: '',
      approved_status: '',
      artist: [],
      medium: [],
      subject: [],
      movement: [],
      tags: [],
      flag: null,
    };

    this.server.postApi(url, req).subscribe((res) => {
      if (res.statusCode == 200) {
        this.artworksArr = res.data;
        this.artworksArr.forEach((ele) => {
          ele['selected'] = false;
        });
        this.totalRecords = res.fiter_totalCount;
        this.totalPage = Math.ceil(res.fiter_totalCount / 5);
      }
    });
  }

  // manage selected artworks
  manageSelectedArtworks() {
    this.isArtworkPopupOpen = false;
    this.artworksArr.forEach((ele) => {
      let index = this.selectedArtworksArr.findIndex(
        (x) => x['_id'] == ele['_id']
      );
      if (ele.selected && index == -1) {
        this.selectedArtworksArr.push(ele);
      }
    });
  }
}
