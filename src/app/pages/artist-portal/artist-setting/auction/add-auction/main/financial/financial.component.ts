import { EditNotifyService } from './../../../../new-artworks/edit-notify.service';
import { ArtworkService } from './../../../../../services/artwork.service';
// import { EditNotifyService } from './../edit-notify.service';
import {
  Component,
  ElementRef,
  NgModule,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { faLock } from '@fortawesome/free-solid-svg-icons';
import { ActivatedRoute, Router } from '@angular/router';
import { DragulaService } from 'ng2-dragula';
import { flagData } from 'src/app/pages/collector/profile/personal/flags';
import { ArtistInfoService } from 'src/app/services/artist-info.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
// import { ArtistService } from '../../../services/artist.service';
// import { ArtworkService } from '../../../services/artwork.service';
@Component({
  selector: 'app-add-artworks-financial',
  templateUrl: './financial.component.html',
  styleUrls: ['./financial.component.scss'],
})
export class FinancialArtworksComponent implements OnInit {
  faLock=faLock;
  dropDownValue = Array(10).fill(null);
  htmlContent = Array(10).fill(null);
  isSubmited = false;
  isDropDownOpen = Array(10).fill(false);
  priceHistoryObj: any = {
    artist_price: '',
    sale_price: '',
    pricing_date: '',
  };

  selectedFiles: File[] = [];

  // repeaters = [
  // 	{
  // 		isOpen: true,
  // 		Artist_Price: null,
  // 		Sale_Price: null,
  // 		Discount: null,
  // 		Pricing_Date: null,
  // 	},
  // ];
  // repeaters2 = [
  // 	{
  // 		isOpen: true,
  // 		Final_Sale_Price: null,
  // 		Discount: null,
  // 		Taxable: null,
  // 		Tax_Percent: null,
  // 	},
  // ];
  form: FormGroup;
  id;
  saleDetailsObj = {
    final_sale_price: '',
    discount: '',
    taxable: '',
    tax_percent: '',
  };
  permissionsObj: any = {};
  showObj: any = {};
  Menuinit: any;
  disableObj: any = {};
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private artworkService: ArtworkService,
    private artistInfoService: ArtistInfoService,
    private server: CollectorService,
    private editNotifyService: EditNotifyService
  ) {
    // dragulaService.createGroup("task-group", {
    // 	removeOnSpill: true
    //   });
  }

  ngOnInit(): void {
    if (localStorage.getItem('artworkID')) {
      this.getArtWork();
    }

    let decode = decodeURIComponent(escape(window.atob(localStorage.getItem('userDetails'))));
    this.permissionsObj  = JSON.parse(decode)[
      'role_id'
    ]['permissions'].find((x) => x.name == 'Artworks').tabsArr.find(x=>x.name=='Financial');
    this.form = this.formBuilder.group({
      catalogNumber: new FormControl(''),
      priceHistory: this.formBuilder.array([]),
      saleOption: new FormControl(''),
      soldDate: new FormControl(''),
      saleInfo: new FormControl(''),
      saleDetails: this.formBuilder.array([]),
    });
    console.log(this.permissionsObj.fields)
    this.permissionsObj.fields.forEach((ele) => {
      this.showObj[ele.name] = ele.show;
      this.disableObj[ele.name] = ele.disabled
      if (ele.parent) {
        ele.child.forEach(ele_child => {
          this.priceHistoryObj[ele_child.name] = ele_child.defaultValue
          this.showObj[ele_child.name] = ele_child.show;
        });
      }
      if (ele.formControl && ele.show) {
        if(ele.mandatory){
          this.form.controls[ele.name].setValidators([Validators.required]);
        }
        ele['disabled']= (ele.disabled == 'true' || ele.disabled == true) ? true:false
        if(ele.disabled){
          this.form.controls[ele.name].disable();
        }
        let obj = {}
        obj[ele.name] = ele.defaultValue
        // console.log(obj)
        // to patch default value
        if (!localStorage.getItem('artworkID')) {
          this.form.patchValue(obj)
        }
      }
    });
   
    
    // this.route.parent.paramMap.subscribe((params) => {
    // 	if (params.get('id')) {
    // 		this.id = params.get('id');
    // 		this.artistInfoService.getFinancialData(this.id).subscribe((data) => {
    // 			const art = data;
    // 			art.priceHistory.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater();
    // 				}
    // 			});
    // 			art.saleDetails.forEach((a, i) => {
    // 				if (i !== 0) {
    // 					this.addRepeater2();
    // 				}
    // 			});
    // 			this.form.patchValue(art);
    // 		});
    // 	} else {
    // 		this.id = null;
    // 	}
    // });
    this.form.valueChanges.subscribe(x => {
      if (this.Menuinit == null) {
        this.Menuinit = x;
      }
      else {
        this.editNotifyService.setOption('Financial', true);
      }


    })
    console.log(this.disableObj);
  }

  // to get artwork
  getArtWork() {
    let url = apiUrl.getArtwork + `/${localStorage.getItem('artworkID')}`;
    this.server.showSpinner()
    this.server.getApi(url).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode == 200) {
        this.form.patchValue({
          catalogNumber: res.data.catalog_number,
          saleOption: res.data.sale_options,
          saleInfo:res.data.sale_information,
          soldDate:res.data.sold_date,
          // priceHistory:res.data.price_history,
          // saleDetails:res.data.sale_details
        });
        res.data.price_history.forEach((ele, index) => {
          this.addRepeater();
          this.form['controls'].priceHistory['controls'][index]
            .get('artist_price')
            .setValue(ele.artist_price);
          this.form['controls'].priceHistory['controls'][index]
            .get('sale_price')
            .setValue(ele.sale_price);
          this.form['controls'].priceHistory['controls'][index]
            .get('pricing_date')
            .setValue(ele.pricing_date);
        });
        res.data.sale_details.forEach((ele, index) => {
          this.addRepeater2();
          this.form['controls'].saleDetails['controls'][index]
            .get('finalSalePrice')
            .setValue(ele.finalSalePrice);
          this.form['controls'].saleDetails['controls'][index]
            .get('discount')
            .setValue(ele.discount);
          this.form['controls'].saleDetails['controls'][index]
            .get('taxable')
            .setValue(ele.taxable);
          this.form['controls'].saleDetails['controls'][index]
            .get('taxPercent')
            .setValue(ele.taxPercent);
        });
      }
    });
  }

  async onFileSelect(files: FileList) {
    if (files[0].size < 2100000) {
      this.selectedFiles.push(files[0]);
    }
  }
  removeItem(index) {
    this.selectedFiles.splice(index, 1);
  }

  get priceHistory(): FormArray {
    return <FormArray>this.form.get('priceHistory');
  }

  get saleDetails(): FormArray {
    return <FormArray>this.form.get('saleDetails');
  }
  // this.priceHistoryObj.pricing_date.split('-').reverse().join('/')
  addRepeater() {
    let obj = {}

    if (this.showObj.artist_price) {
      obj['artist_price'] = new FormControl(this.priceHistoryObj.artist_price)
    }
    if (this.showObj.sale_price) {
      obj['sale_price'] = new FormControl(this.priceHistoryObj.sale_price)
    }
    if (this.showObj.pricing_date) {
      obj['pricing_date'] = new FormControl(this.priceHistoryObj.pricing_date)
    }
    this.priceHistory.push(
      this.formBuilder.group(obj)
    );
    // this.priceHistoryObj={
    // 	artist_price: '',
    // 	sale_price: '',
    // 	pricing_date:''
    // }
  }
  addRepeater2() {
    this.saleDetails.push(
      this.formBuilder.group({
        // isOpen: new FormControl(true),
        finalSalePrice: new FormControl(this.saleDetailsObj.final_sale_price),
        discount: new FormControl(this.saleDetailsObj.discount),
        taxable: new FormControl(this.saleDetailsObj.taxable),
        taxPercent: new FormControl(this.saleDetailsObj.tax_percent),
      })
    );
    // this.saleDetailsObj={
    // 	final_sale_price: '',
    // 	discount: '',
    // 	taxable:'',
    // 	tax_percent:''
    // }
  }
  onSubmit() {
    if (this.form.invalid) {
      alert('Form not valid.Please fill required fields correctly !');
      return;
    }
    if (
      this.form.value.priceHistory.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'priceHistory').mandatory
    ) {
      alert('Please add price history !');
      return;
    }
    if (
      this.form.value.saleDetails.length == 0 &&
      this.permissionsObj.fields.find((x) => x.name == 'saleDetails').mandatory
    ) {
      alert('Please add sale details !');
      return;
    }
    console.log(this.form.value.priceHistory);
    let url = apiUrl.addArtwork;
    let req = {
      catalog_number: this.form.getRawValue().catalogNumber,
      price_history: this.form.getRawValue().priceHistory,
      sale_options: this.form.getRawValue().saleOption.replace(/ /g, ''),
      sale_details: this.form.getRawValue().saleDetails,
      sold_date: this.form.getRawValue().soldDate,
      sale_information:this.form.getRawValue().saleInfo
    };

    if (localStorage.getItem('artworkID')) {
      req['artworkId'] = localStorage.getItem('artworkID');
    }
    this.server.showSpinner()
    this.server.postApi(url, req).subscribe((res) => {
      this.server.hideSpinner()
      if (res.statusCode == 200) {
        localStorage.setItem('artworkID', res.data['_id']);
        this.editNotifyService.reset();
        alert(res.message);
        this.isSubmited = true;
      }
      else {
        this.isSubmited = false;
      }

    });
  }
  changeFocus(index) {
    console.log('in in ');
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }
  async getValueWithAsync() {
    await this.onSubmit();
    if (this.isSubmited) {
      this.router.navigate(['/artist-portal/settings/artworks']);
    }

  }
}
