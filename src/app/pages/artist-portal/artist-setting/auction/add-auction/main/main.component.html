<div class="container__main">
  <div class="content__section">
    <div class="profile-field">
      <div class="w-100 field-contents">
        <form [formGroup]="form">
          <div class="mandatory_elements">
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Publish Auction :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="publishAuction" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to publish the auction on the website.
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="text"
                  placeholder="Autogenerated, unique URL"
                  formControlName="pageUrl"
                />
                <div class="placeholder">Page URL</div>
                <div class="input-info">
                  Exact details to give in URL name for the auction (max two
                  words).
                </div>
              </div>
            </div>
            <div style="margin-top: 2.08vw; font-size: 1.25vw">
              Auction Thumbnail
            </div>
            <div class="field-value flex-block">
              <image-upload
                [accept]="'image/*'"
                [selectedData]="selectedFilesObj?.auctionThumbnail"
                (onFileChange)="onFileSelect($event, 'auctionThumbnail')"
                [fileSize]="1048576"
                [placeholder]="placeholder"
              ></image-upload>

              <div class="input-info">
                Upload image or GIF to be used as auction thumbnail.
              </div>
            </div>
            <div style="margin-top: 2.08vw; font-size: 1.25vw">
              Auction Banner Image
            </div>
            <div class="field-value flex-block">
              <image-upload
                [accept]="'image/*'"
                [selectedData]="selectedFilesObj?.auctionBannerImage"
                (onFileChange)="onFileSelect($event, 'auctionBannerImage')"
                [fileSize]="1048576"
                [placeholder]="placeholder"
              ></image-upload>

              <div class="input-info">
                Upload image or GIF to be used as banner on the website to
                represent the auction. The image will be centered to aspect
                ratio 1440x300 px on Desktop pages.
              </div>
            </div>

            <div class="title_section">
              <div class="splitter">
                <div class="field-value">
                  <div class="input-container" (focusout)="changeFocus(0)">
                    <input
                      (focusin)="isDropDownOpen[0] = true"
                      type="text"
                      class="selection"
                      placeholder="Title Banner Type"
                      formControlName="titleBannerType"
                      [readonly]="true"
                    />
                    <div class="placeholder">Title Banner Type</div>
                    <button
                      (click)="isDropDownOpen[0] = !isDropDownOpen[0]"
                      type="button"
                    >
                      <img
                        src="assets/icons/arrow-down.png"
                        class="flag-arrow"
                      />
                    </button>
                    <div
                      [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[0],
                        'dropdown-visible': isDropDownOpen[0]
                      }"
                    >
                      <ul>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form
                              .get('titleBannerType')
                              .setValue('Fullscreen Image')
                          "
                        >
                          <div class="country-name">Fullscreen Image</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'video/*';
                            form.get('titleBannerType').setValue('Video')
                          "
                        >
                          <div class="country-name">Video</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('titleBannerType').setValue('GIF')
                          "
                        >
                          <div class="country-name">GIF</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form
                              .get('titleBannerType')
                              .setValue('Artwork on wall')
                          "
                        >
                          <div class="country-name">Artwork on wall</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[0] = false;
                            acceptType = 'image/*';
                            form.get('titleBannerType').setValue('Half-Banner')
                          "
                        >
                          <div class="country-name">Half-Banner</div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="input-info">Choose the type of Banner to use</div>
                </div>
                <div class="field-value">
                  <div class="input-container" (focusout)="changeFocus(2)">
                    <input
                      (focusin)="isDropDownOpen[2] = true"
                      type="text"
                      class="selection"
                      placeholder="Header Colour"
                      formControlName="headerColour"
                      [readonly]="true"
                    />
                    <div class="placeholder">Header Colour</div>
                    <button
                      (click)="isDropDownOpen[2] = !isDropDownOpen[2]"
                      type="button"
                    >
                      <img
                        src="assets/icons/arrow-down.png"
                        class="flag-arrow"
                      />
                    </button>
                    <div
                      [ngClass]="{
                        'dropdown-hidden': !isDropDownOpen[2],
                        'dropdown-visible': isDropDownOpen[2]
                      }"
                    >
                      <ul>
                        <li
                          (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Normal')
                          "
                        >
                          <div class="country-name">Normal</div>
                        </li>
                        <li
                          (click)="
                            isDropDownOpen[2] = false;
                            form.get('headerColour').setValue('Inverted')
                          "
                        >
                          <div class="country-name">Inverted</div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="input-info">
                    Choose whether to use regular logo with black font, or
                    inverted logo with white font (depending on the Banner Media
                    chosen)
                  </div>
                </div>
              </div>

              <div
                class="image-type"
                *ngIf="
                  form.value.titleBannerType !== 'Video' &&
                  form.value.titleBannerType
                "
              >
                <!-- Banner-->
                <div style="margin-top: 2.08vw; font-size: 1.25vw">
                  Title Banner media
                </div>
                <div class="field-value flex-block">
                  <image-upload
                    [accept]="acceptType"
                    [selectedData]="selectedFilesObj?.titleBannermedia"
                    (onFileChange)="onFileSelect($event, 'titleBannermedia')"
                    [fileSize]="1048576"
                    [placeholder]="placeholder"
                  ></image-upload>
                  <div class="input-info">
                    Provide the media to be used in the banner.
                  </div>
                </div>
              </div>

              <div
                class="video-type"
                *ngIf="form.value.titleBannerType == 'Video'"
              >
                <div class="splitter">
                  <div class="field-value" style="padding-right: 0.5vw">
                    <input
                      type="text"
                      placeholder="Banner Title"
                      formControlName="titleBannermedia"
                    />
                    <div class="placeholder">Title Banner Video</div>
                    <div class="input-info">
                      Provide the Vimeo player link for the video to be featured
                      as title banner. e.g.:
                      https://www.player.vimeo.com/12342143.
                    </div>
                  </div>
                </div>
              </div>
              <div
                *ngIf="form.get('titleBannerType').value == 'Artwork on wall'"
              >
                <div class="upload-head">Banner Background Image</div>

                <div class="field-value flex-block">
                  <image-upload
                    [accept]="'image/*'"
                    [selectedData]="selectedFilesObj?.bannerBackgroundImage"
                    (onFileChange)="
                      onFileSelect($event, 'bannerBackgroundImage')
                    "
                    [fileSize]="1048576"
                    [placeholder]="placeholder"
                  ></image-upload>
                  <div class="input-info">
                    Upload a wall background image to place the artwork on (a
                    gallery wall image)
                  </div>
                </div>
              </div>

              <!-- Banner Title-->
              <div style="margin-top: 2.08vw; font-size: 1.25vw">
                Auction Title
              </div>
              <div class="field-value">
                <!-- (keypress)="server.toCheckSpace($event, form.value.titleText)" -->
                <angular-editor
                  id="editor20"
                  formControlName="titleText"
                ></angular-editor>
                <div class="input-info">
                  Provide the Auction title to be used in the banner. Maximum 25
                  characters.
                </div>
              </div>
              <div class="splitter">
                <div class="field-value" style="padding-right: 0.5vw">
                  <input
                    type="text"
                    formControlName="titleSize"
                    placeholder="Text-Size"
                  />
                  <div class="placeholder">Title Size</div>
                  <div class="input-info">
                    Provide the font size for the Auction title on the banner
                    (between 1 and 3).
                  </div>
                </div>
                <div class="field-value" style="position: relative">
                  <input
                    type="color"
                    formControlName="bannerTextColor"
                    placeholder="HEX code"
                  />
                  <!-- <div class="forHex"></div> -->
                  <div class="placeholder">Banner Text Colour</div>
                  <div class="input-info">
                    Provide the HEX code for the banner text colours (eg:
                    #6fa8dc).
                  </div>
                </div>
              </div>
            </div>

            <!--  -->

            <div class="field-value">
              <div>
                <!-- /* border: 0.069vw solid var(--timeline-color); */ -->
                <div>
                  <!-- <div class="field-value">
                        <div class="input-container">
                          <div class="text-before">Show Artworks :</div>
                          <label class="switch">
                            <input
                              type="checkbox"
                              formControlName="showArtworks"
                            />
                            <span class="slider round"></span>
                          </label>
                        </div>
                        <div class="input-info">
                          Choose whether to show artworks on the Exhibition
                          page.
                        </div>
                      </div> -->
                  <div class="splitter">
                    <div class="field-value" style="padding-right: 0.5vw">
                      <div
                        tabindex="-1"
                        class="input-container"
                        (click)="
                          isArtworkDropdownOpen1[0] = !isArtworkDropdownOpen1[0]
                        "
                        (focusout)="changeFocusArt('isArtworkDropdownOpen1', 0)"
                      >
                        <input
                          type="text"
                          class="selection"
                          formControlName="artworkGrid"
                          placeholder="Artwork Grid"
                          [readonly]="true"
                        />
                        <div class="placeholder">Artwork Grid</div>
                        <button type="button">
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="
                            isArtworkDropdownOpen1[0]
                              ? 'dropdown-visible'
                              : 'dropdown-hidden'
                          "
                        >
                          <ul>
                            <li
                              (click)="
                                form.get('artworkGrid').setValue('Single')
                              "
                            >
                              <div class="country-name">Single</div>
                            </li>
                            <li
                              (click)="form.get('artworkGrid').setValue('2x1')"
                            >
                              <div class="country-name">2x1</div>
                            </li>
                            <li
                              (click)="form.get('artworkGrid').setValue('3x1')"
                            >
                              <div class="country-name">3x1</div>
                            </li>
                            <li
                              (click)="form.get('artworkGrid').setValue('4x1')"
                            >
                              <div class="country-name">4x1</div>
                            </li>
                            <li
                              (click)="form.get('artworkGrid').setValue('5x1')"
                            >
                              <div class="country-name">5x1</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div class="input-info">
                        Choose the grid to display the artworks in.
                      </div>
                    </div>
                    <!-- <div class="field-value">
                      <div
                        class="input-container"
                        (click)="
                          isArtworkDropdownOpen3[0] = !isArtworkDropdownOpen3[0]
                        "
                        (focusout)="isArtworkDropdownOpen3[0] = false"
                      >
                        <input
                          type="text"
                          class="selection"
                          formControlName="auctionCurrency"
                          placeholder=""
                          disabled
                        />
                        <div class="placeholder">Auction Currency</div>
                        <button type="button">
                          <img
                            src="assets/icons/arrow-down.png"
                            class="flag-arrow"
                          />
                        </button>
                        <div
                          [ngClass]="
                            isArtworkDropdownOpen3[0]
                              ? 'dropdown-visible'
                              : 'dropdown-hidden'
                          "
                        >
                          <ul>
                            <li
                              (click)="
                                form.get('auctionCurrency').setValue('INR')
                              "
                            >
                              <div class="country-name">INR</div>
                            </li>
                            <li
                              (click)="
                                form.get('auctionCurrency').setValue('USD')
                              "
                            >
                              <div class="country-name">USD</div>
                            </li>
                            <li
                              (click)="
                                form.get('auctionCurrency').setValue('GBP')
                              "
                            >
                              <div class="country-name">GBP</div>
                            </li>
                            <li
                              (click)="
                                form.get('auctionCurrency').setValue('EUR')
                              "
                            >
                              <div class="country-name">EUR</div>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div class="input-info">Choose currency.</div>
                    </div> -->
                  </div>
                  <div style="margin-top: 2.08vw; font-size: 1.25vw">
                    Artwork Select
                  </div>
                  <div class="contain_">
                    <div class="selection_container">
                      <div class="selection_headings">
                        <!-- <div class="property_title box">Lot No.</div> -->
                        <div class="property_title box">ID</div>
                        <div class="property_title box2">Primary Image</div>
                        <div class="property_title box2">Artist Name</div>
                        <div class="property_title box2">Artwork Title</div>
                        <div class="property_title box">Year</div>
                        <div class="property_title box">Action</div>
                      </div>
                      <div
                        class="selection_data"
                        [dragula]="'task-group'"
                        [(dragulaModel)]="selectedArtworks"
                      >
                        <div
                          class="artwork_details"
                          *ngFor="
                            let option of selectedArtworksArr;
                            let j = index
                          "
                        >
                          <div class="properties box">
                            {{ option?._id }}
                          </div>
                          <div class="properties box2">
                            <img
                              [src]="
                                option?.thumbnail_of_primary ||
                                'https://picsum.photos/200/300'
                              "
                              alt="Artwork Thumbanail"
                            />
                          </div>
                          <div class="properties box2">
                            {{ option?.artist_id?.display_name }}
                          </div>
                          <div class="properties box2">
                            {{ option?.artwork_title }}
                          </div>
                          <div class="properties box2">
                            {{ option?.year }}
                          </div>
                          <div class="properties box">
                            <p
                              class="close_icon"
                              (click)="selectedArtworksArr.splice(j, 1)"
                            >
                              &times;
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <button class="select" (click)="isArtworkPopupOpen = true">
                      Select Existing
                    </button>
                    <!-- <button class="select" (click)="isAddNewModalOpen = true">
                      Add New Artwork
                    </button> -->
                  </div>
                  <div class="input-info">
                    Choose the artworks to be shown in the artwork grid
                  </div>
                  <div class="splitter">
                    <div class="field-value">
                      <div class="input-container">
                        <div class="text-before">Show Price :</div>
                        <label class="switch">
                          <input type="checkbox" formControlName="showPrice" />
                          <span class="slider round"></span>
                        </label>
                      </div>
                      <div class="input-info">
                        Choose whether to show artwork price
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="input-info">
                Choose 4 to 8 artworks to feature on the Auction
              </div>
            </div>

            <!--  -->
            <div class="field-value">
              <div class="input-container">
                <div class="text-before">Show Above Subtitle :</div>
                <label class="switch">
                  <input type="checkbox" formControlName="showAboveSubtitle" />
                  <span class="slider round"></span>
                </label>
              </div>
              <div class="input-info">
                Choose whether to show description text.
              </div>
            </div>
          </div>
          <div *ngIf="form.get('showAboveSubtitle').value">
            <div style="margin-top: 2.08vw; font-size: 1.25vw">
              Above Subtitle text
            </div>
            <div class="field-value">
              <angular-editor
                id="editor21"
                formControlName="aboveSubText"
              ></angular-editor>
              <div class="input-info">
                Provide the subtitle text to use above the title.
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="number"
                  placeholder="Font-Size"
                  formControlName="aboveSubSize"
                />
                <div class="placeholder">Above Subtitle size</div>
                <div class="input-info">
                  Provide the font size for the subtitle (decimal values between
                  1.0 and 2.0).
                </div>
              </div>
            </div>
          </div>

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Below Subtitle :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showBelowSubtitle" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">Choose whether to show a subtitle.</div>
          </div>
          <div *ngIf="form.get('showBelowSubtitle').value">
            <div style="margin-top: 2.08vw; font-size: 1.25vw">
              Below Subtitle text
            </div>
            <div class="field-value">
              <angular-editor
                id="editor22"
                formControlName="belowSubtitletext"
              ></angular-editor>
              <div class="input-info">
                Provide the subtitle text to use below the title.
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="number"
                  placeholder="Font-Size"
                  formControlName="belowSubtitlesize"
                />
                <div class="placeholder">Below Subtitle size</div>
                <div class="input-info">
                  Provide the font size for the subtitle (decimal values between
                  1.0 and 2.0).
                </div>
              </div>
              <!-- <div class="field-value">
                                <div class="input-container" (focusout)="changeFocus(1)">
                                    <input type="text" class="selection" formControlName="position" placeholder="Subtitle Position" [readonly]="true" />
                                    <div class="placeholder">Subtitle Position</div>
                                    <button (click)="isDropDownOpen[1] = !isDropDownOpen[1]" type="button">
                                        <img src="assets/icons/arrow-down.png" class="flag-arrow" />
                                    </button>
                                    <div [ngClass]="{
                      'dropdown-hidden': !isDropDownOpen[1],
                      'dropdown-visible': isDropDownOpen[1]
                    }">
                                        <ul>
                                            <li (click)="isDropDownOpen[1] = false">
                                                <div class="country-name">Above Title</div>
                                            </li>
                                            <li (click)="isDropDownOpen[1] = false">
                                                <div class="country-name">Below Title</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="input-info">
                                    Choose the position of the subtitle.
                                </div>
                            </div> -->
            </div>
          </div>

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Artist Name :</div>
              <label class="switch">
                <input type="checkbox" formControlName="showArtistName" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show the artist name on the banner.
            </div>
          </div>
          <div *ngIf="form.get('showArtistName').value">
            <div style="margin-top: 2.08vw; font-size: 1.111vw">
              Artist Name
            </div>
            <div class="field-value">
              <angular-editor
                id="editor23"
                formControlName="artistName"
              ></angular-editor>
              <div class="input-info">
                Provide the artist details to be displayed on the banner.
              </div>
            </div>
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="number"
                  placeholder="Font-Size"
                  formControlName="artistNameSize"
                />
                <div class="placeholder">Artist Name Size</div>
                <div class="input-info">
                  Provide the font size for the Artist Name (between 1 and 2).
                </div>
              </div>
            </div>
          </div>

          <div class="field-value">
            <div class="input-container">
              <div class="text-before">Show Dates:</div>
              <label class="switch">
                <input type="checkbox" formControlName="showDate" />
                <span class="slider round"></span>
              </label>
            </div>
            <div class="input-info">
              Choose whether to show the auction dates
            </div>
          </div>
          <div *ngIf="form.get('showDate').value">
            <div class="splitter">
              <div class="field-value" style="padding-right: 0.5vw">
                <input
                  type="date"
                  placeholder="Auction Start Date"
                  formControlName="auctionStartDate"
                />
                <div class="placeholder">Auction Start Date</div>
                <div class="input-info">Provide the Auction start date.</div>
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="auctionStartTime"
                type="time"
                placeholder="Exhibition Date"
              />
              <div class="placeholder">Auction Start Time</div>
              <div class="input-info">
                Provide the auction start time in IST.
              </div>
            </div>
            <div class="field-value">
              <input
                formControlName="auctionEndTime"
                type="time"
                placeholder="Auction End Date"
              />
              <div class="placeholder">Auction End Time</div>
              <div class="input-info">Provide the auction end time in IST.</div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="bidIncrement"
                type="number"
                placeholder="Bid Increment"
              />
              <div class="placeholder">Bid Increment</div>
              <div class="input-info">
                Specify the bid increment value that can be given by the
                collector. E.g.: Rs. 5000.
              </div>
            </div>
            <div class="field-value">
              <input
                formControlName="expressInterestDuration"
                type="number"
                placeholder="Express Interest Duration"
              />
              <div class="placeholder">Express Interest Duration</div>
              <div class="input-info">
                Specify the duration (in seconds) that the user can express
                interest for each artwork.
              </div>
            </div>
          </div>
          <div class="splitter">
            <div class="field-value" style="padding-right: 0.5vw">
              <input
                formControlName="biddingDuration"
                type="number"
                placeholder="Bidding Duration"
              />
              <div class="placeholder">Bidding Duration</div>
              <div class="input-info">
                Specify the duration (in seconds) that the each artwork is
                available for bidding.
              </div>
            </div>
          </div>
        </form>
        <div class="footer-nav">
          <div class="button-group">
            <div (click)="onSubmit()" class="next">Save</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- artwork select mopdal -->
<div
  class="artworkModal"
  [style.display]="isArtworkPopupOpen ? 'block' : 'none'"
>
  <!-- Modal content -->
  <div class="artwork-modal-content">
    <div class="Addselection_container">
      <div class="Addtitle_bar">
        Select Existing
        <p class="Addclose_icon" (click)="isAddNewModalOpen = false">&times;</p>
      </div>
      <div class="Addsearch_bar">
        <div class="search_container">
          <div class="fieldd-value">
            <input
              type="text"
              placeholder="Search"
              [(ngModel)]="searchKey"
              [ngModelOptions]="{ standalone: true }"
              (keyup)="searchResult($event)"
            />
          </div>
        </div>
        <div class="filter_container">
          <div class="fieldd-value">
            <div class="input-container" style="width: 12vw !important">
              <input
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="filter"
                [placeholder]="filter"
                type="text"
                class="selection"
                readonly
              />
              <button
                (click)="isFilterDropdownOpen = !isFilterDropdownOpen"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen,
                  'dropdown-visible': isFilterDropdownOpen
                }"
              >
                <ul>
                  <!-- <li (click)="
                                isFilterDropdownOpen = false;
                                dynamicForm
                                  .get('filter')
                                  .setValue('Artist Name')
                              ">
                                                            <div class="country-name">Artist Name</div>
                                                        </li> -->
                  <li
                    (click)="
                      isFilterDropdownOpen = false; filter = 'Artwork Title'
                    "
                  >
                    <div class="country-name">Artwork Title</div>
                  </li>
                  <!-- <li (click)="
                                isFilterDropdownOpen = false;
                                dynamicForm.get('filter').setValue('ID')
                              ">
                                                            <div class="country-name">ID</div>
                                                        </li> -->
                </ul>
              </div>
            </div>
          </div>
          <div class="fieldd-value">
            <div class="input-container" style="width: 7vw !important">
              <input
                [(ngModel)]="sort"
                [ngModelOptions]="{ standalone: true }"
                placeholder="sort"
                type="text"
                class="selection"
                readonly
              />
              <button
                (click)="isFilterDropdownOpen1 = !isFilterDropdownOpen1"
                type="button"
              >
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isFilterDropdownOpen1,
                  'dropdown-visible': isFilterDropdownOpen1
                }"
              >
                <ul>
                  <li (click)="isFilterDropdownOpen1 = false; sort = 'Desc'">
                    <div class="country-name">&uarr;</div>
                  </li>
                  <li (click)="isFilterDropdownOpen1 = false; sort = 'Asc'">
                    <div class="country-name">&darr;</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="Addselection_headings">
        <div class="Addproperty_title Addbox">
          <input type="checkbox" name="title" id="title" disabled />
        </div>
        <div class="Addproperty_title Addbox">ID</div>
        <div class="Addproperty_title Addbox2">Primary Image</div>
        <div class="Addproperty_title Addbox2">Artist Name</div>
        <div class="Addproperty_title Addbox2">Artwork Title</div>
      </div>
      <div class="Addselection_data" *ngFor="let item of artworksArr">
        <div class="Addartwork_details">
          <div class="Addproperties Addbox">
            <input
              type="checkbox"
              [(ngModel)]="item.selected"
              [ngModelOptions]="{ standalone: true }"
            />
          </div>
          <div class="Addproperties Addbox">
            {{ item?._id | slice: 0:10 }}
          </div>
          <div class="Addproperties Addbox2">
            <img
              [src]="
                item?.primary_image.length > 0
                  ? item?.primary_image[0].url
                  : 'https://picsum.photos/200/300'
              "
              alt="Artwork Thumbanail"
            />
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artist_id?.display_name }}
          </div>
          <div class="Addproperties Addbox2">
            {{ item?.artwork_title }}
          </div>
        </div>
      </div>
      <div class="Addfooter_bar" *ngIf="artworksArr.length != 0">
        <button class="Addselect" (click)="isAddNewModalOpen = false">
          Cancel
        </button>
        <button
          class="Addselect"
          style="margin-left: 10px"
          (click)="manageSelectedArtworks()"
        >
          Done
        </button>

        <span class="d-flex" style="gap: 2vw; float: left">
          <span class="records">found {{ totalRecords }} records</span>
          <span class="d-flex" style="gap: 0.7vw">
            <fa-icon
              [icon]="faAngleLeft"
              (click)="managePagination('prev')"
            ></fa-icon>
            Page
            <input
              style="height: 1vw; width: 1vw"
              type="number"
              [max]="totalPage"
              [(ngModel)]="offset"
              [ngModelOptions]="{ standalone: true }"
              min="1"
              name="pager"
            />
            Of {{ totalPage }}
            <fa-icon
              [icon]="faAngleRight"
              (click)="managePagination('next')"
            ></fa-icon>
          </span>
          <span style="cursor: pointer" (click)="findAll()"> Find ALL</span>
        </span>
      </div>
    </div>
  </div>
</div>

<!-- <div class="AddNewModal" [hidden]="!isAddNewModalOpen">
  <!-- Modal content -->

<!-- </div> -->
-->
