import { ControlPanelComponent } from './add-auction/control-panel/control-panel.component';
import { FinancialArtworksComponent } from './add-auction/main/financial/financial.component';
import { MainArtworksComponent } from './add-auction/main/main/main.component';
import { AuctionDetailsComponent } from './add-auction/auction-details/auction-details.component';
import { RegistrationsComponent } from './add-auction/registrations/registrations.component';
import { AddOnsComponent } from './add-auction/add-ons/add-ons.component';
import { SeoComponent } from './add-auction/seo/seo.component';
import { MainComponent as AddMain } from './add-auction/main/main.component';
import { AddAuctionComponent } from './add-auction/add-auction.component';
import { MainComponent } from './main/main.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';


const routes: Routes = [
  {
    path: '',
    component: MainComponent
  }, {
    path: 'add',
    component: AddAuctionComponent,
    children: [
      {
        path: '',
        component: AddMain,
        data: {
          hidebread: true,
        },
        children: [
          {
            path: '',
            component: MainArtworksComponent,
            data: {
              hidebread: true,
            },
          },
          {
            path: 'financial',
            component: FinancialArtworksComponent,
            data: {
              hidebread: true,
            },
          }]
      },
      {
        path: 'seo',
        component: SeoComponent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'add-on',
        component: AddOnsComponent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'registrations',
        component: RegistrationsComponent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'control-panel',
        component: ControlPanelComponent,
        data: {
          hidebread: true,
        },
      },
      {
        path: 'auction-details',
        component: AuctionDetailsComponent,
        data: {
          hidebread: true,
        },
      }]
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuctionRoutingModule { }
