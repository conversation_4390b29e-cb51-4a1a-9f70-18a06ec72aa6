import { FinancialArtworksComponent } from './add-auction/main/financial/financial.component';
import { MainArtworksComponent } from './add-auction/main/main/main.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MainComponent as AddMain } from './add-auction/main/main.component';
import { AuctionRoutingModule } from './auction-routing.module';
import { MainComponent } from './main/main.component';
import { AddAuctionComponent } from './add-auction/add-auction.component';
import { AddOnsComponent } from './add-auction/add-ons/add-ons.component';
import { SeoComponent } from './add-auction/seo/seo.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { ImageUploadModule } from 'src/app/core/image-upload/image-upload.module';
import { MultiInputModule } from 'src/app/core/multi-input/multi-input.module';
import { PlaceholderModule } from 'src/app/core/placeholder/placeholder.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { DragulaModule, DragulaService } from 'ng2-dragula';
import { RegistrationsComponent } from './add-auction/registrations/registrations.component';
import { AuctionDetailsComponent } from './add-auction/auction-details/auction-details.component';
import { ControlPanelComponent } from './add-auction/control-panel/control-panel.component';

@NgModule({
  declarations: [MainComponent, AddAuctionComponent, AddMain, AddOnsComponent, SeoComponent, RegistrationsComponent, AuctionDetailsComponent,
    MainArtworksComponent,FinancialArtworksComponent, ControlPanelComponent],
  imports: [
    CommonModule,
    AuctionRoutingModule,
    DragulaModule.forRoot(),
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    AngularEditorModule,
    NgxQRCodeModule,
    MultiInputModule,
    ImageUploadModule,
    PlaceholderModule
  ],
  providers: [
    DragulaService,
  ],
})
export class AuctionModule { }
