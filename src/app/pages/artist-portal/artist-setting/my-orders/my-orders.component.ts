import { Component, OnInit } from '@angular/core';
import { CartService } from 'src/app/services/cart.service';

@Component({
  selector: 'app-my-orders',
  templateUrl: './my-orders.component.html',
  styleUrls: ['./my-orders.component.scss'],
})
export class MyOrdersComponent implements OnInit {
  artworks = [{}, {}, {}, {}, {}, {}, {}, {}];
  orderData;

  constructor(private cartService: CartService) {}

  orderDate(str): string {
    const date = new Date(str);
    let mm = String(date.getMonth() + 1);
    let dd = String(date.getDate());
    if (Number(dd) < 10) {
      dd = '0' + dd;
    }

    if (Number(mm) < 10) {
      mm = '0' + mm;
    }
    let yyyy = String(date.getFullYear());
    return dd + '/' + mm + '/' + yyyy;
  }

  ngOnInit(): void {
    this.cartService.getAllOrders().subscribe((data) => {
      this.orderData = data.data;
    });
    // this.collectorService.orderInfo().then((data) => {
    // 	console.log(data);
    // 	this.orderData = data;
    // });
  }

  get radon(): any {
    return Math.floor(Math.random() * 999999);
  }
}
