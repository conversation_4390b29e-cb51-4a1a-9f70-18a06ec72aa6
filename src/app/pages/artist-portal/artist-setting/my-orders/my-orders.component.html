<div class="collector-form">
  <div class="interests">
    <div class="contents">
      <div *ngFor="let order of orderData; let i = index" class="artist">
        <a [routerLink]="'order/' + order._id">
          <img
            [src]="
              'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
              order.orderItem[0].image
            "
          />
        </a>
        <!-- <div class="icon-indicators"> -->
        <!-- <div
            [ngClass]="art?.published ? 'published-dot' : 'unpublished-dot'"
            ></div>
            <div
              class="status-dot"
              [ngStyle]="dotColor(temporary.status)"
            ></div>
            <div
              [ngClass]="temporary.minted ? 'minted' : 'not-minted'"
              [ngStyle]="lockColor(temporary.network)"
            ></div> -->
        <!-- </div> -->
        <div class="wrappp">
          <div class="name">Order no : {{ order._id }}</div>
          <div class="name">Order date: {{ orderDate(order.createdAt) }}</div>
          <div class="name">Order status: {{ order.orderStatus }}</div>
        </div>
      </div>
      <!-- <div *ngFor="let item of emptyDiv" class="artist"></div> -->
    </div>
  </div>
</div>
