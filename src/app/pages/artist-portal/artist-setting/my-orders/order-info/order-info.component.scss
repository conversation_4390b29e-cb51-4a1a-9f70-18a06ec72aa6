.OrderDetails__title {
  font-size:1.666vw;
    color: var(--primary-font-color);
    font-family: var(--primary-font);
    text-align: center;
  }
  .OrderDetails__title__sub {
    font-size: 1.25vw;
    color: var(--primary-font-color);
    font-family: var(--secondary-font);
    text-align: center;
    line-height: 1.11;
  }
  .OrderDetails__IconSection {
    display: flex;
    .OrderDetails__IconSection_col {
      width: 33.33%;
      display: flex;
      justify-content: center;
      margin-top: 3.47vw;
    }
  }
  .Img__checkbox {
    img {
      width: 2.29vw;
      height: 2.29vw;
    }
  }
  .Img__box {
    img {
      width: 2.29vw;
      height: 2.29vw;
    }
  }
  .Img__transport {
    img {
      width: 3.47vw;
      height: 2.29vw;
    }
  }
  
  .OrderDetails__Vertical_Line {
    display: flex;
    width: 100%;
    height: 0.4166vw;
    margin-top: 2.43vw;
    margin-bottom: 3.47vw;
    .OrderDetails__Vertical_Line_33 {
      width: 33.33%;
      display: flex;
      background-color: #004ddd;
      // justify-content: center;
    }
    .OrderDetails__Vertical_Line_66 {
      width: 66.66%;
      display: flex;
      background-color: #d8d8d8;
      // justify-content: center;
    }
  }
  
  .OrderDetails__Main {
    display: flex;
    .OrderDetails__Main__Left {
      width: 50%;
      display: flex;
      flex-direction: column;
    }
    .OrderDetails__Main__Right {
      width: 50%;
      display: flex;
      // padding-left: 8.402vw;
      p {
        margin-bottom: unset;
      }
    }
  }
  .OrderDetails__Main__Text {
    font-family: var(--primary-font);
    font-size: 1.25vw;
    color: var(--primary-font-color);
  }
  .OrderDetails__Sub_Text16 {
    font-family: var(--primary-font);
    font-size: 1.11vw;
    color: var(--primary-font-color);
  }
  .OrderDetails__Sub_Text18 {
    margin-bottom: 1.11vw;
    font-family: var(--secondary-font);
    font-size: 1.111vw;
    color: var(--primary-font-color);
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
  }
  .OrderDetails__Sub_Text16_Tertiary {
    font-family: var(--primary-font);
    font-size: 1.11vw;
  
    // color: var(--tertiary-font-color);
  }
  .OrderDetails__Vertical_Line_2 {
    background-color: #ededed;
    height: 0.069vw;
    margin-bottom: 3.47vw;
  }
  
  .PaymentSummary_Main {
    display: flex;
  }
  
  .Payment__Mode {
    img {
      width: 3.125vw;
      height: 1.041vw;
    }
  }
  
  .PaymentSummary_title {
    font-size: 1.25vw;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
  }
  
  .PaymentSummary_Main__Left {
    width: 60%;
    // display: flex;
    // flex-direction: row;
  }
  .PaymentSummary_Main__Right {
    width: 40%;
    // display: flex;
    // flex-direction: row;
  }
  .PaymentSummary_Main__Left__First {
    width: 100%;
    margin-bottom: 1.388vw !important;
  }
  .PaymentSummary_Main__Left__Second {
    width: 100%;
  }
  .PaymentSummary_Main__Right__First {
    width: 100%;
    margin-bottom: 8.68vw;
  }
  .PaymentSummary_Main__Right__Second {
    width: 100%;
  }
  .PaymentSummary_Main__Left__title {
    margin-bottom: 1.0416vw;
    font-size: 1.25vw;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    font-family: var(--primary-font);
  }
  .PaymentSummary__Address__Name {
    font-size: 1.11vw;
    font-weight: 500;
    font-family: var(--secondary-font);
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    margin-bottom: 0;
  }
  .PaymentSummary__Address {
    font-size: 1.11vw;
    font-weight: normal;
    font-family: var(--secondary-font);
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    margin-bottom:0;
  }
  .ctnbtn {
    border: 2px solid #004ddd;
    background-color: white;
    color: #004ddd;
    padding: 12px 76px;
    font-size: 1.11vw;
    cursor: pointer;
    border-radius: 3.47vw;
  }
  .ctnbtn:hover {
    background: #004ddd;
    color: white;
  }
  
  .OrderDetails__Main__Right__Div {
    background-color: #f6f6f6;
    padding-left: 3.472vw;
    padding-top: 3.472vw;
    padding-right: 3.472vw;
    width: 100%;
    height: 100%;
  }
  .OrderSummary_title {
    margin-bottom: 4.6527vw !important;
    font-size: 1.25vw;
    line-height: 1.25;
  }
  
  .OrderItem {
    display: flex;
  }
  .OrderItem__img {
    width: 25%;
    text-align: left;
    img {
      width: 6.111vw;
      height: 6.111vw;
      -o-object-fit: cover;
         object-fit: cover;
    }
  }
  .OrderItem__Art_Details {
    width: 75%;
    // text-align: right;
  }
  
  .OrderItem__Art_Details {
    display: flex;
    padding-left: 1vw;
  }
  .OrderItem__Art_Details__Name {
    width: 70%;
    // display: flex;
  }
  .OrderItem__Artname {
    font-family: var(--secondary-font);
    font-size: 1.11vw;
    font-style: italic;
  }
  .OrderItem__Artist {
    margin-top: 0.6944vw;
    font-family: var(--secondary-font);
    font-size: 1.11vw;
    color: #808080;
  }
  .OrderItem__ArtFrame {
    font-size: 1.11vw;
    padding-top: 1.388vw;
  }
  .OrderItem__Art_Details__Price {
    width: 30%;
    text-align: right;
  }
  .OrderItem__Art_Details__Art__Price {
    font-size: 1.11vw;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.88;
    letter-spacing: normal;
  }
  
  .Continue__Shopping {
    display: block;
  }
  .Continue__Shopping__Mobile {
    margin-top: 12.077vw;
    display: none;
  }
  
  .OrderItem__Art_Details__Frame__Price {
    padding-top: 1.388vw;
    font-size: 1.11vw;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.88;
    letter-spacing: normal;
  }
  .OrderItemSeparator {
    margin-top: 2.4305vw;
    margin-bottom: 2.4305vw;
    height: 0.06944vw;
    width: 100%;
    background-color: #c6c6c6;
  }
  .Subtotal__Div {
    font-size: 1.11vw;
    line-height: 1.88;
    display: flex;
    margin-bottom: 0.6944vw;
  }
  .Subtotal__Text {
    text-align: left;
    width: 50%;
  }
  .Subtotal__Price {
    text-align: right;
    width: 50%;
  }
  .GrayText__Div {
    font-size: 1.11vw;
    line-height: 1.88;
    display: flex;
    margin-bottom: 0.6944vw;
    color: #808080;
  }
  .Sub_Text16__Spacer {
    margin-bottom: 0.6944vw;
  }
  .GrayText__Text {
    text-align: left;
    width: 50%;
  }
  .GrayText__Price {
    text-align: right;
    width: 50%;
  }
  .OrderTotal__Div {
    font-size: 1.25vw;
    display: flex;
    margin-bottom: 0.6944vw;
  }
  .OrderTotal__Text {
    text-align: left;
    width: 50%;
  }
  .MobileOnly__Separator {
    display: none;
  }
  .OrderTotal__Price {
    text-align: right;
    width: 50%;
  }
  .Text20Black {
    font-size: 1.3888vw;
    margin-bottom: 1.806vw !important;
  }
  .Text16Grey {
    margin-bottom: 1.388vw !important;
    line-height: 1.88;
    font-size: 1.11vw;
    color: #808080;
  }
  
  @media (max-width: 768px) {
    .OrderDetails__title {
      font-size:4.35vw;
    }
    .OrderDetails__title__sub {
      font-size: 3.864vw;
      line-height: 1.25;
      br {
        display: none;
      }
    }
    .Img__checkbox {
      img {
        width: 4.589vw;
        height: 4.598vw;
      }
    }
    .Img__box {
      img {
        width: 4.347vw;
        height: 4.831vw;
      }
    }
    .Img__transport {
      img {
        width: 6.763vw;
        height: 4.589vw;
      }
    }
    .OrderDetails__Vertical_Line {
      height: 1.4492vw;
      margin-top: 7.729vw;
      margin-bottom: 9.661vw;
    }
  
    .OrderDetails__Main {
      display: block;
      // flex-direction: row;
      .OrderDetails__Main__Left {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
      .OrderDetails__Main__Right {
        width: 100%;
        display: flex;
        padding-left: unset;
        p {
          margin-bottom: unset;
        }
      }
    }
  
    .OrderDetails__Main__Text {
      font-size: 5.797vw;
      margin-bottom: 4.831vw;
    }
    .OrderDetails__Sub_Text16 {
      font-size: 3.864vw;
    }
    .OrderDetails__Sub_Text18 {
      font-size: 4.347vw;
      margin-bottom: 3.864vw;
    }
    .OrderDetails__Sub_Text16_Tertiary {
      font-size: 3.864vw;
    }
    .OrderDetails__Vertical_Line_2 {
      height: 0.2415vw;
      margin-bottom: 7.246vw;
    }
  
    .PaymentSummary_Main {
      display: block;
    }
    .PaymentSummary_Main__Left {
      width: 100%;
    }
    .PaymentSummary_Main__Right {
      width: 100%;
    }
    .Shipping__Address {
      margin-bottom: 9.661vw;
    }
    .Billing__Address {
      margin-bottom: 9.661vw;
    }
    .Payment__Mode {
      margin-bottom: 9.661vw;
    }
    .Delivery__Type {
      margin-bottom: 9.661vw;
    }
    .PaymentSummary_title {
      font-size: 5.797vw;
      margin-bottom: 9.661vw;
    }
    .PaymentSummary_Main__Left__title {
      font-size: 4.831vw;
      margin-bottom: 3.623vw;
    }
    .PaymentSummary__Address__Name {
      font-size: 4.347vw;
    }
    .PaymentSummary__Address {
      font-size: 3.864vw;
    }
    .Payment__Mode {
      img {
        width: 10.869vw;
        height: 3.623vw;
      }
    }
    .OrderSummary_title {
      margin-bottom: 9.661vw !important;
      font-size: 5.797vw;
    }
    .OrderItem__img {
      width: 25%;
      text-align: left;
      margin-right: 3.623vw;
      img {
        width: 21.256vw;
        height: 21.256vw;
      }
    }
    .OrderItem__Artname {
      font-size: 3.864vw;
    }
    .OrderItem__Artist {
      margin-top: 2.415vw;
      font-size: 3.864vw;
    }
    .OrderItem__ArtFrame {
      font-size: 3.864vw;
      padding-top: 1.388vw;
    }
    .OrderItem__Art_Details__Art__Price {
      font-size: 3.864vw;
    }
    .OrderItem__Art_Details__Frame__Price {
      padding-top: 8.154vw;
      font-size: 3.864vw;
    }
    .OrderItemSeparator {
      margin-top: 7.246vw;
      margin-bottom: 7.246vw;
      height: 0.241vw;
    }
    .Subtotal__Div {
      font-size: 3.684vw;
      margin-bottom: 2.415vw;
    }
    .GrayText__Div {
      font-size: 3.684vw;
      margin-bottom: 2.415vw;
    }
    .MobileOnly__Separator {
      display: block;
    }
    .OrderTotal__Div {
      font-size: 4.831vw;
    }
    .Text20Black {
      font-size: 4.831vw;
      margin-bottom: 1.806vw !important;
    }
    .Text16Grey {
      margin-bottom: 1.388vw !important;
      font-size: 3.684vw;
    }
  
    .Continue__Shopping {
      display: none;
    }
    .Continue__Shopping__Mobile {
      display: block;
    }
  
    .ctnbtn__mob {
      border: 2px solid #004ddd;
      background-color: white;
      color: #004ddd;
      padding: 3.623vw 26.57vw;
      font-size: 4.347vw;
      cursor: pointer;
      border-radius: 12.077vw;
    }
    .ctnbtn__mob:hover {
      background: #004ddd;
      color: white;
    }
    .Visa__Number {
      margin-bottom: 4.831vw !important;
    }
    .Sub_Text16__Spacer {
      margin-bottom: 2.415vw;
    }
    .OrderDetails__Main__Right {
      // margin-left: -5.797vw;
      // margin-right: -6.04vw !important;
      margin: auto !important;
    }
  }
  