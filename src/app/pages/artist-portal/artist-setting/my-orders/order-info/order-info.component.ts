import { CartService } from 'src/app/services/cart.service';
import { ActivatedRoute } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { CollectorService } from 'src/app/services/collector.service';

@Component({
  selector: 'app-order-info',
  templateUrl: './order-info.component.html',
  styleUrls: ['./order-info.component.scss'],
})
export class OrderInfoComponent implements OnInit {
  // estimatedArrival = 'within 5 Days';
  orderData;
  orderId;

  // orderDate='09/12/2021';
  // userPersonal = {
  //   'first_name': 'Bob',
  //   'last_name': 'The Builder',
  //   'email': '<EMAIL>'
  // }
  // orderDetails = {
  //   'razorpay_order_id': 'razorId12345',
  //   'shipping_status':'pending',
  //   'shipping_address': {
  //     'name': 'bob', 'address_line_1': 'near by streat', 'address_line_2': 'near by streat', 'city': 'Cochin', 'state': 'kerala', 'post_code': '675005',
  //     'country':'australia '
  //   },
  //   'billing_address': {
  //     'name': 'bob', 'address_line_1': 'near by streat', 'address_line_2': 'near by streat', 'city': 'Cochin', 'state': 'kerala', 'post_code': '675005',
  //     'country':'australia '
  //   },
  //   'orderItem': [{
  //     'artwork_title': 'art Title',
  //     'year': '2022',
  //     'artist_name': 'Bob',
  //     'sale_price': ' 215000',
  //     'image': 'https://picsum.photos/seed/picsum/200/300'
  //   },
  //   {
  //     'artwork_title': 'art Title',
  //     'year': '2022',
  //     'artist_name': 'Bob',
  //     'sale_price': ' 215000',
  //     'image': 'https://picsum.photos/seed/picsum/200/300'
  //   }]
  // }

  get estimatedArrival(): string {
    const date = new Date(this.orderData?.createdAt);
    date.setDate(date.getDate() + 14);

    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      weekday: 'short',
    });
  }

  constructor(public route: ActivatedRoute, public cartService: CartService) {}
  get orderDate(): string {
    const date = new Date(this.orderData?.createdAt);
    let mm = String(date.getMonth() + 1);
    let dd = String(date.getDate());
    if (Number(dd) < 10) {
      dd = '0' + dd;
    }

    if (Number(mm) < 10) {
      mm = '0' + mm;
    }
    let yyyy = String(date.getFullYear());
    return dd + '/' + mm + '/' + yyyy;
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.orderData = null;
      this.cartService.getOrderDetails(params.id).subscribe((data) => {
        this.orderData = data.data;
      });
      // this.collectorService.orderDetailsById(params.id).then((data) => {
      //   console.log(data);
      //   this.orderData = data;
      // });
    });
  }
}
