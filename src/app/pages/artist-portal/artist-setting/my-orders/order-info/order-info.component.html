<div class="sectionz section-margin-topz">
  <div class="section-innerz section-margin-horizontalz">
    <section #1>
      <p class="OrderDetails__title">
        Thank you {{ orderData?.orderOwnerId?.fullName }}!
      </p>
      <p
        class="OrderDetails__title__sub"
        *ngIf="orderData?.orderStatus == 'success'"
      >
        We’ve accepted your order and getting it ready. An order confirmation
        <br />
        mail has been sent to {{ orderData?.orderOwnerId?.email }}
      </p>
    </section>
    <section #2>
      <div class="OrderDetails__IconSection">
        <div class="OrderDetails__IconSection_col Img__checkbox">
          <img src="assets/order_details/checkbox/<EMAIL>" />
        </div>
        <div class="OrderDetails__IconSection_col Img__box">
          <img src="assets/order_details/box/<EMAIL>" />
        </div>
        <div class="OrderDetails__IconSection_col Img__transport">
          <img src="assets/order_details/transport/<EMAIL>" />
        </div>
      </div>
    </section>
    <section #3>
      <!--  bar design -->
      <div
        class="OrderDetails__Vertical_Line"
        [ngStyle]="{
          'flex-direction':
            orderData?.orderStatus == 'success' ? 'row' : 'row-reverse'
        }"
      >
        <div class="OrderDetails__Vertical_Line_33"></div>
        <div class="OrderDetails__Vertical_Line_66"></div>
      </div>
    </section>

    <section #4>
      <div class="OrderDetails__Main">
        <div class="OrderDetails__Main__Left">
          <!------------------- left part ----------------->
          <div style="margin-bottom: 34px">
            <p class="OrderDetails__Main__Text">Details of your order</p>
            <p class="OrderDetails__Sub_Text16 Sub_Text16__Spacer">
              Order Number: {{ orderData?._id }}
            </p>
            <p class="OrderDetails__Sub_Text16">Order Date: {{ orderDate }}</p>
          </div>

          <div
            style="margin-bottom: 34px"
            *ngIf="orderData?.orderStatus == 'success'"
          >
            <p class="OrderDetails__Main__Text">
              Estimated Arrival is {{ estimatedArrival }}
            </p>
            <p class="OrderDetails__Sub_Text18">
              <!-- Your order is processing. A tracking number will be sent at your
                email, {{ orderData?.user?.personalInfo?.email }} once your order has been shipped. -->
              Your order is in process.
            </p>
            <!-- <a class="OrderDetails__Sub_Text16_Tertiary" href="#"
                >View order details and delivery info</a
              > -->
          </div>

          <div
            style="margin-bottom: 34px"
            *ngIf="orderData?.orderStatus == 'delivered'"
          >
            <!-- <p class="OrderDetails__Main__Text">
                Estimated Arrival is {{ estimatedArrival }}
              </p> -->
            <p class="OrderDetails__Sub_Text18">
              Your order has been delivered!
            </p>
            <!-- <a class="OrderDetails__Sub_Text16_Tertiary" href="#"
                >View order details and delivery info</a
              > -->
          </div>
          <div
            style="margin-bottom: 34px"
            *ngIf="orderData?.orderStatus == 'cancelled'"
          >
            <!-- <p class="OrderDetails__Main__Text">
                Estimated Arrival is {{ estimatedArrival }}
              </p> -->
            <p class="OrderDetails__Sub_Text18">
              Your order has been cancelled!
            </p>
            <!-- <a class="OrderDetails__Sub_Text16_Tertiary" href="#"
                >View order details and delivery info</a
              > -->
          </div>

          <section>
            <!--  bar design -->
            <div class="OrderDetails__Vertical_Line_2"></div>
          </section>

          <section>
            <div>
              <p class="PaymentSummary_title">Payment Summary</p>
              <div class="PaymentSummary_Main">
                <div class="PaymentSummary_Main__Left">
                  <div class="PaymentSummary_Main__Left__First">
                    <div class="Shipping__Address">
                      <p class="PaymentSummary_Main__Left__title">
                        Shipping Address
                      </p>
                      <p class="PaymentSummary__Address__Name">
                        {{ orderData?.shipping_address?.name }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.shipping_address?.address_line_1 }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.shipping_address?.address_line_2 }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.shipping_address?.city }},
                        {{ orderData?.shipping_address?.state }}
                        {{ orderData?.shipping_address?.post_code }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.shipping_address?.country }}
                      </p>
                    </div>
                  </div>
                  <div class="PaymentSummary_Main__Left__Second">
                    <div class="Billing__Address">
                      <p class="PaymentSummary_Main__Left__title">
                        Billing Address
                      </p>
                      <p class="PaymentSummary__Address__Name">
                        {{ orderData?.billing_address?.name }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.billing_address?.address_line_1 }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.billing_address?.address_line_2 }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.billing_address?.city }},
                        {{ orderData?.billing_address?.state }}
                        {{ orderData?.billing_address?.post_code }}
                      </p>
                      <p class="PaymentSummary__Address">
                        {{ orderData?.billing_address?.country }}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="PaymentSummary_Main__Right">
                  <!-- <div class="PaymentSummary_Main__Right__First">
                      <div class="Payment__Mode">
                        <p class="PaymentSummary_Main__Left__title">
                          Mode of Payment
                        </p>
                        <p class="PaymentSummary__Address Visa__Number">
                          VISA ************9025
                        </p>
                        <img src="assets/order_details/visa/visa2.png" />
                      </div>
                    </div> -->
                  <div class="PaymentSummary_Main__Right__Second">
                    <div class="Delivery__Type">
                      <p class="PaymentSummary_Main__Left__title">
                        Delivery Method
                      </p>
                      <p class="PaymentSummary__Address">Standard Delivery</p>
                      <p class="PaymentSummary__Address">6-13 business days</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="Continue__Shopping" style="margin-top: 3.47vw">
              <a routerLink="/explore/artworks" class="ctnbtn"
                >Continue Shopping</a
              >
            </div>
          </section>
        </div>
        <div class="OrderDetails__Main__Right">
          <div class="OrderDetails__Main__Right__Div">
            <p class="OrderSummary_title">Order Summary</p>
            <div class="OrderItem__List">
              <div *ngFor="let item of orderData?.orderItem; let i = index">
                <div class="OrderItem">
                  <div class="OrderItem__img">
                    <img
                      [src]="
                        'https://www.terrain.art/cdn-cgi/image/width=500,quality=52/' +
                        item?.image
                      "
                    />
                  </div>
                  <div class="OrderItem__Art_Details">
                    <div class="OrderItem__Art_Details__Name">
                      <p class="OrderItem__Artname">
                        {{ item?.artwork_title }}, {{ item?.year }}
                      </p>
                      <p class="OrderItem__Artist">{{ item?.artist_name }}</p>
                    </div>
                    <div class="OrderItem__Art_Details__Price">
                      <p class="OrderItem__Art_Details__Art__Price">
                        ₹ {{ item?.sale_price | number : "1.0" }}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="OrderItemSeparator"></div>
              </div>
            </div>
            <!-- <div class="OrderSummay__Breakdown">
                <div class="Subtotal__Div">
                  <p class="Subtotal__Text">Subtotal</p>
                  <p class="Subtotal__Price">₹ 1,00,000</p>
                </div>
                <div class="GrayText__Div">
                  <p class="GrayText__Text">Taxes</p>
                  <p class="GrayText__Price">₹ 2,674</p>
                </div>
                <div class="GrayText__Div">
                  <p class="GrayText__Text">Shipping</p>
                  <p class="GrayText__Price">₹ 4,500</p>
                </div>
                <div class="GrayText__Div">
                  <p class="GrayText__Text">Addtional Cost (Framing)</p>
                  <p class="GrayText__Price">₹ 1,600</p>
                </div>
                <div class="GrayText__Div">
                  <p class="GrayText__Text">Insurance</p>
                  <p class="GrayText__Price">₹ 4,500</p>
                </div>
              </div> -->
            <div class="OrderItemSeparator MobileOnly__Separator"></div>
            <div class="OrderTotal__Div">
              <p class="OrderTotal__Text">Tansaction Fee</p>
              <p class="OrderTotal__Price">
                ₹
                {{ orderData?.tx_fee_amount | number : "1.0" }}
                | $
                {{
                  orderData?.tx_fee_amount
                    | forex : "USD"
                    | async
                    | number : "1.0"
                }}
              </p>
            </div>
            <div class="OrderTotal__Div">
              <p class="OrderTotal__Text">Tax</p>
              <p class="OrderTotal__Price">
                ₹
                {{ orderData?.tax_amount | number : "1.0" }}
                | $
                {{
                  orderData?.tax_amount | forex : "USD" | async | number : "1.0"
                }}
              </p>
            </div>
            <div class="OrderTotal__Div">
              <p class="OrderTotal__Text">Order Total</p>
              <p class="OrderTotal__Price">
                ₹ {{ orderData?.total_amount | number : "1.0" }} | $
                {{
                  orderData?.total_amount
                    | forex : "USD"
                    | async
                    | number : "1.0"
                }}
              </p>
            </div>
            <div class="OrderItemSeparator"></div>
            <div>
              <p class="Text16Grey" style="line-height: 1.1">
                Costs excl. shipping, shipping rates will be informed via email
                after purchase. Taxes include 12% GST for physical and 18% GST
                for digital works. Includes 3% transaction fee for payment
                gateway.
              </p>
              <p class="Text20Black">Need Help?</p>
              <p class="Text16Grey">
                Read our
                <a routerLink="/faqs" target="_blank" style="color: #004ddd"
                  >FAQs</a
                >
              </p>
              <p class="Text16Grey">
                Write to us at
                <a href="mailto:<EMAIL>" style="color: #004ddd"
                  ><EMAIL></a
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section>
      <div class="Continue__Shopping__Mobile">
        <a routerLink="/explore/artworks" class="ctnbtn__mob"
          >Continue Shopping</a
        >
      </div>
    </section>
  </div>
</div>
