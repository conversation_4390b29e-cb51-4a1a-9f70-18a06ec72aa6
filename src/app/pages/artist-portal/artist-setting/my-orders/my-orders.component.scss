.collector-form {
    .search-input {
      font-size: var(--default-font-size);
      margin-top: 4.16vw;
      position: relative;
      input[type="text"] {
        width: 100%;
        padding: 0.34vw 0;
        border: none;
        text-indent: 2.08vw;
        color: var(--quaternary-font-color);
        border-bottom: 0.138vw solid var(--timeline-color);
        &:focus {
          outline: none;
        }
      }
      img {
        position: absolute;
        left: 0;
        top: 50%;
        height: 0.97vw;
        width: 0.97vw;
        transform: translate(0, -50%);
      }
    }
    .interests {
      width: auto;
      margin-left: 0;
    //   margin-top: 1.74vw;
      overflow-y: auto;
      //   height: calc(100vh - 44.75vw);
      padding-bottom: 8vw;
      .contents {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .artist {
          width: 15.06vw;
          padding: 1.74vw 0;
          img {
            width: 14.93vw;
            height: 10.69vw;
            min-height: 10.69vw;
            -o-object-fit: cover;
            object-fit: cover;
            border-radius: 0.41vw;
            cursor: pointer;
          }
          .wrappp {
            line-height: 0.6;
            .name {
              font-size: 0.9027vw;
              margin-top: 0.5vw;
            }
            &.active {
              color: var(--tertiary-font-color);
              img {
                border: solid 0.013vw var(--tertiary-font-color);
              }
            }
          }
        }
        // .artwork-list {
        //   display: flex;
        //   flex-direction: row;
        //   .list-contain {
        //     display: flex;
        //     flex-direction: row;
  
        //     .artwork-img {
        //       width: 10%;
        //     }
        //     .artwork-indicators {
        //       width: 10%;
        //     }
        //     .artwork-title {
        //       width: 20%;
        //     }
        //     .artist-name {
        //       width: 20%;
        //     }
        //     .sale-price {
        //       width: 20%;
        //     }
        //     .nft-id {
        //       width: 20%;
        //     }
        //   }
        // }
        .icon-indicators {
          padding-top: 0.5vw;
          display: flex;
          align-items: center;
        }
      }
    }
}