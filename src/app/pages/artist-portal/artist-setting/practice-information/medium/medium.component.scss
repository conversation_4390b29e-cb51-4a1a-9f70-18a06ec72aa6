.heading {
  margin-bottom: 3.47vw;
  position: relative;
  .head {
    width: 82.5%;
    font-size: 1.66vw;
    font-weight: 600;
    display: flex;
    justify-content: start;
    align-items: center;
    img {
      width: 2.29vw;
      height: 2.29vw;
      margin-right: 1.08vw;
    }
  }
  .sub {
    font-size: var(--default-font-size);
    font-family: var(--secondary-font);
    margin-top: 0.42vw;
    padding-left: 3.33vw;
  }
}
.flex-block {
  display: flex !important;
}

.collector-form {
  .interests {
    width: auto;
    margin-left: 0;
    margin-top: 1.74vw;
    overflow-y: auto;
    // height: calc(100vh - 25.471vw);
    //   height: calc(100vh - 44.75vw);
    padding-bottom: 8vw;
    .contents {
      // display: flex;
      // justify-content: space-between;
      // flex-wrap: wrap;
      ul {
        list-style-type: none;
        li {
          display: flex;
          align-items: center;
          padding: 1.18vw 0;
          border-bottom: solid 0.069vw var(--timeline-color);
          // flex-wrap: wrap;
          .medium-name {
            font-size: 1.25vw;
            color: var(--quaternary-font-color);
            font-family: var(--secondary-font);
            flex-grow: 1;
          }
          .action-buttons {
            img {
              width: 2.291vw;
              height: 2.291vw;
              margin-left: 1.388vw;
            }
          }
        }
      }
    }
  }
  .buttonList {
    margin-top: 3.47vw;
    .save {
      outline: none;
      font-size: 1.25vw;
      min-width: 20.56vw;
      background-color: transparent;
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }
    .save:hover {
      font-weight: 600;
    }
    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: transparent;
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }
    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }
}

