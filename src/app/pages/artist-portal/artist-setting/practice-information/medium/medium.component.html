<div class="w-100">
  <div class="heading">
    <div class="head">
      <a class="link-head" routerLink=".."
        ><img src="assets/images/back-chevron.png"
      /></a>
      Medium of work
    </div>
    <div class="sub">Select all that apply</div>
  </div>

  <div class="collector-form" *ngIf="!(isCancel || isSave || isMessage)">
    <div class="interests">
      <div class="contents">
        <ul>
          <li>
            <p class="medium-name">Acrylic</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Ceramics</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Drawing</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Print Making</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Sculpture</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Acrylic</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
          <li>
            <p class="medium-name">Ceramics</p>
            <div class="action-buttons">
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-pin"
              />
              <img
                src="../../../../../../assets/icons/Pin-select/<EMAIL>"
                alt="unselected-tick"
              />
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="flex-block buttonList">
      <button class="save" (click)="isSave = true">Save</button>
      <button
        class="cancel"
        [routerLink]="['..']"
        routerLinkActive="router-link-active"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
