<div class="w-100">
  <div class="heading">
    <div class="head">
      <a class="link-head" routerLink=".."
        ><img src="assets/images/back-chevron.png"
      /></a>
      Art movement your work falls into
    </div>
    <div class="sub" (click)="choose(0)">Select all that apply</div>
  </div>

  <div class="collector-form" *ngIf="!(isCancel || isSave || isMessage)">
    <div class="interests">
      <div class="contents">
        <div
          *ngFor="let artist of artists; let i = index"
          class="artist"
          [ngClass]="{
            active: selectedArtist.indexOf(artist?.id) !== -1
          }"
        >
          <img
            (click)="selectArtist(artist?.id)"
            [src]="
              'https://register.terrain.art/artist-portal/assets/' +
              artist?.artist_list_thumbnail?.private_hash
            "
          />
          <div class="name">{{ artist?.display_name }}</div>
        </div>
        <div *ngFor="let item of emptyDiv" class="artist"></div>
      </div>
    </div>
    <div class="flex-block buttonList">
      <button class="save" (click)="isSave = true">Save</button>
      <button
        class="cancel"
        [routerLink]="['..']"
        routerLinkActive="router-link-active"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
