import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-movement',
    templateUrl: './movement.component.html',
    styleUrls: ['./movement.component.scss']
})
export class MovementComponent implements OnInit {
    isCancel = false;
    isMessage = false;
    isSuccess = false;
    isSave = false;

    content = {
        1: {
            title: 'Buy art',
            isChecked: false,
        },
        2: {
            title: 'Learn about art',
            isChecked: false,
        },
        3: {
            title: 'Learn about artists',
            isChecked: false,
        },
        4: {
            title: 'Discover curations',
            isChecked: false,
        },
        5: {
            title: 'Learn about art pricing',
            isChecked: false,
        },
    };

    artists = [
        {
            "id": 1,
            "artist_list_thumbnail": {
                "private_hash": "plo58lp0lmskcwc4"
            },
            "display_name": "<PERSON><PERSON><PERSON>"
        },
        {
            "id": 3,
            "artist_list_thumbnail": {
                "private_hash": "zehgs7j44hcowsk4"
            },
            "display_name": "<PERSON><PERSON>"
        },
        {
            "id": 4,
            "artist_list_thumbnail": {
                "private_hash": "5qp7hbdpkb484000"
            },
            "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>hmukh"
        },
        {
            "id": 5,
            "artist_list_thumbnail": {
                "private_hash": "644de7j13bgocw04"
            },
            "display_name": "Jayeti Bhattacharya"
        },
        {
            "id": 6,
            "artist_list_thumbnail": {
                "private_hash": "4uc9shnqrwqosscs"
            },
            "display_name": "Nabanita Guha"
        },
        {
            "id": 7,
            "artist_list_thumbnail": {
                "private_hash": "kulgigpsg9ww0o84"
            },
            "display_name": "Nur Mahammad"
        },
        {
            "id": 8,
            "artist_list_thumbnail": {
                "private_hash": "o2m27sysujkg4ogo"
            },
            "display_name": "Suman Chandra"
        },
        {
            "id": 9,
            "artist_list_thumbnail": {
                "private_hash": "eft036q2naos048g"
            },
            "display_name": "Urna Sinha"
        },
        {
            "id": 10,
            "artist_list_thumbnail": {
                "private_hash": "rzwbxlv24004gs08"
            },
            "display_name": "Srishti Rana Menon"
        },
        {
            "id": 12,
            "artist_list_thumbnail": {
                "private_hash": "fuxje8k9m5ckwoc0"
            },
            "display_name": "Kodanda Rao Teppala"
        },
        {
            "id": 13,
            "artist_list_thumbnail": {
                "private_hash": "28lu0pc3beckwgcg"
            },
            "display_name": "Deepak Kumar"
        },
        {
            "id": 14,
            "artist_list_thumbnail": {
                "private_hash": "aindjv0s9ts0o4so"
            },
            "display_name": "Sukanya Garg"
        },
        {
            "id": 15,
            "artist_list_thumbnail": {
                "private_hash": "jgjcp8echb4gc808"
            },
            "display_name": "Bhartti Verma"
        },
        {
            "id": 16,
            "artist_list_thumbnail": {
                "private_hash": "lzabvhm44pw48w44"
            },
            "display_name": "Al-Qawi Nanavati"
        },
        {
            "id": 17,
            "artist_list_thumbnail": {
                "private_hash": "5g8l95tpim804ocs"
            },
            "display_name": "Shivangi Ladha"
        },
        {
            "id": 19,
            "artist_list_thumbnail": {
                "private_hash": "ervvyzgxl4ows4sw"
            },
            "display_name": "Puja Mondal"
        },
        {
            "id": 22,
            "artist_list_thumbnail": {
                "private_hash": "lo5jsmpjxnkgso40"
            },
            "display_name": "Aban Raza"
        },
        {
            "id": 23,
            "artist_list_thumbnail": {
                "private_hash": "9hy8ena58wg8oooo"
            },
            "display_name": "Chetnaa"
        },
        {
            "id": 24,
            "artist_list_thumbnail": {
                "private_hash": "oz0ewvfn000484g4"
            },
            "display_name": "Pankaj R. Vishwakarma"
        },
        {
            "id": 27,
            "artist_list_thumbnail": {
                "private_hash": "907kqa6yytc0o80w"
            },
            "display_name": "Abhigna Kedia"
        },
        {
            "id": 30,
            "artist_list_thumbnail": {
                "private_hash": "h9njmvdhn2o84gsc"
            },
            "display_name": "Enit María /\\ Srinivas Mangipudi"
        },
        {
            "id": 32,
            "artist_list_thumbnail": {
                "private_hash": "pbuhc4hw6io0g4wo"
            },
            "display_name": "Susanta Kumar Panda and Ramakanta Samantaray"
        },
        {
            "id": 36,
            "artist_list_thumbnail": {
                "private_hash": "kowweosuq9wkwg4w"
            },
            "display_name": "Starinfinity"
        },
        {
            "id": 51,
            "artist_list_thumbnail": {
                "private_hash": "7ce0n5dxo7kscoos"
            },
            "display_name": "Manjot Kaur"
        }
    ];
    selectedArtist = [];
    emptyDiv = [];

    constructor(
        // private artistService: ArtistService,
        // private router: Router,
        // private collectorService: CollectorService
    ) { }

    ngOnInit(): void {
        this.getData();
        this.searchArtist();
    }

    getData = async () => {
        // this.collectorService.getPreferenceByType('artist').subscribe((data) => {
        //   const current = [];
        //   data?.data?.ids.filter((id: number) => {
        //     current.push(id);
        //   });
        //   this.selectedArtist = current;
        // });
    };

    searchArtist(value: string = '') {
        // this.artistService.getAllArtists(value).then((data) => {
        //   this.artists = data;
        //   const reminder = this.artists?.length % 4;
        //   if (reminder > 0) {
        //     this.emptyDiv = Array(reminder).fill(0);
        //   }
        // });
    }

    selectArtist(id) {
        const position = this.selectedArtist.indexOf(id);
        if (position !== -1) {
            this.selectedArtist.splice(position, 1);
        } else {
            this.selectedArtist.push(id);
        }
        this.selectedArtist.sort((a, b) => a - b);
    }

    choose(index) {
        // if (index === 0) {
        //   for (let i = 1; i < 5; i++) this.content[i].isChecked = true;
        // } else this.content[index].isChecked = !this.content[index].isChecked;
    }

    submit() {
        // this.collectorService
        //   .addPreference('artist', {
        //     ids: this.selectedArtist,
        //   })
        //   .subscribe(
        //     (data) => {
        //       this.isSuccess = true;
        //       this.isCancel = false;
        //       this.isSave = false;
        //       this.isMessage = true;
        //     },
        //     (err) => {
        //       this.isSuccess = false;
        //       this.isCancel = false;
        //       this.isSave = false;
        //       this.isMessage = true;
        //     }
        //   );
    }

    close() {
        //   this.isCancel = false;
        //   this.isSave = false;
        //   this.isMessage = false;
        //   if (this.isSuccess)
        //     this.router.navigateByUrl('collector/profile/preferences');
    }
}
