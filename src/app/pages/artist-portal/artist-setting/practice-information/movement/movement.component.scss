


.heading {
  margin-bottom: 3.47vw;
  position: relative;
  .head {
    width: 82.5%;
    font-size: 1.66vw;
    font-weight: 600;
    display: flex;
    justify-content: start;
    align-items: center;
    img {
      width: 2.29vw;
      height: 2.29vw;
      margin-right: 1.08vw;
    }
  }
  .sub {
    font-size: var(--default-font-size);
    font-family: var(--secondary-font);
    margin-top: 0.42vw;
    padding-left: 3.33vw;
  }
}
.flex-block {
  display: flex !important;
}

.collector-form {
  .search-input {
    font-size: var(--default-font-size);
    margin-top: 4.16vw;
    position: relative;
    input[type="text"] {
      width: 100%;
      padding: 0.34vw 0;
      border: none;
      text-indent: 2.08vw;
      color: var(--quaternary-font-color);
      border-bottom: 0.138vw solid var(--timeline-color);
      &:focus {
        outline: none;
      }
    }
    img {
      position: absolute;
      left: 0;
      top: 50%;
      height: 0.97vw;
      width: 0.97vw;
      transform: translate(0, -50%);
    }
  }
  .interests {
    width: auto;
    margin-left: 0;
    margin-top: 1.74vw;
    overflow-y: auto;
    height: calc(100vh - 25.471vw);
  //   height: calc(100vh - 44.75vw);
    padding-bottom: 8vw;
    .contents {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .artist {
        width: 15.06vw;
        padding: 1.74vw 0;
        img {
          width: 14.93vw;
          height: 10.69vw;
          min-height: 10.69vw;
          -o-object-fit: cover;
          object-fit: cover;
          border-radius: 0.41vw;
          cursor: pointer;
        }
        .name {
          font-size: var(--default-font-size);
          margin-top: 1.04vw;
        }
        &.active {
          color: var(--tertiary-font-color);
          img {
            border: solid 0.013vw var(--tertiary-font-color);
          }
        }
      }
    }
  }
  .buttonList {
    margin-top: 3.47vw;
    .save {
      outline: none;
      font-size: 1.25vw;
      min-width: 20.56vw;
      background-color:transparent;
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }
    .save:hover {
      font-weight: 600;
    }
    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: transparent;
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }
    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }
}