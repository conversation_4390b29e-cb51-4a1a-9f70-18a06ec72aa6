<div class="w-100">
  <div class="heading">
    <div class="head">
      <a class="link-head" routerLink=".."
        ><img src="assets/images/back-chevron.png"
      /></a>
      Tags associated with your work
    </div>
    <div class="sub">Enter upto 20</div>
  </div>

  <div class="collector-form" *ngIf="!(isCancel || isSave || isMessage)">
    <div class="search-input">
      <input type="text" placeholder="Type and hit “Return” key to add a tag" />
    </div>
    <div class="interests">
      <div class="contents">
        <div class="searched-tags">
          <span class="tag-item"
            ><p class="tag-name">Space</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
          <span class="tag-item"
            ><p class="tag-name">Drawing</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
          <span class="tag-item"
            ><p class="tag-name">Sculpture</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
        </div>
        <p class="suggested-title">Suggested tags based off of your profile</p>
        <div class="suggested-tags">
          <span class="tag-item"
            ><p class="tag-name">Space</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
          <span class="tag-item"
            ><p class="tag-name">Drawing</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
          <span class="tag-item"
            ><p class="tag-name">Sculpture</p>
            <img src="../../../../../../assets/icons/close.png" alt=""
          /></span>
        </div>
      </div>
    </div>
    <div class="flex-block buttonList">
      <button class="save" (click)="isSave = true">Save</button>
      <button
        class="cancel"
        [routerLink]="['..']"
        routerLinkActive="router-link-active"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
