.heading {
  margin-bottom: 3.47vw;
  position: relative;
  .head {
    width: 82.5%;
    font-size: 1.66vw;
    font-weight: 600;
    display: flex;
    justify-content: start;
    align-items: center;
    img {
      width: 2.29vw;
      height: 2.29vw;
      margin-right: 1.08vw;
    }
  }
  .sub {
    font-size: var(--default-font-size);
    font-family: var(--secondary-font);
    margin-top: 0.42vw;
    padding-left: 3.33vw;
  }
}
.flex-block {
  display: flex !important;
}

.collector-form {
  .search-input {
    font-size: var(--default-font-size);
    margin-top: 3.472vw;
    position: relative;
    input[type="text"] {
      width: 100%;
      padding: 0.347vw 0;
      border: none;
      // text-indent: 2.08vw;
      font-size: var(--default-font-size);

      color: var(--quaternary-font-color);
      border-bottom: 0.138vw solid var(--timeline-color);
      &:focus {
        outline: none;
      }
    }
    img {
      position: absolute;
      left: 0;
      top: 50%;
      height: 0.97vw;
      width: 0.97vw;
      transform: translate(0, -50%);
    }
  }
  .interests {
    //   width: auto;
    margin-left: 0;
    margin-top: 1.74vw;
    //   overflow-y: auto;
    //   height: calc(100vh - 25.471vw);
    //   height: calc(100vh - 44.75vw);
    // padding-bottom: 8vw
    .contents {
      //   display: flex;
      // justify-content: space-between;
      //   flex-wrap: wrap;

      .searched-tags {
        margin-top: 2.083vw;
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        p {
          margin-top: unset;
          margin-bottom: unset;
        }
        .tag-item {
          border-radius: 1.215vw;
          background-color: rgba(0, 77, 221, 0.1);
          padding: 0.555vw 0.6944vw;
          margin-right: 1.388vw;
          margin-bottom: 0.6944vw;
          display: flex;
          align-items: center;
          .tag-name {
            color: var(--tertiary-font-color);
            margin-right: 0.6944vw;
          }
          img {
            width: 0.6944vw;
            height: 0.6944vw;
            cursor: pointer;
          }
        }
      }
      .suggested-title {
        margin-top: 6.944vw;
        font-size: 1.25vw;
        font-family: var(--secondary-font);
      }
      .suggested-tags {
        margin-top: 2.083vw;
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        p {
          margin-top: unset;
          margin-bottom: unset;
        }
        .tag-item {
          border-radius: 1.215vw;
          background-color: rgba(128, 128, 128, 0.1);
          padding: 0.555vw 0.6944vw;
          margin-right: 1.388vw;
          margin-bottom: 0.6944vw;
          display: flex;
          align-items: center;
          .tag-name {
            color: var(--secondary-color);
            margin-right: 0.6944vw;
          }
          img {
            width: 0.6944vw;
            height: 0.6944vw;
            cursor: pointer;
          }
        }
      }
    }
  }
  .buttonList {
    margin-top: 3.47vw;
    .save {
      outline: none;
      font-size: 1.25vw;
      min-width: 20.56vw;
      background-color: transparent;
      color: var(--tertiary-font-color);
      padding: 0.83vw 1.04vw;
      border: 0.069vw solid var(--tertiary-font-color);
      border-radius: 1.46vw;
    }
    .save:hover {
      font-weight: 600;
    }
    .cancel {
      outline: none;
      font-size: 1.25vw;
      background-color: transparent;
      color: var(--quaternary-font-color);
      padding: 0.83vw 2.78vw;
      border: none;
    }
    .cancel:hover {
      font-weight: 600;
      // color: var(--tertiary-font-color);
    }
  }
}
