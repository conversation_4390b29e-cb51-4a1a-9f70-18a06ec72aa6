.container__main {
    //   padding-right: calc(33.263vw - var(--page-default-margin));
    padding-bottom: 6.3vw;
    .division{
        height: 0.069vw;
        width: 100%;
        background-color: var(--timeline-color);
    }
    .main__heading {
      font-size: 1.805vw;
      margin-bottom: 1.042vw;
    }
    .content-section {
      width: 100%;
      display: inline-flex;
      margin-top: 2.083vw;
      .content {
           
        flex-grow: 1;
        .wrapper {
          margin-bottom: 2.083vw;
        }
        .label {
          font-family: var(--primary-font);
          font-size: 1.388vw;
        }
        .element {
          font-family: var(--secondary-font);
          font-size: 1.25vw;
          img{
              width: 1.25vw;
              height: 1.25vw;
          }
          .divider{
              display: inline-block;
              width:0.069vw ;
              height:1.041vw;
              background-color:var(--timeline-color);
              margin-left: 0.6944vw;
              margin-right: 0.416vw;
          }
        }
        .last-changed{
          font-family: var(--primary-font);
          font-size: 0.972vw;
          color: var(--quaternary-font-color);
        }
      }
      img {
        width: 2.847vw;
        height: 2.847vw;
      }
    }
  }
  