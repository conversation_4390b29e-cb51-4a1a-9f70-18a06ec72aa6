import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { exhibitionData } from 'src/app/demo/exhibition.demo';
import { ExhibitionService } from 'src/app/pages/explore/services/exhibition.service';
import { LazyComponentService } from 'src/app/shared/services/lazy-components.service';
import {
  NavBarColor,
  NavBarService,
  NavBarType,
} from 'src/app/shared/services/navbar.service';
import { getTheme } from 'src/app/theme';

@Component({
  selector: 'amex-item',
  templateUrl: './amex.component.html',
  styleUrls: ['./amex.component.scss'],
})
export class AmexComponent
  implements OnInit, OnDestroy, AfterViewChecked, AfterViewInit
{
  @ViewChild('exhibitionContents', { read: ViewContainerRef })
  exhibitionContents: ViewContainerRef;

  data;
  id = '6253e89fe12bc75612355b1a';
  code = 'AMEXEXCLUSIVE';
  isAcceess;

  constructor(
    private loader: LazyComponentService,
    private navBarService: NavBarService,
    private cdRef: ChangeDetectorRef,
    private route: ActivatedRoute,
    private exhibitionService: ExhibitionService
  ) {}
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.navBarService.changeNavbarType(NavBarType.TRANSPARENT);
      this.cdRef.detectChanges();
    }, 0);
  }
  ngAfterViewChecked(): void {}
  ngOnDestroy(): void {
    this.navBarService.changeNavbarType(NavBarType.DEFAULT);
    this.navBarService.changeNavbarColor(NavBarColor.NORMAL);
  }

  async ngOnInit(): Promise<void> {
    addEventListener('code-entered-success', (event) => {
      console.log(event);
    });
    this.getData();
  }
  getData() {
    this.isAcceess = localStorage.getItem('amexCode') === this.code;
    this.exhibitionService.getExhibitionsById(this.id).then((data) => {
      this.data = data.data;
      if (this.data.headerColor == 'Inverted') {
        this.navBarService.changeNavbarColor(NavBarColor.INVERTED);
      } else {
        this.navBarService.changeNavbarColor(NavBarColor.NORMAL);
      }
      this.renderComponents();
    });
  }
  renderComponents = async () => {
    if (this.data?.titleBanner?.type === 'Video') {
      const theme = getTheme['video-banner']['model-1'];
      await this.loader
        .loadModules(theme.import(), theme?.module)
        .then(async (result) => {
          if (result) {
            await this.loader.loadComponent(
              theme?.module,
              this.exhibitionContents,
              {
                ...this.data?.titleBanner,
                title: this.data.title,
                artistName: this.data.artistName,
                date: this.data.exhibitionDateNote,
                type: 2,
                code: this.code,
                event: () => {
                  this.renderSubcomponents();
                },
              }
            );
          }
        });
    } else if (this.data?.titleBanner?.type === 'Artwork on wall') {
      const theme = getTheme['image-banner']['model-2'];
      await this.loader
        .loadModules(theme.import(), theme?.module)
        .then(async (result) => {
          if (result) {
            await this.loader.loadComponent(
              theme?.module,
              this.exhibitionContents,
              {
                ...this.data?.titleBanner,
                title: this.data.title,
                artistName: this.data.artistName,
                date: this.data.exhibitionDateNote,
                bannerType: 2,
                media: this.data?.titleBanner?.backgroundImage,
                backgroundImage: this.data?.titleBanner?.media,
              }
            );
          }
        });
    } else {
      const theme = getTheme['image-banner']['model-2'];
      await this.loader
        .loadModules(theme.import(), theme?.module)
        .then(async (result) => {
          if (result) {
            await this.loader.loadComponent(
              theme?.module,
              this.exhibitionContents,
              {
                ...this.data?.titleBanner,
                title: this.data.title,
                artistName: this.data.artistName,
                date: this.data.date,
              }
            );
          }
        });
    }
    if (this.isAcceess) {
      this.renderSubcomponents();
    }
  };
  renderSubcomponents = async () => {
    for (const content of this.data?.contents) {
      if (content?.component === 'text-containers') {
        const theme = getTheme[content?.component]?.['model-2'];
        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    text: content?.data?.text,
                    isReadMore: content?.data?.readMore,
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.component === 'video-containers') {
        const theme = getTheme[content?.component]?.[content?.model];
        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    src: content?.data?.src,
                    desc: content?.data?.description,
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.component === 'artwork-container') {
        const theme = getTheme['artwork-containers']?.[content?.model];
        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    artworks: content?.data?.selectedArtworks.map((a) => {
                      return this.data?.artwork_containers?.find(
                        (b) => b._id == a._id
                      );
                    }),
                    showPrice: content?.data?.showPrice,
                    linkToViewing:
                      content?.data?.artworkInterior == 'View In Room',
                    isAmex: true,
                    isInteractive:
                      content?.data?.artworkInterior == 'Interactive Artwork',
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.component === 'quote-cards') {
        const theme = getTheme[content?.component]?.[content?.model];
        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    quoteBy: content?.data?.image,
                    theme:
                      content?.data?.mediaBackground === 'No Background'
                        ? 'white'
                        : 'grey',
                    position: content?.data?.mediaPosition,
                    image: content?.data?.mediaUrl,
                    quote: content?.data?.quote,
                    type: content?.data?.quoteMedia,
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.component === 'audio-containers') {
        const theme = getTheme[content?.component]?.[content?.model];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    src: content?.data?.src,
                    desc: content?.data?.desc,
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.component === 'profile-containers') {
        const theme = getTheme['details-cards']?.[content?.model];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    type:
                      content?.data?.mediaType === 'Video' ? 'VIDEO' : 'IMAGE',
                    media: {
                      src: content?.data?.mediaUrl,
                    },
                    title: 'About the Artist',
                    showButton: content?.data?.showProfileLink,
                    description: content?.data?.text,
                    buttonText: `View Artist's profile`,
                    buttonUrl: content?.data?.url,
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.compType === 'bannerDivide') {
        const theme = getTheme['media-banner']?.[content?.model];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  content?.data
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else if (content?.compType === 'artworkFeature') {
        const theme = getTheme['feature-containers']?.[content?.model];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  {
                    data: {
                      tech_image_1: content?.data?.imageUrls,
                      tech_section: content?.data?.textArr?.map((a) => {
                        return {
                          text_on_image: a?.showText,
                          text_position: a?.textPosition?.toLowerCase(),
                          center_x_axis: String(a?.xAxis),
                          center_y_axis: String(a?.yAxis),
                          text_content: a?.showTextData,
                          zoom_level: String(a?.zoomLevel),
                        };
                      }),
                    },
                  }
                );
              }
            });
        } else {
          console.log('module not found');
        }
        //feature-containers
      } else if (content?.compType === 'artistProfile2') {
        const theme = getTheme['details-cards']?.['model-2'];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  content?.data
                );
              }
            });
        } else {
          console.log('module not found');
        }
      } else {
        const theme = getTheme[content?.component]?.[content?.model];

        if (theme && content?.data?.publish) {
          await this.loader
            .loadModules(theme?.import(), theme?.module)
            .then(async (result) => {
              if (result) {
                this.loader.loadComponent(
                  theme?.module,
                  this.exhibitionContents,
                  content?.data
                );
              }
            });
        } else {
          console.log('module not found');
        }
      }
    }
  };
}
