import { Web3Service } from './../../shared/services/web3.service';
import { CartService } from './../../services/cart.service';
import { AuthService } from './../../services/auth.service';

import {
  NavBarColor,
  NavBarService,
  NavBarType,
} from './../../shared/services/navbar.service';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { faBars, faSearch, faTimes } from '@fortawesome/free-solid-svg-icons';
import { CollectorService } from 'src/app/services/collector.service';
import { UserInt } from 'src/app/models/user.model';
import { UserPersonalInfoInt } from 'src/app/models/personal-info.model';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit {
  @ViewChild('navBar') navBarEl: ElementRef;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    // event.target.innerWidth;
    const w = event.target.innerWidth;
    this.isMobile = this.getIsMobile(w);
  }
  logoUrl = 'assets/images/logo.png';
  faSearch = faSearch;
  faBars = faBars;
  faTimes = faTimes;
  isMobile = false;
  isOpen = false;
  openLogin = false;
  openRegister = false;
  openVerify = false;
  openSignupPopup: boolean = false;
  userLogged;
  userPersonal: UserPersonalInfoInt;

  cartItemsCount = 0;
  navbar_white = false;
  isNFTs = false;
  isMetaLogged = false;
  ethereumAdd;
  searchValue;
  changeNavbarTextColor(url) {
    var expression = /(exhibitions\/)/gi;
    var regex = new RegExp(expression);
    // console.log(url+ 'is the URL');
    if (url == '/' || url == '/discover' || url.match(regex)) {
      this.navbar_white = true;
      this.logoUrl = 'assets/images/new-inverted-logo.png';
    } else {
      this.navbar_white = false;
      this.logoUrl = 'assets/images/logo.png';
    }
  }
  components = [
    {
      name: 'Home',
      link: '/',
    },
    {
      name: 'Discover',
      link: '/discover',
    },
    {
      name: 'Marketplace',
      link: '/marketplace',
    },

    {
      name: 'Community',
      link: '/community',
    },
    {
      name: 'Open',
      link: '/discover/open',
    },
     {
      name: 'Press',
      link: '/press',
    },
  ];
  navbarType = NavBarType.DEFAULT;
  navbarColor = NavBarColor.NORMAL;
  constructor(
    private renderer: Renderer2,
    private router: Router,
    private collectorService: CollectorService,
    private navBarService: NavBarService,
    private authService: AuthService,
    private cartService: CartService,
    private web3Service: Web3Service,
    @Inject(PLATFORM_ID) private platformId: any,
    private cdr: ChangeDetectorRef
  ) {
    // addEventListener('storage', (event) => {
    //   if (event.key === 'isLogged') {
    //     this.checkLogged();
    //   }
    // });
  }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      // this.changeNavbarTextColor(this.router.url);
      this.router.events.subscribe((event) => {
        if (event instanceof NavigationEnd) {
          this.changeNavbarTextColor(event.urlAfterRedirects);
          if (event.urlAfterRedirects.includes('/nfts')) {
            this.isNFTs = true;
          } else {
            this.isNFTs = false;
          }
        }
      });
      this.navBarService.navbarObserv$.subscribe((data) => {
        this.navbarType = data;
        this.cdr.detectChanges();
      });
      this.navBarService.navbarColorObserv$.subscribe((data) => {
        this.navbarColor = data;
        this.cdr.detectChanges();
      });

      this.navBarService.signupObser$.subscribe((data) => {
        this.openRegister = true;
      });
      this.navBarService.signinObser$.subscribe((data) => {
        this.openLogin = true;
      });
      const w = document.documentElement.clientWidth;
      this.isMobile = this.getIsMobile(w);
      this.authService.me().subscribe();
      this.authService.loggedUserObser$.subscribe((data) => {
        this.userLogged = data;
      });
      this.authService.userPersonalInfo$.subscribe((data) => {
        this.userPersonal = data;
      });

      this.cartService.cartItems$.subscribe((data) => {
        this.cartItemsCount = 0;
        if (data?.data?.artwork_id?.length) {
          this.cartItemsCount = this.cartItemsCount + data?.data?.artwork_id?.length;
        }
        if (data?.data?.editions?.length) {
          this.cartItemsCount = this.cartItemsCount + data?.data?.editions?.length;
        }

      });

      // this.web3Service.web3Observ$.subscribe((data) => {
      //   console.log(data);

      //   this.isMetaLogged = true;
      //   this.ethereumAdd = data.address;
      // });
    }

    // this.checkLogged();
  }
  connectWallet() {
    this.web3Service.connectAccount();
  }
  // async checkLogged() {
  //   const logged = localStorage.getItem('isLogged');
  //   if (logged === 'true') {
  //     this.isLogged = true;
  //     this.userData = await this.collectorService.personalInfo();
  //   } else {
  //     this.isLogged = false;
  //   }
  // }

  public getIsMobile(w): boolean {
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  public openNav() {
    const element = this.navBarEl.nativeElement;
    this.renderer.removeClass(element, 'nav-close');
    this.renderer.addClass(element, 'nav-open');
    this.isOpen = true;
  }
  public closeNav() {
    const element = this.navBarEl.nativeElement;
    this.renderer.addClass(element, 'nav-close');
    this.renderer.removeClass(element, 'nav-open');
    this.isOpen = false;
  }
  onLogin() {
    this.openLogin = true;
    this.closeNav();
  }

  logOut() {
    // this.authService.me().subscribe();
    this.authService.logOut();
    this.userLogged = null;
    this.router.navigate(['']);
    // window.location.reload();
  }
  showCart() {
    this.cartService.changeCartOpen(true);
  }
  // async metaLogin() {
  //   this.web3Service.loginWithMetaMask();
  // }
  searchResult() {
    this.router.navigate(['/search'], {
      queryParams: { q: this.searchValue },
    });
  }
}
