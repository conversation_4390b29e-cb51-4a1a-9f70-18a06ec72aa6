import { AuthService } from './../../../services/auth.service';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
@Component({
  selector: 'app-signup-popup',
  templateUrl: './signup-popup.component.html',
  styleUrls: ['./signup-popup.component.scss']
})

export class SignupPopupComponent implements OnInit {
  @Output() closePopup = new EventEmitter();
  @Output() openLogin = new EventEmitter();
  @Output() openVerify = new EventEmitter();

  form: FormGroup;
 

  constructor(
    private server : CollectorService,
    private formBuilder: FormBuilder,

  ) {}

  ngOnInit(): void {
    this.form = this.formBuilder.group({
      one: new FormControl(false, [Validators.required]),
      two: new FormControl(false, [Validators.required]),
      three: new FormControl(false, [Validators.required]),
    });
  }

  onSubmit() {
    console.log(this.form)
    if(this.form.value.one == true && this.form.value.two ==true && this.form.value.three == true){
      let url = apiUrl.register
      let data = JSON.parse(localStorage.getItem('signupDetails'))
      this.server.showSpinner()
      this.server.postApi(url,data).subscribe(res=>{
        this.server.hideSpinner()
        console.log(res)
        if(res.statusCode == 200){
          this.closePopup.emit();
          alert(res.message)
          localStorage.setItem('userId',res.data.userId)
          this.openVerify.emit();
        }
      },err=>{
        this.closePopup.emit();
        localStorage.removeItem('signupDetails')
        alert(err.error.message)
      });
    }
    
    
  }
  
}

