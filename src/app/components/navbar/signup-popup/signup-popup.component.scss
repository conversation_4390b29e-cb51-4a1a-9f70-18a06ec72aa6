.section {
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    position: fixed;
    z-index: 200;
    left: 0;
    top: 0;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.8);
    .popup-close {
      position: absolute;
      right: 1vw;
      top: 1vw;
      font-size: 2.4vw;
      a {
        color: white;
        img {
          width: 2.22vw;
          height: 2.22vw;
        }
      }
    }
  }
  
  .form-enquire {
    width: 30vw;
    background-color: white;
    border-radius: 0.45vw;
    .input-field {
      input::-webkit-input-placeholder {
        font-weight: 500;
      }
      input::-moz-placeholder {
        font-weight: 500;
      }
      input:-ms-input-placeholder {
        font-weight: 500;
      }
      input::-ms-input-placeholder {
        font-weight: 500;
      }
      input::placeholder {
        font-weight: 500;
      }
    }
  }
  .button {
    text-align: center;
    cursor: pointer;
    color: var(--tertiary-font-color);
    font-size: 1vw;
    vertical-align: middle;
    width: 100%;
    color: var(--tertiary-font-color);
    padding: 0.55vw 1.69vw;
    background-color: transparent;
    margin-right: 0.8vw;
    border-radius: 2.08vw;
    border-color: var(--tertiary-font-color);
    border-style: solid;
    border-width: 1px;
  }
  @media (max-width: 768px) {
    .section {
      height: 100vh;
      width: 100vw;
      max-width: 100%;
      position: fixed;
      z-index: 200;
      left: 0;
      top: 0;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.8);
      .popup-close {
        position: absolute;
        right: 2vw;
        top: 2vw;
        font-size: 2.4vw;
        a {
          color: white;
          img {
            width: 4vw;
            height: 4vw;
          }
        }
      }
    }
  
    .form-enquire {
      width: 100vw;
      height: 100vh;
      background-color: white;
      border-radius: 0.45vw;
      justify-content: center;
      & > img {
        margin-bottom: 20vw !important;
      }
      .form-control-label {
        font-size: 3.5vw;
      }
      .input-field {
        input::-webkit-input-placeholder {
          font-weight: 500;
        }
        input::-moz-placeholder {
          font-weight: 500;
        }
        input:-ms-input-placeholder {
          font-weight: 500;
        }
        input::-ms-input-placeholder {
          font-weight: 500;
        }
        input::placeholder {
          font-weight: 500;
        }
      }
    }
    .button {
      text-align: center;
      cursor: pointer;
      color: var(--tertiary-font-color);
      font-size: 3.8vw;
      vertical-align: middle;
      width: 100%;
      color: var(--tertiary-font-color);
      padding: 2.55vw 3.5vw;
      background-color: transparent;
      margin-right: 0.8vw;
      border-radius: 5.08vw;
      border-color: var(--tertiary-font-color);
      border-style: solid;
      border-width: 1px;
      margin-bottom: 5vw;
      margin-top: 5vw;
    }
    .bottom-link {
      font-size: 2.8vw !important;
    }
  }

  .view-terms {
    color: var(--tertiary-font-color) !important;
    font-size: smaller;
    margin-left: 20px;
  }
  