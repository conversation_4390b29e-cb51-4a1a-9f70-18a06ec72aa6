import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { AuthService } from 'src/app/services/auth.service';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit {
  @ViewChild('recaptcha', { static: true }) recaptchaElement: ElementRef;
  @Output() closePopup = new EventEmitter();
  @Output() openRegister = new EventEmitter();
  @Output() openVerify = new EventEmitter();

  loginForm: FormGroup;
  Haserror: boolean = false;
  ErroMsg: string;

  artwork_id: number;
  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private server: CollectorService
  ) {
    this.loginForm = this.formBuilder.group({
      email: new FormControl('', [
        Validators.required,
        Validators.pattern(
          /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        ),
      ]),
      password: new FormControl('', [Validators.required]),
    });
  }

  ngOnInit(): void {
    this.addRecaptchaScript();
  }
  addRecaptchaScript() {
    window['grecaptchaCallback'] = () => {
      this.renderReCaptcha();
    };

    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['grecaptchaCallback']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src =
        'https://www.google.com/recaptcha/api.js?onload=grecaptchaCallback&render=explicit';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'recaptcha-jssdk', this);
  }

  renderReCaptcha() {
    window['grecaptcha'].render(this.recaptchaElement.nativeElement, {
      sitekey: '6LeBSv0ZAAAAAByosZw1-W6q_qN-CjevjjnSQq7E',
      callback: (response) => {
        console.log(response);
      },
    });
  }
  async onSubmit() {
    this.Haserror = false;
    if (this.loginForm.controls.email.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Please provide a valid email address.';
    } else if (this.loginForm.controls.password.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Password required!!';
    } else if (window['grecaptcha'].getResponse() == '') {
      this.Haserror = true;
      this.ErroMsg = 'reCAPTCHA is not verified!!';
    } else {
      let url = apiUrl.login;
      let req = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password,
      };
      this.server.showSpinner()
      this.server.postApi(url, req).subscribe(
        (res) => {
          this.server.hideSpinner()
          console.log(res);
          if (res.statusCode == 200) {
            alert(res.message);
            this.closePopup.emit();
            this.authService.saveTokens(res.data);
          } else if (res.statusCode == 201) {
            localStorage.setItem('userId', res.userId);
            alert(res.message);
            this.sendOTP();
          }
        },
        (err) => {
          console.log(err);
          alert(err.error.message);
        }
      );
      // this.authService
      //   .userLogin(
      //     this.loginForm.controls.email.value,
      //     this.loginForm.controls.password.value
      //   )
      //   .subscribe(
      //     (data) => {
      //       this.closePopup.emit();
      //     },
      //     (error) => {
      //       this.Haserror = true;
      //       this.ErroMsg =
      //         'Incorrect Username or Password. Please check, or click Sign Up to register as a new user';
      //     }
      //   );
    }
  }

  sendOTP() {
    let url = apiUrl.sendOTP;
    this.server.showSpinner()
    this.server
      .postApi(url, {
        userId: localStorage.getItem('userId'),
      })
      .subscribe((res) => {
        this.server.hideSpinner()
        if (res.statusCode == 200) {
          this.closePopup.emit();
          this.openVerify.emit();
        }
      });
  }
}
