<div class="section login">
  <div class="section-inner h-100">
    <div
      class="
        d-flex
        align-items-center
        justify-content-center
        h-100
        w-100
        form-container
      "
    >
      <div
        class="form-enquire"
        style="
          padding: 4vw 4vw;
          position: relative;
          display: flex;
          flex-direction: column;
        "
      >
        <img
          src="assets/images/logo.png"
          alt="Logo"
          style="width: 40%; align-self: center; margin-bottom: 2vw"
        />
        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">Email</label>
            <input
              formControlName="email"
              type="email"
              class="form-control"
              placeholder=""
            />
          </div>
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">Password</label>
            <input
              formControlName="password"
              type="password"
              class="form-control"
              placeholder=""
            />
          </div>
          <div class="d-flex justify-content-center" style="margin-top: 1vw">
            <div #recaptcha style="transform: scale(0.77)"></div>
          </div>
          <div
            class="message alert alert-danger alert-dismissible mt-2"
            *ngIf="Haserror"
          >
            <a
              (click)="Haserror = false"
              class="close"
              data-dismiss="alert"
              aria-label="close"
              >&times;</a
            >
            <strong>Error!</strong> {{ ErroMsg }}
          </div>
          <div class="d-flex justify-content-center" style="margin-top: 1vw">
            <button type="submit" class="button">Sign in</button>
          </div>
        </form>
        <div class="bottom-link" style="margin-top: 0.5vw; font-size: 0.9vw">
          Need an account?
          <a
            (click)="openRegister.emit()"
            style="cursor: pointer; color: var(--tertiary-font-color)"
            >Sign up</a
          >
        </div>

        <div class="popup-close">
          <a (click)="closePopup.emit()" style="cursor: pointer">
            <img src="assets/images/close.png" />
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
