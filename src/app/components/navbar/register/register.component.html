<div class="section login">
  <div class="section-inner h-100">
    <div
      class="d-flex align-items-center justify-content-center h-100 w-100 form-container"
    >
      <div
        class="form-enquire"
        style="
          padding: 4vw 4vw;
          position: relative;
          display: flex;
          flex-direction: column;
        "
      >
        <img
          src="assets/images/logo.png"
          alt="Logo"
          style="width: 40%; align-self: center; margin-bottom: 2vw"
        />
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
          <div class="gender-inner">
            <div class="input-container">
              <input
                formControlName="type"
                type="text"
                class="selection"
                placeholder="Role"
                (focus)="isDropDownOpen[0] = true"
                (focusout)="changeFocus(0)"
                readonly="readonly"
              />
              <button (click)="isDropDownOpen[0] = !isDropDownOpen[0]">
                <img src="assets/icons/arrow-down.png" class="flag-arrow" />
              </button>
              <div
                [ngClass]="{
                  'dropdown-hidden': !isDropDownOpen[0],
                  'dropdown-visible': isDropDownOpen[0]
                }"
              >
                <ul>
                  <li
                    *ngFor="let item of roleList; let i = index"
                    (click)="
                      registerForm.get('type').setValue(item?.role);
                      isDropDownOpen[0] = false
                    "
                  >
                    <div class="country-name">{{ item?.role }}</div>
                  </li>
                </ul>
              </div>
            </div>
            <!-- <label class="check-label" *ngFor="let role of roleList">
              <input
                formControlName="type"
                type="radio"
                name="type"
                [value]="role['_id']"
              />
              <span class="check"></span>
              <span class="title">{{ role?.role }}</span>
            </label> -->
            <!-- <label class="check-label">
              <input formControlName="type" type="radio" name="type" value="curated" />
              <span class="check"></span>
              <span class="title">Curated Artist</span>
            </label> -->
          </div>
          <!-- <div class="gender-inner">

            <label class="check-label">
              <input formControlName="type" type="radio" name="type" value="open-market" />
              <span class="check"></span>
              <span class="title">Open Market Artist</span>
            </label>
            <label class="check-label">
              <input formControlName="type" type="radio" name="type" value="curator" />
              <span class="check"></span>
              <span class="title">Curator</span>
            </label>
          </div> -->
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">First name</label>
            <input
              formControlName="first_name"
              type="text"
              class="form-control"
              placeholder=""
            />
          </div>
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">Last name</label>
            <input
              formControlName="last_name"
              type="text"
              class="form-control"
              placeholder=""
            />
          </div>
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">Email</label>
            <input
              formControlName="email"
              type="email"
              class="form-control"
              placeholder=""
            />
          </div>
          <div class="input-field" style="width: 100%; margin-top: 0.5vw">
            <label class="form-control-label">Password</label>
            <input
              formControlName="password"
              type="password"
              class="form-control"
              placeholder=""
            />
          </div>
          <div
            class="d-flex align-items-center justify-content-start input-field"
            style="width: 100%; margin-top: 0.5vw"
          >
            <input formControlName="policy_accept" type="checkbox" />
            <label
              class="form-control-label"
              style="margin: 0; margin-left: 0.69vw"
            >
              I agree to Terrain.Art’s
              <a style="cursor: pointer; color: var(--tertiary-font-color)"
                >Terms of Use</a
              >
              and
              <a style="cursor: pointer; color: var(--tertiary-font-color)"
                >Privacy Policy</a
              ></label
            >
          </div>
          <div
            class="d-flex align-items-center justify-content-start input-field"
            style="width: 100%; margin-top: 0.5vw"
          >
            <input formControlName="receive_notification" type="checkbox" />
            <label
              class="form-control-label"
              style="margin: 0; margin-left: 0.69vw"
              >I wish to receive email updates & newsletters from
              Terrain.art</label
            >
          </div>
          <div class="d-flex justify-content-center" style="margin-top: 0.5vw">
            <div #recaptcha style="transform: scale(0.77)"></div>
          </div>
          <div
            class="message alert alert-danger alert-dismissible mt-2"
            *ngIf="Haserror"
          >
            <a
              (click)="Haserror = false"
              class="close"
              data-dismiss="alert"
              aria-label="close"
              >&times;</a
            >
            <strong>Error!</strong> {{ ErroMsg }}
          </div>
          <div class="d-flex justify-content-center" style="margin-top: 1vw">
            <button type="submit" class="button">Sign up</button>
          </div>
        </form>
        <div class="bottom-link" style="margin-top: 0.5vw; font-size: 0.9vw">
          Already have an account?
          <a
            (click)="openLogin.emit()"
            style="cursor: pointer; color: var(--tertiary-font-color)"
            >Sign in</a
          >
        </div>

        <div class="popup-close">
          <a (click)="closePopup.emit()" style="cursor: pointer">
            <img src="assets/images/close.png" />
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
