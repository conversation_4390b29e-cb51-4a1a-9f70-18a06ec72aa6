.section {
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.8);
  .popup-close {
    position: absolute;
    right: 1vw;
    top: 1vw;
    font-size: 2.4vw;
    a {
      color: white;
      img {
        width: 2.22vw;
        height: 2.22vw;
      }
    }
  }
}

.form-enquire {
  width: 30vw;
  background-color: white;
  border-radius: 0.45vw;
  .input-field {
    input::-webkit-input-placeholder {
      font-weight: 500;
    }
    input::-moz-placeholder {
      font-weight: 500;
    }
    input:-ms-input-placeholder {
      font-weight: 500;
    }
    input::-ms-input-placeholder {
      font-weight: 500;
    }
    input::placeholder {
      font-weight: 500;
    }
  }
}
.button {
  text-align: center;
  cursor: pointer;
  color: var(--tertiary-font-color);
  font-size: 1vw;
  vertical-align: middle;
  width: 100%;
  color: var(--tertiary-font-color);
  padding: 0.55vw 1.69vw;
  background-color: transparent;
  margin-right: 0.8vw;
  border-radius: 2.08vw;
  border-color: var(--tertiary-font-color);
  border-style: solid;
  border-width: 1px;
}
@media (max-width: 768px) {
  .section {
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    position: fixed;
    z-index: 200;
    left: 0;
    top: 0;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.8);
    .popup-close {
      position: absolute;
      right: 2vw;
      top: 2vw;
      font-size: 2.4vw;
      a {
        color: white;
        img {
          width: 4vw;
          height: 4vw;
        }
      }
    }
  }

  .form-enquire {
    width: 100vw;
    height: 100vh;
    background-color: white;
    border-radius: 0.45vw;
    justify-content: center;
    & > img {
      margin-bottom: 20vw !important;
    }
    .form-control-label {
      font-size: 3.5vw;
    }
    .input-field {
      input::-webkit-input-placeholder {
        font-weight: 500;
      }
      input::-moz-placeholder {
        font-weight: 500;
      }
      input:-ms-input-placeholder {
        font-weight: 500;
      }
      input::-ms-input-placeholder {
        font-weight: 500;
      }
      input::placeholder {
        font-weight: 500;
      }
    }
  }
  .button {
    text-align: center;
    cursor: pointer;
    color: var(--tertiary-font-color);
    font-size: 3.8vw;
    vertical-align: middle;
    width: 100%;
    color: var(--tertiary-font-color);
    padding: 2.55vw 3.5vw;
    background-color: transparent;
    margin-right: 0.8vw;
    border-radius: 5.08vw;
    border-color: var(--tertiary-font-color);
    border-style: solid;
    border-width: 1px;
    margin-bottom: 5vw;
    margin-top: 5vw;
  }
  .bottom-link {
    font-size: 2.8vw !important;
  }
}

.check-label {
  margin-right: 2vw;
  .title {
    margin-left: 0.5vw;
  }
}
.input-container {
  width: 100%;
  position: relative;
  display: inline-flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
  .text-before {
    @media (max-width: 768px) {
      font-size: 4.34vw;
    }
  }
  .flag-icon {
    position: absolute;
    left: 1.04vw;
  }
  button {
    outline: none;
    background: none;
    border: none;
  }
  .flag-arrow {
    position: absolute;
    max-width: 0.69vw;
    height: auto;
    right: 2.08vw;
    top: 0;
    bottom: 0;
    margin: auto 0;
    @media (max-width: 768px) {
      max-width: 1.95vw;
    }
  }
  .division {
    position: absolute;
    width: 0.069vw;
    height: 1.04vw;
    background-color: var(--timeline-color);
    left: 3.33vw;
  }
  input[type="text"] {
    // background: transparent;
    font-family: var(--secondary-font);
    border: 0.069vw solid var(--timeline-color);
    padding: 1.04vw 1.11vw 1.04vw 4.2vw;
    border-radius: 0.14vw;
    height: 3.47vw;
    width: 100%;
    z-index: 0;
    @media (max-width: 768px) {
      height: calc(1.5em + 0.75rem + 2px);
    }
    &.selection {
      padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }
  .dropdown-visible {
    background-color: var(--primary-background-color);
    visibility: visible;
    position: absolute;
    top: 3.47vw;
    z-index: 1;
    box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
    width: 100%;
    @media (max-width: 768px) {
      top: 5.5vw;
    }
    ul {
      list-style: none;
      padding: 0.69vw 0;
      max-height: 27.97vw;
      margin: 0;
      overflow: hidden;
      overflow-y: scroll;
      @media (max-width: 768px) {
        padding: 1.69vw 0;
      }
      li {
        padding: 0.69vw 1.04vw;
        width: 34.9vw;
        display: flex;
        @media (max-width: 768px) {
          padding: 1.69vw 1.04vw;
          width: 100%;
        }
        .country-name {
          margin-left: 0.69vw;
          @media (max-width: 768px) {
            font-size: 1rem;
          }
        }
      }
      li:hover {
        background-color: var(--timeline-color);
      }
    }
    ul::-webkit-scrollbar {
      display: none;
    }
  }
  .dropdown-hidden {
    display: none;
  }
}
