import { AuthService } from './../../../services/auth.service';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { apiUrl } from 'src/environments/environment.prod';
@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
})
export class RegisterComponent implements OnInit {
  @ViewChild('recaptcha', { static: true }) recaptchaElement: ElementRef;
  @Output() closePopup = new EventEmitter();
  @Output() openLogin = new EventEmitter();
  @Output() openVerify = new EventEmitter();
  @Output() openSignupPopup = new EventEmitter();
  registerForm: FormGroup;

  Haserror: boolean = false;
  ErroMsg: string;
  isDropDownOpen = Array(10).fill(false);

  artwork_id: number;
  roleList: any = [];
  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private server: CollectorService
  ) {
    this.registerForm = this.formBuilder.group({
      email: new FormControl('', [
        Validators.required,
        Validators.pattern(
          /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        ),
      ]),
      password: new FormControl('', [
        Validators.required,
        Validators.minLength(8),
      ]),
      first_name: new FormControl('', [Validators.required]),
      last_name: new FormControl('', [Validators.required]),
      policy_accept: new FormControl(false, [Validators.required]),
      receive_notification: new FormControl(false, [Validators.required]),
      type: new FormControl('OPEN CREATOR', [Validators.required]),
    });
  }

  ngOnInit(): void {
    this.addRecaptchaScript();
    this.getRoles();
    // this.openVerify.emit();
  }

  // to get roles
  getRoles() {
    let url = apiUrl.addRole + `?status=ACTIVE&disable=false`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200) {
        this.roleList = [
          {
            _id: '61df024613450872ca0b0b59',
            createdBy: '61d812ae94eba23c3e5771ee',
            role: 'COLLECTOR',
            status: 'ACTIVE',
            updatedAt: '2022-01-12T16:31:02.758Z',
            createdAt: '2022-01-12T16:31:02.758Z',
            artwork_approval_RoleId: [],
            is_approval_required: false,
            is_artwork_approval_required: false,
            user_approval_RoleId: [],
            disable: false,
            artist_type: '',
          },
          {
            _id: '61dfceb75f1c8a1939f250e6',
            createdBy: '61d812ae94eba23c3e5771ee',
            role: 'OPEN CREATOR',
            status: 'ACTIVE',
            updatedAt: '2022-01-13T07:03:19.594Z',
            createdAt: '2022-01-13T07:03:19.594Z',
            artwork_approval_RoleId: [],
            is_approval_required: false,
            is_artwork_approval_required: false,
            user_approval_RoleId: [],
            disable: false,
            artist_type: 'Open',
          },
        ];
      }
    });
  }
  addRecaptchaScript() {
    window['grecaptchaCallback'] = () => {
      this.renderReCaptcha();
    };

    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['grecaptchaCallback']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src =
        'https://www.google.com/recaptcha/api.js?onload=grecaptchaCallback&render=explicit';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'recaptcha-jssdk', this);
  }

  renderReCaptcha() {
    window['grecaptcha'].render(this.recaptchaElement.nativeElement, {
      sitekey: '6LeBSv0ZAAAAAByosZw1-W6q_qN-CjevjjnSQq7E',
      callback: (response) => {
        console.log(response);
      },
    });
  }
  onSubmit() {
    this.Haserror = false;

    if (this.registerForm.controls.email.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Please provide a valid email address.';
      return;
    } else if (this.registerForm.controls.first_name.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Your first name required!';
      return;
    } else if (this.registerForm.controls.last_name.invalid) {
      this.Haserror = true;
      this.ErroMsg = 'Your last name required!';
      return;
    } else if (this.registerForm.controls.password.invalid) {
      this.Haserror = true;
      this.ErroMsg =
        'Invalid password choice, please choose a password with minimum 8 characters!!';
      return;
    } else if (window['grecaptcha'].getResponse() == '') {
      this.Haserror = true;
      this.ErroMsg = 'reCAPTCHA is not verified!';
      return;
    } else if (!this.registerForm.controls.policy_accept.value) {
      this.Haserror = true;
      this.ErroMsg = 'Please accept Terms of Use and Privacy Policy!';
      return;
    }
    let role_id = this.roleList.find(
      (role) => role.role == this.registerForm.value.type
    );

    let data = {
      first_name: this.registerForm.value.first_name,
      last_name: this.registerForm.value.last_name,
      role_id: role_id._id,
      email: this.registerForm.value.email?.toLowerCase()?.trim(),
      password: this.registerForm.value.password,
    };

    localStorage.setItem('signupDetails', JSON.stringify(data));
    //this.closePopup.emit();
    if (this.registerForm.value.type === 'COLLECTOR') {
      let url = apiUrl.register;
      let data = JSON.parse(localStorage.getItem('signupDetails'))
      this.server.showSpinner()
      this.server.postApi(url, data).subscribe(res => {
        this.server.hideSpinner()
        console.log(res)
        if (res.statusCode == 200) {
          this.closePopup.emit();
          alert(res.message)
          localStorage.setItem('userId', res.data.userId)
          this.openVerify.emit();
        }
      }, err => {
        this.closePopup.emit();
        localStorage.removeItem('signupDetails')
        alert(err.error.message)
      });

    } else {
      this.openSignupPopup.emit();
    }

    // else {
    //   this.authService
    //     .userRegister(
    //       this.registerForm.controls.email.value,
    //       this.registerForm.controls.first_name.value,
    //       this.registerForm.controls.last_name.value,
    //       this.registerForm.controls.password.value,
    //       this.registerForm.controls.receive_notification.value
    //     )
    //     .subscribe(
    //       (data) => {
    //         this.openVerify.emit();
    //       },
    //       (error) => {
    //         this.Haserror = true;
    //         this.ErroMsg =
    //           'Something went wrong. Apologies for the inconvenience, please try again.!!';
    //       }
    //     );
    // }
  }
  changeFocus(index) {
    setTimeout(() => {
      this.isDropDownOpen[index] = false;
    }, 200);
  }
  // async onSubmit() {
  //   this.Haserror = false;

  //   if (this.registerForm.controls.email.invalid) {
  //     this.Haserror = true;
  //     this.ErroMsg = 'Please provide a valid email address.';
  //   } else if (this.registerForm.controls.first_name.invalid) {
  //     this.Haserror = true;
  //     this.ErroMsg = 'Your first name required!!';
  //   } else if (this.registerForm.controls.last_name.invalid) {
  //     this.Haserror = true;
  //     this.ErroMsg = 'Your last name required!!';
  //   } else if (this.registerForm.controls.password.invalid) {
  //     this.Haserror = true;
  //     this.ErroMsg =
  //       'Invalid password choice, please choose a password with minimum 8 characters!!';
  //   } else if (window['grecaptcha'].getResponse() == '') {
  //     this.Haserror = true;
  //     this.ErroMsg = 'reCAPTCHA is not verified!!';
  //   } else if (!this.registerForm.controls.policy_accept.value) {
  //     this.Haserror = true;
  //     this.ErroMsg = 'Please accept Terms of Use and Privacy Policy!!';
  //   } else {
  //     this.authService
  //       .userRegister(
  //         this.registerForm.controls.email.value,
  //         this.registerForm.controls.first_name.value,
  //         this.registerForm.controls.last_name.value,
  //         this.registerForm.controls.password.value,
  //         this.registerForm.controls.receive_notification.value
  //       )
  //       .subscribe(
  //         (data) => {
  //           this.openVerify.emit();
  //         },
  //         (error) => {
  //           this.Haserror = true;
  //           this.ErroMsg =
  //             'Something went wrong. Apologies for the inconvenience, please try again.!!';
  //         }
  //       );
  //   }
  // }
}
