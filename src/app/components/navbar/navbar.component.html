<header
  *ngIf="navbarType !== 'hide'"
  [ngStyle]="{
    'background-color': navbarType === 'block' ? 'white' : 'transparent'
  }"
>
  <div class="d-flex align-items-center justify-content-between w-100 h-100">
    <div
      [ngClass]="{ white: navbarColor !== 'normal' }"
      class="d-flex align-items-center justify-content-start h-100"
    >
      <a id="logo" routerLink="/">
        <img
          [src]="
            navbarColor === 'normal'
              ? 'assets/images/logo.png'
              : 'assets/images/inverted-logo.png'
          "
          alt="Logo"
        />
      </a>

      <nav class="d-flex align-items-center" *ngIf="!isMobile">
        <div class="filter" [style.width.vw]="false && userLogged ? 23 : 38">
          <input
            type="text"
            placeholder="Search by artist or artworks"
            class="search-input"
            name="search"
            [(ngModel)]="searchValue"
            [ngModelOptions]="{ standalone: true }"
            (keyup.enter)="searchResult()"
          />
        </div>

        <div class="nav-items">
          <a routerLink="/discover" class="nav-item" routerLinkActive="active">
            Discover
          </a>
          <div class="mega-menu">
            <div
              class="d-flex align-items-start justify-content-around h-100 w-100"
            >
              <div class="mega-item">
                <div class="title">
                  <a routerLink="/discover/curated"> Curated </a>
                </div>
                <p>
                  Curated selection of cutting-edge artworks and exhibitions.
                </p>
                <a
                  routerLink="/marketplace"
                  [queryParams]="{ artworks_group: 'curated' }"
                  >Artworks</a
                >
                <a routerLink="/discover/artists">Artists</a>
                <a routerLink="/discover/thought-leaders">Curators </a>
                <a routerLink="/discover/exhibitions">Exhibitions</a>
                <a routerLink="/discover/education-videos">Videos</a>
              </div>
              <div class="mega-item">
                <div class="title">
                  <a style="cursor: default; text-decoration: none"> &nbsp; </a>
                </div>
                <p style="line-height: 2.3">&nbsp;</p>
                <a routerLink="/discover/blogs">Blogs</a>
                <a routerLink="/discover/glossary">Glossary</a>
                <a routerLink="/discover/art-resources">Art Resources</a>
                <a routerLink="/discover/timeline">Timeline</a>
              </div>
              <div class="mega-item">
                <div class="title">
                  <a routerLink="/discover/open"> Open </a>
                </div>
                <p>Featuring artworks from a wide range of creators.</p>
                <a
                  routerLink="/marketplace"
                  [queryParams]="{ artworks_group: 'open' }"
                  >Artworks</a
                >
                <a
                  routerLink="/discover/artists"
                  [queryParams]="{ artist_type: 'Open' }"
                  >Artists</a
                >
              </div>
              <div class="mega-item"></div>
            </div>
          </div>
        </div>
        <div class="nav-items">
          <a
            routerLink="/marketplace"
            class="nav-item"
            routerLinkActive="active"
          >
            Marketplace
          </a>
          <div class="mega-menu">
            <div
              class="d-flex align-items-start justify-content-around h-100 w-100"
            >
              <div class="mega-item">
                <div class="title">
                  <a
                    routerLink="/marketplace"
                    [queryParams]="{ artworks_group: 'curated' }"
                  >
                    Curated
                  </a>
                </div>
                <p>Curated selection of cutting-edge artworks</p>
              </div>
              <div class="mega-item">
                <div class="title">
                  <a
                    routerLink="/marketplace"
                    [queryParams]="{ artworks_group: 'open' }"
                    >Open
                  </a>
                </div>
                <p>Featuring artworks from a wide range of creators</p>
              </div>
              <div class="mega-item"></div>
              <div class="mega-item"></div>
            </div>
          </div>
        </div>

        <div class="nav-items">
          <a routerLink="/community" class="nav-item" routerLinkActive="active">
            Community
          </a>
        </div>
        <div class="nav-items">
          <a
            routerLink="/discover/open"
            class="nav-item"
            routerLinkActive="active"
          >
            Open&nbsp;<sup style="color: #004ddd">new</sup>
          </a>
        </div>
        <div class="nav-items">
          <a
            routerLink="/press"
            class="nav-item"
            routerLinkActive="active"
          >
           Press
          </a>
        </div>
        <div *ngIf="false && userLogged" class="nav-items">
          <button class="Wallet-Buttons" (click)="connectWallet()">
            Connect Wallet
          </button>
        </div>
        <div *ngIf="false && userLogged" class="nav-items">
          <button
            class="Wallet-Buttons"
            routerLink="/artist-portal/settings/create-wallet"
          >
            Create Wallet
          </button>
        </div>
        <!-- <div class="nav-items">
          <a
            routerLink="/explore/artworks"
            routerLinkActive="active"
            [queryParams]="{ sale: 1 }"
          >
            Buy
          </a>
          <div class="mega-menu">
            <div
              class="
                d-flex
                align-items-start
                justify-content-around
                h-100
                w-100
              "
            >
              <div style="flex: 0 0 65%">
                <div
                  class="cat-title"
                  style="text-align: center; font-size: 1.65vw; margin-top: 2vw"
                >
                  Terrain Works
                </div>
                <div
                  class="
                    d-flex
                    align-items-start
                    justify-content-around
                    h-100
                    w-100
                  "
                >
                  <div class="mega-item">
                    <div class="title">
                      <a routerLink="/discover/artists"> Terrain Artists </a>
                    </div>
                    <a routerLink="/discover/artists/chetnaa/artworks"
                      >Chetnaa</a
                    >
                    <a routerLink="/discover/artists/srishti-menon/artworks"
                      >Srishti Rana Menon</a
                    >
                    <a routerLink="/discover/artists/shivangi-ladha/artworks"
                      >Shivangi Ladha</a
                    >
                    <a routerLink="/discover/artists/nur-mahammad/artworks"
                      >Nur Mahammad</a
                    >
                    <a
                      routerLink="/discover/artists/jayeti-bhattacharya/artworks"
                      >Jayeti Bhattacharya</a
                    >
                    <a routerLink="/discover/artists/nabanita-guha/artworks"
                      >Nabanita Guha</a
                    >
                    <div class="hr-line"></div>
                    <div class="bottom">
                      <a routerLink="/explore/artworks">All Artworks </a>
                    </div>
                  </div>
                  <div class="mega-item">
                    <div class="title">
                      <a routerLink="">NFTs - Digital art </a>
                    </div>
                    <a routerLink="">Thukral & Tagra</a>
                    <a routerLink="">Raqs Media Collective</a>
                    <a routerLink="">Tanya Goel</a>
                    <a routerLink="">Bharti Kher</a>
                    <a routerLink="">Shilpa Gupta</a>
                    <div class="hr-line"></div>
                    <div class="bottom">
                      <a routerLink="">All Digital Art </a>
                    </div>
                  </div>
                  <div class="mega-item">
                    <div class="title">
                      <a routerLink="">Limited Edition Prints </a>
                    </div>
                    <a routerLink="">Jitish Kallat</a>
                    <a routerLink="">Dhruvi Acharya</a>
                    <a routerLink="">Gieve Patel</a>
                    <a routerLink="">Sudhir Patwardhan</a>
                    <div class="hr-line"></div>
                    <div class="bottom">
                      <a routerLink="">All Prints </a>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div style="width: 0.1vw; height: 100%"></div> -->
        <!-- <div style="flex: 0 0 35%">
          <div
            class="cat-title"
            style="text-align: center; font-size: 1.65vw; margin-top: 2vw"
          >
            Open Marketplace
          </div>

          <div
            class="d-flex align-items-start justify-content-around h-100 w-100"
          >
            <div class="mega-item">
              <div class="title">
                <a routerLink="">NFTs</a>
              </div>
              <a routerLink="">Artist XYZ</a>
              <a routerLink="">Artist 123</a>
              <a routerLink="">Artist ABC</a>
              <div class="hr-line"></div>
              <div class="bottom">
                <a routerLink="">All Artworks </a>
              </div>
            </div>
          </div>
        </div> -->
      </nav>
    </div>

    <div class="nav-end d-flex justify-content-end" *ngIf="!isMobile">
      <div class="d-flex justify-content-center align-items-center">
        <button
          *ngIf="!userLogged"
          (click)="openRegister = true"
          class="loginBtn"
        >
          Create
        </button>
        <!-- <button
          *ngIf="userLogged?.role !== 'collector'"
          (click)="openLogin = true"
          class="loginBtn"

        >
          Login
        </button> -->
        <button *ngIf="!userLogged" (click)="openLogin = true" class="loginBtn">
          Login
        </button>

        <!-- <button
          *ngIf="userLogged?.role !== 'collector'"
          (click)="openRegister = true"
          class="loginBtn"
        >
          Sign up
        </button> -->

        <!-- <div
          [ngClass]="{ whitebg: navbarColor !== 'normal' }"
          (click)="showCart()"
          class="nav-cart"
          style="cursor: pointer"
        >
          Cart ({{ cartItemsCount }})
        </div> -->
        <div class="logged-user-name" *ngIf="userLogged">
          {{ userLogged.fullName }}
          <img
            src="assets/icons/down-arrow.png"
            style="width: 0.8vw; padding-bottom: 0.3vw; margin-left: 1vw"
          />
          <div *ngIf="userLogged" class="dropdown-container w-100">
            <div class="dropdown-content">
              <a routerLink="/artist-portal/settings/"
                ><div style="padding: 0.6vw; cursor: pointer; color: black">
                  My Portal
                </div></a
              >
              <a
                *ngIf="
                  userLogged?.url_name &&
                  userLogged?.publish &&
                  userLogged?.role !== 'THOUGHT-LEADER'
                "
                [routerLink]="'/discover/artists/' + userLogged?.url_name"
                ><div style="padding: 0.6vw; cursor: pointer; color: black">
                  My Profile
                </div></a
              >
              <div (click)="logOut()" style="padding: 0.6vw; cursor: pointer">
                Log Out
              </div>
            </div>
          </div>
        </div>

        <!-- <button *ngIf="isNFTs" (click)="metaLogin()" style="margin-left: 2vw">
          {{ isMetaLogged ? ethereumAdd : "MetaMask" }}
        </button> -->

        <!-- <div *ngIf="userLogged?.role === 'collector'">
          {{ userPersonal?.first_name }}
          <img
            src="assets/icons/down-arrow.png"
            style="width: 0.8vw; padding-bottom: 0.3vw; margin-left: 1vw"
          />
        </div> -->
      </div>

      <div *ngIf="userLogged?.role === 'collector'" class="dropdown-container">
        <div class="dropdown-content">
          <a routerLink="/collector/profile"
            ><div style="padding: 0.6vw; cursor: pointer; color: black">
              Profile
            </div></a
          >
          <div (click)="logOut()" style="padding: 0.6vw; cursor: pointer">
            Log Out
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="isMobile">
      <div class="filter">
        <input
          type="text"
          placeholder="Search by artist or artworks"
          class="search-input"
          name="search"
          [(ngModel)]="searchValue"
          [ngModelOptions]="{ standalone: true }"
          (keyup.enter)="searchResult()"
        />
      </div>
    </div>

    <div *ngIf="isMobile && !isOpen">
      <div
        *ngIf="isMobile"
        (click)="showCart()"
        class="nav-cart"
        style="cursor: pointer; font-size: 3vw; display: contents"
      >
        Cart ({{ cartItemsCount }})
      </div>

      <button class="icon" (click)="openNav()">
        <fa-icon [icon]="faBars"></fa-icon>
      </button>
    </div>

    <div *ngIf="isMobile && isOpen">
      <button class="icon" (click)="closeNav()">
        <fa-icon [icon]="faTimes"></fa-icon>
      </button>
    </div>
  </div>
</header>

<div #navBar class="nav-bar nav-close">
  <div class="sec" *ngFor="let item of components">
    <a
      (click)="closeNav()"
      [routerLink]="item.link"
      routerLinkActive="active"
      [routerLinkActiveOptions]="{ exact: true }"
    >
      {{ item.name }}
    </a>
    <!-- <div style="background-color: black; height: 1px; width: 100vw;"></div> -->
  </div>
  <div class="sec">
    <a *ngIf="!userLogged" (click)="onLogin()" style="color: blue"> Log In </a>
    <div *ngIf="userLogged">{{ userLogged.fullName }}</div>
  </div>
</div>
<app-login
  (closePopup)="openLogin = false"
  (openRegister)="openLogin = false; openRegister = true"
  (openVerify)="openRegister = false; openVerify = true"
  *ngIf="openLogin"
></app-login>
<app-register
  (closePopup)="openRegister = false"
  (openLogin)="openRegister = false; openLogin = true"
  (openSignupPopup)="openRegister = false; openSignupPopup = true"
  (openVerify)="openRegister = false; openVerify = true"
  *ngIf="openRegister"
></app-register>
<app-verify
  (closePopup)="openVerify = false"
  (openLogin)="openVerify = false; openLogin = true"
  (openVerify)="openRegister = false; openVerify = true"
  *ngIf="openVerify"
>
</app-verify>

<app-signup-popup
  (closePopup)="openSignupPopup = false"
  (openLogin)="openSignupPopup = false; openLogin = true"
  (openVerify)="openRegister = false; openVerify = true"
  *ngIf="openSignupPopup"
>
</app-signup-popup>
