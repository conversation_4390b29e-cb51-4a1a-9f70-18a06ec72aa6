header {
  padding: 0px var(--page-default-margin);
  height: 5.833vw;
  box-shadow: 0 13px 13px -12px rgba(0, 0, 0, 0.2);
  background-color: var(--primary-background-color);
  position: relative;
  z-index: 2;
  #logo img {
    height: 1.67vw;
    margin-right: 1vw;
  }

  nav {
    a {
      color: var(--primary-font-color);
      padding: 2.27vw 0.5vw;
      text-decoration: none;
    }
    .nav-items > a {
      // color: var(--primary-font-color);
      font-size: 1.25vw;
      // color: white;
    }
    .nav-items > a:hover {
      // color: var(--primary-font-color);
    }
  }

  .nav-end {
    font-size: 1.25vw;
    .nav-cart {
      color: var(--primary-font-color);
      // margin-right: 1.38vw;
      margin: 0 0.5vw;
      // font-size: 1.25vw;
    }
    .nav-cart:hover {
      color: var(--primary-font-color);
    }
    .loginBtn {
      background-color: black;
      border: solid 0.069vw var(--primary-font-color);
      margin: 0 0.5vw;
      color: white;
      padding: 0.367vw 0.966vw;
      font-size: 1.2vw;
    }
    .loginBtn:hover {
      border: solid 0.069vw var(--primary-font-color);
      //color: var(--primary-font-color);
    }
    button {
      background-color: var(--tertiary-font-color);
      border: solid 0.069vw var(--tertiary-font-color);
      border-radius: 2.5vw;
      // border: none;
      color: var(--secondary-font-color);
      // padding: 0.5vw 1vw;
      padding: 0.486vw 2.083vw;
      text-decoration: none;
      // border-radius: 6px;
    }
    .dropdown-container {
      display: none;
      height: auto;
      position: fixed;

      z-index: 20;
    }

    .dropdown-content {
      background-color: white;
      width: 8vw;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
      z-index: 20;
      margin-top: 1vw;
      // margin-right: -5vw;
    }
    .logged-user-name {
      &:hover .dropdown-container,
      .dropdown-container:hover {
        display: block;
      }
    }
  }
  @media (max-width: 768px) {
    height: 14.97vw;
    #logo img {
      height: 3.87vw;
    }
    .icon {
      height: 3.87vw;
      outline: none;
      background: none;
      border: none;
      font-size: 3.87vw;
      // position: absolute;
      right: var(--page-default-margin);
      top: 5.8vw;
    }
    .icon:hover {
      color: var(--tertiary-font-color);
    }
  }
}
.White {
  color: white !important;
}
.White:hover {
  color: var(--primary-font-color) !important;
}
.active {
  //font-weight: 600;
  color: var(--tertiary-font-color) !important;
}

.nav-bar {
  background-color: var(--secondary-background-color);
  height: calc(100vh - 14.97vw);
  vertical-align: middle;
  // transition: all 0.5s linear;
  transition: width 2s ease-in-out;
  .sec {
    height: 12%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 4.35vw;
    a {
      color: var(--primary-font-color);
      text-decoration: none;
      width: 100%;
      height: 100%;
      justify-content: center;
      display: flex;
      align-items: center;
    }
    hr {
      margin: 0;
      border: 0;
      border-top: 0.069vw solid var(--hr-primary-color);
    }
    .nav-end {
      button {
        background-color: var(--tertiary-font-color);
        border: none;
        color: var(--secondary-font-color);
        padding: 1.15vw 1.5vw;
        text-decoration: none;
        border-radius: 6px;
      }
    }
  }
  .active {
    background-color: var(--primary-background-color);
  }
}
.nav-close {
  display: none !important;
  width: 0 !important;
}

.nav-open {
  width: 100vw !important;
  display: table-cell !important;
}
.nav-items {
  .mega-menu {
    display: none;
    width: 100vw;
    position: absolute;
    left: 0;
    top: 5.84vw;
    z-index: 20;
    background-color: #fff;
    border-width: 0.1vw;
    border-style: solid;
    border-color: #cccccc;
    padding-left: var(--page-default-margin);
    padding-right: var(--page-default-margin);
    .mega-item {
      width: 24%;
      margin-top: 1.5vw;
      margin-bottom: 1.5vw;
      a {
        padding: 0;
        display: block;
        margin-bottom: 0.3vw;
        &:hover {
          text-decoration: underline;
        }
      }
      .title {
        margin-top: 0.6vw;
        font-size: 1.6vw;
        font-weight: 400;
      }
      p {
        margin-top: 0.9vw;
        font-weight: 300;
        font-family: var(--secondary-font);
        margin-bottom: 0.9vw;
      }
      .hr-line {
        margin-top: 0.9vw;
        background-color: #cccccc;
        height: 0.01vw;
        width: 80%;
      }
      .bottom {
        margin-top: 0.6vw;
        a {
          color: #808080;
        }
        font-size: 0.9vw;
      }
    }
  }
  &:hover .mega-menu,
  .mega-menu:hover {
    display: block;
  }
  &:hover {
    & > a {
      color: var(--tertiary-font-color);
      //font-weight: 600;
    }
  }
}
.filter {
  color: var(--quaternary-font-color);
  font-size: var(--default-font-size);
  width: 38vw;
  margin: 0 0.5vw;
  @media (max-width: 768px) {
    width: auto;
    input[type="text"]:focus {
      border: solid 0.07vw var(--tertiary-font-color);
    }
  }
  input[type="text"] {
    padding: 0.7vw 1.32vw 0.7vw 1vw;
    -o-object-fit: contain;
    object-fit: contain;
    outline: none;
    width: 100%;
    font-family: Arial, FontAwesome;
    border-radius: 0.5vw;
    font-weight: 500;
    // margin-bottom: 2.08vw;
    color: var(--quaternary-font-color);
    height: 2.8vw;
    border: solid 0.07vw var(--secondary-font-color);
    text-indent: 1.73vw;
    background-position: 0 50%;
    background-repeat: no-repeat;
    background-position-x: 1.04vw;
    background-size: 1.04vw;
    background-image: url("../../../assets/icons/search/<EMAIL>");
    @media (max-width: 768px) {
      padding: 2vw 4vw;
      height: auto;
      border-radius: 1vw;
      font-size: 3.4vw;
      background-size: 3vw;
      width: 45vw;
      background-position-x: 2vw;
    }
  }
  input[type="text"]:focus {
    background-image: none;
    text-indent: 0px;
  }
}
.search-input {
  background-color: transparent;
}

.Wallet-Buttons {
  font-size: 1.111vw;
  background-color: white;
  padding: 0.5vw 1vw;
  border: 0.06944vw solid black;
  margin: 0 0.5vw;
  border-radius: 2vw;
}
.Wallet-Buttons:hover {
  background-color: black;
  // padding: 0.5vw 1vw;
  color: white;
  border: 0.06944vw solid white;
}

.cpy {
  border: gray;
  padding: 0px 8px 0px 8px;
  background: #f6f1f1;
  margin-left: 5px;
  cursor: pointer;
}
.white {
  .nav-item {
    color: white !important;
    &:hover {
      color: var(--primary-font-color) !important;
    }
  }
}
.whitebg {
  color: white !important;
}
