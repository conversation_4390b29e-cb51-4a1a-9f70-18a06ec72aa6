import { AuthService } from './../../../services/auth.service';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { CollectorService } from 'src/app/services/collector.service';
import { Router } from '@angular/router';
import { apiUrl } from 'src/environments/environment.prod';

@Component({
  selector: 'app-verify',
  templateUrl: './verify.component.html',
  styleUrls: ['./verify.component.scss'],
})
export class VerifyComponent implements OnInit {
  @ViewChild('recaptcha', { static: true }) recaptchaElement: ElementRef;
  @Output() closePopup = new EventEmitter();
  @Output() openLogin = new EventEmitter();

  verifyForm: FormGroup;
  otp: string;
  Haserror: boolean = false;
  ErroMsg: string;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private route: Router,
    private server : CollectorService,

  ) {
    this.verifyForm = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.addRecaptchaScript();
  }
  onOtpChange(otp) {
    this.otp = otp;
  }
  addRecaptchaScript() {
    window['grecaptchaCallback'] = () => {
      this.renderReCaptcha();
    };

    (function (d, s, id, obj) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        window['grecaptchaCallback']();
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.async = true;
      js.src =
        'https://www.google.com/recaptcha/api.js?onload=grecaptchaCallback&render=explicit';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'recaptcha-jssdk', this);
  }

  renderReCaptcha() {
    window['grecaptcha'].render(this.recaptchaElement.nativeElement, {
      sitekey: '6LeBSv0ZAAAAAByosZw1-W6q_qN-CjevjjnSQq7E',
      callback: (response) => {
        console.log(response);
      },
    });
  }
  async onSubmit() {
    this.Haserror = false;

    if (this.otp.length != 6) {
      this.Haserror = true;
      this.ErroMsg = 'Please provide a valid OTP.';
    } else if (window['grecaptcha'].getResponse() == '') {
      this.Haserror = true;
      this.ErroMsg = 'reCAPTCHA is not verified!!';
    } else {
      let url = apiUrl.verifyEmailOtp
      let req={
        "platform": "",
        "email_otp": this.otp,
        "userId": localStorage.getItem('userId')
      }
      this.server.putApi(url,req).subscribe(res=>{
        if(res.statusCode == 200) {
          alert(res.message);
          localStorage.removeItem('userId')
          this.closePopup.emit();
          this.openLogin.emit();
        }
      })
      // this.authService.verifyOtp(this.otp).subscribe(
      //   (data) => {
      //     if (data.statusCode == 200) {
      //       this.closePopup.emit();
      //       this.route.navigate(['/collector/register']);
      //     } else {
      //       this.Haserror = true;
      //       this.ErroMsg =
      //         'Something went wrong. Apologies for the inconvenience, please try again.!!';
      //     }
      //   },
      //   (error) => {
      //     localStorage.setItem('isLogged', 'false');
      //     this.Haserror = true;
      //     this.ErroMsg =
      //       'Something went wrong. Apologies for the inconvenience, please try again.!!';
      //   }
      // );
    }
  }
}
