<div class="section login">
  <div class="section-inner h-100">
    <div
      class="d-flex align-items-center justify-content-center h-100 w-100 form-container"
    >
      <div
        class="form-enquire"
        style="
          padding: 4vw 4vw;
          position: relative;
          display: flex;
          flex-direction: column;
        "
      >
        <img
          src="assets/images/logo.png"
          alt="Logo"
          style="width: 40%; align-self: center; margin-bottom: 2vw"
        />
        <div class="bottom-link" style="margin-bottom: 0.5vw">
          A six digit code (OTP) has been sent to your email. Please enter the
          code to verify your email address.
        </div>
        <form [formGroup]="verifyForm" (ngSubmit)="onSubmit()">
          <div class="d-flex justify-content-center w-100">
            <ng-otp-input
              (onInputChange)="onOtpChange($event)"
              [config]="{ length: 6, allowNumbersOnly: true }"
            ></ng-otp-input>
          </div>

          <div class="d-flex justify-content-center" style="margin-top: 1vw">
            <div #recaptcha style="transform: scale(0.77)"></div>
          </div>
          <div
            class="message alert alert-danger alert-dismissible mt-2"
            *ngIf="Haserror"
          >
            <a
              (click)="Haserror = false"
              class="close"
              data-dismiss="alert"
              aria-label="close"
              >&times;</a
            >
            <strong>Error!</strong> {{ ErroMsg }}
          </div>
          <div class="d-flex justify-content-center" style="margin-top: 1vw">
            <button type="submit" class="button">Verify</button>
          </div>
        </form>

        <div class="popup-close">
          <a (click)="closePopup.emit()" style="cursor: pointer">
            <img src="assets/images/close.png" />
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
