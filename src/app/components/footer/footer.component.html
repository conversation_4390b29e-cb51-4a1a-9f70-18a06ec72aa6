<footer [ngStyle]="{ display: footerType }">
  <div class="section">
    <div class="mobile-header">
      <p>Subscribe to our Newsletter</p>
    </div>
    <div class="mobile-email">
      <input
        type="email"
        name="subscribe"
        placeholder="Email Address"
        id="subscribe"
      />
      <button type="button">Submit</button>
    </div>
    <div class="head-section">
      <span>Subscribe to our Newsletter</span>
      <div class="icon-row">
        <a href="https://discord.gg/K24vjwxAZq" target="blank"
          ><fa-icon [icon]="faDiscord"></fa-icon
        ></a>
        <a href="https://twitter.com/terrain_art" target="blank"
          ><fa-icon [icon]="faTwitter"></fa-icon
        ></a>
        <a href="https://www.instagram.com/terrain_art/" target="blank">
          <fa-icon [icon]="faInstagram"></fa-icon
        ></a>
        <a href="https://www.facebook.com/the.terrain.art" target="blank">
          <fa-icon [icon]="faFacebook"></fa-icon
        ></a>
      </div>
    </div>
    <div class="email-section">
      <input
        type="email"
        [(ngModel)]="email"
        name="subscribe"
        placeholder="Email Address"
        id="subscribe"
      />
      <button (click)="submit()" type="button">Submit</button>
    </div>
    <div
      class="message alert alert-success alert-dismissible mt-2"
      *ngIf="Submitted"
    >
      <a
        (click)="closeAlert()"
        class="close"
        data-dismiss="alert"
        aria-label="close"
        >&times;</a
      >
      <strong>Success!</strong> Email subscribed successfully!
    </div>
    <div class="horizontal-line"></div>
    <div class="target-item-wrapped">
      <div class="item-row">
        <ng-container *ngFor="let item of footerData; let i = index">
          <a
            *ngIf="!checkIsFullUrl(item.url)"
            [routerLink]="item.url"
            [ngStyle]="{
              paddingLeft: i == 0 ? 0 : null,
              borderRight: i == footerData.length - 1 ? 'unset' : null
            }"
            >{{ item.title }}</a
          >
          <a
            *ngIf="checkIsFullUrl(item.url)"
            [href]="item.url"
            [ngStyle]="{
              paddingLeft: i == 0 ? 0 : null,
              borderRight: i == footerData.length - 1 ? 'unset' : null
            }"
            >{{ item.title }}</a
          >
        </ng-container>
      </div>
      <div class="year-row">
        <span>&copy; {{ year }} Terrain.art</span>
      </div>
    </div>
  </div>
</footer>
