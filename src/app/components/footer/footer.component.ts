import {
  FooterService,
  FooterType,
} from './../../shared/services/footer.service';
import { Component, OnInit } from '@angular/core';
import {
  faTwitter,
  faInstagram,
  faFacebook,
  faDiscord,
} from '@fortawesome/free-brands-svg-icons';
import { EnquireService } from 'src/app/pages/home/<USER>/enquire.service';
import { CollectorService } from 'src/app/services/collector.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterComponent implements OnInit {
  year = new Date().getFullYear();
  faTwitter = faTwitter;
  faInstagram = faInstagram;
  faFacebook = faFacebook;
  faDiscord = faDiscord;
  footerType = FooterType.DEFAULT;
  email;
  state = Array(4).fill(false);
  footerData;
  Submitted: boolean = false;
  constructor(
    private footerService: FooterService,
    private enquireService: EnquireService,
    private server: CollectorService,
  ) { }

  ngOnInit(): void {
    this.footerService.footerObserv$.subscribe((data) => {
      console.log('hey');
      console.log(data);

      this.footerType = data;
    });
    this.getPageData()
  }
  submit() {
    this.enquireService.subscription(this.email).then((data) => {
      this.Submitted = true;
    });
  }
  closeAlert() {
    this.Submitted = false;
  }

  public getPageData() {
    let url = `website?page=home`;
    this.server.getApi(url).subscribe((res) => {
      if (res.statusCode == 200 && res.data) {
        this.footerData = res.data.contents?.footer?.filter((a) => a.publish);
        console.log(this.footerData);

      }
    });
  }

  checkIsFullUrl(url) {
    return url.match(/^(http|https):\/\/[^\s\/]+[\s\/]+/);
  }

}
