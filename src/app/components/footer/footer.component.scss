// footer {
//   // height: 5.833vw;
//   margin-bottom: 1.2vw;

//   .footer-content {
//     color: var(--quaternary-font-color);

//     span {
//       font-size: 0.69vw;
//     }

//     fa-icon {
//       margin-left: 1.3vw;
//       font-size: 1.2vw;
//     }
//   }
//   .footer-links {
//     margin-bottom: 1.2vw;
//     justify-content: space-between;
//     align-items: flex-start;
//     .footer-col {
//       // width: 15%;
//       a {
//         display: block;
//         color: var(--quaternary-font-color);
//       }
//     }
//   }
//   .line-seperator {
//     height: 4.3vw;
//     border-left: 1px solid #0000001a;
//   }
// }
// hr {
//   margin: 0;
//   margin-bottom: 2.08vw;
//   border: 0;
//   border-top: 0.069vw solid var(--hr-primary-color);
// }
// .link-head {
//   display: block !important;
// }
// .mobile-link-head {
//   display: none !important;
// }
// .sub-links {
//   max-height: 100vh;
// }
// @media (max-width: 768px) {
//   .footer-content {
//     flex-wrap: wrap;
//     .footer-copy {
//       width: 100%;
//       order: 2;
//       justify-content: center;
//       span {
//         font-size: 3.2vw;
//       }
//     }
//     .footer-social {
//       order: 1;
//       width: 100%;
//       justify-content: center;
//       margin-bottom: 2vw;
//       margin-top: 2vw;
//       fa-icon {
//         margin-left: 1.3vw;
//         margin-right: 1.3vw;

//         font-size: 3.8vw;
//       }
//     }
//   }
//   .footer-links {
//     flex-direction: column;
//     justify-content: center;
//     align-items: center !important;
//     text-align: center;
//     width: 100%;
//     a {
//       font-size: 3.8vw;
//       margin-top: 0.5vw;
//       margin-bottom: 0.5vw;
//     }
//     .sub-links {
//       max-height: 0vw;
//       transition: max-height 0.5s cubic-bezier(0.445, 0.05, 0.55, 0.95);
//       overflow: hidden;
//       &.active {
//         max-height: 100vw;
//       }
//     }
//   }
//   .mobile-link-head {
//     display: block !important;
//   }
//   .link-head {
//     display: none !important;
//   }
// }
// a {
//   color: var(--quaternary-font-color);
//   &:hover {
//     color: var(--tertiary-font-color);
//   }
// }

footer {
  background-color: #f6f6f6;
  .section {
    padding: 3.472vw 7.222vw 2.5vw 7.222vw;
    .mobile-header {
      display: none;
    }
    .mobile-email {
      display: none;
    }
    .head-section {
      width: 100%;
      display: inline-block;
      // justify-content: space-between;
      margin-bottom: 1.5vw;
      span {
        vertical-align: middle;
        margin-top: 0.8vw;
        font-size: 1.25vw;
        float: left;
      }
      .icon-row {
        vertical-align: middle;
        float: right;
        a {
          fa-icon {
            width: 2.083vw;
            height: 2.083vw;
            margin-left: 2.083vw;
            font-size: 2.083vw;
            color: #bfbfbf;
          }
        }
      }
    }
    .email-section {
      display: flex;
      align-items: center;
      input[type="email"] {
        float: left;
        width: 27.777vw;
        height: 3.46vw;
        border-radius: 0.4vw;
        border: solid 0.069vw #ced4db;
        background-color: transparent;
        padding: 0.8vw 1vw;
        margin-right: 1.388vw;
      }
      input[type="email"]:focus {
        background-color: white;
      }
      button {
        float: right;
        border: solid 0.069vw var(--primary-font-color);
        background-color: var(--primary-font-color);
        color: var(--secondary-font-color);
        padding: 0 3.5vw;
        height: 3.055vw;
        border-radius: 1.5vw;
        font-size: 1.25vw;
      }
    }
    .horizontal-line {
      height: 0.069vw;
      width: 100%;
      background-color: #bfbfbf;
      margin-top: 3vw;
      margin-bottom: 2vw;
    }
    .target-item-wrapped {
      display: flex;
      justify-content: space-between;
      // display: inline-block;
      .item-row {
        float: left;
        a {
          color: var(--quaternary-font-color);
          padding: 0 1vw;
          border-right: 0.069vw solid #bfbfbf;
        }
      }
      .year-row {
        float: right;
        span {
          color: #808080;
          font-size: 1.111vw;
        }
      }
    }
  }
}
@media (max-width: 768px) {
  footer {
    background-color: #f6f6f6;
    .section {
      .mobile-header {
        display: flex;
        justify-content: center;
        p {
          padding-top: 2vw;
          font-size: 3.5vw;
        }
      }
      .mobile-email {
        display: inline-block;
        input[type="email"] {
          float: left;
          width: 59.777vw;
          height: 8vw;
          border-radius: 1vw;
          border: solid 0.1388vw #ced4db;
          background-color: transparent;
          padding: 1.8vw 2vw;
          margin-right: 3vw;
          font-size: 2.5vw;
        }
        input[type="email"]:focus {
          background-color: white;
        }
        button {
          float: right;
          border: solid 0.069vw var(--primary-font-color);
          background-color: var(--primary-font-color);
          color: var(--secondary-font-color);
          padding: 0 7.5vw;
          height: 8vw;
          border-radius: 8.5vw;
          font-size: 2.5vw;
        }
      }
      padding: 3.472vw 7.222vw 2.5vw 7.222vw;
      .head-section {
        width: 100%;
        display: inline-block;
        // justify-content: space-between;
        margin-bottom: 1.5vw;
        span {
          display: none;
          vertical-align: middle;
          margin-top: 0.8vw;
          font-size: 1.25vw;
          float: left;
        }
        .icon-row {
          margin-top: 3vw;
          width: 100%;
          vertical-align: middle;
          float: unset;
          justify-content: center;
          display: flex;
          gap: 4vw;
          a {
            fa-icon {
              // width: 10vw;
              // height: 10vw;
              // margin-left: 2.083vw;
              font-size: 6vw;
              color: #bfbfbf;
            }
          }
        }
      }
      .email-section {
        display: none;
      }
      .horizontal-line {
        height: 0.1388vw;
        width: 100%;
        background-color: #bfbfbf;
        margin-top: 0vw;
        margin-bottom: 3vw;
      }
      .target-item-wrapped {
        // display: flex;
        // justify-content: space-between;

        display: flex;
        // justify-content: center;
        align-items: center;
        gap: 2vw;
        flex-direction: column;
        .item-row {
          float: unset;
          justify-content: center;
          display: flex;
          flex-wrap: wrap;
          a {
            color: var(--quaternary-font-color);
            padding: 0 2vw;
            border-right: 0.1388vw solid #bfbfbf;
            font-size: 2.222vw;
            line-height: 4vw;
          }
        }
        .year-row {
          float: unset;
          span {
            color: #808080;
            font-size: 2.222vw;
          }
        }
      }
    }
  }
}
