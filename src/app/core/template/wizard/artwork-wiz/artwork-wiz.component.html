<div class="Container_">
  <fa-icon
    [hidden]="current_step === 1"
    [icon]="faArrowAltCircleLeft"
    (click)="back()"
    [style.width.vw]="2"
    [style.cursor]="'pointer'"
    style="color: #004ddd"
  ></fa-icon>
  <ng-content> </ng-content>

  <div class="wiz-current-step">
    <ng-container
      *ngFor="let item of [].constructor(total_steps); let i = index"
    >
      <div
        class="step-number"
        [ngClass]="i + 1 <= current_step ? 'active-step' : 'non-active-step'"
      >
        {{ i + 1 }}
      </div>
      <div
        class="step-leader"
        [ngClass]="i + 1 < current_step ? 'active-lead' : 'non-active-lead'"
        *ngIf="i < total_steps - 1"
      ></div>
    </ng-container>
  </div>
</div>
