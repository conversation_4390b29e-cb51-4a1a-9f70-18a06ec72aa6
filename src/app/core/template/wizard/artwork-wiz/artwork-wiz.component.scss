.Container_ {
  border: 0.06944vw solid var(--tertiary-font-color);
  border-radius: 1vw;
  padding: 3vw 6vw;
  margin: 2.5vw;

  .Heading {
    font-size: 2.8vw;
    font-weight: 600;
    text-align: center;
    margin-bottom: 4vw;
  }
}

.wiz-current-step {
  margin-top: 2.5vw;
  display: inline-flex;
  align-items: center;
  width: 100%;

  .step-number {
    border-radius: 50%;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 2vw;
    width: 2vw;
  }

  .step-leader {
    height: 0.19vw;
    flex-grow: 1;
  }
}

.active-lead {
  background-color: var(--tertiary-font-color);
}

.non-active-lead {
  background-color: var(--timeline-color);
}

.active-step {
  background-color: var(--tertiary-font-color);
  border: 0.13vw solid var(--tertiary-font-color);
  color: var(--secondary-font-color);
}

.non-active-step {
  background-color: var(--primary-background-color);
  border: 0.13vw solid var(--timeline-color);
  color: var(--timeline-color);
}
