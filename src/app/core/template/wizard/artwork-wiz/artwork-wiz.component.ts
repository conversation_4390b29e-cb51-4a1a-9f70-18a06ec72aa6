import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { faArrowAltCircleLeft } from '@fortawesome/free-regular-svg-icons';

@Component({
  selector: 'artwork-wiz',
  templateUrl: './artwork-wiz.component.html',
  styleUrls: ['./artwork-wiz.component.scss'],
})
export class ArtworkWizComponent implements OnInit {
  @Input() total_steps: number;
  @Input() current_step: number;
  @Output() backStep = new EventEmitter();

  faArrowAltCircleLeft = faArrowAltCircleLeft;

  constructor() {}
  back() {
    if (this.current_step > 1) this.backStep.emit();
  }
  ngOnInit(): void {}
}
