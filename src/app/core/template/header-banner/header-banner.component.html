<div
  [style.background-image]="
    'url(https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
    data?.banner_image +
    ')'
  "
  class="banner_Container"
>
  <div class="top_section">
    <!-- <fa-icon [icon]="faShare"></fa-icon>
    <fa-icon [icon]="faElipses"></fa-icon> -->
  </div>
  <div class="mid_section">
    <avatar
      [src]="
        'https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
        data?.thumbnail_image
      "
      [size]="isMobile ? 32 : 16"
    ></avatar>
    <div class="title_section">
      <p class="leaderName">{{ data?.display_name }}</p>
      <div class="username_badge">
        <div class="badge">
          <!-- <img class="avatar__image" [src]="usericon" /> -->
        </div>
        <span class="username">@{{ data?.url_name }}</span>
      </div>
    </div>
  </div>
  <div class="social-icons">
    <div class="social-links">
      <a *ngIf="getsocialUrl('Instagram')" [href]="getsocialUrl('Instagram')">
        <fa-icon [icon]="faInst"></fa-icon>
      </a>
      <a *ngIf="getsocialUrl('Facebook')" [href]="getsocialUrl('Facebook')"
        ><fa-icon [icon]="fafb"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Twitter')" [href]="getsocialUrl('Twitter')"
        ><fa-icon [icon]="faTwit"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Youtube')" [href]="getsocialUrl('Youtube')"
        ><fa-icon [icon]="faYoutube"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Behance')" [href]="getsocialUrl('Behance')"
        ><fa-icon [icon]="faBehance"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Website')" [href]="getsocialUrl('Website')"
        ><fa-icon [icon]="faGlobe"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Vimeo')" [href]="getsocialUrl('Vimeo')"
        ><fa-icon [icon]="faVim"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('LinkedIn')" [href]="getsocialUrl('LinkedIn')"
        ><fa-icon [icon]="faLinkedinIn"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Flickr')" [href]="getsocialUrl('Flickr')"
        ><fa-icon [icon]="faFlickr"></fa-icon
      ></a>
      <a *ngIf="getsocialUrl('Other')" [href]="getsocialUrl('Other')"
        ><fa-icon [icon]="faGlobe"></fa-icon
      ></a>
    </div>
  </div>
</div>
