import { Component, HostListener, Input, OnInit } from '@angular/core';
import {
  faFacebook,
  faInstagram,
  faTwitter,
  faVimeo,
  faYoutube,
  faBehance,
  faLinkedinIn,
  faFlickr,
} from '@fortawesome/free-brands-svg-icons';
import {
  faEllipsisH,
  faGlobe,
  faShareAltSquare,
} from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'header-banner',
  templateUrl: './header-banner.component.html',
  styleUrls: ['./header-banner.component.scss'],
})
export class HeaderBannerComponent implements OnInit {
  isMobile;
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    // event.target.innerWidth;
    const w = event.target.innerWidth;
    this.isMobile = this.getIsMobile(w);
  }
  @Input() data;
  bgUrl =
    'https://vetovation.com/wp-content/uploads/2015/12/banner-gradient.jpg';
  usericon =
    'https://static.vecteezy.com/system/resources/previews/013/279/348/large_2x/emotional-violet-round-playful-lustful-emoji-face-on-a-background-of-abstract-purple-rays-illustration-free-vector.jpg';
  constructor() {}
  faShare = faShareAltSquare;
  faElipses = faEllipsisH;
  faInst = faInstagram;
  fafb = faFacebook;
  faTwit = faTwitter;
  faVim = faVimeo;
  faYoutube = faYoutube;
  faBehance = faBehance;
  faGlobe = faGlobe;
  faLinkedinIn = faLinkedinIn;
  faFlickr = faFlickr;

  ngOnInit(): void {}
  public getIsMobile(w): boolean {
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }
  getsocialUrl(type) {
    return this.data?.socialUrl?.find((a) => a.type == type)?.url;
  }
}
