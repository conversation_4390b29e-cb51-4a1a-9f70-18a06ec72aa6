.banner_Container {
  height: 26vw;
  width: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  padding: 3vw var(--page-default-margin);

  display: flex;
  flex-direction: column;

  .top_section {
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 2vw;

    fa-icon {
      color: white;
      font-size: 2vw;
    }
  }

  .mid_section {
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 1.8vw;

    .title_section {
      .leaderName {
        font-size: 2.36vw;
        // font-weight: 600;
        margin-bottom: 0.8vw;
        color: white;
        text-shadow: 0.0694vw 0.0694vw 0.1388vw black;
      }

      .username_badge {
        padding: 0.4vw 0.9vw 0.4vw 0.8vw;
        height: 3.5vw;
        width: max-content;
        background-color: white;
        border-radius: 2vw;
        display: flex;
        justify-content: start;
        align-items: center;
        gap: 0.5vw;

        .badge {
          height: 2.5vw;
          width: 2.5vw;
          // margin-left: 0.3vw;
          border-radius: 50%;
          align-items: center;
          display: flex;
          justify-content: center;
          overflow: hidden;
          background-color: black;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .username {
          // font-weight: 600;
          color: var(--quaternary-font-color);
        }
      }
    }
  }
}

.hero {
  width: 100%;
  height: 100vh;
  background: #f1f9ff;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

h1 {
  font-size: 40px;
  margin-bottom: 100px;
  margin-top: -100px;
}
.social-icons {
  position: absolute;
  bottom: -2vw;
  left: 4vw;
}
.social-links {
  display: flex;
  gap: 0.5vw;
}

.social-links a {
  width: 4vw;
  height: 4vw;
  /* text-align: center; */
  /* text-decoration: none; */
  color: var(--quaternary-font-color);
  box-shadow: 0 0 20px 10px rgb(0 0 0 / 5%);
  /* margin: 0 2vw; */
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  transition: transform 0.5s;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border: 0.06944vw solid var(--timeline-color);
}

.social-links a fa-icon {
  font-size: 2vw;
  /* line-height: 80px; */
  position: relative;
  z-index: 10;
  transition: color 0.5s;
}

// .social-links a::after {
//   content: "";
//   width: 100%;
//   height: 100%;
//   top: -90px;
//   left: 0;
//   background: #000;
//   background: linear-gradient(-45deg, #ed1c94, #ffec17);
//   position: absolute;
//   transition: 0.5s;
// }

.social-links a:hover::after {
  top: 0;
}

.social-links a:hover fa-icon {
  //color: #fff;
}

.social-links a:hover {
  transform: translateY(-10px);
}
@media (max-width: 768px) {
  .banner_Container {
    height: 75vw;
    // width: 100%;
    // background-position: center;
    // background-repeat: no-repeat;
    // background-size: cover;
    // position: relative;
    // padding: 3vw var(--page-default-margin);

    display: flex;
    flex-direction: column;

    .top_section {
      //   display: flex;
      //   justify-content: end;
      //   align-items: center;
      //   gap: 2vw;

      fa-icon {
        // color: white;
        font-size: 4vw;
      }
    }

    .mid_section {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 1.8vw;
      flex-grow: 1;

      .title_section {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .leaderName {
          font-size: 5.8vw;
          // font-weight: 600;
          // margin-bottom: .8vw;
          // color: white;
          // text-shadow: 0.0694vw 0.0694vw 0.1388vw black;
        }

        .username_badge {
          // padding: 0.4vw 0.9vw 0.4vw 0.8vw;
          padding: 1vw 1.6vw 1vw 1.28vw;

          // height: 3.5vw;
          height: unset;
          width: max-content;
          background-color: white;
          border-radius: 4vw;
          display: flex;
          justify-content: start;
          align-items: center;
          gap: 0.5vw;
          transform: scale(0.8);
          .badge {
            height: 5vw;
            width: 5vw;
            // margin-left: 0.3vw;
            border-radius: 50%;
            align-items: center;
            display: flex;
            justify-content: center;
            overflow: hidden;
            background-color: black;

            // img {
            //   width: 100%;
            //   height: 100%;
            // }
          }

          .username {
            // font-weight: 600;
            font-size: 2.25vw;
            // color: var(--quaternary-font-color);
          }
        }
      }
    }
  }
  .social-links a {
    width: 6vw;
    height: 6vw;
    text-align: center;
    /* text-decoration: none; */
    color: var(--quaternary-font-color);
    box-shadow: 0 0 20px 10px rgb(0 0 0 / 5%);
    /* margin: 0 2vw; */
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    transition: transform 0.5s;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    border: 0.1388vw solid var(--timeline-color);
  }

  .social-links a fa-icon {
    font-size: 3.5vw;
    /* line-height: 80px; */
    position: relative;
    z-index: 10;
    // transition: color 0.5s;
  }
}
