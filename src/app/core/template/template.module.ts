import { ItemNumberRowComponent } from './item-number-row/item-number-row.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderBannerComponent } from './header-banner/header-banner.component';
import { AvatarComponent } from './avatar/avatar.component';
import { DropdownButtonComponent } from './dropdown-button/dropdown-button.component';
import { ArtworkWizComponent } from './wizard/artwork-wiz/artwork-wiz.component';
import { RoundedDropdownComponent } from './rounded-dropdown/rounded-dropdown.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    HeaderBannerComponent,
    AvatarComponent,
    ItemNumberRowComponent,
    DropdownButtonComponent,
    ArtworkWizComponent,
    RoundedDropdownComponent,
  ],
  imports: [CommonModule, FontAwesomeModule, FormsModule, ReactiveFormsModule],
  exports: [
    HeaderBannerComponent,
    AvatarComponent,
    ItemNumberRowComponent,
    DropdownButtonComponent,
    ArtworkWizComponent,
    RoundedDropdownComponent,
  ],
})
export class TemplateModule {}
