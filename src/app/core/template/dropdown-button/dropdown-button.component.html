<div class="contain">
    <button class="button_drop" (focusout)="toggleDrop()" (click)="dropdownShow=!dropdownShow;rotate();">
       <div>
        <ng-content></ng-content>
       </div> <fa-icon [icon]="chevDow" [@rotatedState]='state'></fa-icon>
    </button>
    <div class="dropdown" *ngIf="dropdownShow" >
        <div *ngFor="let item of dropdown_items" class="dropItems" (click)="setValue(item.value)">{{item.name|titlecase}}</div>
    </div>
</div>