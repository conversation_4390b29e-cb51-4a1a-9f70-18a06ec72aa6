.button_drop {
  background-color: black;
  color: white;
  border: 0.06944vw solid white;
  padding: .5vw 1vw;
  // border-radius: 2vw;
  display: inline-flex;
  gap: .5vw;

  fa-icon {
    transition: all .2s linear;
  }
}

.dropdown {
  z-index: 10;
  position: absolute;
  top: 2.7vw;
  right: 0;
  border-radius: 1vw;
  width: 100%;
  max-height: 20vw;
  overflow-y: auto;
  display: flex;
  gap: 0.0002vw;
  flex-direction: column;
  background-color: var(--primary-background-color);
  box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);

  .dropItems{
width: 100%;
// height: 2vw;
// padding: .5;
cursor: pointer;
padding: 0.69vw 1.04vw;
  }
  .dropItems:hover {
    background-color: var(--timeline-color);
  }
}

.contain {
  position: relative;
}