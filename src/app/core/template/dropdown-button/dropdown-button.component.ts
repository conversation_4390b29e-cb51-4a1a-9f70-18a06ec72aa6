import { trigger, state, style, transition, animate } from '@angular/animations';
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { faChevronUp, faChevronDown } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'dropdown-button',
  templateUrl: './dropdown-button.component.html',
  styleUrls: ['./dropdown-button.component.scss'],
  animations: [
    trigger('rotatedState', [
      state('default', style({ transform: 'rotate(0deg)' })),
      state('rotated', style({ transform: 'rotate(-180deg)' })),
      transition('rotated => default', animate('200ms ease-out')),
      transition('default => rotated', animate('200ms ease-in'))
    ])
  ]
})
export class DropdownButtonComponent implements OnInit {
  @Input() dropdown_items: [object];
  @Output() selectedValue = new EventEmitter<any>();
  chevUp = faChevronUp;
  chevDow = faChevronDown;
  dropdownShow = false;
  state: string = 'default';
  rotate() {
    this.state = (this.state === 'default' ? 'rotated' : 'default');
  }

  constructor() { }

  ngOnInit(): void {
  }
  toggleDrop() {
    setTimeout(() => {
      this.dropdownShow = false;
      this.state = (this.state === 'default' ? 'rotated' : 'default');
    }, 500)
    // this.dropdownShow=!this.dropdownShow;

  }
  setValue(value) {
    this.selectedValue.emit(value);

  }
}
