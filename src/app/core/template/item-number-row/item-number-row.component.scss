.contain {
  width: 100%;
  // height: 6vw;
  display: inline-flex;
  justify-content: start;
  padding: 2vw;
  border-radius: 1vw;
  font-size: 1.666vw;
  //font-weight: 600;

  .item {
    flex-grow: 1;
    width: 11.5vw;
    flex-basis: 11.5vw;
    display: flex;
    justify-content: center;
    align-items: center;

    .align {
      display: flex;
      flex-direction: column;
      gap: 1vw;
      p {
        margin-bottom: 0;
      }
    }
  }

  .item:first-child {
    justify-content: start;
  }
  .item:last-child {
    justify-content: end;
  }
}

@media (max-width: 768px) {
  .contain {
    width: 100%;
    // height: 6vw;
    display: inline-flex;
    justify-content: start;
    padding: 4vw;
    border-radius: 2vw;
    // font-size: 1.666vw;
    font-size: 3.5vw;
    //font-weight: 600;

    .item {
      flex-grow: 1;
      // width: 11.5vw;
      width: 27.5vw;
      flex-basis: 27.5vw;
      display: flex;
      justify-content: center;
      align-items: center;

      .align {
        display: flex;
        flex-direction: column;
        gap: 2vw;
        p {
          margin-bottom: 0;
        }
      }
    }

    .item:first-child {
      justify-content: start;
    }
    .item:last-child {
      justify-content: end;
    }
  }
}
