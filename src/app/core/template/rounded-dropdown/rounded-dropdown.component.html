<div class="contain" #dropDown>
  <button class="button_drop" (click)="rotate()">
    <div class="wrapper">
      <ng-content #image></ng-content>
      <ng-content> </ng-content>
    </div>
    <fa-icon [icon]="chevDow" [@rotatedState]="state"></fa-icon>
  </button>
  <div class="dropdown" *ngIf="dropdownShow">
    <div *ngIf="withSearch" class="filterInput">
      <input
        [(ngModel)]="searchValue"
        (keyup)="searchResult()"
        [placeholder]="searchPlaceHolder"
        type="text"
      />
    </div>
    <ng-content select="[checkBox]"> </ng-content>
    <div
      *ngFor="let item of dropdown_items"
      class="dropItems"
      (click)="setValue(item)"
      [ngClass]="{ selected: selectedItems.includes(item.value) }"
    >
      {{ item.name | titlecase }}
    </div>
  </div>
</div>
