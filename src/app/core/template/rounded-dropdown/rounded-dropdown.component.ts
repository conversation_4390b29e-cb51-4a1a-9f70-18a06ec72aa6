import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import {
  Component,
  Input,
  OnInit,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { faChevronUp, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { Subject } from 'rxjs';
@Component({
  selector: 'rounded-dropdown',
  templateUrl: './rounded-dropdown.component.html',
  styleUrls: ['./rounded-dropdown.component.scss'],
  animations: [
    trigger('rotatedState', [
      state('default', style({ transform: 'rotate(0deg)' })),
      state('rotated', style({ transform: 'rotate(-180deg)' })),
      transition('rotated => default', animate('200ms ease-out')),
      transition('default => rotated', animate('200ms ease-in')),
    ]),
  ],
  host: {
    '(document:click)': 'onClick($event)',
  },
})
export class RoundedDropdownComponent implements OnInit {
  @Input() dropdown_items: [object];
  @Output() selectedValue = new EventEmitter<any>();
  @Input() withSearch = false;
  @Input() searchPlaceHolder = false;
  @ViewChild('dropDown') dropDown: ElementRef;
  @Output() onSearch = new EventEmitter<any>();

  @Input() changeSelected: Subject<string>;

  chevUp = faChevronUp;
  chevDow = faChevronDown;
  selectedItems = [];
  dropdownShow = false;
  state: string = 'default';
  isFirstClick = false;
  searchValue;
  rotate() {
    this.dropdownShow = !this.dropdownShow;
    this.state = this.state === 'default' ? 'rotated' : 'default';
  }

  constructor() {}

  ngOnInit(): void {
    this.changeSelected?.subscribe((v) => {
      console.log(v);

      let searchIndex = this.selectedItems.indexOf(v);
      if (searchIndex >= 0) {
        this.selectedItems.splice(searchIndex, 1);
      } else {
        this.selectedItems.push(v);
      }
    });
  }
  toggleDrop() {
    setTimeout(() => {
      this.dropdownShow = false;
      this.state = this.state === 'default' ? 'rotated' : 'default';
    }, 500);
    // this.dropdownShow=!this.dropdownShow;
  }
  setValue(value) {
    console.log(this.selectedItems);
    let searchIndex = this.selectedItems.indexOf(value.value);
    if (searchIndex >= 0) {
      this.selectedItems.splice(searchIndex, 1);
    } else {
      this.selectedItems.push(value.value);
    }

    this.selectedValue.emit(value);
  }
  searchResult() {
    console.log(this.searchValue);

    this.onSearch.emit(this.searchValue);
  }

  onClick(event) {
    if (
      this.dropdownShow &&
      !this.dropDown.nativeElement.contains(event.target)
    ) {
      this.dropdownShow = false;
      this.state = this.state === 'default' ? 'rotated' : 'default';
    }
  }
}
