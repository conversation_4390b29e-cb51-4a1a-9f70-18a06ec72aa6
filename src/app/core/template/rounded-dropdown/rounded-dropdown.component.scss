.button_drop {
  background-color: white;
  color: black;
  border: 0.06944vw solid var(--timeline-color);
  padding: 0.7vw 1.5vw;
  border-radius: 2vw;
  display: inline-flex;
  //font-weight: 600;
  width: max-content;
  gap: 0.5vw;
  justify-content: center;
  align-items: center;
  fa-icon {
    transition: all 0.2s linear;
    font-size: 1vw;
    color: var(--timeline-color);
  }
}

.dropdown {
  z-index: 10;
  position: absolute;
  // top: 2.7vw;
  top: 3.7vw;
  // right: -2vw;
  border-radius: 0.6vw;
  width: 15vw;
  // left: 50%;
  // bottom: 20px;
  //transform: translate(-50%, 0%);
  // right: 0;
  // border-radius: 1vw;
  // width: 100%;
  max-height: 20vw;
  overflow-y: auto;
  display: flex;
  gap: 0.0002vw;
  flex-direction: column;
  background-color: var(--primary-background-color);
  box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);

  .dropItems {
    width: 100%;
    // height: 2vw;
    // padding: .5;
    cursor: pointer;
    padding: 0.69vw 1.04vw;
    &.selected {
      background-color: black;
      color: white;
    }
  }
  .dropItems:hover {
    // background-color: var(--timeline-color);
    background-color: black;
    color: white;
  }
}

.contain {
  position: relative;
}
.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5vw;
}
.filterInput {
  display: flex;
  gap: 0.5vw;
  width: 100%;
  justify-content: center;
  padding: 0.5vw;

  input[type="text"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.5vw;
    padding: 1.04vw 1.11vw;
    width: 90%;
    height: 2.47vw;

    @media (max-width: 768px) {
      height: 6vw;
      font-size: 3vw;
      padding: 1.04vw 2.11vw;
    }
  }
}
@media (max-width: 768px) {
  .button_drop {
    background-color: black;
    color: white;
    border: 0.06944vw solid var(--timeline-color);
    padding: 1.2vw 3vw;
    border-radius: 4vw;
    font-size: 3vw;
    display: inline-flex;
    width: 100%;
    gap: 0.5vw;
    justify-content: space-between;
    align-items: center;
    fa-icon {
      transition: all 0.2s linear;
      font-size: 1vw;
      color: var(--timeline-color);
    }
  }
  .wrapper {
    display: flex;
    justify-items: center;
    width: 100%;
  }

  fa-icon {
    font-size: 3vw !important;
  }
  .dropdown {
    z-index: 10;
    position: absolute;
    top: 6.2vw;
    border-radius: 0.6vw;
    width: 100%;
    max-height: 40vw;
    overflow-y: auto;
    display: flex;
    gap: 0.0002vw;
    flex-direction: column;
    background-color: var(--primary-background-color);
    box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);

    .dropItems {
      width: 100%;
      cursor: pointer;
      padding: 1.3vw 2vw;
      font-size: 3vw;
      &.selected {
        background-color: black;
        color: white;
      }
    }
    .dropItems:hover {
      // background-color: var(--timeline-color);
      background-color: black;
      color: white;
    }
  }
}
