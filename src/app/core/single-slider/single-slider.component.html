<div class="slider-wrapper" [ngClass]="{ playing: isPlaying }">
  <div class="slider-contents" #sliderContent>
    <div *ngFor="let item of sliderData; let i = index" class="content-wrapper">
      <ng-container
        *ngIf="item.url"
        [ngTemplateOutlet]="
          item.mediaType === 'VIDEO'
            ? videoContent
            : item.mediaType === 'VIDEO1'
            ? videoContent1
            : imageContent
        "
        [ngTemplateOutletContext]="{ url: item.url, title: title, id: i }"
      >
      </ng-container>
    </div>
  </div>
  <div class="edit-te">
    <button
      (click)="editEvent.emit()"
      *ngIf="editable"
      class="edit-btn-stl"
      style="margin-left: 100%"
    >
      <fa-icon [icon]="fapencil"></fa-icon>Edit
    </button>
  </div>
  <div
    *ngIf="showNavControls && sliderData.length > 1"
    class="control left-control image-icon"
    [ngClass]="{ disabled: activeItem === 0 }"
    (click)="sliderLeft()"
  >
    <img src="assets/images/left-chevron.png" />
  </div>
  <div
    *ngIf="sliderData[activeItem]?.mediaType === 'VIDEO' && !hidePlayButton"
    class="control center-control image-icon bg-icon"
    (click)="isPlaying ? pauseVideo() : playVideo()"
  >
    <img
      *ngIf="iconOption !== 1"
      [src]="
        isPlaying
          ? 'assets/images/pause.png'
          : 'assets/images/video-play-cta.png'
      "
    />
    <img
      *ngIf="iconOption === 1"
      [src]="isPlaying ? '' : 'assets/icons/video-play/<EMAIL>'"
    />
  </div>
  <div
    *ngIf="showNavControls && sliderData.length > 1"
    class="control right-control rounded-icon"
    [ngClass]="{ disabled: activeItem === sliderData.length - 1 }"
    (click)="sliderRight()"
  >
    <img src="assets/images/right-chevron.png" />
  </div>
</div>
<ng-template #videoContent let-url="url" let-id="id">
  <div
    *ngIf="url"
    [ngStyle]="{
      width: mobilePop ? '100vw' : '100%',
      height: mobilePop ? '100vh' : '100%',
      position: mobilePop ? 'fixed' : 'static',
      top: mobilePop ? '0' : 'auto',
      left: mobilePop ? '0' : 'auto',
      'z-index': mobilePop ? '100' : 'auto',
      'background-color': mobilePop ? 'rgb(0, 0, 0)' : 'rgba(0, 0, 0,0)'
    }"
  >
    <video
      *ngIf="url && externalUrl"
      autoplay
      loop
      [muted]="true"
      defaultMuted
      playsinline
      preload="auto"
      [ngStyle]="{
        transform:
          isPlaying && isMobile
            ? 'scale(1, 1)'
            : isMobile
            ? 'scale(3.5, 3.5)'
            : transform
      }"
      style="width: 100%; height: 100%"
      #autoPlayVideo
    >
      <source type="video/mp4" [src]="url" />
    </video>
    <vimeo-player
      *ngIf="url && !externalUrl"
      (duration)="durationEvent($event)"
      [transform]="
        isPlaying && isMobile
          ? 'scale(1, 1)'
          : isMobile
          ? 'scale(3.5, 3.5)'
          : transform
      "
      [background]="isMobile ? false : true"
      [autoplay]="autoPlay"
      [sliderIndex]="id"
      [controls]="true"
      [vimeoUrl]="url"
      #vimeoPlayer
      (pauseOut)="pauseOutFun()"
    >
    </vimeo-player>
  </div>
</ng-template>
<ng-template #imageContent let-url="url"><img [src]="url" /></ng-template>

<ng-template #videoContent1 let-url="url" let-title="title" let-id="id">
  <div
    *ngIf="url"
    (click)="onClick(url)"
    [ngStyle]="{
      width: mobilePop ? '100vw' : '100%',
      height: mobilePop ? '100vh' : '100%',
      position: mobilePop ? 'fixed' : 'static',
      top: mobilePop ? '0' : 'auto',
      left: mobilePop ? '0' : 'auto',
      'z-index': mobilePop ? '100' : 'auto',
      'background-color': mobilePop ? 'rgb(0, 0, 0)' : 'rgba(0, 0, 0,0)'
    }"
  >
    <vimeo-player
      *ngIf="url"
      (click)="onClick(url)"
      (duration)="durationEvent($event)"
      [transform]="
        isPlaying && isMobile
          ? 'scale(1, 1)'
          : isMobile
          ? 'scale(3.5, 3.5)'
          : transform
      "
      [background]="isMobile ? false : true"
      [autoplay]="autoPlay"
      [sliderIndex]="id"
      [vimeoUrl]="url"
      [controls]="true"
      #vimeoPlayer
      (pauseOut)="pauseOutFun()"
    >
    </vimeo-player>
    <div class="content">
      <h1>{{ title }}</h1>
    </div>
  </div>
</ng-template>
