.slider-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  .slider-contents {
    overflow: hidden;
    scroll-behavior: smooth;
    width: 100%;
    height: 100%;
    .content-wrapper {
      display: block;
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        -o-object-fit: cover;
        object-fit: cover;
      }
    }
  }
  .control {
    position: absolute;
    top: calc((100% - 2.5vw) / 2);
    color: var(--tertiary-font-color);
    cursor: pointer;

    &.left-control {
      left: 2.1vw;
    }
    &.center-control {
      top: calc((100% - 5.7vw) / 2);
      left: calc((100% - 5.7vw) / 2);
    }

    &.right-control {
      right: 2.1vw;
    }

    &.disabled {
      cursor: default;
      color: var(--secondary-font-color);
    }
  }
  &.playing {
    .control {
      opacity: 0;
    }
    &:hover .control {
      opacity: 1;
    }
  }
  .content {
    position: absolute;
    bottom: 3.5vw;
    width: 100%;
    padding-right: 3.47vw;
    padding-left: 3.47vw;
    color: var(--secondary-font-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;
    h1 {
      font-size: 1.95vw;
      line-height: 2.1vw;
      margin-bottom: 1.05vw;
      font-weight: 500;
    }
  }

  @media (max-width: 768px) {
    .image-icon {
      width: 9.66vw;
      height: 9.66vw;
    }
    .content {
      bottom: 9.66vw;
      padding-right: 7.24vw;
      padding-left: 7.24vw;
      h1 {
        font-size: 5.79vw;
        margin-bottom: 4.83vw;
      }
    }
    .control {
      &.center-control {
        top: calc((100% - 8.7vw) / 2);
        left: calc((100% - 8.7vw) / 2);
      }
    }
  }
}
.edit-te {
  position: absolute;
  bottom: 1vw;
  right: 5vw;
  .edit-btn-stl {
    font-size: 1.111vw;
    margin-left: 2vw;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.2vw;
    border: 0.06vw solid var(--tertiary-font-color);
    padding: 0.2vw 0.5vw;
    border-radius: 0.3vw;
    background-color: var(--tertiary-font-color);
    color: white;
    fa-icon {
      font-size: 1vw;
    }
  }
  .edit-btn-stl:hover {
    color: inherit;
    background-color: transparent;
  }
}
