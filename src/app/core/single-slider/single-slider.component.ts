import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { MediaForSlider, SlideMediaType } from './single-slider.model';
import {
  faChevronRight,
  faChevronLeft,
  faPlay,
  faPause,
  faPencilAlt,
} from '@fortawesome/free-solid-svg-icons';
import { VimeoPlayerComponent } from '../vimeo-player/vimeo-player.component';

@Component({
  selector: 'single-slider',
  templateUrl: './single-slider.component.html',
  styleUrls: ['./single-slider.component.scss'],
})
export class SingleSliderComponent implements OnInit, AfterViewInit {
  @ViewChild('autoPlayVideo', { static: true }) autoPlayVideo: ElementRef;

  @Input() sliderData: MediaForSlider[];

  @Input() transform = 'scale(1.4, 1.4)';

  @Input() title: string = '';
  @Input() iconOption = 0;
  @Input() editable = false;

  @Input() showNavControls = true;

  @Input() enablePlay = true;

  @Input() hidePlayButton = false;

  @Input() autoPlay = false;
  @Input() isMobile = false;

  @Input() externalUrl = false;

  @Output() duration = new EventEmitter<number>();
  @Output() clickEvent = new EventEmitter<any>();

  @Output() editEvent = new EventEmitter<any>();

  @ViewChild('sliderContent') sliderContent: ElementRef;
  @ViewChildren('vimeoPlayer') vimeoPlayers: QueryList<VimeoPlayerComponent>;

  faChevronRight = faChevronRight;
  faChevronLeft = faChevronLeft;
  faPlay = faPlay;
  faPause = faPause;
  fapencil = faPencilAlt;

  videoSlides: MediaForSlider[];
  isPlaying = false;
  currentPlayingEle: VimeoPlayerComponent;

  activeItem = 0;
  mobilePop = false;

  constructor() {}

  ngAfterViewInit(): void {}

  ngOnInit(): void {
    this.videoSlides = this.sliderData.filter(
      (a) => a?.mediaType === SlideMediaType.VIDEO
    );
    this.videoOnLoad();
  }

  sliderLeft(): void {
    if (this.activeItem === 0) {
      return;
    }
    this.activeItem--;
    this.scrollSlider();
  }
  sliderRight(): void {
    if (this.activeItem === this.sliderData.length - 1) {
      return;
    }
    this.activeItem++;
    this.scrollSlider();
  }
  async playVideo(): Promise<void> {
    console.log('play');
    if (this.isMobile) {
      this.mobilePop = true;
      try {
        await document.body.requestFullscreen();
        await screen.orientation.lock('landscape');
      } catch {}
    }

    if (this.enablePlay || this.isMobile) {
      this.currentPlayingEle =
        this.vimeoPlayers.toArray()[
          this.videoSlides.indexOf(this.sliderData[this.activeItem])
        ];
      this.currentPlayingEle.playVideo();
      this.isPlaying = true;
    } else {
      this.onClick('clicked');
    }
  }
  async pauseVideo(): Promise<void> {
    console.log('pause');
    if (this.mobilePop && this.isMobile) {
      try {
        screen.orientation.unlock();
        await document.exitFullscreen();
      } catch {}
    }
    if (this.enablePlay || this.isMobile) {
      this.currentPlayingEle.pauseVideo();

      this.mobilePop = false;
      this.isPlaying = false;
    }
  }
  private scrollSlider(): void {
    if (this.isPlaying) {
      this.pauseVideo();
    }
    this.sliderContent.nativeElement.scrollTop =
      this.activeItem * this.slidingAMount;
  }

  private get slidingAMount(): number {
    return Math.round(
      this.sliderContent.nativeElement.getBoundingClientRect().height
    );
  }
  durationEvent(event): void {
    this.duration.emit(event);
  }

  async pauseOutFun() {
    if (this.mobilePop) {
      try {
        screen.orientation.unlock();
        await document.exitFullscreen();
      } catch {}
    }
    this.mobilePop = false;
    this.isPlaying = false;
  }
  videoOnLoad() {
    (this.autoPlayVideo.nativeElement as HTMLVideoElement).onplaying = () => {
      // this.isLoaded = true;
    };
  }
  onClick(url) {
    this.clickEvent.emit({ url });
  }
  test() {
    console.log('hey');
  }
}
