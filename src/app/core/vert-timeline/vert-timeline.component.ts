import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { faLink } from '@fortawesome/free-solid-svg-icons';
import { faFile } from '@fortawesome/free-regular-svg-icons';
import { SliderTypes } from './vert-timeline.model';

@Component({
  selector: 'vert-timeline',
  templateUrl: './vert-timeline.component.html',
  styleUrls: ['./vert-timeline.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class VertTimelineComponent implements OnInit {
  @Input() SliderType: SliderTypes = SliderTypes.Practice;
  @Input() isMobile: boolean = false;

  faLink = faLink;
  faFile = faFile;
  @Input() data: any;

  constructor() { }

  ngOnInit(): void {
  }

}
