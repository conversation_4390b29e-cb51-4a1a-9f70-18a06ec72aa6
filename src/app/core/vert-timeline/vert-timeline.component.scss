ol {
  margin: 0;
  padding-left: 0;
  width: 100%;
  position: relative;
  .entry {
    clear: both;
    text-align: left;
    position: relative;
    .title {
      float: left;
      font-size: 1.7vw;
      padding-right: 2.2vw;
      position: relative;
      width: 7%;
      &:before {
        content: "";
        position: absolute;
        width: 0.76vw;
        height: 0.76vw;
        top: 0.5vw;
        background-color: var(--tertiary-font-color);
        border-radius: 50%;
        left: 5.7vw;
        z-index: 1;
      }
    }
    .title1 {
      float: left;
      font-size: 1.7vw;
      position: relative;
      &:before {
        content: "";
        position: absolute;
        width: 0.76vw;
        height: 0.76vw;
        top: 0.5vw;
        background-color: var(--tertiary-font-color);
        border-radius: 50%;
        left: 0;
        z-index: 1;
        @media (max-width: 768px) {
          width: 2.42vw;
          height: 2.42vw;
          top: 2vw;
        }
      }
    }
    .title-body {
      float: right;
      padding-left: 2.2vw;
      width: 100%;
      .title-main {
        font-size: 1.7vw;
        margin-bottom: 1.04vw;
      }
      .heading {
        font-size: 1.4vw;
        margin-bottom: 1.04vw;
      }
      .subtitle {
        font-size: 1.25vw;
        margin-bottom: 0.3vw;
        font-family: var(--secondary-font);
      }
      .subtitle1 {
        font-size: 1.25vw;
        // margin-bottom: 2.1vw;
        margin-top: 0.23vw;
        font-family: var(--secondary-font);
        a {
          color:  var(--quaternary-font-color) !important;
        }
      }
      .description {
        font-size: 1.11vw;
        margin-bottom: 2.5vw;
      }
      .linkText {
        font-size: 1.11vw;
        color:var(--quaternary-font-color);
        margin-bottom: 2.5vw;
        display: inline-block;
        // margin-right: 2.8vw;

        a {
          color: var(--tertiary-font-color);
        }
        a:hover{
          text-decoration: underline;
        }
      }
      @media (max-width: 768px) {
        padding-left: 4.62vw;
        .title-main {
          font-size: 5.31vw;
          margin-bottom: 8.21vw;
        }
        .heading {
          font-size: 5.31vw;
          margin-bottom: 3.62vw;
        }
        .subtitle {
          font-size: 4.35vw;
          margin-bottom: 1.21vw;
        }
        .subtitle1 {
          font-size: 4.35vw;
          // margin-bottom: 2.1vw;
          margin-top: 0.23vw;
          font-family: var(--secondary-font);
          a {
            color:  var(--quaternary-font-color) !important;
          }
        }
        .description {
          font-size: 3.87vw;
          margin-bottom: 7.25vw;
        }
        .linkText {
          font-size: 3.87vw;
          margin-bottom: 5.31vw;
          &:last-child {
            margin-bottom: 12.08vw;
          }
        }
        hr {
          margin-top: 1.93vw;
          margin-left: -3.62vw;
        }
      }
    }
  }
}
ol .tab li {
  &:before {
    content: "";
    position: absolute;
    top: 0px;
    left: 5.95vw;
    bottom: 0px;
    width: 0.07vw;
    background: var(--timeline-color);
  }
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}
ol .tab:first-child li {
  &:before {
    top: 0.5vw;
  }
}
ol .tab:last-child li {
  &:before {
    height: 1vw;
  }
}

ol .tab1 li {
  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0.3vw;
    bottom: 0;
    width: 0.07vw;
    background: var(--timeline-color);
    @media (max-width: 768px) {
      left: 1vw;
      width: 0.242vw;
    }
  }
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}
ol .tab1:first-child li {
  &:before {
    top: 0.5vw;
    @media (max-width: 768px) {
      top: 2vw;
    }
  }
}
ol .tab1:last-child li {
  &:before {
    height: 1vw;
    @media (max-width: 768px) {
      height: 3.5vw;
    }
  }
}
hr {
  margin: 0;
  margin-bottom: 2.1vw;
  border: 0;
  border-top: 0.069vw solid var(--hr-secondary-color);
  @media (max-width: 768px) {
    margin-bottom: 7.25vw;
    border-top: 0.242vw solid var(--hr-secondary-color);
  }
}

.image-wrapper-2 {
  outline: none;
  border: none;
  background: none;
  width: 2.29vw;
  height: 2.29vw;
  padding: 0vw;
  margin-right: 0.625vw;
  border-radius: 50%;
  img {
    border-radius: 50%;
    width: 2.29vw;
    height: 2.29vw;
  }
  @media (max-width: 768px) {
    width: 7.97vw;
    height: 7.97vw;
    margin-right: 3.62vw;
    img {
      width: 7.97vw;
      height: 7.97vw;
    }
  }
}
.image-wrapper-2:hover {
  background: var(--primary-border-color);
}
