<div class="section">
  <div class="section-inner">
    <ng-container
      [ngTemplateOutlet]="
        {
          Practice: timeline1,
          Artist: timeline2
        }[SliderType]
      "
    >
    </ng-container>
  </div>
</div>
<ng-template #timeline1>
  <ol class="timeline">
    <div
      *ngFor="let exhib of data; let i = index"
      [ngClass]="isMobile ? 'tab1' : 'tab'"
    >
      <li class="d-flex entry">
        <div
          class="title"
          *ngIf="!isMobile && (exhib.title != null || exhib.title != undefined)"
        >
          {{ exhib.title }}
        </div>
        <div class="title1" *ngIf="isMobile"></div>
        <div class="title-body">
          <div
            class="title-main"
            *ngIf="
              isMobile && (exhib.title != null || exhib.title != undefined)
            "
          >
            {{ exhib.title }}
          </div>
          <div
            class="heading"
            *ngIf="exhib.date != null || exhib.date != undefined"
          >
            {{ exhib.date }}
          </div>
          <div
            class="subtitle"
            *ngIf="exhib.subtitle != null || exhib.subtitle != undefined"
          >
            {{ exhib.subtitle }}
          </div>
          <div
            class="description"
            *ngIf="exhib.description != null || exhib.description != undefined"
          >
            {{ exhib.description }}
          </div>
          <div
            class="linkText"
            *ngIf="exhib.link != null || exhib.link != undefined"
          >
            <button class="image-wrapper-2">
              <img src="assets/icons/link_2020-11-12/<EMAIL>" alt="" />
            </button>
            visit
            <a href="{{ exhib.link }}" target="_blank">{{
              exhib.link.length > (isMobile ? 25 : 50)
                ? (exhib.link | slice: 0:(isMobile ? 25 : 50)) + "..."
                : exhib.link
            }}</a>
          </div>
          <div *ngIf="exhib?.pdf_link" class="linkText" target="_blank">
            <button class="image-wrapper-2">
              <img src="assets/icons/pdf_2020-11-12/<EMAIL>" alt="" />
            </button>
            view <a [href]="exhib.pdf_link" target="_blank">PDF</a>
          </div>
          <hr *ngIf="data.length - 1 != i" />
        </div>
      </li>
    </div>
  </ol>
</ng-template>
<ng-template #timeline2>
  <ol class="timeline">
    <div class="tab1" *ngFor="let exhib of data; let i = index">
      <li class="d-flex entry">
        <div class="title1"></div>
        <div class="title-body">
          <div
            class="subtitle1"
            *ngIf="exhib.subtitle != null || exhib.subtitle != undefined"
          >
            {{ exhib.subtitle }}
          </div>
          <div
            class="subtitle1"
            *ngIf="exhib.story != null || exhib.story != undefined"
            [innerHTML]="exhib.story"
          ></div>
          <div
            class="linkText"
            *ngIf="exhib.info_source != null || exhib.info_source != undefined"
          >
            <button class="image-wrapper-2">
              <img src="assets/icons/link_2020-11-12/<EMAIL>" alt="" />
            </button>
            source
            <a href="{{ exhib.info_source }}" target="_blank">{{
              exhib.info_source.length > (isMobile ? 25 : 50)
                ? (exhib.info_source | slice: 0:(isMobile ? 25 : 50)) + "..."
                : exhib.info_source
            }}</a>
          </div>
          <div
            class="linkText"
            *ngIf="
              exhib.info_source === null || exhib.info_source === undefined
            "
          ></div>
          <hr *ngIf="data.length - 1 != i" />
        </div>
      </li>
    </div>
  </ol>
</ng-template>
