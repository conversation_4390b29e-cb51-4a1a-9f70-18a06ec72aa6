input[type="text"][disabled] {
  color: var(--quaternary-font-color) !important;
}
.Disabled-Color {
  color: var(--quaternary-font-color) !important;
}
.Disable-border {
  border: solid 0.069vw var(--quaternary-font-color) !important ;
  color: var(--quaternary-font-color) !important ;
}
.Lock-position {
  position: absolute;
  top: 1vw;
  right: 1vw;
  // z-index: 1;
  color: var(--quaternary-font-color);
}
.text-content {
  text-align: center;
  margin-top: 1.38vw;
  width: 20.83vw;
  position: relative;
  padding-right: 1vw;
  padding-left: 1vw;
  @media (max-width: 768px) {
    width: 100%;
    margin-top: 4.83vw;
  }
  .title {
    font-size: 1.11vw;
    @media (max-width: 768px) {
      font-size: 3.86vw;
    }
  }
  .sub-title {
    color: #808080;
    margin-top: 0.69vw;
    font-size: 0.97vw;
    @media (max-width: 768px) {
      margin-top: 2.415vw;
      font-size: 3.38vw;
    }
  }
  .close {
    position: absolute;
    right: 0;
    top: 0;
    padding: 0.5vw;
    img {
      cursor: pointer;
      width: 0.69vw;
      height: 0.69vw;
    }
  }
}
.file-upload {
  height: 20vw;
  border-radius: 0.07vw;
  border: solid 0.07vw #ced4db;
  display: flex;
  justify-content: start;
  flex-direction: column;
  .icon {
    margin-top: 2.08vw;
    @media (max-width: 768px) {
      margin-top: 7.24vw;
    }
    img {
      height: 1.38vw;
      width: 1.38vw;
      @media (max-width: 768px) {
        height: 4.83vw;
        width: 4.83vw;
      }
    }
  }
  .text-content {
    text-align: center;
    margin-top: 1.38vw;
    width: 20.83vw;
    position: relative;
    padding-right: 1vw;
    padding-left: 1vw;
    @media (max-width: 768px) {
      width: 100%;
      margin-top: 4.83vw;
    }
    .title {
      font-size: 1.11vw;
      @media (max-width: 768px) {
        font-size: 3.86vw;
      }
    }
    .sub-title {
      color: #808080;
      margin-top: 0.69vw;
      font-size: 0.97vw;
      @media (max-width: 768px) {
        margin-top: 2.415vw;
        font-size: 3.38vw;
      }
    }
    .close {
      position: absolute;
      right: 0;
      top: 0;
      padding: 0.5vw;
      img {
        cursor: pointer;
        width: 0.69vw;
        height: 0.69vw;
      }
    }
  }
}
.upload-input {
  position: absolute;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  outline: none;
  opacity: 0;
  cursor: pointer;
}
.button-container {
  margin-top: 2.08vw;
  margin-bottom: 2.08vw;
  @media (max-width: 768px) {
    margin-top: 12.07vw;
    margin-bottom: 7.24vw;
  }
  .button {
    // width: 13.33vw;
    // height: 3.05vw;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    padding: 0.89vw 4.33vw;
    border-radius: 1.49vw;
    border: solid 0.069vw #004ddd;
    color: #004ddd;
    @media (max-width: 768px) {
      font-size: 3.86vw;
      padding: 2.89vw 22.33vw;
      border-radius: 7.49vw;
    }
  }
}
.input-container {
  width: 100%;
  position: relative;
  display: inline-flex;
  justify-content: start;
  align-items: center;
  flex-wrap: wrap;
  .text-before {
    @media (max-width: 768px) {
      font-size: 4.34vw;
    }
  }
  .flag-icon {
    position: absolute;
    left: 1.04vw;
  }
  button {
    outline: none;
    background: none;
    border: none;
  }
  .flag-arrow {
    position: absolute;
    max-width: 0.69vw;
    height: auto;
    right: 2.08vw;
    top: 0;
    bottom: 0;
    margin: auto 0;
    @media (max-width: 768px) {
      max-width: 1.95vw;
    }
  }
  .division {
    position: absolute;
    width: 0.069vw;
    height: 1.04vw;
    background-color: var(--timeline-color);
    left: 3.33vw;
  }
  input[type="text"] {
    // background: transparent;
    font-family: var(--secondary-font);
    border: 0.069vw solid var(--timeline-color);
    padding: 1.04vw 1.11vw 1.04vw 4.2vw;
    border-radius: 0.14vw;
    height: 3.47vw;
    width: 100%;
    z-index: 0;
    @media (max-width: 768px) {
      height: 11.35vw;
    }
    &.selection {
      padding: 1.04vw 1.11vw 1.04vw 1.04vw;
    }
  }
  .dropdown-visible {
    background-color: var(--primary-background-color);
    visibility: visible;
    position: absolute;
    top: 3.47vw;
    z-index: 1;
    box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
    width: 100%;
    @media (max-width: 768px) {
      top: 10.47vw;
    }
    ul {
      list-style: none;
      padding: 0.69vw 0;
      max-height: 27.97vw;
      margin: 0;
      overflow: hidden;
      overflow-y: scroll;
      @media (max-width: 768px) {
        padding: 1.69vw 0;
      }
      li {
        padding: 0.69vw 1.04vw;
        width: 34.9vw;
        display: flex;
        @media (max-width: 768px) {
          padding: 1.69vw 1.04vw;
        }
        .country-name {
          margin-left: 0.69vw;
          @media (max-width: 768px) {
            font-size: 3.38vw;
          }
        }
      }
      li:hover {
        background-color: var(--timeline-color);
      }
    }
    ul::-webkit-scrollbar {
      display: none;
    }
  }
  .dropdown-hidden {
    display: none;
  }
}
.file-item {
  //display: flex;
  margin-top: 1.3vw;
  .thumb {
    width: 5vw !important;
    height: 5vw !important;
    object-fit: cover !important;
  }
}

.cross-icon {
  width: 10px;
  height: 10px;
  margin-left: 10px;
  margin-top: 25px;
}

.thumb-folder {
  width: 48px !important;
  height: 49px;
  object-fit: cover !important;
}
.splitter {
  display: flex;
  // justify-content: space-between;
  margin-bottom: 1.5vw;
  flex-wrap: wrap;
  .field-value {
    width: 50%;
    @media (max-width: 768px) {
      width: 100%;
    }
  }
}
.field-value {
  margin-top: 0;
  margin-bottom: 2vw;
  position: relative;
  //display: flex;
  justify-content: start;
  align-items: center;
  input[type="text"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 100%;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
      font-size: 3.86vw;
      padding: 1.04vw 2.11vw;
    }
  }
  input[type="date"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    color: var(--quaternary-font-color);
    //   ::placeholder{
    // color: var(--quaternary-font-color);
    // }
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  input[type="password"] {
    background: transparent;
    font-family: var(--secondary-font);
    border: none;
    border: 0.069vw solid var(--timeline-color);
    border-radius: 0.14vw;
    padding: 1.04vw 1.11vw;
    width: 35vw;
    height: 3.47vw;
    @media (max-width: 768px) {
      height: 11.35vw;
    }
  }
  .input-container {
    width: 100%;
    position: relative;
    display: inline-flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
    .text-before {
      @media (max-width: 768px) {
        font-size: 4.34vw;
      }
    }
    .flag-icon {
      position: absolute;
      left: 1.04vw;
    }
    button {
      outline: none;
      background: none;
      border: none;
    }
    .flag-arrow {
      position: absolute;
      max-width: 0.69vw;
      height: auto;
      right: 2.08vw;
      top: 0;
      bottom: 0;
      margin: auto 0;
      @media (max-width: 768px) {
        max-width: 1.95vw;
      }
    }
    .division {
      position: absolute;
      width: 0.069vw;
      height: 1.04vw;
      background-color: var(--timeline-color);
      left: 3.33vw;
    }
    input[type="text"] {
      // background: transparent;
      font-family: var(--secondary-font);
      border: 0.069vw solid var(--timeline-color);
      padding: 1.04vw 1.11vw 1.04vw 4.2vw;
      border-radius: 0.14vw;
      height: 3.47vw;
      width: 100%;
      z-index: 0;
      @media (max-width: 768px) {
        height: 11.35vw;
      }
      &.selection {
        padding: 1.04vw 1.11vw 1.04vw 1.04vw;
      }
    }
    .dropdown-visible {
      background-color: var(--primary-background-color);
      visibility: visible;
      position: absolute;
      top: 3.47vw;
      z-index: 1;
      box-shadow: 0px 1px 5px 1px rgb(0 0 0 / 20%);
      width: 100%;
      @media (max-width: 768px) {
        top: 10.47vw;
      }
      ul {
        list-style: none;
        padding: 0.69vw 0;
        max-height: 27.97vw;
        margin: 0;
        overflow: hidden;
        overflow-y: scroll;
        @media (max-width: 768px) {
          padding: 1.69vw 0;
        }
        li {
          padding: 0.69vw 1.04vw;
          width: 34.9vw;
          display: flex;
          @media (max-width: 768px) {
            padding: 1.69vw 1.04vw;
          }
          .country-name {
            margin-left: 0.69vw;
            @media (max-width: 768px) {
              font-size: 3.38vw;
            }
          }
        }
        li:hover {
          background-color: var(--timeline-color);
        }
      }
      ul::-webkit-scrollbar {
        display: none;
      }
    }
    .dropdown-hidden {
      display: none;
    }
  }
  .ph-flag {
    height: 1.25vw;
    // padding-right: 0.62vw;
    // border-right: solid 0.09vw var(--quaternary-font-color);
  }
  .placeholder {
    position: absolute;
    top: -0.4vw;
    left: 1.04vw;
    font-size: 0.8333vw;
    color: var(--quaternary-font-color);
    padding: 0 0.3vw;
    background-color: var(--primary-background-color);
    // background-color: #ededf1;
    @media (max-width: 768px) {
      top: -1.8vw;
      left: 2.04vw;
      font-size: 3.38vw;
    }
  }
  .send {
    margin-left: 2.08vw;
    color: var(--tertiary-font-color);
  }
}
.input-info {
  font-size: 0.95vw;
  margin-top: 0.5vw;
  @media (max-width: 768px) {
    font-size: 3.38vw;
    margin-top: 0.76vw;
    margin-bottom: 4.62vw;
  }
}
