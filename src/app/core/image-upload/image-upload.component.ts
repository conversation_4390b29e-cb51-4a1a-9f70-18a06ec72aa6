import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';

import { Router } from '@angular/router';
import { faLock } from '@fortawesome/free-solid-svg-icons';
import { CartService } from 'src/app/services/cart.service';
@Component({
  selector: 'image-upload',
  templateUrl: './image-upload.component.html',
  styleUrls: ['./image-upload.component.scss'],
})
export class ImageUploadComponent implements OnInit {
  @ViewChild('imageDrop') imageDrop;
  @Input() fileSize: number = 8388608;
  @Input() accept: any = 'image/*';
  @Input() placeholder: string =
    'You can upload or drop your file here. Recommended upload size';
  @Input() selectedData: any = [];
  @Input() Disable: boolean = false;
  @Input() hideAltText: boolean = false;
  faLock = faLock;
  // @Input() selectedData= [];
  // @Input() selectedData :any= [];
  @Output() onFileChange = new EventEmitter();
  fileSizeText;
  fileSizeDimention;
  // selectedFiles: File[] =[]
  constructor() {}
  byteToGigaByte(n) {
    return n / Math.pow(10, 9);
  }
  ngOnInit(): void {
    console.log(this.selectedData);
    // this.reader = new FileReader();
    // this.reader.onloadend = () => {
    // 	this.selectedData.push(this.reader.result);
    // };
    if (this.fileSize == null) {
      this.fileSize = 8388608;
      // this.fileSizeText = '< 8 MB'
    } else if (this.fileSize < 1073741824) {
      this.fileSizeText = `${this.fileSize / 1048576} MB`;
    } else if (this.fileSize > 1073741824) {
      this.fileSizeText = `${this.fileSize / 1073741824} GB`;
    }
  }

  async onFileSelect(files) {
    let type;
    if (!files) {
      return;
    }
    if (this.accept != 'all') {
      if (this.accept == 'image/*') {
        type = 'image';
      } else if (this.accept == 'video/*') {
        type = 'video';
      } else if (this.accept == 'audio/*') {
        type = 'audio';
      }
      console.log(files[0].type);
      if (!files[0].type.includes(type)) {
        alert(`Only ${type} can be uploaded!`);
        return;
      }
    }

    if (files[0].size < this.fileSize) {
      // this.selectedFiles = [];
      this.selectedData = [];
      let reader = new FileReader();
      reader.onloadend = () => {
        // this.selectedData.push(reader.result);
        this.selectedData.push({
          filedata: reader.result,
          url: '',
          name: files[0].name,
          size: files[0].size,
          file_event: files[0],
          type: type,
        });
        this.onFileChange.emit(this.selectedData);
      };
      reader.readAsDataURL(files[0]);

      // this.selectedFiles.push(files[0]);
    } else {
      alert(`File size must not be more than ${this.fileSizeText}!`);
    }
  }

  removeItem(index) {
    this.selectedData.splice(index, 1);
    // this.selectedFiles.splice(index, 1);
    this.onFileChange.emit(this.selectedData);
  }

  drop(e) {
    e.preventDefault();
    this.checkfiles(e.dataTransfer.files);
  }

  checkfiles(files) {
    let type;
    if (this.accept != 'all') {
      if (this.accept == 'image/*') {
        type = 'image';
      } else if (this.accept == 'video/*') {
        type = 'video';
      } else if (this.accept == 'audio/*') {
        type = 'audio';
      }
      console.log(files[0].type);
      if (!files[0].type.includes(type)) {
        alert(`Only ${type} can be uploaded!`);
        return;
      }
    }
    if (files[0].size < this.fileSize) {
      this.readfiles(files, type);
    } else {
      alert(`File size must not be more than ${this.fileSizeText}!`);
    }
  }

  readfiles(files, type) {
    var reader = new FileReader();
    reader.onload = (event) => {
      // let image ;
      // image = new Image();
      // let fileReader = event.target as FileReader;
      // image['src'] = fileReader.result;
      // image.width = 50;
      this.selectedData = [];
      // this.selectedFiles = [];
      this.selectedData.push({
        filedata: reader.result,
        url: '',
        file_event: files[0],
        name: files[0].name,
        size: files[0].size,
        type: type,
      });
      this.onFileChange.emit(this.selectedData);
    };
    reader.readAsDataURL(files[0]);
  }
}
