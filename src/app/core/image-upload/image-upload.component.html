<div class="input-container file-upload" #imageDrop (drop)="drop($event)">
  <fa-icon
    class="Lock-position"
    [icon]="faLock"
    title="You do not have the rights to edit this field,&#013; please reach <NAME_EMAIL> &#013;if you have any questions"
    [hidden]="!Disable"
  >
  </fa-icon>

  <input
    class="upload-input"
    type="file"
    [accept]="accept"
    (change)="onFileSelect($event.target.files)"
    [disabled]="Disable"
  />
  <div *ngIf="selectedData?.length <= 0" class="icon">
    <img src="assets/images/<EMAIL>" />
  </div>
  <div *ngIf="selectedData?.length <= 0" class="text-content">
    <div class="title">{{ placeholder }}</div>
    <div class="sub-title">Recommended upload size:&lt; {{ fileSizeText }}</div>
  </div>

  <ng-container *ngIf="selectedData?.length > 0">
    <ng-container *ngFor="let file of selectedData; let i = index">
      <div class="file-item">
        <div
          *ngIf="accept == 'image/*'"
          style="display: flex; justify-content: center"
        >
          <img class="thumb" [src]="file?.filedata || file?.url" />
        </div>
        <div *ngIf="accept == 'video/*'">
          <video class="thumb" [src]="file?.filedata || file?.url"></video>
        </div>
        <div *ngIf="accept == 'audio/*'">
          <audio controls [src]="file?.url" *ngIf="file.url"></audio>
        </div>
        <div *ngIf="accept == 'glb/*'">
          <!-- <model-viewer camera-controls camera-orbit="45deg 55deg 2.5m"
						src="https://modelviewer.dev/shared-assets/models/Astronaut.glb"
						alt="A 3D model of an astronaut" data-js-focus-visible="" ar-status="not-presenting">
					</model-viewer> -->
        </div>
        <div *ngIf="accept == 'all'">
          <img
            class="thumb-folder"
            src="https://ta-python.s3.us-east-2.amazonaws.com/1643982789683_folder.png"
          />
        </div>
        <div class="text-content">
          <div class="title">
            <a [href]="file?.url" target="_blank"
              >Click to open
              {{
                file?.name?.slice(0, 4) + "..." + file?.name?.slice(-5) ||
                  "File"
              }}</a
            >
          </div>
          <div class="sub-title">
            File size: {{ (file?.size / 1048576).toFixed(2) || "--" }} MB
          </div>
          <div *ngIf="!Disable" (click)="removeItem(i)" class="close">
            <img src="assets/icons/close.png" />
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>

  <div class="button-container">
    <div class="button" [ngClass]="{ 'Disable-border': Disable }">
      {{ selectedData?.length > 0 ? "Replace file" : "Choose file" }}
    </div>
  </div>

  <ng-content> </ng-content>

  <div *ngIf="!hideAltText" class="asdasd">
    <div
      class="field-value"
      style="padding-right: 0.5vw"
      [ngClass]="{ 'Disabled-Color': Disable }"
    >
      <input
        type="text"
        placeholder="Alternate Text "
        [disabled]="Disable"
        [value]="selectedData ? selectedData[0]?.name : ''"
      />
      <div class="placeholder">Alternate Text</div>
      <div class="input-info">
        Provide alternate text to display in case the image doesn't load
      </div>
    </div>
  </div>
</div>
<!-- <div class="splitter">
  <div
    class="field-value"
    style="padding-right: 0.5vw"
  >
    <input type="text" placeholder="Alternate Text " />
    <div class="placeholder">Alternate Text </div>
    <div class="input-info">Provide alternate text to display in case the image doesn't load</div>
  </div>
</div> -->
