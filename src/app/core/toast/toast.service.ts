import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class ToastService {
   simpleObservable = new BehaviorSubject<{ status: boolean, text: string, time: number, type: number }>({ status: false, text: "", time: null, type: 1 })


   createToast(str: string, time = 3000, type = 1, status = true) {
      this.simpleObservable.next({ status, text: str, time, type })
   }

}
