import { AppComponent } from '../../app.component';
import { ToastService } from './toast.service';
import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';

@Component({
  selector: 'app-toast',
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss']
})
export class ToastComponent implements OnInit, OnDestroy {
  text = " ";
  status: boolean = false;
  public unique_key: number;
  public parentRef: AppComponent;

  constructor(private toastService: ToastService) {
  }

  ngOnInit(): void {
    this.toastService.simpleObservable.subscribe((data) => {
      if (data.status === true) {
        this.status = data.status;
        this.text = data.text;
        if (data.type == 1) {
          setTimeout(() => {
            this.status = false;
          }, data.time);
        }


      } else {
        this.status = false;
      }
    })
  }

  ngOnDestroy(): void {
    this.toastService.simpleObservable.unsubscribe();
  }

}
