<div class="scale-container">
  <div class="scale-image">
    <img src="assets/images/scale-image.jpeg" />
  </div>
  <div class="artwork-image">
    <img
      [src]="
        artworkData?.newAPI
          ? artworkData?.imgHash
          : 'https://register.terrain.art/artist-portal/assets/' +
            artworkData?.imgHash
      "
      [ngStyle]="{ width: artworkWidth + 'vw', height: artworkHeight + 'vw' }"
    />
  </div>
</div>
