.banner-content {
  height: 38vw;
  width: 100vw;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  transition: height 1s ease-in-out;
  background-color: black;
  .vimeo-wrapper {
    width: 100%;
    height: 100%;
  }
  .play-button {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay-background-color);
    & > span {
      cursor: pointer;
      color: var(--tertiary-font-color);
    }
    &.playing {
      opacity: 0;
      transition: opacity 0.3s ease-in-out;

      &:hover {
        opacity: 1;
      }
    }
  }

  .banner-text {
    position: absolute;
    bottom: 3.47vw;
    left: 0;
    width: 100%;
    color: var(--secondary-font-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    h1 {
      text-shadow: 0 0.14vw 0.28 rgba(0, 0, 0, 0.5);
      font-size: 2.08vw;
      margin-bottom: 1.38vw;
    }
    p {
      font-size: 1.11vw;
      font-family: var(--secondary-font);
      line-height: 1.14;
    }
    @media (max-width: 768px) {
      bottom: 9.66vw;
      h1 {
        text-shadow: 0 0.48vw 0.96 rgba(0, 0, 0, 0.5);
        font-size: 5.31vw;
        margin-bottom: 4.83vw;
      }
      p {
        font-size: 3.38vw;
      }
    }
  }
  &.playing.expand {
    height: 100vh;
  }
  &.playing.popup {
    height: 100vh;
    width: 100vw;
    max-width: 100%;
    position: fixed;
    z-index: 200;
    left: 0;
    top: 0;
    overflow: hidden;
    transform: translate3d(0, 0, 200px);
    background-color: rgb(0, 0, 0);
    background-color: rgba(0, 0, 0, 0.4);
    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
    }
  }
}

@media (max-width: 768px) {
  .banner-content {
    height: 76.32vw;
  }
  .pageCss {
    height: 131.4vw;
  }
}
.edit-te {
  position: absolute;
  bottom: 1vw;
  right: 5vw;
  .edit-btn-stl {
    font-size: 1.111vw;
    margin-left: 2vw;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.2vw;
    border: 0.06vw solid var(--tertiary-font-color);
    padding: 0.2vw 0.5vw;
    border-radius: 0.3vw;
    background-color: var(--tertiary-font-color);
    color: white;
    fa-icon {
      font-size: 1vw;
    }
  }
  .edit-btn-stl:hover {
    color: inherit;
    background-color: transparent;
  }
}
