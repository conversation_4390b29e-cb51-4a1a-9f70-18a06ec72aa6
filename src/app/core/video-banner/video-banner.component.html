<div
  class="banner-content"
  [ngClass]="{
    playing: isPlaying,
    expand: bannerType === 'EXPAND',
    popup: bannerType === 'POPUP',
    pageCss: isMobile
  }"
  [ngStyle]="{ height: pageType === 'exhi' && isMobile ? '76.33vw' : null }"
  #videoBanner
>
  <span *ngIf="isPlaying && bannerType === 'POPUP'" class="close">&times;</span>
  <div
    class="vimeo-wrapper"
    [ngStyle]="{
      width: mobilePop ? '100vw' : '100%',
      height: mobilePop ? '100vh' : '100%',
      position: mobilePop ? 'fixed' : 'static',
      top: mobilePop ? '0' : 'auto',
      left: mobilePop ? '0' : 'auto',
      'z-index': isMobile && isPlaying ? '200' : null,
      'background-color': mobilePop ? 'rgb(0, 0, 0)' : 'rgba(0, 0, 0,0)'
    }"
  >
    <vimeo-player
      [vimeoUrl]="bannerData.url"
      [background]="false"
      [transform]="
        isPlaying
          ? 'scale(1, 1)'
          : pageType === 'home'
          ? 'scale(3,3)'
          : isMobile && !mobilePop
          ? pageType === 'exhi'
            ? 'scale(3.3, 3.3)'
            : 'scale(4.7, 4.7)'
          : 'scale(1.5, 1.5)'
      "
      (pauseOut)="pauseOutFun()"
      [pageTypeValue]="pageType"
      #vimeoPlayer
    ></vimeo-player>
  </div>

  <!-- *ngIf="!(isPlaying && showControls)" -->
  <div
    *ngIf="!(isPlaying && showControls)"
    class="play-button d-flex align-items-center justify-content-center"
    (click)="(null)"
    [ngClass]="{ playing: isPlaying }"
  >
    <span
      class="image-icon bg-icon"
      (click)="isPlaying ? pauseVideo() : playVideo()"
    >
      <img
        [src]="
          isPlaying
            ? 'assets/images/pause.png'
            : 'assets/images/video-play-cta.png'
        "
      />
    </span>
  </div>
  <div *ngIf="!isPlaying" class="banner-text text-center">
    <h1>{{ bannerData.heading }}</h1>
    <p>{{ bannerData.subheading }}</p>
  </div>
  <div class="edit-te">
    <button
      (click)="editEvent.emit()"
      *ngIf="editable"
      class="edit-btn-stl"
      style="margin-left: 100%"
    >
      <fa-icon [icon]="faPencilAlt"></fa-icon>Edit
    </button>
  </div>
</div>
