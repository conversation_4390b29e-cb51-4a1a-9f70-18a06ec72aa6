import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnInit,
  Output,
  ViewChild,
  EventEmitter,
} from '@angular/core';
import { VideoBannerItem, VideoBannerType } from './video-banner.model';
import {
  faPlay,
  faPause,
  faPencilAlt,
} from '@fortawesome/free-solid-svg-icons';
import { VimeoPlayerComponent } from '../vimeo-player/vimeo-player.component';

@Component({
  selector: 'video-banner',
  templateUrl: './video-banner.component.html',
  styleUrls: ['./video-banner.component.scss'],
})
export class VideoBannerComponent implements OnInit {
  @Input() bannerData: VideoBannerItem;
  @Input() bannerType: VideoBannerType = VideoBannerType.EXPAND;
  @Input() pageType: string = '';
  @Input() isMobile: boolean = false;
  @Input() showControls: boolean = false;
  @Input() mobileFullscreen: boolean = false;

  @ViewChild('vimeoPlayer') vimeoPlayer: VimeoPlayerComponent;
  @ViewChild('videoBanner') videoBanner: ElementRef;

  @Input() editable = false;

  @Output() editEvent = new EventEmitter<any>();

  faPlay = faPlay;
  faPause = faPause;

  isPlaying = false;
  mobilePop = false;
  faPencilAlt = faPencilAlt;
  previousScroll = 0;


  constructor(private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.isMobile = this.getIsMobile();
    window.onresize = () => {
      this.isMobile = this.getIsMobile();
    };
  }

  public getIsMobile(): boolean {
    const w = document.documentElement.clientWidth;
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }

  async playVideo(): Promise<void> {
    console.log(
      'TCL ~ file: video-banner.component.ts ~ line 83 ~ VideoBannerComponent ~ playVideo ~ this.bannerData',
      this.bannerData
    );
    if (this.isMobile && this.mobileFullscreen) {
      this.mobilePop = true;
      this.previousScroll = window.scrollY;
      try {
        await document.body.requestFullscreen();
        await screen.orientation.lock('landscape');
      } catch { }
    }

    console.log(
      'TCL ~ file: video-banner.component.ts ~ line 70 ~ VideoBannerComponent ~ this.vimeoPlayer.playVideo ~ this.vimeoPlayer',
      this.vimeoPlayer
    );
    this.vimeoPlayer.playVideo().then(async () => {
      this.isPlaying = true;

      if (this.bannerType === VideoBannerType.EXPAND) {
        window.scroll({
          top:
            window.scrollY +
            this.videoBanner.nativeElement.getBoundingClientRect().y,
          left: 0,
          behavior: 'smooth',
        });
      }
    });
  }
  pauseVideo(): void {
    this.vimeoPlayer.pauseVideo().then(async () => {
      if (this.mobilePop) {
        try {
          screen.orientation.unlock();
          await document.exitFullscreen();
        } catch { }
      }
      this.mobilePop = false;
      this.isPlaying = false;
    });
  }
  async pauseOutFun() {
    if (this.mobilePop) {
      try {
        screen.orientation.unlock();
        await document.exitFullscreen();
      } catch { }

      window.scroll({
        top: this.previousScroll,
        left: 0,
        behavior: 'auto',
      });
    }
    this.mobilePop = false;
    this.isPlaying = false;
  }

}
