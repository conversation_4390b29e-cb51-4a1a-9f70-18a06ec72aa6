<!-- <div class="cart-info"> -->
<div class="info-wrapper">
  <div class="info-container">
    <div class="info-header">
      <h2 *ngIf="type == 'Cart'">Shopping Cart</h2>
      <h2 *ngIf="type == 'Checkout'">Order Review</h2>

      <div *ngIf="type == 'Cart'" class="close-button">
        <a (click)="closeCart()" style="cursor: pointer"
          ><img src="assets/icons/grey-close.png"
        /></a>
        <!-- <span>Edit Cart</span> -->
      </div>
    </div>

    <div (click)="openSummary = !openSummary" *ngIf="isMobile" class="summary">
      {{ openSummary ? "Close summary &#5123;" : "Open summary &#5121;" }}
    </div>

    <div
      class="info"
      style="display: flex; height: 100%; flex-direction: column"
    >
      <div *ngIf="!isMobile || openSummary">
        <div class="info-main">
          <div *ngFor="let item of cartData; let i = index">
            <div class="info-box">
              <a
                [routerLink]="
                  '/discover/artists/' +
                  (item?.artist_id?.url_name
                    ? item?.artist_id?.url_name
                    : 'null') +
                  '/artworks/' +
                  trimSpaces(item?.artwork_title) +
                  '-' +
                  item.year +
                  '-' +
                  item?._id
                "
                style="color: var(--primary-font-color)"
              >
                <img
                  class="icon"
                  [src]="
                    'https://www.terrain.art/cdn-cgi/image/width=300,quality=52/' +
                    item.thumbnail_of_primary
                  "
                  alt=""
                  style="object-fit: contain"
              /></a>
              <div class="content">
                <div class="header">
                  <a
                    [routerLink]="
                      '/discover/artists/' +
                      (item?.artist_id?.url_name
                        ? item?.artist_id?.url_name
                        : 'null') +
                      '/artworks/' +
                      trimSpaces(item?.artwork_title) +
                      '-' +
                      item.year +
                      '-' +
                      item?._id
                    "
                    style="color: var(--primary-font-color)"
                    ><div class="title" style="font-style: italic">
                      {{ item.artwork_title }},
                      {{ item.year }}
                    </div></a
                  >
                  <div *ngIf="item?.payment_modes !== 'amex'" class="price">
                    ₹ {{ item.sale_price | number : "1.0" }} | $
                    {{
                      item.sale_price | forex : "USD" | async | number : "1.0"
                    }}
                  </div>
                  <div *ngIf="item?.payment_modes == 'amex'" class="price">
                    ₹
                    {{ item?.sale_price * 0.85 | number : "1.0" }}

                    <span class="discount">
                      <del
                        >₹ {{ item?.sale_price | number : "1.0" }}</del
                      > </span
                    ><br />
                    <span class="offer"> 15% off </span>
                  </div>
                </div>
                <div *ngIf="item?.edition_id" class="artist-name">
                  {{ item?.edition_id }}
                </div>
                <!-- <div class="header">
              <div class="title1">
                Black wood frame
                <img src="assets/icons/delete.png">
                <img src="assets/icons/edit-grey.png">
              </div>
              <div class="price">₹ 800</div>
            </div> -->
                <div
                  (click)="deleteItem(i)"
                  class="delete"
                  style="cursor: pointer"
                >
                  <img src="assets/icons/delete.png" alt="" />
                </div>
              </div>
            </div>

            <div class="hr-line" *ngIf="i != 8"></div>
          </div>
        </div>
      </div>
      <div class="info-detail">
        <div *ngIf="!isMobile || openSummary" class="info-tax">
          <div class="title">Transaction Fee</div>
          <div class="price">
            ₹ {{ txfee | number : "1.0" }} | $
            {{ txfee | forex : "USD" | async | number : "1.0" }}
          </div>
        </div>
        <div *ngIf="!isMobile || openSummary" class="info-tax">
          <div class="title">Taxes</div>
          <div class="price">
            ₹ {{ tax | number : "1.0" }} | $
            {{ tax | forex : "USD" | async | number : "1.0" }}
          </div>
        </div>
        <div class="info-total">
          <div class="title">Subtotal</div>
          <div class="price">
            ₹ {{ total | number : "1.0" }} | $
            {{ total | forex : "USD" | async | number : "1.0" }}
          </div>
        </div>
        <div *ngIf="!isMobile || openSummary" class="details">
          Costs excl. shipping, shipping rates will be informed via email after
          purchase. Taxes include 12% GST for physical and 18% GST for digital
          works. Includes 3% transaction fee for payment gateway.
        </div>

        <div *ngIf="type == 'Cart'" class="info-btn">
          <a (click)="closeCart()" routerLink="/collector/checkout">Checkout</a>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- </div> -->
<!-- <div class="info-wrapper">
  qertyui
</div> -->
