import { isPlatformBrowser } from '@angular/common';
import { Component, Inject, Input, OnInit, PLATFORM_ID } from '@angular/core';
import { Router } from '@angular/router';
import { CartService } from 'src/app/services/cart.service';
declare let Swal: any;
@Component({
  selector: 'app-cart',
  templateUrl: './cart.component.html',
  styleUrls: ['./cart.component.scss'],
})
export class CartComponent implements OnInit {
  @Input() type = 'Cart';
  @Input() isMobile = false;
  openSummary = false;
  cartData: any[];
  total = 0;
  tax = 0;
  txfee = 0;
  billingAddress;
  Toast;

  constructor(private cartService: CartService, private route: Router, @Inject(PLATFORM_ID) private platformId: any) { }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.Toast = Swal.mixin({
        toast: true,
        position: 'top-right',
        iconColor: 'white',
        customClass: {
          popup: 'colored-toast',
        },
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
      });
    }
    this.cartService.cartItems$.subscribe((data) => {
      console.log(data);

      this.cartData = [...data.data?.artwork_id];
      this.billingAddress = data.data.billingAddress;
      this.tax = 0;
      this.total = 0;
      this.txfee = 0;

      if (data.data?.editions) {
        data.data?.editions.forEach((a) => {
          let edition = a.artwork_id?.editionDetails.find((b) => { return b.edition == a.edition_id })

          this.cartData.push({ ...a.artwork_id, edition_id: a.edition_id, sale_price: edition?.sale_price })
        })
      }
      this.cartData?.forEach((element) => {
        if (
          this.billingAddress?.country &&
          this.billingAddress?.country !== 'India'
        ) {
          if (element?.payment_modes == 'amex') {
            this.txfee = this.txfee + element.sale_price * 0.03 * 0.85;
            this.tax = 0;
            this.total = this.total + element.sale_price * 1.03 * 0.85;
          } else {
            this.txfee = this.txfee + element.sale_price * 0.03;
            this.tax = 0;
            this.total = this.total + element.sale_price * 1.03;
          }
        } else if (element?.physicalOrDigital == 'physical') {
          if (element?.payment_modes == 'amex') {
            this.txfee = this.txfee + element.sale_price * 0.03 * 0.85;
            this.tax = this.tax + element.sale_price * 1.03 * 0.85 * 0.12;
            this.total = this.total + element.sale_price * 1.03 * 0.85 * 1.12;
          } else {
            this.txfee = this.txfee + element.sale_price * 0.03;
            this.tax = this.tax + element.sale_price * 1.03 * 0.12;
            this.total = this.total + element.sale_price * 1.03 * 1.12;
          }
        } else {
          if (element?.payment_modes == 'amex') {
            this.txfee = this.txfee + element.sale_price * 0.03 * 0.85;
            this.tax = this.tax + element.sale_price * 1.03 * 0.85 * 0.18;
            this.total = this.total + element.sale_price * 1.03 * 0.85 * 1.18;
          } else {
            this.txfee = this.txfee + element.sale_price * 0.03;
            this.tax = this.tax + element.sale_price * 1.03 * 0.18;
            this.total = this.total + element.sale_price * 1.03 * 1.18;
          }
        }
      });
    });
  }

  closeCart() {
    this.cartService.changeCartOpen(false);
  }
  deleteItem(index) {

    let deleteData = this.cartData[index];

    if (deleteData?.edition_id) {
      this.cartService.deleteCartItem2(deleteData?._id, deleteData?.edition_id).subscribe((d) => {
        this.Toast.fire({
          icon: 'success',
          title: 'Removed from cart!',
        });
      });
    } else {
      this.cartService.deleteCartItem(deleteData?._id).subscribe((d) => {
        this.Toast.fire({
          icon: 'success',
          title: 'Removed from cart!',
        });
      });
    }


  }
  public trimSpaces(str) {
    let a = str.trim().replace('(', '_').replace(')', '_');
    let b = a.split(' ');
    return encodeURIComponent(b.join('_'));
  }
}
