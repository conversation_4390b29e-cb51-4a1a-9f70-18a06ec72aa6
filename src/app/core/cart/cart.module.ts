import { NgModule } from '@angular/core';
import { CartComponent } from './cart.component';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CartService } from 'src/app/services/cart.service';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [CartComponent],
  imports: [SharedModule, RouterModule, FontAwesomeModule],
  exports: [CartComponent],
})
export class CartModule {}
