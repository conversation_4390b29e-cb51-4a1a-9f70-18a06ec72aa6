.cart-info {
  background-color: #e7e3e3c4;
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
}

.info-wrapper {
  // position: -webkit-sticky;
  position: relative;
  // top: 0px;
  // right: 0;
  width: 100%;
  height: 100vh;
  background-color: #f6f6f6;
  // margin-top: -3.47vw;
  z-index: 15;
  .info-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0 7.22vw 3.47vw 7.22vw;
    // margin-top: 11.6vw;
    overflow: hidden;
    z-index: 15;
    //overflow-y: scroll;

    .info-header {
      background-color: #f6f6f6;
      padding-top: 7vw;
      padding-bottom: 2.08vw;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .close-button {
        // position: fixed;
        // top: 7vw;
        // right: 7.22vw;
        color: var(--quaternary-font-color);
        img {
          width: 2.29vw;
          height: 2.29vw;
        }
      }
      h2 {
        font-size: 2.08vw;
        margin-bottom: 1.38vw;
        // position: fixed;
        // top: 7vw;
      }
    }

    .info {
      // margin-top: 11.6vw;
      .info-main {
        padding-right: 1vw;
        padding-left: 1vw;
        padding-bottom: 35vw;
        height: 52vw;
        overflow: hidden;
        overflow-y: scroll;
        .info-box {
          display: flex;
          justify-content: space-between;
          padding: 2.08vw 0;
          .icon {
            width: 6.11vw;
            height: 6.11vw;
          }
          .content {
            width: 71.5%;
            .header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: var(--default-font-size);
              margin-top: 1.38vw;
              .title {
                width: 65%;
                font-family: var(--primary-font);
                display: flex;
                justify-content: start;
                align-items: center;
              }
              .title1 {
                width: 65%;
                font-family: var(--primary-font);
                display: flex;
                justify-content: start;
                align-items: center;
                img {
                  margin-left: 1.04vw;
                  height: 0.833vw;
                }
              }
              .price {
              }
            }
            .header:first-child {
              margin-top: 0;
            }
            .artist-name {
              margin-top: 0.7vw;
              font-family: var(--secondary-font);
              font-size: var(--default-font-size);
              color: var(--quaternary-font-color);
            }
            .delete {
              margin-top: 1.38vw;
              width: 100%;
              text-align: right;
              img {
                height: 1.25vw;
              }
            }
          }
        }
        .hr-line {
          height: 0.07vw;
          width: 100%;
          background-color: var(--hr-primary-color);
        }
      }
      .info-detail {
        width: 27.78vw;
        position: -webkit-sticky;
        position: sticky;
        padding-top: 3.75vw;
        //padding-bottom: 3.75vw;
        bottom: -3.47vw;
        background-color: #f6f6f6;

        .info-btn {
          margin-top: 3.47vw;
          margin-bottom: 4vw;
          a {
            width: 100%;
            background: none;
            outline: none;
            text-align: center;
            cursor: pointer;
            color: var(--tertiary-font-color);
            font-size: 1.25vw;
            padding: 0.83vw 2.64vw;
            border-radius: 1.5vw;
            border: 0.07vw solid var(--tertiary-font-color);
          }
        }

        .info-total {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 1.38vw;

          .title {
            width: 48%;
            font-family: var(--primary-font);
          }
          .price {
          }
        }
        .info-tax {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 1.11vw;
          // margin-top: 2.78vw;
          margin-bottom: 0.7vw;
          .title {
            width: 48%;
            font-family: var(--primary-font);
          }
          .price {
          }
        }
        .details {
          margin-top: 0.7vw;
          font-size: 0.97vw;
          color: var(--quaternary-font-color);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .info-wrapper {
    width: 100vw;
    height: 100%;
    margin-bottom: 6vw;
    .info-container {
      width: 100%;
      height: 100%;
      padding: 0 6.04vw 7.25vw 5.797vw;
      // margin-top: 27.78vw;
      .info-header {
        margin-top: 11.6vw;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .close-button {
          top: 12.08vw;
          right: 6.04vw;
          img {
            width: 9.66vw;
            height: 9.66vw;
          }
        }
        h2 {
          font-size: 5.797vw;
          margin-bottom: 2.25vw;
          top: 13.53vw;
        }
      }

      .info {
        .info-main {
          height: auto;
          overflow: auto;
          padding-right: 0;
          padding-bottom: 5vw;
          .info-box {
            padding: 7.25vw 0;
            .icon {
              width: 21.25vw;
              height: 21.25vw;
            }
            .content {
              width: 72.18%;
              .header {
                font-size: 3.87vw;
                margin-top: 2.42vw;
                .title {
                  width: 52.29%;
                  font-family: var(--secondary-font);
                  display: flex;
                  justify-content: start;
                  align-items: center;
                }
                .title1 {
                  width: 46.5vw;
                  font-family: var(--secondary-font);
                  display: flex;
                  justify-content: start;
                  align-items: center;
                  img {
                    margin-left: 3.62vw;
                    height: 2.9vw;
                  }
                }
                .price {
                }
              }
              .header:first-child {
                margin-top: 0;
              }
              .artist-name {
                margin-top: 2, 42vw;
                font-size: 3.87vw;
                color: var(--quaternary-font-color);
              }
              .delete {
                margin-top: 4.84vw;
                width: 100%;
                text-align: right;
                img {
                  height: 4.35vw;
                }
              }
            }
          }
          .hr-line {
            height: 0.242vw;
          }
        }
        .info-detail {
          width: 88.16vw;
          position: static;
          bottom: 0;
          background-color: #f6f6f6;

          .info-btn {
            margin-top: 12.08vw;
            margin-bottom: 12.08vw;
            a {
              font-size: 4.35vw;
              padding: 4.84vw 3.62vw;
              border-radius: 5.19vw;
              border: 0.242vw solid var(--tertiary-font-color);
            }
          }

          .info-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 4.84vw;

            .title {
              width: 40%;
            }
            .price {
            }
          }
          .info-tax {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 4.84vw;
            margin-top: 2.25vw;
            margin-bottom: 7.25vw;
            .title {
              width: 40%;
            }
            .price {
            }
          }
          .details {
            margin-top: 3.62vw;
            font-size: 3.38vw;
          }
        }
      }
    }
  }
  .summary {
    font-size: 3.5vw;
    color: var(--quaternary-font-color);
    cursor: pointer;
  }
}
.discount,
.offer {
  font-size: 0.8vw;
  @media (max-width: 768px) {
    font-size: 2.5vw;
  }
}
.offer {
  color: var(--tertiary-font-color);
}
