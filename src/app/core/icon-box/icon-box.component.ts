import { Component, Input, OnInit } from '@angular/core';
import { IconData } from './icon-box.model';
import { faLink } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-icon-box',
  templateUrl: './icon-box.component.html',
  styleUrls: ['./icon-box.component.scss']
})
export class IconBoxComponent implements OnInit {

  @Input() data: IconData;
  @Input() isMobile: boolean = false;
  faLink = faLink;

  constructor() { }

  ngOnInit(): void {
  }

}
