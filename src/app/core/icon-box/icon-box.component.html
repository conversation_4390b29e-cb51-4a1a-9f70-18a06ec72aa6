<div class="section">
  <div
    class="d-flex justify-content-start align-items-center"
    style="flex-wrap: wrap; width: 100%"
  >
    <!-- <div class="left-section">
    <img src="{{data.image}}" class="image" >
  </div> -->
    <div class="right-section">
      <div class="title" *ngIf="data.title != undefined">{{ data.title }}</div>
      <div
        class="subtitle1"
        *ngIf="data.subtitle != undefined"
        [innerHTML]="data.subtitle"
      ></div>
      <div class="subtitle1" *ngIf="data.subtitle1 != undefined">
        {{ data.subtitle1 }}
      </div>
      <div class="subtitle2" *ngIf="data.subtitle2 != undefined">
        {{ data.subtitle2 }}
      </div>
      <div class="content" *ngIf="data.content != undefined">
        {{ data.content }}
      </div>
      <div class="linkText" *ngIf="data.link != null || data.link != undefined">
        <button class="image-wrapper">
          <img src="assets/icons/link_2020-11-12/<EMAIL>" alt="" />
        </button>
        visit
        <a href="{{ data.link }}" target="_blank">{{
          data.link.length > 25 ? data.link.slice(0, 25) + ".." : data.link
        }}</a>
      </div>
    </div>
  </div>
  <hr *ngIf="data.last" />
</div>
