import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import Player from '@vimeo/player';
import { log } from 'console';
declare let gtag: Function;
declare let YT: any;
@Component({
  selector: 'vimeo-player',
  templateUrl: './vimeo-player.component.html',
  styleUrls: ['./vimeo-player.component.scss'],
})
export class VimeoPlayerComponent implements OnInit, AfterViewInit {
  @Input() vimeoUrl: string;
  @Input() background = true;
  @Input() autoplay = false;
  @Input() sliderIndex: number;
  @Input() controls = false;
  @Input() zIndex = 'auto';
  @Input() pageTypeValue: string = '';
  @Input() transform = 'scale(1.4, 1.4)';

  @Output() pauseOut = new EventEmitter<string>();
  @Output() playerLoad = new EventEmitter<string>();
  @Output() duration = new EventEmitter<number>();

  public player: Player;
  @ViewChild('vimeoIframe') vimeoIframe: ElementRef;

  public playerYt: any;
  isYouTube = false;

  constructor() { }

  ngAfterViewInit(): void {
    if (this.isValidYouTubeUrl(this.vimeoUrl)) {
      this.isYouTube = true;
      this.playerYt = new YT.Player(this.vimeoIframe.nativeElement, {

      });
    } else {
      this.isYouTube = false;
      this.player = new Player(this.vimeoIframe.nativeElement);
      this.player.on('pause', async (data) => {
        gtag('event', 'vimeo_pause', {
          event_category: 'vimeo',
          event_label: 'pause',
          vimeo_url: this.vimeoUrl,
          vimeo_time: this.player.getCurrentTime(),
        });
        this.pauseOut.emit('paused');
      });
      this.player.on('loaded', async (data) => {
        this.playerLoad.emit('loaded');
        this.duration.emit(await this.player.getDuration());
      });
    }

  }

  ngOnInit(): void { }

  public stopVideo(): Promise<void> {
    gtag('event', 'vimeo_stopped', {
      event_category: 'vimeo',
      event_label: 'stopped',
      vimeo_url: this.vimeoUrl,
      vimeo_time: this.player.getCurrentTime(),
    });
    return this.player.unload();
  }

  public async playVideo(): Promise<void> {
    gtag('event', 'vimeo_playing', {
      event_category: 'vimeo',
      event_label: 'playing',
      vimeo_url: this.vimeoUrl,
    });

    if (this.isYouTube) {
      console.log(this.vimeoIframe.nativeElement);

      return (this.vimeoIframe.nativeElement as HTMLVideoElement).play();
    } else {
      return this.player.play();
    }

  }
  public pauseVideo(): Promise<void> {
    return this.player.pause();
  }

  public get isBackground(): number {
    if (this.background) {
      return 1;
    }
    return 0;
  }
  public get isAutoplay(): number {
    if (this.autoplay) {
      return 1;
    }
    return 0;
  }
  public get getUrl(): string {
    const url =
      this.vimeoUrl +
      '?background=' +
      this.isBackground +
      '&autoplay=' +
      this.isAutoplay +
      (this.pageTypeValue === 'home' || this.controls ? '&controls=0' : '');
    return url;
  }
  isValidYouTubeUrl(url) {
    // Regular expressions for matching YouTube URL patterns
    const youtubePattern = /(?:https?:\/\/)?(?:www\.)?youtube\.com\/.+/;
    const youtuPattern = /(?:https?:\/\/)?youtu\.be\/.+/;

    // Check if the URL matches either pattern
    if (youtubePattern.test(url) || youtuPattern.test(url)) {
      return true;
    }

    return false;
  }
}
