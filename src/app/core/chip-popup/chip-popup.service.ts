import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
@Injectable({
  providedIn: 'root',
})
export class ChipPopupService {
  private apiUrl = environment.priceDBApi;
  private apiUrl1 = environment.artistApi;
  constructor(private http: HttpClient) {}

  async getMediumFromRegister(id): Promise<string> {
    return (
      await this.http
        .get<any>(
          `${this.apiUrl1}/items/30_medium_keywords?filter[id]=${id}&fields=name&limit=1`
        )
        .toPromise()
    ).data[0].name;
  }

  async getSubjectFromRegister(id): Promise<string> {
    return (
      await this.http
        .get<any>(
          `${this.apiUrl1}/items/33_subject_keywords?filter[id]=${id}&fields=name&limit=1`
        )
        .toPromise()
    ).data[0].name;
  }

  async getMovementFromRegister(id): Promise<string> {
    return (
      await this.http
        .get<any>(
          `${this.apiUrl1}/items/32_movement_keywords?filter[id]=${id}&fields=name&limit=1`
        )
        .toPromise()
    ).data[0].name;
  }

  async getVideoFromRegister(id): Promise<string> {
    return (
      await this.http
        .get<any>(
          `${this.apiUrl1}/items/50_ed_videos?filter[id]=${id}&fields=page_url&limit=1`
        )
        .toPromise()
    ).data[0].page_url;
  }
}
