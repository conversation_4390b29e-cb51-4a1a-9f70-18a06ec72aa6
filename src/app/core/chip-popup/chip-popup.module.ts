import { ChipPopupService } from './chip-popup.service';
import { ChipPopupComponent } from './chip-popup.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@NgModule({
  declarations: [ChipPopupComponent],
  imports: [CommonModule, RouterModule, FontAwesomeModule],
  exports: [ChipPopupComponent],
  providers: [ChipPopupService],
})
export class ChipPopupModule {}
