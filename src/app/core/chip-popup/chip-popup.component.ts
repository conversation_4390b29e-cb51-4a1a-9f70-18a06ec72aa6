import { ChipPopupService } from './chip-popup.service';
import { Component, Input, OnInit } from '@angular/core';
import { faLink } from '@fortawesome/free-solid-svg-icons';
import { GlossaryModel } from 'src/app/pages/learn/models/glossary.model';

@Component({
  selector: 'chip-popup',
  templateUrl: './chip-popup.component.html',
  styleUrls: ['./chip-popup.component.scss'],
})
export class ChipPopupComponent implements OnInit {
  @Input() Data: GlossaryModel;

  isPopupOpen = false;
  availableData: string[] = [];

  constructor(private chipPopupService: ChipPopupService) {}

  ngOnInit(): void {
    let apiPromises: Promise<string>[] = [];
    if (this.Data?.register_medium) {
      apiPromises.push(
        this.chipPopupService.getMediumFromRegister(this.Data?.register_medium)
      );
      this.availableData.push('medium');
    }
  }
  ClickedOut(event) {
    if (
      event.target.className ===
      'd-flex align-items-center justify-content-center h-100 w-100 form-container'
    ) {
      this.isPopupOpen = false;
    }
  }
  // openPopup(){

  // };
}
