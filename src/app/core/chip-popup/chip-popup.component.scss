.section {
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.8);
  .popup-close {
    position: absolute;
    right: 1vw;
    top: 1vw;
    font-size: 2.4vw;
    a {
      color: white;
      img {
        width: 2.22vw;
        height: 2.22vw;
      }
    }
  }
}

.chip-popup {
  width: 30vw;
  //min-height: 30vw;
  background-color: white;
  border-radius: 0.45vw;
  .input-field {
    input::-webkit-input-placeholder {
      font-weight: 500;
    }
    input::-moz-placeholder {
      font-weight: 500;
    }
    input:-ms-input-placeholder {
      font-weight: 500;
    }
    input::-ms-input-placeholder {
      font-weight: 500;
    }
    input::placeholder {
      font-weight: 500;
    }
  }
  h1 {
    font-size: 1.6vw;
    margin-bottom: 2vw;
  }
  p {
    margin-bottom: 0.69vw;
  }
  .go-to {
    color: #004ddd;
    img {
      margin-left: 0.625vw;
      display: inline-block;
      width: 1.6vw;
      height: 1.6vw;
    }
    margin-bottom: 1.2vw;
  }
}
.button {
  border: none;
  cursor: pointer;
  padding: 0.7vw 4.37vw;
  background-color: #004ddd;
  color: #ffffff;
  border-radius: 0.45vw;
}
hr {
  margin: 0;
  margin-bottom: 1.2vw;
  border: 0;
  border-top: 0.069vw solid var(--hr-primary-color);
}
