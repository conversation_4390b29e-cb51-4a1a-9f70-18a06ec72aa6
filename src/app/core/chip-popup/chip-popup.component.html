<div (click)="isPopupOpen = true" class="chip">
  {{ Data?.glossary_term }}
</div>

<!-- <div *ngIf="isPopupOpen" class="section" (click)="ClickedOut($event)">
  <div class="section-inner h-100">
    <div
      class="d-flex align-items-center justify-content-center h-100 w-100 form-container"
    >
      <div class="chip-popup" style="padding: 2vw 2vw; position: relative">
        <h1>{{ Data?.glossary_term }}</h1>
        <hr />
        <p [innerHTML]="Data?.description"></p>
        <div class="d-flex justify-content-end">
          <div class="go-to" style="font-size: 1vw">
            Go to definition
            <img src="assets/icons/front-chevron.png" />
          </div>
        </div>
        <hr />
        <div class="d-flex justify-content-center">
          <div class="go-to">
            Go to glossary
            <img src="assets/icons/front-chevron.png" />
          </div>
        </div>
        <hr />
        <div class="d-flex justify-content-center">
          <div class="go-to">
            Go to artist
            <img src="assets/icons/front-chevron.png" />
          </div>
        </div>
        <div class="popup-close">
          <a (click)="isPopupOpen = false" style="cursor: pointer">
            <img src="assets/images/close.png" />
          </a>
        </div>
      </div>
    </div>
  </div>
</div> -->
