import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { CardBoxItem } from './card-box.model';

@Component({
  selector: 'card-box',
  templateUrl: './card-box.component.html',
  styleUrls: ['./card-box.component.scss'],
})
export class CardBoxComponent implements OnInit {
  @Input() cardData: CardBoxItem;
  @Input() type: string = 'Card';

  start = 0;
  end = 0;
  constructor() {}

  ngOnInit(): void {}

  public get getParam(): any {
    if (this.cardData.routerParam) {
      return {
        data: encodeURIComponent(
          JSON.stringify({
            url: this.cardData.routerParam,
            name: this.cardData.title,
            title: this.cardData.heading,
            subtitle: this.cardData.subheading,
          })
        ),
      };
    } else {
      return null;
    }
  }
}
