<a
  class="card-container"
  *ngIf="!cardData.routerLink?.startsWith('http') && type === 'Card'"
  [routerLink]="cardData.routerLink"
>
  <div class="w-100 h-100">
    <img [src]="cardData.image" width="auto" />
    <div class="card-gradient"></div>
    <div class="card-content">
      <h1 [innerHTML]="cardData.heading"></h1>
      <p [innerHTML]="cardData.subheading"></p>
    </div>
  </div>
</a>
<a
  class="card-container"
  *ngIf="cardData.routerLink?.startsWith('http') && type === 'Card'"
  [href]="cardData.routerLink"
  target="_blank"
>
  <div class="w-100 h-100">
    <img [src]="cardData.image" width="auto" />
    <div class="card-gradient"></div>
    <div class="card-content">
      <h1 [innerHTML]="cardData.heading"></h1>
      <p [innerHTML]="cardData.subheading"></p>
    </div>
  </div>
</a>
<a
  class="card-container"
  [routerLink]="cardData.routerLink"
  [state]="getParam"
  *ngIf="type === 'Catalog'"
>
  <div class="w-100 h-100">
    <img [src]="cardData.image" width="auto" />
    <div class="card-gradient"></div>
    <div class="card-content">
      <h1 [innerHTML]="cardData.heading"></h1>
      <p [innerHTML]="cardData.subheading"></p>
    </div>
  </div>
</a>

<a
  [routerLink]="cardData.routerLink"
  class="card-container1"
  *ngIf="type === 'Artist'"
>
  <div class="w-100 h-100">
    <img [src]="cardData.image" width="auto" />
    <div class="card-gradient"></div>
    <div class="card-content">
      <h1 [innerHTML]="cardData.heading"></h1>
      <p [innerHTML]="cardData.subheading"></p>
    </div>
  </div>
</a>
