.card-container {
  display: flex;
  position: relative;
  border-radius: 0.65vw;
  cursor: pointer;
  overflow: hidden;
  bottom: 0px;
  flex: 0 0 32.48%;
  &:hover {
    img {
      transform: scale(1.2);
    }
  }

  &:focus {
    outline: none;
  }

  img {
    height: 36.5vw;
    width: 100%;
    border-radius: 0.65vw;
    transform: scale(1);
    -o-object-fit: cover;
    object-fit: cover;
    transition: transform 0.3s ease-in-out, visibility 0.3s ease-in;
  }
  .card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0.65vw;
    overflow: hidden;
    background: var(--card-linear-gradient);
  }
  .card-content {
    position: absolute;
    bottom: 3.5vw;
    width: 100%;
    padding-right: 3.47vw;
    padding-left: 3.47vw;
    color: var(--secondary-font-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;

    h1 {
      font-size: 1.95vw;
      line-height: 2.1vw;
      margin-bottom: 1.05vw;
      font-weight: 500;
    }
    p {
      font-weight: 200;
      font-family: var(--secondary-font);
      line-height: 1.5vw;
      margin-bottom: 0;
    }
  }
  @media (max-width: 768px) {
    img {
      height: 72.46vw;
    }
    .card-content {
      bottom: 9.66vw;
      padding-right: 7.24vw;
      padding-left: 7.24vw;
      h1 {
        font-size: 5.79vw;
        margin-bottom: 4.83vw;
        line-height: normal;
      }
      p {
        font-size: 3.86vw;
        margin-bottom: 0;
        line-height: 5vw;
      }
    }
  }
}
.card-container1 {
  display: flex;
  position: relative;
  border-radius: 0.65vw;
  cursor: pointer;
  overflow: hidden;
  bottom: 0px;
  flex: 0 0 32.48%;
  &:hover {
    img {
      transform: scale(1.2);
    }
  }

  &:focus {
    outline: none;
  }

  img {
    height: 36.5vw;
    width: 100%;
    border-radius: 0.65vw;
    transform: scale(1);
    -o-object-fit: cover;
    object-fit: cover;
    transition: transform 0.3s ease-in-out, visibility 0.3s ease-in;
  }
  .card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0.65vw;
    overflow: hidden;
    background: var(--card-linear-gradient);
  }
  .card-content {
    position: absolute;
    bottom: 3.5vw;
    width: 100%;
    padding-right: 3.47vw;
    padding-left: 3.47vw;
    color: var(--secondary-font-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    text-align: center;

    h1 {
      font-size: 1.95vw;
      line-height: 2.1vw;
      margin-bottom: 1.05vw;
      font-weight: 500;
    }
    p {
      font-weight: 200;
      font-family: var(--secondary-font);
      line-height: 1.5vw;
      margin-bottom: 0;
    }
  }
  @media (max-width: 768px) {
    // img {
    //   height: 72.46vw;
    // }
    // .card-content {
    //   bottom: 9.66vw;
    //   padding-right: 7.24vw;
    //   padding-left: 7.24vw;
    //   h1 {
    //     font-size: 5.79vw;
    //     margin-bottom: 4.83vw;
    //   }
    //   p {
    //     font-size: 3.86vw;
    //     margin-bottom: 0;
    //     line-height: 5vw;
    //   }
    // }
  }
}
