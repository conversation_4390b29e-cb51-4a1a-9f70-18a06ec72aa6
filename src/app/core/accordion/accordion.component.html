<div class="slider-wrapper" [ngClass]="[addClass]">
  <div class="d-flex accordion" [ngStyle]="{ padding: padding }">
    <div [ngClass]="{'title': type!=='faq', 'title1': type==='faq'}">{{ data[0]?.name }}</div>
    <div class="buttonAlign">
      <button
        class="image-wrapper"
        (click)="scroll(data[0]?.index, 1)"
        *ngIf="!data[0]?.isOpen[data[0]?.index]"
      >
        <img
          src="assets/icons/accordion-close_2020-11-12/<EMAIL>"
          alt=""
        />
      </button>
      <button
        class="image-wrapper"
        (click)="scroll(data[0]?.index, 2)"
        *ngIf="data[0]?.isOpen[data[0]?.index]"
      >
        <img
          src="assets/icons/accordion-open_2020-11-12/<EMAIL>"
          alt=""
        />
      </button>
    </div>
  </div>
</div>
