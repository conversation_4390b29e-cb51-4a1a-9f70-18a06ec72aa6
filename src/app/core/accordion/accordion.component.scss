.accordion {
  width: 100%;
  .title {
    font-size: 2.1vw;
    max-width: 65.57%;
  }
  .title1 {
    font-size: 1.67vw;
    max-width: 70%;
  }
  .buttonAlign {
    float: right;
    right: var(--page-default-margin);
    position: absolute;
  }
  .back {
    color: var(--tertiary-font-color);
    width: 2.85vw;
    height: 2.85vw;
    padding: 0.75vw 0.85vw;
    border: 1px solid var(--primary-border-color);
    border-radius: 50%;
    font-size: 1.11vw;
  }
  .image-wrapper {
    outline: none;
    border: none;
    background: none;
    width: 2.85vw;
    height: 2.85vw;
    padding: 0vw;
    border-radius: 50%;
    img {
      border-radius: 50%;
      width: 2.85vw;
      height: 2.85vw;
    }
  }
  .image-wrapper:hover {
    background: var(--primary-border-color);
  }
  @media (max-width: 768px) {
    .title {
      font-size: 5.8vw;
    }
    .title1 {
      font-size: 4.83vw;
    }
    .image-wrapper {
      width: 9.9vw;
      height: 9.9vw;
      img {
        width: 9.9vw;
        height: 9.9vw;
      }
    }
  }
}


.about-us {
  .title {
    width: 100%;
    text-align: center !important;
    font-size: 1.6vw;
  }
}
