import { Data } from './accordion.model';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { Router } from '@angular/router';
declare let gtag: Function;
@Component({
  selector: 'app-accordion',
  templateUrl: './accordion.component.html',
  styleUrls: ['./accordion.component.scss'],
})
export class AccordionComponent implements OnInit {
  @Input() data: Data;
  @Input() isMobile: boolean = false;
  @Input() padding: string = '4.17vw 0';
  @Input() addClass: string = '';
  @Input() type: string = '';
  @Output() scrollEvent = new EventEmitter<any>();

  faChevronDown = faChevronDown;
  faChevronUp = faChevronUp;

  constructor() {}

  ngOnInit(): void {}

  scroll(index, i): void {
    if (i === 1) {
      gtag('event', 'accordion_open', {
        event_category: 'accordion',
        event_label: 'open',
        route_from: window.location.href,
        accordion_title: this.data[0].name,
      });
    } else {
      gtag('event', 'accordion_close', {
        event_category: 'accordion',
        event_label: 'close',
        route_from: window.location.href,
        route_to: this.data[0].name,
      });
    }
    this.scrollEvent.emit({ index, action: i });
  }

  // scrollUp(): void {
  //   this.scrollUpEvent.emit(this.index);
  // }
}
