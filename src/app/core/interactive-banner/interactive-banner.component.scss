.content-wrapper {
  overflow: hidden;
  width: 100%;
  transition: height 2s ease-in-out;
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  left: 0;

  .banner-contents {
    width: 100%;
    height: 100vh;
    position: relative;
    .image-wrapper {
      img {
        width: 100%;
        height: 100vh;
        -o-object-fit: cover;
        object-fit: cover;
        transition: transform 2s,
          object-position 1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
        transition: transform 2s,
          object-position 1s cubic-bezier(0.895, 0.03, 0.685, 0.22),
          -o-object-position 1s cubic-bezier(0.895, 0.03, 0.685, 0.22);
      }
      // .bg-image {
      //   width: 100%;
      //   height: 100vh;
      //   background-size: cover;
      //   background-repeat: no-repeat;
      //   transition: transform 2s, background-position 2s;
      //   -webkit-filter: blur(8px);
      //   filter: blur(8px);
      // }
      // .image {
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   width: 100%;
      //   height: 100vh;
      //   img {
      //     width: 100%;
      //     height: auto;
      //   }
      // }
    }
    .text-container {
      position: absolute;
      background-color: #988d8dc9;
      color: var(--secondary-font-color);
      border-radius: 0.69vw;
      transition: all 1s;
      min-width: 35%;
      max-width: 60%;
      font-size: 1.8vw;
      p {
        margin-bottom: 0;
      }
      &.top {
        top: 5%;
        &.center {
          transform: translate(-50%, 0);
        }
      }
      &.middle {
        top: 50%;
        &.center {
          transform: translate(-50%, -50%);
        }
      }
      &.bottom {
        top: 95%;
        transform: translateY(-100%);
        &.center {
          transform: translate(-50%, -100%);
        }
      }
      &.left {
        left: 5%;
        &.middle {
          transform: translate(0, -50%);
        }
      }
      &.right {
        left: 95%;
        transform: translateX(-100%);
        &.middle {
          transform: translate(-100%, -50%);
        }
        &.bottom {
          transform: translate(-100%, -100%);
        }
      }
      &.center {
        left: 50%;
      }
    }
  }
}
.progress-bar {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  bottom: 0;
  height: 1vw;
  width: 100%;
  background-color: var(--quaternary-font-color);
  .progress-active {
    height: 100%;
    background-color: var(--tertiary-font-color);
  }
}
@media (max-width: 768px) {
  .text-container {
    font-size: 3.6vw !important;
    p {
      font-size: 3.6vw;
    }
  }
}
