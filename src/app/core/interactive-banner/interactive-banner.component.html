<div #interactiveBanner>
  <div [ngStyle]="{ height: bannerHeight }"></div>
  <div class="content-wrapper">
    <div class="banner-contents">
      <div class="image-wrapper">
        <img
          [src]="data?.tech_image_1"
          [ngStyle]="{
            maxHeight: bannerHeight,
            objectPosition: imageTranslateX + ' ' + imageTranslateY,
            transform: 'scale(' + imageScale + ')'
          }"
        />
      </div>

      <div
        class="text-container"
        [ngStyle]="{
          width: textWidth,
          height: textHeight,
          padding: textPadding
        }"
        [ngClass]="data?.tech_section[activeBanner]?.text_position"
      >
        <div [innerHTML]="textValue"></div>
      </div>
    </div>
  </div>
  <div *ngIf="isOnView" class="progress-bar">
    <div class="progress-active" [ngStyle]="{ width: progress + '%' }"></div>
  </div>
</div>
