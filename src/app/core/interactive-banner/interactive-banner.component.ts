import {
  Component,
  ElementRef,
  HostListener,
  Input,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { BannerDataModel } from 'src/app/pages/explore/models/exhibition.model';
@Component({
  selector: 'interactive-banner',
  templateUrl: './interactive-banner.component.html',
  styleUrls: ['./interactive-banner.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class InteractiveBannerComponent implements OnInit {
  @Input() data: BannerDataModel;
  @ViewChild('interactiveBanner') interactiveBanner: ElementRef;

  imageMarginTop = '0px';
  imageScale = 1;
  imageTranslateX = 'center';
  imageTranslateY = 'center';

  noOfSlides = 0;
  bannerHeight = '38vw';

  isOnView = false;
  imageWidth;
  imageHeight;

  textWidth = '0';
  textHeight = '0';
  textPadding = '0';

  progress = 0;
  activeBanner = -1;

  constructor() {}

  async ngOnInit() {
    await this.getMeta(this.data?.tech_image_1);

    this.noOfSlides = this.data.tech_section.length;
  }

  @HostListener('window:scroll', ['$event'])
  onScroll(event) {
    const bannerPoistionY =
      this.interactiveBanner.nativeElement.getBoundingClientRect().y;
    if (bannerPoistionY <= window.innerHeight / 4 && !this.isOnView) {
      this.isOnView = true;
      this.bannerHeight = String(this.noOfSlides * 30) + 'vh';
      window.scroll({
        top: window.scrollY + bannerPoistionY,
        left: 0,
        behavior: 'smooth',
      });
    }
    if (bannerPoistionY > window.innerHeight) {
      this.bannerHeight = '38vw';
      this.isOnView = false;
    }
    if (bannerPoistionY <= 0) {
      // this.imageMarginTop = String(-bannerPoistionY) + 'px';
      this.progress =
        (-bannerPoistionY / (this.noOfSlides * window.innerHeight * 0.3)) * 100;
    }

    this.activeBanner = Math.floor(
      -bannerPoistionY / (window.innerHeight * 0.3)
    );

    if (this.activeBanner >= 0 && this.activeBanner < this.noOfSlides) {
      const imageCenterX = this.imageWidth / 2;
      const imageCenterY = this.imageHeight / 2;

      const imageAspectRatio = imageCenterX / imageCenterY;
      const screenAspectRatio = window.innerWidth / window.innerHeight;
      const zoomLevel = Number(
        this.data?.tech_section?.[this.activeBanner]?.zoom_level
      );
      let scaleRatio;
      if (imageAspectRatio > screenAspectRatio) {
        scaleRatio = window.innerHeight / this.imageWidth;
      } else {
        scaleRatio = window.innerWidth / this.imageHeight;
      }
      this.textWidth = 'auto';
      this.textHeight = 'auto';
      this.textPadding = '1vw 2vw';
      this.imageScale = zoomLevel;
      const translateX = Math.floor(
        (imageCenterX -
          Number(
            this?.data?.tech_section?.[this.activeBanner]?.center_x_axis
          )) *
          scaleRatio
      );
      const translateY = Math.floor(
        (imageCenterY -
          Number(
            this?.data?.tech_section?.[this.activeBanner]?.center_y_axis
          )) *
          scaleRatio
      );
      let imageTranslateX, imageTranslateY;
      if (imageAspectRatio > screenAspectRatio) {
        imageTranslateX =
          translateX - (this.imageWidth * scaleRatio - window.innerWidth) / 2;
        imageTranslateY = translateY;
      } else {
        imageTranslateX = translateX;
        imageTranslateY =
          translateY - (this.imageHeight * scaleRatio - window.innerHeight) / 2;
      }
      const maximumX =
        (window.innerWidth / 2) * (1 - 1 / zoomLevel) - 5 * zoomLevel;

      const maximumY =
        (window.innerHeight / 2) * (1 - 1 / zoomLevel) - 5 * zoomLevel;

      const minimumX = -(
        this.imageWidth * scaleRatio -
        window.innerWidth +
        maximumX
      );

      const minimumY = -(
        this.imageHeight * scaleRatio -
        window.innerHeight +
        maximumY
      );
      if (imageTranslateX > maximumX) {
        this.imageTranslateX = String(maximumX) + 'px';
      } else if (imageTranslateX < minimumX) {
        this.imageTranslateX = String(minimumX) + 'px';
      } else {
        this.imageTranslateX = String(imageTranslateX) + 'px';
      }
      if (imageTranslateY > maximumY) {
        this.imageTranslateY = String(maximumY) + 'px';
      } else if (imageTranslateY < minimumY) {
        this.imageTranslateY = String(minimumY) + 'px';
      } else {
        this.imageTranslateY = String(imageTranslateY) + 'px';
      }
    } else {
      this.imageScale = 1;
      this.imageTranslateX = 'center';
      this.imageTranslateY = 'center';
      this.textPadding = '0';
      this.textWidth = '0';
      this.textHeight = '0';
    }
  }

  public get textValue(): string {
    return this.data?.tech_section?.[this.activeBanner]?.text_content;
  }
  async getMeta(url) {
    const img = new Image();
    return new Promise<boolean>((resolve, reject) => {
      // img.addEventListener('load', (e) => {
      //   this.imageWidth = (e.currentTarget as HTMLImageElement).naturalWidth;
      //   this.imageHeight = (e.currentTarget as HTMLImageElement).naturalHeight;
      //   console.log('hey');
      // });

      img.onload = (e) => {
        this.imageWidth = (e.currentTarget as HTMLImageElement).naturalWidth;
        this.imageHeight = (e.currentTarget as HTMLImageElement).naturalHeight;
        resolve(true);
      };
      img.onerror = (e) => {
        reject(e);
      };
      img.src = url;
    });
  }
}
