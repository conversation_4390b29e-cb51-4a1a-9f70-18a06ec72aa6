@-webkit-keyframes placeHolder<PERSON>low {
  0% {
    //background-position: -468px 0
    //opacity: 0.8;
    background: #eeeeee;
  }

  100% {
    //background-position: 468px 0
    //opacity: 1;
    background: #dddddd;
  }
}

@keyframes placeHolderGlow {
  0% {
    //background-position: -468px 0
    //opacity: 0.8;
    background: #eeeeee;
  }

  100% {
    //background-position: 468px 0
    //opacity: 1;
    background: #dddddd;
  }
}

.place-holder {
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-name: placeHolder<PERSON><PERSON>;
  animation-name: placeHolderGlow;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  background: #f6f7f8;
  background: linear-gradient(to right, #eeeeee 8%, #dddddd 18%, #eeeeee 33%);
  background-size: 1000px 104px;
  position: relative;
  overflow: hidden;
}
