export interface GalleriesForSlider {
  logo: string;
  artwork_title: string;
  medium: string;
  primary_image: string;
}

export enum SliderTypes {
  Artwork = 'Artwork',
  Artwork2 = 'Artwork2',
  Artwork1 = 'Artwork1',
  Gallery = 'Gallery',
  Artist = 'Artist',
  Artist1 = 'Artist1',
  Artist2 = 'Artist2',
  VideoSeries = 'VideoSeries',
  VideoSeries1 = 'VideoSeries1',
  VideoSeries2 = 'VideoSeries2',
  Following = 'Following',
  Prints = 'Prints',
  Exhibitions = 'Exhibitions',
  ArtResource = 'ArtResource',
  Basic = 'basic'
}
