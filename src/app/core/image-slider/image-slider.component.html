<div class="slider-wrapper" [ngClass]="[sliderContentClass]">
  <div class="slider-contents" #sliderContent (scroll)="scrollHandler($event)">
    <ng-container
      [ngTemplateOutlet]="
        {
          Artist: artist,
          Artwork: normal3,
          Artwork1: normal4,
          Artwork2: normal3,
          Gallery: normal,
          Artist1: normalArtist,
          Artist2: normal2,
          VideoSeries: artist,
          VideoSeries1: normalArtist,
          VideoSeries2: videoSlider,
          Following: following,
          Exhibitions: exhibitions,
          ArtResource: ArtResource,
          Basic: basic
        }[SliderType]
      "
    >
    </ng-container>
  </div>
  <div
    [ngClass]="{
      control: SliderType != 'Following',
      'control-follow': SliderType == 'Following',
      basic: SliderType == 'Basic'
    }"
    class="left-control image-icon"
    (click)="sliderLeft()"
    *ngIf="!isMobile"
  >
    <!-- <fa-icon [icon]="faChevronLeft"></fa-icon> -->
    <img src="assets/images/left-chevron.png" />
  </div>
  <div
    [ngClass]="{
      control: SliderType != 'Following',
      'control-follow': SliderType == 'Following',
      basic: SliderType == 'Basic'
    }"
    class="right-control image-icon"
    (click)="sliderRight()"
    *ngIf="!isMobile"
  >
    <img src="assets/images/right-chevron.png" />
  </div>
</div>
<ng-template #artist>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <card-box
      *ngIf="pageType !== 'Catalog'"
      [type]="'Card'"
      [cardData]="{
        routerLink: sliderItem.routerLink
          ? sliderItem.routerLink
          : sliderRouterLink(sliderItem.url_name),

        image: sliderItem.image,
        heading: sliderItem.heading,
        subheading: sliderItem.subheading
      }"
    ></card-box>
    <card-box
      *ngIf="pageType === 'Catalog'"
      [type]="'Catalog'"
      [cardData]="{
        routerLink: sliderItem.routerLink,
        routerParam: sliderItem.routerParam,
        title: sliderItem.title,
        image: sliderItem.image,
        heading: sliderItem.heading,
        subheading: sliderItem.subheading
      }"
    ></card-box>
  </div>
</ng-template>
<ng-template #normalArtist>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <div class="image-wrapper">
      <card-box
        *ngIf="pageType !== 'Catalog'"
        [type]="'Card'"
        [cardData]="{
          routerLink: sliderItem.routerLink,
          image: sliderItem.image,
          heading: sliderItem.heading,
          subheading: sliderItem.subheading
        }"
      ></card-box>
      <card-box
        *ngIf="pageType === 'Catalog'"
        [type]="'Catalog'"
        [cardData]="{
          routerLink: sliderItem.routerLink,
          image: sliderItem.image,
          heading: sliderItem.heading,
          subheading: sliderItem.subheading
        }"
      ></card-box>
    </div>
    <div
      class="slider-head-main"
      *ngIf="sliderData.length > 1 && pageType === 'video-series' && isMobile"
    >
      <div class="slider-heads">
        <img (click)="sliderLeft()" src="assets/images/back-chevron.png" />
        <img (click)="sliderRight()" src="assets/icons/front-chevron.png" />
      </div>
    </div>
  </div>
</ng-template>
<ng-template #normal>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <div class="image-wrapper">
      <a>
        <img [src]="sliderItem.primary_image" />
      </a>
    </div>
    <div class="bottom-details">
      <div class="d-flex align-items-center logo-wrapper">
        <img
          *ngIf="SliderType === 'Gallery'"
          class="logo"
          [src]="sliderItem.logo"
        />
      </div>

      <h3 class="title">
        {{ sliderItem.artwork_title }}
      </h3>
      <span class="medium">
        {{ sliderItem?.medium }}
      </span>
    </div>
  </div>
</ng-template>
<ng-template #basic>
  <div *ngFor="let sliderItem of sliderData" class="slider-item-basic">
    <div class="image-wrapper">
      <a [routerLink]="sliderItem.url">
        <img [src]="sliderItem.primary_image" />
      </a>
    </div>
    <div class="bottom-details">
      <!-- <div class="d-flex align-items-center logo-wrapper">
        <img
          *ngIf="SliderType === 'Gallery'"
          class="logo"
          [src]="sliderItem.logo"
        />
      </div> -->

      <a [routerLink]="sliderItem.url">
        <div class="title">
          {{ sliderItem.artwork_title }}
        </div></a
      >
      <span class="medium" style="color: #808080">
        by {{ sliderItem?.display_name }}
      </span>
    </div>
  </div>
</ng-template>

<ng-template #normal2>
  <div *ngFor="let sliderItem of sliderData" class="slider-item artist2">
    <div class="image-wrapper">
      <a [routerLink]="sliderRouterLink(sliderItem.id)">
        <img
          (click)="filterMessage(sliderItem)"
          [src]="
            'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://register.terrain.art/artist-portal/assets/' +
            sliderItem.image +
            '?key=website-cards'
          "
        />
      </a>
    </div>
    <div class="bottom-details">
      <h3 class="title">
        <i>
          {{ sliderItem.name }}
        </i>
      </h3>
      <h3 class="subtitle">
        {{
          sliderItem.count === 1
            ? sliderItem.count + " artist"
            : sliderItem.count + " artists"
        }}
      </h3>
    </div>
  </div>
</ng-template>
<ng-template #normal3>
  <div
    #slider
    *ngFor="let sliderItem of sliderData; let i = index"
    class="slider-item"
  >
    <div
      (mouseover)="!isMobile && onHover(i)"
      (mouseout)="!isMobile && onHoverOut(i)"
      class="image-wrapper"
    >
      <a
        [routerLink]="
          sliderRouterLink(
            sliderItem?._id,
            sliderItem?.nft_id,
            sliderItem.artwork_title,
            sliderItem.year
          )
        "
      >
        <img
          class="hover-image"
          [src]="
            get_url_extension(sliderItem?.primary_image[0]?.url) != 'gif'
              ? 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                sliderItem?.primary_image[0]?.url
              : sliderItem?.primary_image[0]?.url
          "
          [ngStyle]="{ 'z-index': sliderState[i] ? '0' : '-1' }"
        />
        <img
          class="thumbnail"
          [src]="
            get_url_extension(sliderItem?.thumbnail_of_primary) != 'gif'
              ? 'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/' +
                sliderItem?.thumbnail_of_primary
              : sliderItem?.thumbnail_of_primary
          "
          [ngStyle]="{ 'z-index': sliderState[i] ? '-1' : '0' }"
        />
      </a>
      <div
        class="banner-chip"
        *ngIf="
          sliderItem?.sale_options === 'NotForSale' ||
          sliderItem?.sale_options === 'ReturnedToArtist' ||
          sliderItem?.sale_options === 'OnLoan' ||
          sliderItem?.sale_options === 'Sold' ||
          sliderItem?.sale_options === 'Reserved'
        "
      >
        {{
          sliderItem?.sale_options === "NotForSale" ||
          sliderItem?.sale_options === "ReturnedToArtist" ||
          sliderItem?.sale_options === "OnLoan"
            ? "Not For Sale"
            : sliderItem?.sale_options
        }}
      </div>
      <div class="banner-chip" *ngIf="sliderItem?.payment_modes === 'amex'">
        Reserved
      </div>
    </div>
    <div class="bottom-details">
      <div class="d-flex align-items-center logo-wrapper">
        <img
          *ngIf="SliderType === 'Gallery'"
          class="logo"
          [src]="sliderItem?.logo"
        />
      </div>

      <div class="title">
        <h3>
          <ng-container
            *ngIf="
              !isMobile
                ? sliderItem.artwork_title.length <= 50
                : sliderItem.artwork_title.length <= 25
            "
            >{{ sliderItem.artwork_title }}</ng-container
          >

          <abbr
            *ngIf="
              !isMobile
                ? sliderItem.artwork_title.length > 50
                : sliderItem.artwork_title.length > 25
            "
            [title]="sliderItem.artwork_title"
            >{{
              isMobile
                ? sliderItem.artwork_title.slice(0, 25)
                : sliderItem.artwork_title.slice(0, 50)
            }}...</abbr
          ><span
            *ngIf="SliderType === 'Artwork' || SliderType === 'Artwork2'"
            style="font-style: normal"
            >, {{ sliderItem?.year }}</span
          >
          <img
            *ngIf="sliderItem.artwork_group == 'curated'"
            src="assets/icons/tc badges_400px.png"
            alt="verified"
          />
          <img
            *ngIf="sliderItem.artwork_group == 'open'"
            src="assets/icons/To badges_400px.png"
            alt="verified"
          />
        </h3>
        <h3
          *ngIf="sliderItem?.artist_name?.first_name"
          style="font-style: normal"
        >
          {{
            sliderItem?.artist_name?.first_name +
              " " +
              sliderItem?.artist_name?.last_name
          }}
        </h3>
      </div>
      <span *ngIf="!isExhibition" class="medium">
        <ng-container
          *ngIf="
            !isMobile
              ? sliderItem.medium.length <= 68
              : sliderItem.medium.length <= 38
          "
        >
          {{ sliderItem?.medium }}
        </ng-container>
        <abbr
          *ngIf="
            !isMobile
              ? sliderItem.medium.length > 68
              : sliderItem.medium.length > 38
          "
          [title]="sliderItem.medium"
          >{{
            !isMobile
              ? sliderItem.medium.slice(0, 68)
              : sliderItem.medium.slice(0, 38)
          }}...</abbr
        >
      </span>
      <div
        *ngIf="
          SliderType === 'Artwork' ||
          (SliderType === 'Artwork2' && !isExhibition)
        "
        class="dimension"
      >
        <ng-container
          *ngIf="
            !isMobile
              ? sliderItem.dimensions.length <= 68
              : sliderItem.dimensions.length <= 38
          "
        >
          {{ sliderItem?.dimensions }}
        </ng-container>
        <abbr
          *ngIf="
            !isMobile
              ? sliderItem.dimensions.length > 68
              : sliderItem.dimensions.length > 38
          "
          [title]="sliderItem.dimensions"
          >{{
            !isMobile
              ? sliderItem.dimensions.slice(0, 68)
              : sliderItem.dimensions.slice(0, 38)
          }}...</abbr
        >
      </div>
      <!-- <p *ngIf="!isExhibition && sliderItem?.nft?.id" class="dimension">
        <ng-container>
          Nft Id:
          {{ nft_name(sliderItem?.nft?.network, sliderItem?.nft?.id) }}
          <abbr
            title="NFTs are minted on Ethereum test network, check out the FAQs for more information"
            ><span class="material-icons-outlined"> info </span></abbr
          >
        </ng-container>
      </p>
      <p *ngIf="!isExhibition && !sliderItem?.nft?.id">
        <ng-container> &nbsp; </ng-container>
      </p> -->
      <p
        *ngIf="
          SliderType === 'Artwork' ||
          (SliderType === 'Artwork2' && !isExhibition)
        "
        class="dimension"
      >
        <!-- price -->
        <ng-container
          *ngIf="
            sliderItem?.sale_options != 'NotForSale' || sliderItem?.sale_price
          "
        >
          ₹
          {{ sliderItem?.sale_price | number : "1.0" }}
          | $
          {{ sliderItem?.sale_price | forex : "USD" | async | number : "1.0" }}
        </ng-container>
        <ng-container
          *ngIf="
            sliderItem?.sale_options == 'NotForSale' && !sliderItem?.sale_price
          "
        >
          Price not available
        </ng-container>
      </p>

      <!-- <div
        *ngIf="
          sliderItem?.sale_options === 'ForSale' && !isExhibition && !isNFT
        "
        class="rounded-pill"
      >
        <a [routerLink]="['/explore/artworks/', sliderItem?.id, 'enquire']"
          >Enquire</a
        >
      </div> -->
      <div
        *ngIf="
          sliderItem?.sale_options === 'ForSale' &&
          sliderItem?.payment_modes !== 'amex' &&
          !isExhibition
        "
        class="rounded-pill"
        [ngClass]="{
          sold: isAddedToCart[i] || sliderItem?.sale_options == 'NotForSale'
        }"
      >
        <!-- <a
          (click)="
            sliderItem?.sale_options !== 'NotForSale' &&
              !isAddedToCart[i] &&
              addToCart(sliderItem?._id)
          "
          >{{ isAddedToCart[i] ? "Added to Cart" : "Add to Cart" }}</a
        > -->
        <a
          [href]="getWhatappLink(sliderItem)"
          >Enquire</a>
      </div>
    </div>
  </div>

  <div
    class="slider-head-main"
    *ngIf="sliderData.length > 1 && pageType === 'home' && isMobile"
  >
    <div class="slider-heads">
      <img (click)="sliderLeft()" src="assets/images/back-chevron.png" />
      <img (click)="sliderRight()" src="assets/icons/front-chevron.png" />
      <!-- <div
        *ngFor="let item of sliderData; let i = index"
        class="slider-head"
        [ngClass]="{ active: i === currentIndex }"
      ></div> -->
    </div>
  </div>
</ng-template>
<ng-template #normal4>
  <div *ngFor="let sliderItem of sliderData; let i = index" class="slider-item">
    <div
      class="image-wrapper"
      (mouseover)="!isMobile && onHover(i)"
      (mouseout)="!isMobile && onHoverOut(i)"
      *ngIf="sliderItem?.primary_image?.private_hash"
    >
      <a [routerLink]="sliderRouterLink(sliderItem?.id)">
        <img
          class="hover-image"
          [src]="
            'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://pricedb.terrain.art/pricedb/assets/' +
            sliderItem?.primary_image?.private_hash
          "
          [ngStyle]="{ 'z-index': sliderState[i] ? '0' : '-1' }"
        />
        <img
          class="thumbnail"
          [src]="
            'https://www.terrain.art/cdn-cgi/image/width=1000,quality=52/https://pricedb.terrain.art/pricedb/assets/' +
            sliderItem?.primary_image?.private_hash
          "
          [ngStyle]="{ 'z-index': sliderState[i] ? '-1' : '0' }"
        />
      </a>
    </div>
    <div class="bottom-details" *ngIf="sliderItem?.primary_image?.private_hash">
      <h3 class="title">
        {{ sliderItem?.artwork_title
        }}<span *ngIf="SliderType === 'Artwork1'" style="font-style: normal"
          >, {{ sliderItem?.year }}</span
        >
      </h3>
      <span class="medium">
        {{ sliderItem?.medium }}
      </span>
      <p *ngIf="SliderType === 'Artwork1'" class="dimension">
        {{ sliderItem?.dimensions }}
      </p>
    </div>
  </div>
  <div
    class="slider-head-main"
    *ngIf="sliderData.length > 1 && pageType === 'home' && isMobile"
  >
    <div class="slider-heads">
      <img (click)="sliderLeft()" src="assets/images/back-chevron.png" />
      <img (click)="sliderRight()" src="assets/icons/front-chevron.png" />
    </div>
  </div>
</ng-template>
<ng-template #videoSlider>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <div class="image-wrapper2">
      <single-slider
        *ngIf="sliderItem?.Video_URL"
        (clickEvent)="openVideo($event)"
        (click)="openVideo({ url: sliderItem?.Video_URL })"
        [enablePlay]="false"
        class="vimeo-player"
        [sliderData]="[{ url: sliderItem?.Video_URL, mediaType: 'VIDEO1' }]"
        [transform]="'scale(3, 3)'"
        [title]="sliderItem?.Video_Title"
        [showNavControls]="false"
        [iconOption]="1"
        [isMobile]="isMobile"
      ></single-slider>
    </div>
  </div>
</ng-template>

<ng-template #following>
  <div
    #slider
    *ngFor="let sliderItem of sliderData; let i = index"
    class="slider-item"
  >
    <div
      (mouseover)="onHover(i)"
      (mouseout)="onHoverOut(i)"
      class="image-wrapper"
    >
      <a [routerLink]="sliderRouterLink(sliderItem?.id)">
        <!-- <img
          class="hover-image"
          [src]="
            sliderItem?.image
          "
          [ngStyle]="{ 'z-index': sliderState[i] ? '0' : '-1' }"
        /> -->
        <img
          class="thumbnail"
          [src]="sliderItem?.image"
          [ngStyle]="{ 'z-index': sliderState[i] ? '-1' : '0' }"
        />
      </a>
    </div>
    <div class="bottom-details">
      <h3 class="title">
        <ng-container>{{ sliderItem.title }}</ng-container>
      </h3>
      <p class="dimension">
        <ng-container>
          {{ sliderItem?.subtitle }}
        </ng-container>
      </p>
    </div>
  </div>

  <div
    class="slider-head-main"
    *ngIf="sliderData.length > 1 && pageType === 'home' && isMobile"
  >
    <div class="slider-heads">
      <img (click)="sliderLeft()" src="assets/images/back-chevron.png" />
      <img (click)="sliderRight()" src="assets/icons/front-chevron.png" />
    </div>
  </div>
</ng-template>
<ng-template #exhibitions>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <div class="image-wrapper">
      <a [routerLink]="sliderItem.routerLink">
        <img [src]="sliderItem.image" />
      </a>
      <div class="tag">Exhibition feature</div>
    </div>
    <div class="bottom-details">
      <h3 class="title">
        {{ sliderItem.heading }}
      </h3>
      <span class="medium">
        {{ sliderItem?.subheading }}
      </span>
    </div>
  </div>
</ng-template>
<ng-template #ArtResource>
  <div *ngFor="let sliderItem of sliderData" class="slider-item">
    <a
      [routerLink]="sliderItem.routerLink"
      [queryParams]="{ selected: sliderItem.queryparam }"
    >
      <div class="image-wrapper">
        <img [src]="sliderItem.image" />
        <div class="gradient"></div>
        <div class="inner-content">
          <h1>{{ sliderItem.heading }}</h1>
        </div>
      </div>
    </a>
    <div class="bottom-details"></div>
  </div>
</ng-template>
