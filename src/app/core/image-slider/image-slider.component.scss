.slider-wrapper {
  position: relative;

  .slider-contents {
    overflow: hidden;
    scroll-behavior: smooth;

    @media (max-width: 768px) {
      overflow-x: scroll;
    }

    .slider-item {
      display: table-cell;
      padding-left: 0.65vw;
      padding-right: 1.5vw;

      &:first-child {
        padding-left: 3.2vw;
      }

      &:last-child {
        padding-right: var(--page-default-margin);
        // @media (max-width: 768px) {
        // }
      }

      .image-wrapper {
        position: relative;
        height: 28.13vw;
        width: 40vw;
        border-radius: 6px;
        overflow: hidden;
        background-color: var(--secondary-background-color);

        img:focus,
        img:active {
          border-radius: 3px;
          border-color: var(--tertiary-font-color);
          border-style: solid;
          border-width: 3px;
        }

        .gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 0.65vw;
          overflow: hidden;
          background: var(--card-linear-gradient);
        }

        @media (max-width: 768px) {
          width: 83.8vw;
          height: 58.69vw;
        }

        img {
          height: 100%;
          width: 100%;
          -o-object-fit: cover;
          object-fit: cover;
        }
      }

      .image-wrapper2 {
        width: 27.77vw;
        height: 36.5vw;
        border-radius: 6px;
        overflow: hidden;
        background-color: var(--secondary-background-color);

        @media (max-width: 768px) {
          height: 72.46vw;
          width: 72.46vw;
        }

        img {
          height: 100%;
          width: 100%;
          -o-object-fit: cover;
          object-fit: cover;
        }
      }

      .bottom-details {
        margin-top: 1.39vw;
        font-family: var(--secondary-font);
        color: var(--quaternary-font-color);
        font-weight: 300;

        // .medium {
        //   font-size: 1.11vw;
        //   @media (max-width: 768px) {
        //     font-size: 3.86vw;
        //   }
        // }
        .title {
          font-size: 1.25vw;
          color: var(--primary-font-color);
          font-style: italic;
          margin-bottom: 1.39vw;
          font-weight: 400;

          h3 {
            font-size: 1.25vw;
          }

          @media (max-width: 768px) {
            font-size: 4.83vw;
            margin-bottom: 4.83vw;

            h3 {
              font-size: 4.83vw;
            }
          }

          img {
            height: 1.388vw;
            width: 1.388vw;
            margin-left: 0.4vw;

            @media (max-width: 768px) {
              height: 4.388vw;
              width: 4.388vw;
              margin-left: 1vw;
            }
          }
        }

        .title.non-italic {
          font-style: normal;
        }

        .dimension {
          margin-top: 0.35vw;
        }

        @media (max-width: 768px) {
          font-size: 3.86vw;

          p {
            margin-bottom: 0;
          }

          .dimension {
            margin-top: 1.2vw;
          }
        }
      }

      card-box {
        display: block;
        width: 27.77vw;
        height: 36.5vw;

        @media (max-width: 768px) {
          // width: 100%;
        }
      }

      &.artist2 {
        .image-wrapper {
          height: 16.7vw;
          width: 27.8vw;

          img {
            height: 100%;
            width: 100%;
          }
        }

        .bottom-details {
          .title {
            font-style: italic;
            font-family: var(--secondary-font);
            margin-bottom: 0.69vw;
            font-size: 1.25vw;
          }

          .subtitle {
            font-style: normal;
            margin-bottom: 3.47vw;
            font-size: 1.11vw;
            font-family: var(--secondary-font);
            color: var(--quaternary-font-color);
          }
        }

        @media (max-width: 768px) {
          padding-left: 1.21vw;
          padding-right: 1.21vw;

          &:first-child {
            padding-left: var(--page-default-margin);
          }

          &:last-child {
            padding-right: var(--page-default-margin);
          }

          .image-wrapper {
            height: 28.99vw;
            width: 46.62vw;
          }

          .bottom-details {
            margin-top: 2.42vw;

            .title {
              margin-bottom: 2.42vw;
              font-size: 4.35vw;
            }

            .subtitle {
              font-style: normal;
              margin-bottom: 9.66vw;
              font-size: 3.87vw;
            }
          }
        }
      }
    }
  }

  .slider-contents::-webkit-scrollbar {
    display: none;
  }

  &.artist1 {
    // .slider-item {
    //   // padding-bottom: 0.69vw;
    // }
    .slider-contents {
      overflow-x: scroll;
      scroll-behavior: smooth;

      .slider-item {
        display: table-cell;
        padding-left: 0.56vw;
        padding-right: 0.56vw;

        &:first-child {
          padding-left: var(--page-default-margin);
        }

        .image-wrapper {
          height: 72.46vw;
          width: 72.46vw;

          img {
            height: 100%;
            width: 100%;
          }
        }

        card-box {
          display: block;
          height: 72.46vw;
          width: 72.46vw;
        }
      }
    }
  }

  &.gallery {
    .slider-contents {
      .slider-item {
        .image-wrapper {
          width: 33.51vw;
        }

        & > img {
          -o-object-fit: cover;
          object-fit: cover;
        }

        .logo-wrapper {
          height: 4vw;
          width: 17vw;
          margin-bottom: 1.39vw;

          .logo {
            max-width: 17vw;
            max-height: 4vw;
            height: auto;
          }
        }

        .bottom-details {
          .title {
            margin-bottom: 0.7vw;
          }
        }
      }
    }
  }

  &.exhibition {
    .slider-contents {
      .slider-item {
        .image-wrapper {
          height: 27.77vw;
          width: 27.77vw;
          position: relative;

          .tag {
            font-size: 0.97vw;
            position: absolute;
            background-color: #ffffffd0;
            border-radius: 1.11vw;
            top: 1.04vw;
            left: 1.04vw;
            padding: 0.56vw 0.69vw;
          }

          .inner-content {
            position: absolute;
            bottom: 3.5vw;
            width: 100%;
            padding-right: 3.47vw;
            padding-left: 3.47vw;
            color: var(--secondary-font-color);
            text-shadow: 0 2px 4px rgb(0 0 0 / 50%);
            text-align: center;

            h1 {
              font-size: 1.95vw;
              line-height: 2.1vw;
              margin-bottom: 1.05vw;
              font-weight: 500;
            }
          }

          @media (max-width: 768px) {
            height: 72.46vw;
            width: 72.46vw;

            .tag {
              font-size: 3.9vw;
              top: 2.4vw;
              border-radius: 3.4vw;
              left: 2.4vw;
              padding: 1.9vw 1.5vw;
            }

            .inner-content {
              bottom: 9.66vw;
              padding-right: 7.24vw;
              padding-left: 7.24vw;

              h1 {
                font-size: 5.79vw;
                margin-bottom: 4.83vw;
                line-height: normal;
              }
            }
          }
        }
      }
    }
  }

  .control {
    position: absolute;
    top: 12.855vw;
    color: var(--tertiary-font-color);
    cursor: pointer;

    &.left-control {
      left: 0.2vw;
    }
    &.left-control.basic {
      left: 0vw;
      @media (max-width: 768px) {
        top: 39.855vw;
      }
    }

    &.right-control {
      right: 0.2vw;
    }
    &.right-control.basic {
      right: 0vw;
      @media (max-width: 768px) {
        top: 39.855vw;
      }
    }
  }

  .control-follow {
    position: absolute;
    top: 4.2vw;
    color: var(--tertiary-font-color);
    cursor: pointer;

    &.left-control {
      left: 0;
    }

    &.right-control {
      right: -3.18vw;
    }
  }

  &.artist {
    .slider-item {
      padding-bottom: 0.69vw;
    }

    .control {
      top: 17.15vw !important;
    }
  }

  &.basic {
    .slider-contents {
      display: flex;
      gap: 2vw;

      .slider-item-basic {
        display: table-cell;
        padding-left: 0.56vw;
        padding-right: 0.56vw;

        &:first-child {
          //padding-left: var(--page-default-margin);
          padding-right: 3vw;
          @media (max-width: 768px) {
            padding-left: 6vw;
          }
        }

        &:last-child {
          padding-right: var(--page-default-margin);
          padding-right: 3vw;
          @media (max-width: 768px) {
            padding-left: 6vw;
          }
        }

        .image-wrapper {
          position: relative;
          // height: 28.13vw;
          // width: 40vw;
          height: 26vw;
          width: 26vw;
          border-radius: 3vw;
          overflow: hidden;
          background-color: var(--secondary-background-color);

          img:focus,
          img:active {
            border-radius: 3px;
            border-color: var(--tertiary-font-color);
            border-style: solid;
            border-width: 3px;
          }

          .gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 0.65vw;
            overflow: hidden;
            background: var(--card-linear-gradient);
          }

          @media (max-width: 768px) {
            width: 80vw;
            height: 80vw;
          }

          img {
            height: 100%;
            width: 100%;
            -o-object-fit: cover;
            object-fit: cover;
          }
        }

        .image-wrapper2 {
          width: 27.77vw;
          height: 36.5vw;
          border-radius: 6px;
          overflow: hidden;
          background-color: var(--secondary-background-color);

          @media (max-width: 768px) {
            height: 72.46vw;
            width: 72.46vw;
          }

          img {
            height: 100%;
            width: 100%;
            -o-object-fit: cover;
            object-fit: cover;
          }
        }

        .bottom-details {
          margin-top: 1.39vw;
          font-family: var(--primary-font);
          // color: var(--quaternary-font-color);
          font-size: 1.25vw;
          font-weight: 300;

          .title {
            // font-size: 1.25vw;
            color: var(--primary-font-color);
            //font-weight: 600;
            font-family: var(--primary-font);
            margin-bottom: 0.69vw;
            //font-size: 2vw;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            @supports (-webkit-line-clamp: 2) {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: initial;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }

            @media (max-width: 768px) {
              font-size: 4.83vw;
              margin-bottom: 1.83vw;

              h3 {
                font-size: 4.83vw;
              }
            }

            img {
              height: 1.388vw;
              width: 1.388vw;
              margin-left: 0.4vw;

              @media (max-width: 768px) {
                height: 4.388vw;
                width: 4.388vw;
                margin-left: 1vw;
              }
            }
          }

          .title.non-italic {
            font-style: normal;
          }

          .dimension {
            margin-top: 0.35vw;
          }

          @media (max-width: 768px) {
            font-size: 3.86vw;

            p {
              margin-bottom: 0;
            }

            .dimension {
              margin-top: 1.2vw;
            }
          }
        }

        card-box {
          display: block;
          width: 27.77vw;
          height: 36.5vw;

          @media (max-width: 768px) {
            // width: 100%;
          }
        }

        &.artist2 {
          .image-wrapper {
            height: 16.7vw;
            width: 27.8vw;

            img {
              height: 100%;
              width: 100%;
            }
          }

          .bottom-details {
            .title {
              font-style: italic;
              font-family: var(--secondary-font);
              margin-bottom: 0.69vw;
              font-size: 1.25vw;
            }

            .subtitle {
              font-style: normal;
              margin-bottom: 3.47vw;
              font-size: 1.11vw;
              font-family: var(--secondary-font);
              color: var(--quaternary-font-color);
            }
          }

          @media (max-width: 768px) {
            padding-left: 1.21vw;
            padding-right: 1.21vw;

            &:first-child {
              padding-left: var(--page-default-margin);
            }

            &:last-child {
              padding-right: var(--page-default-margin);
            }

            .image-wrapper {
              height: 28.99vw;
              width: 46.62vw;
            }

            .bottom-details {
              margin-top: 2.42vw;

              .title {
                margin-bottom: 2.42vw;
                font-size: 4.35vw;
              }

              .subtitle {
                font-style: normal;
                margin-bottom: 9.66vw;
                font-size: 3.87vw;
              }
            }
          }
        }
      }
    }
  }

  &.artwork {
    .slider-contents {
      .slider-item {
        .image-wrapper {
          a {
            position: relative;

            img {
              position: absolute;
              height: 28.13vw;
              width: 40vw;

              @media (max-width: 768px) {
                width: 83.8vw;
                height: 58.69vw;
              }

              &.hover-image {
                -o-object-fit: contain;
                object-fit: contain;
                padding: 1vw;
                background-color: var(--secondary-background-color);
              }
            }

            // &:hover {
            //   img {

            //   }
            // }
          }
        }
      }
    }
  }

  &.follow {
    .slider-contents {
      .slider-item {
        .image-wrapper {
          width: 14.93vw;
          height: 10.69vw;

          a {
            position: relative;

            img {
              position: absolute;
              width: 14.93vw;
              height: 10.69vw;
            }
          }
        }

        .bottom-details {
          margin-top: 1.39vw;
          font-family: var(--secondary-font);
          color: var(--quaternary-font-color);
          font-weight: 300;

          // .medium {
          //   font-size: 1.11vw;
          //   @media (max-width: 768px) {
          //     font-size: 3.86vw;
          //   }
          // }
          .title {
            font-size: 1.25vw;
            color: var(--primary-font-color);
            font-style: italic;
            margin-bottom: 1.39vw;
            font-weight: 400;

            @media (max-width: 768px) {
              font-size: 4.83vw;
              margin-bottom: 4.83vw;
            }
          }

          .dimension {
            margin-top: 0.35vw;
          }

          @media (max-width: 768px) {
            font-size: 3.86vw;

            p {
              margin-bottom: 0;
            }

            .dimension {
              margin-top: 1.2vw;
            }
          }
        }

        @media (max-width: 768px) {
          padding-right: 1.21vw;

          .image-wrapper {
            width: 42.75vw;
            height: 30.68vw;

            a {
              position: relative;

              img {
                position: absolute;
                width: 42.75vw;
                height: 30.68vw;
              }
            }
          }

          .bottom-details {
            margin-top: 4.84vw;

            .title {
              font-size: 4.35vw;
              margin-bottom: 4.84vw;
            }

            .dimension {
              font-size: 3.87vw;
              margin-top: 0vw;
            }
          }
        }

        &:first-child {
          padding-left: 3.88vw;
        }
      }
    }
  }

  &.artist2 {
    .control {
      top: 7.22vw !important;
    }
  }
}

img.normal-state {
  padding: 0;
  -o-object-fit: cover;
  object-fit: cover;
}

.rounded-pill {
  margin-bottom: 1vw;
  margin-top: 1.5vw;
  display: flex;

  a {
    font-family: var(--primary-font);
    text-align: center;
    cursor: pointer;
    color: var(--tertiary-font-color);
    font-size: 1vw;
    vertical-align: middle;
    color: var(--tertiary-font-color);
    padding: 0.55vw 1.69vw;
    background-color: transparent;
    margin-right: 0.8vw;
    border-radius: 2.08vw;
    border-color: var(--tertiary-font-color);
    border-style: solid;
    border-width: 1px;

    &:hover {
      text-decoration: underline;
    }
  }

  &.sold {
    a {
      border-color: var(--primary-border-color);
      background-color: var(--primary-border-color);
      cursor: not-allowed;
      color: var(--quaternary-font-color);
    }
  }

  @media (max-width: 768px) {
    margin-top: 2.42vw;

    a {
      font-size: 3.47vw;
      padding: 2.3vw 2.85vw;
      border-radius: 7vw;
      font-weight: normal;
    }
  }
}

.slider-head-main {
  height: 13.77vw;

  .slider-heads {
    position: absolute;
    bottom: 0;
    // margin-top: 10.87vw;
    left: 50%;
    transform: translate(-50%, 0);
    display: flex;

    .slider-head {
      display: inline-block;
      margin: 0 1.25vw;
      width: 2.17vw;
      height: 2.17vw;
      background-color: var(--slider-color);
      border-radius: 50%;
      align-self: center;

      &.active {
        height: 2.9vw;
        width: 2.9vw;
      }
    }

    img {
      cursor: pointer;
      min-width: 8vw;
      max-width: 8vw;

      &:first-child {
        margin-right: 13vw;
      }
    }

    // @media (max-width: 768px) {
    //   bottom: 23.42vw;
    //   .slider-head {
    //     width: 2.17vw;
    //     height: 2.17vw;
    //     border-radius: 2.17vw;
    //     &.active {
    //       height: 2.89vw;
    //       width: 2.89vw;
    //       border-radius: 2.89vw;
    //     }
    //   }
    // }
  }
}

abbr {
  cursor: context-menu;
  text-decoration: none;
}

.banner-chip {
  position: absolute;
  top: 1.2vw;
  left: 1.04vw;
  padding: 0.69vw;
  min-width: 4.8vw;
  background: var(--chip-transparent-bg);
  font-size: 0.97vw;
  height: 2.36vw;
  text-align: center;
  border-radius: 1.8vw;

  @media (max-width: 768px) {
    position: absolute;
    top: 2.8vw;
    left: 2.42vw;
    padding: 1vw 2.42vw;
    width: auto;
    height: auto;
    background: var(--chip-transparent-bg);
    font-size: 2.9vw;
    text-align: center;
    border-radius: 2.4vw;
  }
}
