import { ArtistInfoModel } from "./../../pages/explore/artists/models/artist-list.model";
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from "@angular/core";
import { GalleriesForSlider, SliderTypes } from "./image-slider.model";
import {
  faChevronRight,
  faChevronLeft,
} from "@fortawesome/free-solid-svg-icons";
import { Artwork } from "src/app/pages/explore/artists/models/artwork.model";
import { CardBoxItem } from "../card-box/card-box.model";
import { Observable } from "rxjs";
import { CartService } from "src/app/services/cart.service";
import { DomSanitizer } from "@angular/platform-browser";

@Component({
  selector: "image-slider",
  templateUrl: "./image-slider.component.html",
  styleUrls: ["./image-slider.component.scss"],
})
export class ImageSliderComponent implements OnInit, OnChanges {
  @Input() SliderType: SliderTypes = SliderTypes.Artwork;
  _sliderData:
    | Artwork[]
    | ArtistInfoModel[]
    | GalleriesForSlider[]
    | CardBoxItem[];
  @Input() directusUrl = false;
  @Input() pageType: string = "";
  @Input() isMobile: boolean = false;

  @Input() isNFT: boolean = false;
  @Input() isExhibition = false;

  @Input() artistPath: string = "";

  @Input() set sliderData(value:
    | Artwork[]
    | ArtistInfoModel[]
    | GalleriesForSlider[]
    | CardBoxItem[]) {
    if (value) {
      this._sliderData = value;
    } else {
      this._sliderData = [];
    }
  }
  @Output() playEvent = new EventEmitter<any>();
  scrollHandler($event) {
    let scrollValue = ($event.target.scrollLeft / window.innerWidth) * 100;
    scrollValue = scrollValue + 30.18;
    const width = this.SliderType === "VideoSeries1" ? 72.5 : 83.8;

    if (
      (this.pageType === "home" || this.pageType === "video-series") &&
      this.isMobile
    ) {
      this.currentIndex = Math.ceil(scrollValue / width) - 1;
    }
  }

  get sliderData():
    | Artwork[]
    | ArtistInfoModel[]
    | GalleriesForSlider[]
    | CardBoxItem[] {
    return this._sliderData;
  }

  @Output() filterEmit = new EventEmitter();

  @ViewChild("sliderContent") sliderContent: ElementRef;

  faChevronRight = faChevronRight;
  faChevronLeft = faChevronLeft;
  sliderState: boolean[];
  currentIndex = 0;
  isAddedToCart = Array(20).fill(false);
  constructor(
    private cartService: CartService,
    private sanitized: DomSanitizer,
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["sliderData"]) {
      setTimeout(() => {
        this.isArtworkOnCart();
      }, 1000);
    }
  }

  ngOnInit(): void {
    console.log(this.sliderData);

    this.sliderState = Array(20).fill(false);
  }

  sliderLeft(): void {
    const currentScrollPos = this.sliderContent.nativeElement.scrollLeft;
    this.sliderContent.nativeElement.scrollLeft =
      currentScrollPos - this.slidingAMount;
  }
  sliderRight(): void {
    const currentScrollPos = this.sliderContent.nativeElement.scrollLeft;
    this.sliderContent.nativeElement.scrollLeft =
      currentScrollPos + this.slidingAMount;
  }

  private get slidingAMount(): number {
    const width = window.innerWidth;
    return width * 0.8224;
  }

  public get sliderContentClass(): string {
    switch (this.SliderType) {
      case SliderTypes.Artwork:
        return "artwork";
      case SliderTypes.Gallery:
        return "gallery";
      case SliderTypes.Artist:
        return "artist";
      case SliderTypes.Artist1:
        return "artist1";
      case SliderTypes.Artist2:
        return "artist2";
      case SliderTypes.VideoSeries:
        return "artist";
      case SliderTypes.VideoSeries1:
        return "artist1";
      case SliderTypes.VideoSeries2:
        return "artist";
      case SliderTypes.Exhibitions:
        return "exhibition";
      case SliderTypes.ArtResource:
        return "exhibition";
      case SliderTypes.Following:
        return "follow";
      case SliderTypes.Basic:
        return "basic";
      default:
        return "basic";
    }
  }

  public sliderRouterLink(
    url,
    nft = null,
    title = "",
    year = "",
  ): Array<string> {
    switch (this.SliderType) {
      case SliderTypes.Artwork:
        if (this.isExhibition) {
          return ["/discover/artworks/", url, "viewing-room"];
        } else if (this.isNFT) {
          return [
            "/discover/masters-on-terrain/lalu-prasad-shaw/artworks/",
            nft,
          ];
        } else {
          return [
            "/discover/artists",
            this.artistPath,
            "artworks",
            this.trimSpaces(title) + "-" + year + "-" + url,
          ];
        }
      case SliderTypes.Artwork2:
        return [
          "/discover/artists",
          this.artistPath,
          "artworks",
          this.trimSpaces(title) + "-" + year + "-" + url,
        ];

      case SliderTypes.Gallery:
        return null;
      case SliderTypes.Artist:
        return ["/discover/artists/", url];
      case SliderTypes.VideoSeries:
        return ["/discover/education-videos/", url];
      default:
        return null;
    }
  }

  filterMessage(value): void {
    this.filterEmit.emit({ name: value.name });
  }
  public onHover(index) {
    this.sliderState[index] = true;
  }

  public onHoverOut(index) {
    this.sliderState[index] = false;
  }
  openVideo(event) {
    console.log(
      "TCL ~ file: image-slider.component.ts ~ line 157 ~ ImageSliderComponent ~ openVideo ~ event",
      event,
    );
    this.playEvent.emit(event);
  }
  addToCart(id) {
    this.cartService.addCartItem(id).subscribe();
  }

  public isArtworkOnCart() {
    this.cartService.cartItems$.subscribe((data) => {
      this.sliderData.forEach((element, i) => {
        if (
          data?.data?.artwork_id?.find((a) => {
            return a._id === element?._id;
          })
        ) {
          this.isAddedToCart[i] = true;
        } else {
          this.isAddedToCart[i] = false;
        }
      });
    });
    return;
  }

  // nextItem() {
  //   if (this.currentIndex < this.sliderData.length) {
  //     this.currentIndex++;
  //   }
  //   const width = this.SliderType === 'VideoSeries1' ? 72.5 : 83.8;

  //   let scrollValue = ($event.target.scrollLeft / window.innerWidth) * 100;
  //   scrollValue = scrollValue + 30.18;

  //   if (
  //     (this.pageType === 'home' || this.pageType === 'video-series') &&
  //     this.isMobile
  //   ) {
  //     this.currentIndex = Math.ceil(scrollValue / width) - 1;
  //   }
  // }
  // prevItem() {
  //   if (this.currentIndex > 0) {
  //     this.currentIndex--;
  //   }
  // }
  public trimSpaces(str) {
    let a = str.trim().replace("(", "_").replace(")", "_");
    let b = a.split(" ");
    return encodeURIComponent(b.join("_"));
  }

  get_url_extension(url) {
    return url?.split(/[#?]/)[0].split(".").pop().trim();
  }
  public nft_name(network, id): string {
    switch (network) {
      case "1":
        return `TRA_ETH_${id}`;
      case "5":
        return `TRA_GOERLI_${id}`;
      case "137":
        return `TRA_MATIC_${id}`;
      case "80001":
        return `TRA_MUMBAI_${id}`;

      default:
        return ``;
    }
  }
  getWhatappLink(sliderItem) {
    return this.sanitized.bypassSecurityTrustUrl(
      `whatsapp://send?text=I would like to know more about ${encodeURIComponent(
        sliderItem?.artwork_title,
      )}, ${encodeURIComponent(sliderItem?.year)} by ${encodeURIComponent(
        sliderItem?.artist_id?.display_name,
      )}. ${encodeURIComponent(window.location.href)}&phone=+919220522788`,
    );
  }
}
