import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ImageSliderComponent } from './image-slider.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { CardBoxModule } from '../card-box/card-box.module';
import { RouterModule } from '@angular/router';
import { SingleSliderModule } from '../single-slider/single-slider.module';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [ImageSliderComponent],
  imports: [SharedModule, CardBoxModule, RouterModule, SingleSliderModule],
  exports: [ImageSliderComponent],
})
export class ImageSliderModule {}
