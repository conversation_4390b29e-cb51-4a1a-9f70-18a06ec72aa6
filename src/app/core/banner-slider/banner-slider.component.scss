.section-inner {
  &.full-banner {
    overflow: hidden;
    img {
      width: 100vw;
      max-width: 100%;
      height: 38.19vw;
      -o-object-fit: cover;
      object-fit: cover;
    }
    .imageType {
      height: 17.08vw;
    }
    @media (max-width: 768px) {
      height: auto;
      img {
        height: 131.4vw;
      }
      .imageType {
        height: 66.9vw;
      }
    }
  }
}
.banner-text {
  &.full-banner {
    padding-left: 6vw;
    padding-right: 6vw;
    position: absolute;
    bottom: 6.73vw;
    left: 0;
    width: 100%;
    color: var(--secondary-font-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    img {
      height: 2.138vw;
      width: 2.138vw;
      margin-bottom: 1.666vw;
    }
    h1 {
      font-size: 2.36vw;
      margin-bottom: 1.04vw;
    }
    & > p {
      font-family: var(--secondary-font);
    }
    @media (max-width: 768px) {
      padding-left: 9.54vw;
      padding-right: 9.54vw;
      bottom: 10.14vw;
      img {
        height: 7.439vw;
        width: 7.439vw;
        margin-bottom: 9.661vw;
      }
      h1 {
        font-size: 5.8vw;
        margin-bottom: 7.25vw;
      }
    }
  }
}
.control {
  position: absolute;
  top: calc((100% - 2.5vw) / 2);
  color: var(--tertiary-font-color);
  cursor: pointer;

  &.left-control {
    left: 2.1vw;
  }

  &.right-control {
    right: 2.1vw;
  }

  &.disabled {
    cursor: default;
    color: var(--secondary-font-color);
  }
}
.slider-heads {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  display: flex;
  .slider-head {
    display: inline-block;
    margin: 0 1.25vw;
    width: 0.64vw;
    height: 0.64vw;
    background-color: var(--secondary-background-color);
    border-radius: 0.64vw;
    align-self: center;
    &.active {
      height: 0.84vw;
      width: 0.84vw;
      border-radius: 0.84vw;
    }
  }
  @media (max-width: 768px) {
    bottom: 23.42vw;
    .slider-head {
      width: 2.17vw;
      height: 2.17vw;
      border-radius: 2.17vw;
      &.active {
        height: 2.89vw;
        width: 2.89vw;
        border-radius: 2.89vw;
      }
    }
  }
}

.slider-bottom {
  bottom: 2.08vw;
  @media (max-width: 768px) {
    bottom: 7.25vw;
  }
}
.slider-bottom1 {
  bottom: 2.08vw;
  @media (max-width: 768px) {
    bottom: 23.42vw;
  }
}
.section-inner {
  &.top-banner {
    background-color: #d9dade;
  }
  .banner-wrapper {
    height: 38.19vw;
    @media (max-width: 768px) {
      height: 131vw;
      flex-wrap: wrap;
      align-items: start;
      .w-50 {
        width: 100% !important;
      }
      .banner-text {
        font-size: 5.797vw !important;
        height: 74.87vw !important;
        padding: 0 !important;
        & > p {
          margin: 0;
          line-height: 1.25;
          padding: 13.28vw 6.03vw 0 5.797vw !important;
          p {
            padding: 0;
            margin: 0;
          }
        }
      }
    }
    .image-wrapper {
      overflow: hidden;
      img {
        width: 50vw;
        height: 100%;
        -o-object-fit: cover;
        object-fit: cover;
      }
      .bottom-text {
        background-color: #988d8d;
        border-radius: 1.18vw;
        padding: 0.2vw 0.6vw;
        opacity: 0.6;
        bottom: 5vw;
        right: 2.77vw;
        font-size: 1.3vw;
        color: var(--secondary-font-color);
      }
      @media (max-width: 768px) {
        img {
          width: 100vw !important;
          height: 56.28vw !important;
        }
        .bottom-text {
          margin-left: 5.797vw;
          bottom: 10vw;
          right: 5.79vw;
          font-size: 3.38vw;
          border-radius: 3vw;
          padding: 1.2vw 2.6vw;
          display: inline-table;
        }
      }
    }
    .banner-text {
      font-weight: 300;
      color: var(--primary-font-color);
      font-size: 2.36vw;
      line-height: 1.32;
      padding-left: var(--page-default-margin);
      padding-right: 0.55vw;
      // & > p {
      //   //padding: 0 1.04vw 6.25vw 0;
      // }
      a {
        color: var(--primary-font-color);
        text-decoration: underline;
      }
      // fa-icon {
      //   display: block;
      //   margin-top: 6.45vw;
      // }
      .arrow-img {
        height: 0.69vw;
        width: auto;
        margin-top: 6.45vw;
      }
      .banner-bottom-text {
        position: absolute;
        bottom: 5vw;
        right: 1vw;
        font-size: 1.3vw;
        margin-right: 50vw;
        @media (max-width: 768px) {
          bottom: 70vw;
          right: 5.79vw;
          margin-right: 0vw;
          font-size: 3.38vw;
          padding-left: 5.797vw;
        }
      }
    }
  }
}
.enhanced {
  .slider-heads {
    left: 7.22vw;
    transform: translate(0%, 0);
  }
  .section-inner {
    overflow: hidden;
    .enhanced-img {
      height: 38.19vw;
      width: 100%;
      -o-object-fit: cover;
      object-fit: cover;
    }
    .banner-wrapper {
      // -webkit-backdrop-filter: blur(15px);
      // backdrop-filter: blur(15px);
      width: 50vw;
      .banner-text {
        text-shadow: 0 2px 4px rgb(255 255 255 / 50%);
      }
    }
  }
  @media (max-width: 768px) {
    .slider-heads {
      left: 50%;
      transform: translate(-50%, 0);
    }
    .section-inner {
      .banner-wrapper {
        // -webkit-backdrop-filter: blur(15px);
        // backdrop-filter: blur(15px);
        width: 100%;
        height: 74.87vw;
        .banner-text {
          height: 74.87vw;
          & > p {
            padding: 0vw 6.03vw 0 5.797vw !important;
          }
        }
      }
      .enhanced-img {
        height: 131vw;
        width: 100%;
        -o-object-fit: cover;
        object-fit: cover;
      }
    }
  }
}
.Tshadow {
  .heading {
    text-shadow: 2px 2px #363535;
    @media (max-width: 768px) {
      text-shadow: 1px 1px #363535;
    }
  }
}
