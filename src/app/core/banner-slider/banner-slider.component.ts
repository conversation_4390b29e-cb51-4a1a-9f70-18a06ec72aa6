import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';
import { BannerSliderItem } from './banner-slider.model';

@Component({
  selector: 'banner-slider',
  templateUrl: './banner-slider.component.html',
  styleUrls: ['./banner-slider.component.scss'],
  animations: [
    trigger('fade', [
      state('in', style({ opacity: '1' })),
      state('out', style({ opacity: '0.6' })),
      transition('* <=> *', [animate(500)]),
    ]),
  ],
  encapsulation: ViewEncapsulation.None,
})
export class BannerSliderComponent implements OnInit {
// , transform: 'scale(1, 1)'
// , transform: 'scale(1.5, 1.5)'
// 
  @Input() sliderData: BannerSliderItem[];
  @Input() bannerType = 1;
  @Input() autoMate = false;
  @Input() isMobile = false;

  @Input() typeValue = 0;

  @Input() pageLoc: string;

  state = 'in';
  selectedSlide = 0;
  selecingSlide = 0;
  enableAnimation = false;
  counter = 0;

  faChevronLeft = faChevronLeft;
  faChevronRight = faChevronRight;
  constructor(private router: Router) {}

  ngOnInit(): void {
    if (this.autoMate) {
      setInterval(() => {
        if (this.selectedSlide === this.sliderData.length - 1) {
          this.showSlide(0);
        } else {
          this.sliderRight();
        }
      }, 6000);
    }
  }

  public onDone($event): void {
    if (this.enableAnimation) {
      if (this.counter === 1) {
        this.selectedSlide = this.selecingSlide;
      }
      this.toggleState();
    }
  }
  private toggleState(): void {
    if (this.counter < 2) {
      this.state = this.state === 'in' ? 'out' : 'in';
      this.counter++;
    }
  }
  public sliderLeft(): void {
    this.counter = 0;
    this.enableAnimation = true;
    this.selecingSlide = this.selectedSlide > 0 ? this.selectedSlide - 1 : 0;
    this.toggleState();
  }
  public sliderRight(): void {
    this.counter = 0;
    this.enableAnimation = true;
    this.selecingSlide =
      this.selectedSlide < this.sliderData.length - 1
        ? this.selectedSlide + 1
        : this.sliderData.length - 1;
    this.toggleState();
  }
  public showSlide(index: number): void {
    this.counter = 0;
    this.enableAnimation = true;
    this.selecingSlide = index;
    this.toggleState();
  }
  processLinks(e) {
    const element: HTMLElement = e.target;
    if (element.nodeName === 'A') {
      e.preventDefault();
      const link = element.getAttribute('href');
      this.router.navigate([link]);
    }
  }
}
