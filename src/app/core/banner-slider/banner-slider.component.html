<div class="section" [ngClass]="bannerType === 3 ? 'enhanced' : ''">
  <div class="section-inner full-banner" *ngIf="bannerType === 1||4">
    <img
      [@fade]="state"
      (@fade.done)="onDone($event)"
      [src]="sliderData[selectedSlide]?.image"
      [ngClass]="typeValue === 1 ? 'imageType' : ''"
    />
  </div>
  
  <div
    class="section-inner top-banner"
    *ngIf="bannerType === 2"
    [ngStyle]="{
      'background-color': sliderData[selectedSlide]?.backgroundColor
    }"
  >
    <div class="banner-wrapper d-flex">
      <div class="w-50 h100 align-self-center banner-text">
        <p
          class=""
          [innerHTML]="sliderData[selectedSlide]?.heading"
          (click)="processLinks($event)"
        ></p>
        <div
          [innerHTML]="sliderData[selectedSlide]?.bottomLeftText"
          class="banner-bottom-text"
        ></div>
      </div>
      <div class="w-50 h100 position-relative image-wrapper">
        <img
          [@fade]="state"
          (@fade.done)="onDone($event)"
          [src]="sliderData[selectedSlide]?.image"
        />
        <!-- <div
          [innerHTML]="sliderData[selectedSlide]?.bottomLeftText"
          class="bottom-text position-absolute"
        ></div> -->
      </div>
    </div>
  </div>
  <div
    class="section-inner top-banner"
    *ngIf="bannerType === 3"
    [ngStyle]="{
      'background-color': sliderData[selectedSlide]?.backgroundColor
    }"
  >
    <img
      class="enhanced-img"
      [@fade]="state"
      (@fade.done)="onDone($event)"
      [src]="sliderData[selectedSlide]?.image"
    />
    <div
      class="banner-wrapper d-flex"
      style="position: absolute; top: 0; left: 0"
    >
      <div
        class="
          w-100
          d-flex
          justify-content-center
          align-self-center
          banner-text
        "
        style="flex-direction: column"
      >
        <p
          class=""
          [innerHTML]="sliderData[selectedSlide]?.heading"
          (click)="processLinks($event)"
        ></p>
      </div>
    </div>
  </div>
  <div class="banner-text text-center full-banner Tshadow" *ngIf="bannerType === 1">
    <!-- <img class="AR-Icon" src="../../../assets/icons/<EMAIL>" alt="AR" > -->
    <h1 class="heading">{{ sliderData[selectedSlide]?.heading }}</h1>
    <p >{{ sliderData[selectedSlide]?.subheading }}</p>
  </div>
  <div class="banner-text text-center full-banner" *ngIf="bannerType === 4">
    <!-- <img class="AR-Icon" src="../../../assets/icons/<EMAIL>" alt="AR" > -->
    <h1>{{ sliderData[selectedSlide]?.heading }}</h1>
    <p>{{ sliderData[selectedSlide]?.subheading }}</p>
  </div>
  <!-- <div class="control left-control rounded-icon" (click)="sliderLeft()">
    <fa-icon [icon]="faChevronLeft"></fa-icon>
  </div>
  <div class="control right-control rounded-icon" (click)="sliderRight()">
    <fa-icon [icon]="faChevronRight"></fa-icon>
  </div> -->
  <div
    *ngIf="sliderData.length > 1"
    class="slider-heads"
    [ngClass]="bannerType === 1 ? 'slider-bottom' : 'slider-bottom1'"
  >
    <div
      *ngFor="let item of sliderData; let i = index"
      class="slider-head"
      (click)="showSlide(i)"
      [ngClass]="{ active: i === selectedSlide }"
    ></div>
  </div>
</div>
