import { faToggleOn, faToggleOff } from '@fortawesome/free-solid-svg-icons';
import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import * as BABYLON from 'babylonjs';
import 'babylonjs-loaders';

@Component({
  selector: 'app-artwork-3d',
  templateUrl: './artwork-3d.component.html',
  styleUrls: ['./artwork-3d.component.scss'],
})
export class Artwork3dComponent implements OnInit {
  @ViewChild('viewroom', { static: true })
  canvasRef: ElementRef<HTMLCanvasElement>;

  @Input() artworkData;
  @Output() onOrOff = new EventEmitter<boolean>();

  engine: BABYLON.Engine;
  scene: BABYLON.Scene;
  camera: BABYLON.ArcRotateCamera;
  faToggleOff = faToggleOff;
  faToggleOn = faToggleOn;
  isOn = false;
  constructor() {}

  ngOnInit(): void {
    this.engine = new BABYLON.Engine(this.canvasRef.nativeElement, true);

    const scene = this.CreateScene();

    this.engine.runRenderLoop(() => {
      scene.render();
    });

    this.scene = scene;
  }
  private CreateScene = () => {
    const scene = new BABYLON.Scene(this.engine);

    this.camera = new BABYLON.ArcRotateCamera(
      'Camera',
      -Math.PI / 2,
      Math.PI / 2,
      90,
      new BABYLON.Vector3(0, 5, 0),
      scene
    );
    if (this.artworkData.height >= this.artworkData.width) {
      this.camera.radius = 0.164 * this.artworkData.height;
    } else {
      this.camera.radius = 0.088 * this.artworkData.width;
    }

    const light = new BABYLON.DirectionalLight(
      'hemiLight2',
      new BABYLON.Vector3(0.05, -0.15, 0.12),
      scene
    );
    var light2 = new BABYLON.HemisphericLight(
      'HemiLight',
      new BABYLON.Vector3(-100, -100, -100),
      scene
    );
    light.position = new BABYLON.Vector3(10, 100, -300);
    light.intensity = 0.7;
    light2.intensity = 0.73;

    const wall = BABYLON.MeshBuilder.CreateBox(
      'wall',
      { height: 400, width: 400, depth: 0.5 },
      scene
    );
    const wall2 = BABYLON.MeshBuilder.CreateBox(
      'wall',
      { height: 10, width: 400, depth: 0.5 },
      scene
    );
    const art = BABYLON.MeshBuilder.CreateBox(
      'art',
      {
        height: this.artworkData.height / 10,
        width: this.artworkData.width / 10,
        depth: 0.2,
      },
      scene
    );

    const wallMaterial = new BABYLON.StandardMaterial('', scene);
    wallMaterial.diffuseColor = new BABYLON.Color3(0.9294, 0.9294, 0.9294);
    wallMaterial.specularColor = new BABYLON.Color3(0, 0, 0);
    wall.material = wallMaterial;
    wall2.material = wallMaterial;

    const artmat = new BABYLON.StandardMaterial('artmat', scene);
    artmat.diffuseTexture = new BABYLON.Texture(
      this.artworkData?.newAPI
        ? this.artworkData.imgHash
        : `https://register.terrain.art/artist-portal/assets/${this.artworkData.imgHash}`,
      scene
    );
    art.material = artmat;

    art.position.y = 5;
    art.position.z = -0.55;

    this.camera.lowerAlphaLimit = -Math.PI / 2 - 0.3;
    this.camera.upperAlphaLimit = -Math.PI / 2 + 0.3;
    this.camera.lowerBetaLimit = Math.PI / 2 - 0.5;
    this.camera.upperBetaLimit = Math.PI / 2 + 0.2;
    this.camera.lowerRadiusLimit = 3;
    this.camera.upperRadiusLimit = 50;

    wall.position.y = 86;
    wall2.position.y = -9.3;
    return scene;
  };

  toggle3d() {
    this.isOn = !this.isOn;
    this.onOrOff.emit(this.isOn);
    if (this.isOn) {
      this.camera.attachControl(this.canvasRef.nativeElement, true);
    } else {
      this.camera.detachControl();
    }
  }
}
