<div *ngIf="_Type == 1">
  <div class="input-container">
    <input
      [(ngModel)]="value"
      type="text"
      class="selection"
      [placeholder]="placeholder"
      (focus)="isDropDown = !isDropDown"
      (input)="onSearchChange($event.target.value)"
      (keyup.enter)="addNew($event.target.value)"
      [readonly]="Disable"
    />
    <button (click)="isDropDown = !isDropDown">
      <img src="assets/icons/arrow-down.png" class="flag-arrow" />
    </button>
    <div
      [ngClass]="{
        'dropdown-hidden': !isDropDown,
        'dropdown-visible': isDropDown
      }"
      [hidden]="Disable"
    >
      <ul>
        <li
          *ngFor="let item of data; let i = index"
          (click)="selectData(item)"
          [ngClass]="{
            highLighted: isSelected(item)
          }"
        >
          <div
            *ngIf="item?.name"
            class="country-name"
            [innerHTML]="item?.name"
          ></div>
          <div *ngIf="!item?.name" class="country-name">{{ item }}</div>
        </li>
        <li *ngIf="data.length <= 0">
          <div class="country-name">
            No matching entries, please press Enter to create
          </div>
        </li>
      </ul>
    </div>
  </div>
  <div *ngFor="let item of selectedData; let i = index" class="chip">
    <span [innerHTML]="item?.name ? item?.name : item"></span>
    <button (click)="closeItem(item)" class="buttons" [hidden]="Disable">
      <img
        src="../../../assets/icons/group-6-copy-2_2020-11-25/<EMAIL>"
      />
    </button>
  </div>
</div>

<div *ngIf="_Type == 2">
  <div class="input-container">
    <input
      [(ngModel)]="value"
      type="text"
      class="selection"
      [placeholder]="placeholder"
      (keyup.enter)="addNewAI($event.target.value)"
      [readonly]="Disable"
    />
  </div>
  <div style="display: flex">
    <div
      *ngFor="let item of aiTagsFinal | keyvalue; let i = index"
      class="chipAI"
      [ngClass]="{ trueTag: aiTagsFinal[item.key].confidence == 1 }"
    >
      <span
        class="input"
        role="textbox"
        [contentEditable]="aiTagToggler[i]"
        [textContent]="item.key"
        (focusout)="EditedAI(item.key, $event.target.textContent)"
        style="text-transform: capitalize"
      >
      </span>
      <span style="display: flex; gap: 0.2vw">
        <fa-icon
          [title]="'Edit' + item.key"
          [icon]="faPencilAlt"
          (click)="aiTagToggler[i] = !aiTagToggler[i]"
        ></fa-icon>
        <fa-icon
          [title]="'Verify' + item.key"
          [icon]="faCheck"
          (click)="AiCheck(item.key)"
        ></fa-icon>

        <fa-icon
          (click)="closeItemAI(item.key)"
          [title]="'Remove' + item.key"
          [icon]="faTimes"
        ></fa-icon>
      </span>
    </div>
  </div>
</div>
