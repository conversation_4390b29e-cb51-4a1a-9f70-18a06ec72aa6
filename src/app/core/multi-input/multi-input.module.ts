import { NgModule } from '@angular/core';
import { MultiInputComponent } from './multi-input.component';
import { RouterModule } from '@angular/router';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
	declarations: [MultiInputComponent],
	imports: [SharedModule, RouterModule, FontAwesomeModule, FormsModule,ReactiveFormsModule],
	exports: [MultiInputComponent],
})
export class MultiInputModule {}
