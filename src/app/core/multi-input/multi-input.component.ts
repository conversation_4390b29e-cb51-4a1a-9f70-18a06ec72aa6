import { Data } from './../accordion/accordion.model';
import {
  faCheck,
  faPencilAlt,
  faTimes,
} from '@fortawesome/free-solid-svg-icons';
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { CartService } from 'src/app/services/cart.service';
// import { } from '@angular/core';

@Component({
  selector: 'multi-input',
  templateUrl: './multi-input.component.html',
  styleUrls: ['./multi-input.component.scss'],
})
export class MultiInputComponent implements OnInit {
  @Input() placeholder: string;
  faPencilAlt = faPencilAlt;
  faTimes = faTimes;
  faCheck = faCheck;
  _itemsArray: any = [];
  _previosItems: any = [];
  _aiSuggestedTags: object;
  aiTagsFinal: object;
  aiTagToggler;
  _Type: number = 1;
  @Input() Disable: boolean = false;
  @Input() newEntry: boolean = true;
  @Input() disableSearch: boolean = false;

  // @Input() control: FormControl;
  @Output() selectedItem = new EventEmitter<Array<string>>();
  @Output() selectedItemAI = new EventEmitter<object>();
  @Output() searchEvent = new EventEmitter<string>();
  isDropDown = false;
  value;
  dataUnfiltered = [];
  i = 7;
  data;
  selectedData = [];

  @Input()
  public set Type(val: number) {
    this._Type = val;
    console.log(val);
  }
  public get Type() {
    return this._Type;
  }

  @Input()
  public set AIsuggestedTags(val: any) {
    this._aiSuggestedTags = val;
    this.aiTagsFinal = val;
    console.log(this.aiTagsFinal);
    this.aiTagToggler = Array(Object.keys(this.aiTagsFinal).length).fill(false);
  }

  public get AIsuggestedTags() {
    return this._aiSuggestedTags;
  }

  update_fields(value) {
    let timeOut = setTimeout(() => {
      console.log(value);
    }, 500);
  }
  consss() {
    console.log(this.aiTagsFinal);
    console.log(this.aiTagToggler);
  }

  @Input()
  public set previosItems(val: any) {
    this._previosItems = val;
    this.selectedData = val;
    console.log(this.selectedData);
  }

  public get previosItems() {
    return this._previosItems;
  }
  @Input()
  public set itemsArray(val: any) {
    this._itemsArray = val;
    this.dataUnfiltered = val;
    this.data = this.dataUnfiltered;
  }

  public get itemsArray() {
    return this._previosItems;
  }

  constructor() {}
  ngOnInit(): void {
    // this.selectedData = this.previosItems;
    // this.dataUnfiltered = this.itemsArray;
    // this.data = this.dataUnfiltered;
    // console.log(this.data);
  }
  onSearchChange(searchValue: string): void {
    if (this.disableSearch) {
      this.searchEvent.emit(searchValue);
    } else {
      this.data = this.matchWith(this.dataUnfiltered, searchValue);
    }
  }
  isSelected(item) {
    return this.selectedData?.some((a) => a == item);
  }
  matchWith(array, key) {
    const matcher = new RegExp(`${key}`, 'g');
    if (array.length > 0 && array[0]?.name) {
      return array.filter((word) => matcher.test(word.name));
    } else {
      return array.filter((word) => matcher.test(word));
    }
  }
  selectData(i) {
    console.log(this.selectedData);
    if (this.selectedData.findIndex((x) => x == i) == -1) {
      this.selectedData?.push(this.data.find((x) => x == i));
    }
    this.isDropDown = false;
    this.value = null;
    this.data = this.dataUnfiltered;
    this.selectedItem.emit(this.selectedData);
  }
  closeItem(i) {
    console.log(this.selectedData.findIndex((x) => x == i));
    this.selectedData.splice(
      this.selectedData.findIndex((x) => x == i),
      1
    );
    this.selectedItem.emit(this.selectedData);
  }
  addNew(j) {
    if (this.data.length <= 0 && this.newEntry) {
      this.dataUnfiltered.push(j);
      this.data = this.dataUnfiltered;
      this.selectData(j);
    }
  }

  addNewAI(j) {
    let previouslyAdded = false;
    for (let index = 0; index < Object.keys(this.aiTagsFinal).length; index++) {
      const element = this.aiTagsFinal[index];
      if (element == j) {
        previouslyAdded = true;
        break;
      }
    }
    if (previouslyAdded == false) {
      this.aiTagsFinal[j] = {
        confidence: 1,
      };
      this.aiTagToggler = Array(Object.keys(this.aiTagsFinal).length).fill(
        false
      );
      this.selectedItemAI.emit(this.aiTagsFinal);
    }
  }
  AiCheck(key) {
    this.aiTagsFinal[key].confidence = 1;
  }
  closeItemAI(key) {
    delete this.aiTagsFinal[key];
    this.aiTagToggler = Array(Object.keys(this.aiTagsFinal).length).fill(false);

    this.selectedItemAI.emit(this.aiTagsFinal);
  }
  EditedAI(oldname, newname) {
    Object.defineProperty(
      this.aiTagsFinal,
      newname,
      Object.getOwnPropertyDescriptor(this.aiTagsFinal, oldname)
    );
    delete this.aiTagsFinal[oldname];
    this.aiTagsFinal[newname].confidence = 1;
  }
}
