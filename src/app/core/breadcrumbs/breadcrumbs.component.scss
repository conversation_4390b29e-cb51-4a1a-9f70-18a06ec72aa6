.btn-image-wrapper {
  outline: none;
  border: none;
  background: none;
  width: 1.5vw;
  height: 1.5vw;
  padding: 0vw;
  margin-right: 0.625vw;
  border-radius: 50%;
  img {
    border-radius: 50%;
    width: 1.5vw;
    height: 1.5vw;
  }
}
.margin-vertical {
  margin-top: 1.5vw;
  margin-bottom: 1.5vw;
}
.btn-image-wrapper:hover {
  background: var(--primary-border-color);
}
.bread-crumbs {
  &.ontop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    a {
      color: #fff !important;
    }
  }

  z-index: 1;
}
a {
  color: #004ddd;
}
* {
  font-size: 1vw;
}
@media (max-width: 768px) {
  * {
    font-size: 3vw;
  }

  .margin-vertical {
    margin-top: 3vw;
    margin-bottom: 3vw;
  }
  .btn-image-wrapper {
    width: 5vw;
    height: 5vw;
    margin-right: 2.42vw;
    img {
      width: 5vw;
      height: 5vw;
    }
  }
}
abbr {
  text-decoration: none !important;
}
