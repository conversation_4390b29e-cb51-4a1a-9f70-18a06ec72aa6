<div
  *ngIf="!hidebread"
  class="section margin-vertical bread-crumbs"
  [ngClass]="{ ontop: onTop }"
  [ngStyle]="{ 'margin-top': isMobile ? marginTopm : marginTop }"
>
  <div
    class="section-inner section-margin-horizontal justify-content-start align-items-center d-flex"
    style="color: #808080"
  >
    <button class="btn-image-wrapper">
      <a (click)="gotoBack()" class="linked" style="cursor: pointer">
        <img
          src="assets/icons/back-chevron_2020-11-12/<EMAIL>"
          alt=""
        />
      </a>
    </button>
    <div style="display: block">
      <div
        *ngFor="let breadcrumb of menuItems; let i = index"
        style="display: inline-block"
      >
        <a *ngIf="!isMobile" [href]="breadcrumb.url" class="linked">{{
          breadcrumb.label
        }}</a>
        <a *ngIf="isMobile" [href]="breadcrumb.url" class="linked">
          <abbr *ngIf="breadcrumb.label.length > 9" [title]="breadcrumb.label"
            >{{ breadcrumb.label.slice(0, 9) }}...</abbr
          >
          <span *ngIf="breadcrumb.label.length <= 9">{{
            breadcrumb.label
          }}</span>
        </a>
        <span *ngIf="i !== menuItems.length - 1">&nbsp;/&nbsp;</span>
      </div>
    </div>
  </div>
</div>
