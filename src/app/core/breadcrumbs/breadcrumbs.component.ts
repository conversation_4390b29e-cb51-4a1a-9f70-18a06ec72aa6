import { Component, HostListener, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Observable } from 'babylonjs/Misc/observable';
import { distinctUntilChanged, filter } from 'rxjs/operators';
import { Location } from '@angular/common';
export interface MenuItem {
  label: string;
  url: string;
}
@Component({
  selector: 'app-breadcrumbs',
  templateUrl: './breadcrumbs.component.html',
  styleUrls: ['./breadcrumbs.component.scss'],
})
export class BreadcrumbsComponent implements OnInit {
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    // event.target.innerWidth;
    const w = event.target.innerWidth;
    this.isMobile = this.getIsMobile(w);
  }
  isMobile = false;
  menuItems: MenuItem[] = [];
  hidebread = false;
  onTop = false;
  marginTop = null;
  marginTopm = null;
  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    private location: Location
  ) {}

  ngOnInit(): void {
    const w = document.documentElement.clientWidth;
    this.isMobile = this.getIsMobile(w);
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(
        () =>
          (this.menuItems = this.createBreadcrumbs(this.activatedRoute.root))
      );
  }

  private createBreadcrumbs(
    route: ActivatedRoute,
    url: string = '',
    breadcrumbs: MenuItem[] = []
  ): MenuItem[] {
    const children: ActivatedRoute[] = route.children;

    if (children.length === 0) {
      console.log(breadcrumbs);

      return breadcrumbs.slice(0, breadcrumbs.length - 1);
    }
    console.log(children);

    for (const child of children) {
      const routeURL: string = child.snapshot.url
        .map((segment) => segment.path)
        .join('/');
      if (routeURL !== '') {
        url += `/${routeURL}`;
      }

      let label = child.snapshot.data['breadcrumb'];
      if (label === 'param') {
        label = child.snapshot.params[child.snapshot.data['param']];
      }
      this.hidebread = child.snapshot.data['hidebread'];
      this.onTop = child.snapshot.data['ontop'];
      this.marginTop = child.snapshot.data['margintop'];
      this.marginTopm = child.snapshot.data['margintopm'];
      if (label !== null && label !== undefined) {
        breadcrumbs.push({ label, url });
      }

      return this.createBreadcrumbs(child, url, breadcrumbs);
    }
  }
  public getIsMobile(w): boolean {
    const breakpoint = 768;
    if (w <= breakpoint) {
      this.isMobile = true;
      return true;
    } else {
      this.isMobile = false;
      return false;
    }
  }
  gotoBack() {
    this.location.back();
  }
}
